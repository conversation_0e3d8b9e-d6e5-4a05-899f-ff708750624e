/**
 * @file    Lri.cpp
 * @brief   Lri class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "RosMgr.h"
#include "Lri.h"

/******************************************************************************
*
* LRI - Long Range Interrogation
*
* $--LRI,x,a,xxxxxxxxx,xxxxxxxxx,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a*hh<CR><LF>
*        | | |         |         |       | |        | |       | |        |
*        1 2 3         4         5       6 7        8 9      10 11       12
*
* 1.     Sequence Number , 0 to 9
* 2.     Control Flag
* 3.     MMSI of "requester"
* 4.     MMSI of "destination"
* 5.6.   Latitude  - N/S (north-east co-ordinate)
* 7.8.   Longitude - E/W (north-east co-ordinate)
* 9.10.  Latitude  - N/S (south-west co-ordinate)
* 11.12. Longitude - E/W (south-west co-ordinate)
*
******************************************************************************/
// Define static member variables
int8_t  CLri::m_wSeqNum = 0;
char    CLri::m_chCtrlFlag = '0';
int32_t CLri::m_dwMMSIReq = 0;
int32_t CLri::m_dwMMSIDest = 0;
double  CLri::m_fLatNE = 0;
double  CLri::m_fLonNE = 0;
double  CLri::m_fLatSW = 0;
double  CLri::m_fLonSW = 0;

CLri::CLri() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CLri::Parse(const char *pszSentence)
{
    m_wSeqNum       = GetFieldInteger(pszSentence,1);
    m_chCtrlFlag    = GetFieldChar(pszSentence, 2);
    m_dwMMSIReq     = GetFieldInteger(pszSentence, 3);
    m_dwMMSIDest    = GetFieldInteger(pszSentence, 4);

    if (m_chCtrlFlag == '1' || m_dwMMSIDest == NMEA_NULL_INTEGER)
    {
        char pstrSubData1[30];
        char pstrSubData2[10];

        CSentence::GetFieldString(pszSentence, 5, pstrSubData1, sizeof(pstrSubData1));
        CSentence::GetFieldString(pszSentence, 6, pstrSubData2, sizeof(pstrSubData2));
        m_fLatNE = CAisLib::GetLatitude(pstrSubData1, pstrSubData2[0]);

        CSentence::GetFieldString(pszSentence, 7, pstrSubData1, sizeof(pstrSubData1));
        CSentence::GetFieldString(pszSentence, 8, pstrSubData2, sizeof(pstrSubData2));
        m_fLonNE = CAisLib::GetLongitude(pstrSubData1, pstrSubData2[0]);

        CSentence::GetFieldString(pszSentence, 9, pstrSubData1, sizeof(pstrSubData1));
        CSentence::GetFieldString(pszSentence, 10, pstrSubData2, sizeof(pstrSubData2));
        m_fLatSW = CAisLib::GetLatitude(pstrSubData1, pstrSubData2[0]);

        CSentence::GetFieldString(pszSentence, 11, pstrSubData1, sizeof(pstrSubData1));
        CSentence::GetFieldString(pszSentence, 12, pstrSubData2, sizeof(pstrSubData2));
        m_fLonSW = CAisLib::GetLongitude(pstrSubData1, pstrSubData2[0]);
    }

    return true;
}

/**
 * @brief Make the LRI sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CLri::MakeSentence(char *pszSentence)
{
    return strlen(pszSentence);
}




