/**
 * @file    Vtg.cpp
 * @brief   Vtg class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Vtg.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * VTG - 
 *
 * $--VTG,x.x,T,x.x,M,x.x,N,x.x,K,a*hh<CR><LF>
 *         |--|  |--|  |--|  |--| |
 *         1     2     3     4    5
 *
 * 1. Course over ground, degrees true
 * 2. Course over ground, degrees magnetic
 * 3. Speed over ground, knots
 * 4. Speed over ground, km/h
 * 5. Mode indicator
 * 
 ******************************************************************************/
CVtg::CVtg() : CSentence()
{
    ClearData();
}

void CVtg::ClearData(void)
{
    m_nSpdData = NMEA_SOG_NULL;
    m_nVelData = NMEA_SOG_NULL;
    m_nCrsData = NMEA_COG_NULL;
    m_dwCrsValidTick = 0;
    m_dwSpdValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CVtg::Parse(const char *pszSentence, int nCurCrs)
{
    char pstrTmp[128];
    int  nTrue = NMEA_COG_NULL;
    int  nMag = NMEA_COG_NULL;
    int  nSpd, nVel;

    bool bCourseValid = false;
    bool bSpdValidKnot = false;
    bool bSpdValidKm = false;
    bool bTrueCogValid = false;
    bool bMagCogValid = false;

    GetFieldString(pszSentence, 1, pstrTmp);     // Course over ground
    if (strlen(pstrTmp) == 0)
        nTrue = nCurCrs;
    else
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
        {
            return false;
        }
        nTrue = (int)(atof(pstrTmp) * NMEA_SCALE_COG);                        // COG, degree true
    }

    GetFieldString(pszSentence, 2, pstrTmp);     // degrees true
    if(pstrTmp[0] == 'T')
    {
        bTrueCogValid = TRUE;
    }

    GetFieldString(pszSentence, 3, pstrTmp);     // Course over ground
    if (strlen(pstrTmp) == 0)
        nMag = nTrue;
    else
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
        {
            return false;
        }
        nMag = (int)(atof(pstrTmp) * NMEA_SCALE_COG);
    }

    GetFieldString(pszSentence, 4, pstrTmp);     // degrees magnetic
    if(pstrTmp[0] == 'M')
    {
        bMagCogValid = TRUE;
    }

    GetFieldString(pszSentence, 5, pstrTmp);     // Speed over ground
    if (strlen(pstrTmp) != 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
        {
            return false;
        }

        nSpd = (int)(atof(pstrTmp) * NMEA_SCALE_SOG);
    }

    GetFieldString(pszSentence, 6, pstrTmp);     // knots
    if(pstrTmp[0] == 'N')
    {
        bSpdValidKnot = (nSpd >= 0);// The speed over the ground should always be non-negative
    }

    GetFieldString(pszSentence, 7, pstrTmp);     // Speed over ground,
    if(strlen(pstrTmp) > 0 && !CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
    {
        return false;
    }
    nVel = (int)(atof(pstrTmp) * NMEA_SCALE_SOG);
    if (strlen(pstrTmp) == 0)
    {
        nVel = (int)(nSpd * 1.852); // SOG, km/h
        bSpdValidKm = (nVel >= 0);  // The speed over the ground should always be non-negative
    }

    GetFieldString(pszSentence, 8, pstrTmp);     // km/h
    if(pstrTmp[0] == 'K')
    {
        GetFieldString(pszSentence, 9, pstrTmp); // Mode indicator
        if(pstrTmp[0] != 'N' && pstrTmp[0] != 'E' && pstrTmp[0] != 'M' && pstrTmp[0] != 'S')        // Check mode indicator
        {
            if(!bSpdValidKnot && bSpdValidKm)
                nSpd = (int)(nVel / 1.852);

            if (nSpd > 9999)
            {
                return false;
            }
            if (nVel > 9999)
            {
                return false;
            }
            if (nTrue >= 3600)
            {
                return false;
            }
            if (nMag  >= 3600)
            {
                return false;
            }

            bCourseValid = bTrueCogValid && bMagCogValid && nTrue != NMEA_COG_NULL && nMag != NMEA_COG_NULL;

            if(bCourseValid)
            {
                m_nCrsData = nTrue;
                m_dwCrsValidTick = SysGetSystemTimer();
            }

            if(bSpdValidKnot || bSpdValidKm)
            {
                m_nSpdData = nSpd;
                if(bSpdValidKm)
                    m_nVelData = nVel;
                m_dwSpdValidTick = SysGetSystemTimer();
            }

            return true;
        }
    }

    return true;
}

/**
 * @brief Check received course data is valid or not
 */
bool CVtg::IsValidCourseData(void)
{
    return (m_dwCrsValidTick && SysGetDiffTimeMili(m_dwCrsValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Check received speed data is valid or not
 */
bool CVtg::IsValidSpeedData(void)
{
    return (m_dwSpdValidTick && SysGetDiffTimeMili(m_dwSpdValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Get received course data
 */
int CVtg::GetCourse(void)
{
    return m_nCrsData;
}

/**
 * @brief Get received speed data
 */
int CVtg::GetSpeed(void)
{
    return m_nSpdData;
}

/**
 * @brief Get received speed data
 */
int CVtg::GetVelocity(void)
{
    return m_nVelData;
}

/**
 * @brief Call function periodically
 */
void CVtg::RunPeriodically(void)
{
    if (m_dwCrsValidTick && SysGetDiffTimeMili(m_dwCrsValidTick) < NMEA_COG_LASTDATA_STAYMS)
    {
        m_dwCrsValidTick = 0;
        m_nCrsData = NMEA_COG_NULL;
    }

    if (m_dwSpdValidTick && SysGetDiffTimeMili(m_dwSpdValidTick) < NMEA_SOG_LASTDATA_STAYMS)
    {
        m_dwSpdValidTick = 0;
        m_nSpdData = NMEA_SOG_NULL;
        m_nVelData = NMEA_SOG_NULL;
    }
}
