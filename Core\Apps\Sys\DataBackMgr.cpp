/**
 * @file    DataBack.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "GPIO.h"
#include "FlashMem.h"
#include "EEPROM.h"
#include "DataBackMgr.h"

//===========================================================================
CDataBackMgr::CDataBackMgr(int nDeviceId, DWORD dStartBackAddr)
{
    if (nDeviceId == BACK_DEVICE_FLASH) {
        m_pBackDeviceCtrl = std::dynamic_pointer_cast<CDevMem>(cSTM32IntFlash::getInst());
    }
    else {
        m_pBackDeviceCtrl = std::dynamic_pointer_cast<CDevMem>(CEEPROM::getInst());
    }

    m_nDeviceId = nDeviceId;
    m_nStartPos = -1;

    SetAllClearMode(0);
    SetStartBackAddr(dStartBackAddr);
}

CDataBackMgr::~CDataBackMgr(void)
{
}

void  CDataBackMgr::SetAllClearMode(int nMode)
{
    m_nAllClearMode = nMode;
}

int   CDataBackMgr::GetAllClearMode(void)
{
    return(m_nAllClearMode);
}

void  CDataBackMgr::SetStartBackAddr(DWORD dStartBackAddr)
{
    m_dStartBackAddr = dStartBackAddr;
}

DWORD CDataBackMgr::GetStartBackAddr(void)
{
    return (m_dStartBackAddr);
}

int   CDataBackMgr::BackDataSegmentWrite(void *pData, int nWriteSize)
{
    if (m_nDeviceId == BACK_DEVICE_FLASH)
    {
        DWORD pHead[FLASH_NB_32BITWORD_IN_FLASHWORD];
        int   nSegSize;
        int   nCount;

        pHead[0] = DATA_SIGNATURE_HEX;

        nSegSize = FindDataSegmentSize(nWriteSize + _FLASH_WORD_);

        nCount = m_pBackDeviceCtrl->GetSectorSize(m_dStartBackAddr) / nSegSize;

        if (m_nStartPos == -1)
        {
            m_nStartPos = FindDataSegmentStart(nSegSize);

            if (m_nStartPos < 0)
                m_nStartPos = 0;
            else
            {
                ++m_nStartPos;
                if (m_nStartPos >= nCount)
                    m_nStartPos = 0;
            }
        }

        if (GetAllClearMode())
            m_nStartPos = 0;

        if (m_nStartPos == 0)
        {
            m_pBackDeviceCtrl->EraseSector(m_dStartBackAddr);
        }

        DEBUG_LOG("FlashWrite] %x, BaseAddr: %x, segSize: %d, startPos: %d, writeSize: %d, cnt: %d, head: %x\r\n",
                            m_dStartBackAddr + m_nStartPos * nSegSize, m_dStartBackAddr, nSegSize, m_nStartPos, nWriteSize, nCount, pHead[0]);

        m_pBackDeviceCtrl->WriteData(m_dStartBackAddr + m_nStartPos * nSegSize + 0           , pHead, _FLASH_WORD_);
        m_pBackDeviceCtrl->WriteData(m_dStartBackAddr + m_nStartPos * nSegSize + _FLASH_WORD_, pData, nWriteSize  );

        ++m_nStartPos;
        if (m_nStartPos >= nCount)
            m_nStartPos = 0;

        return (m_nStartPos);
    }
    else {
        m_pBackDeviceCtrl->WriteData(m_dStartBackAddr, pData, nWriteSize);
    }

    return nWriteSize;
}

int   CDataBackMgr::BackDataSegmentRead(void *pData, int nReadSize)
{
    if (m_nDeviceId == BACK_DEVICE_FLASH)
    {
        int   nStartPos;
        int   nSegSize;
        DWORD pHead[FLASH_NB_32BITWORD_IN_FLASHWORD];

        nSegSize = FindDataSegmentSize(nReadSize + _FLASH_WORD_);

        nStartPos = FindDataSegmentStart(nSegSize);

        if (nStartPos == -1)
            memset(pData, 0xff, nSegSize);
        else
        {
            m_pBackDeviceCtrl->ReadData(m_dStartBackAddr + nStartPos * nSegSize, pHead, _FLASH_WORD_);
            m_pBackDeviceCtrl->ReadData(m_dStartBackAddr + nStartPos * nSegSize + _FLASH_WORD_, pData, nReadSize);
        }

        DEBUG_LOG("FlashRead] %x, BaseAddr: %x, segSize: %d, startPos: %d, readSize: %d, head: %x\r\n",
                            m_dStartBackAddr + nStartPos * nSegSize, m_dStartBackAddr, nSegSize, nStartPos, nReadSize,
                            ((DWORD*)m_dStartBackAddr)[0]);

        return (nReadSize);
    }
    else {
        m_pBackDeviceCtrl->ReadData(m_dStartBackAddr, pData, nReadSize);
    }

    return nReadSize;
}

int   CDataBackMgr::FindDataSegmentStart(int nSegSize)
{
    DWORD dHead;
    int   i, nCount;
    DWORD dAddr;

    nCount = m_pBackDeviceCtrl->GetSectorSize(m_dStartBackAddr) / nSegSize;
    dAddr  = m_dStartBackAddr;

    for (i = 0; i < nCount; i++)
    {
        m_pBackDeviceCtrl->ReadData(dAddr, &dHead, sizeof(dHead));

        if (dHead != DATA_SIGNATURE_HEX)
            return(i - 1);

        dAddr += nSegSize;
    }

    return(nCount - 1);
}

int   CDataBackMgr::FindDataSegmentSize(int nDataSize)
{
    int  nSegSize;

    nSegSize = _FLASH_WORD_;
    while (nSegSize < nDataSize)
        nSegSize = nSegSize * 2;

    return(nSegSize);
}

