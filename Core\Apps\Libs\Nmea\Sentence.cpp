/**
 * @file    Sentence.cpp
 * @brief   Sentence class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "Sentence.h"


/**
 * @fn      void CSentence::GetTalkerID(char szTalker[3])
 * @brief   Function to get the talker ID from the NMEA sentence.
 * @param   pszSentence [IN] NMEA Sentence to be parsed.
 * @param   szTalker [OUT] Talker ID
 * @return  None
 * @remarks "$--XXX,,,,.....*hh<CR><LF>" Here, '--' is the Talker.
 */
void CSentence::GetTalkerID(const char *pszSentence, char *pszTalker/*[3]*/)
{
    if( strlen(pszSentence) < 3 )
    {
        pszTalker[0] = '\0';
        return;
    }

    pszTalker[0] = pszSentence[1];
    pszTalker[1] = pszSentence[2];
    pszTalker[2] = '\0';
}

/**
 * @fn     void CSentence::GetSenderID(const char *pszSentence, char *pszFormat)
 * @brief  Function to get the sender id from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  pszFormat [OUT] Sender ID
 * @return None
 */
void CSentence::GetSenderID(const char *pszSentence, char *pszFormat/*[4]*/)
{
    // $--XXX,,,,.....
    // 'XXX' : Sender ID
    int offset = (pszSentence[1] == '0' ) ? 1 : 0;

    if( strlen(pszSentence) < 6-offset )
    {
        pszFormat[0] = '\0';
        return;
    }

    pszFormat[0] = pszSentence[3-offset];
    pszFormat[1] = pszSentence[4-offset];
    pszFormat[2] = pszSentence[5-offset];
    pszFormat[3] = '\0';
}

/**
 * @fn     void CSentence::GetTalkerSenderID(const char *pszSentence, char *pszFormat)
 * @brief  Function to get the sender id from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param   szTalker [OUT] Talker ID
 * @param  pszFormat [OUT] Sender ID
 * @return None
 */
void CSentence::GetTalkerSenderID(const char *pszSentence, char *pszTalker, char *pszFormat)
{
    int nLen = strlen(pszSentence);

    if( nLen < 3 )
    {
        pszTalker[0] = '\0';
        return;
    }

    pszTalker[0] = pszSentence[1];
    pszTalker[1] = pszSentence[2];
    pszTalker[2] = '\0';

    if( nLen < 6 )
    {
        pszFormat[0] = '\0';
        return;
    }

    pszFormat[0] = pszSentence[3];
    pszFormat[1] = pszSentence[4];
    pszFormat[2] = pszSentence[5];
    pszFormat[3] = '\0';
}

/**
 * @fn     void CSentence::AddSentenceTail(const char *pszSentence)
 * @brief  Function to add sentence checksum and tail info.
 * @param  pszSentence NMEA sentence to add tail info
 * @return None
 */
void CSentence::AddSentenceTail(char *pszSentence)
{
    char strTail[8];
    int nStrLen;

    nStrLen = strlen((char*)pszSentence);
    sprintf((char*)strTail, "*%02X\r\n", ComputeCheckSum(pszSentence, nStrLen));
    strcat(pszSentence, strTail);
    return;
}

/**
 * @fn     int CSentence::ComputeCheckSum(char *pszSentence)
 * @brief  Function to compute the checksum from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @return Checksum value
 */
int CSentence::ComputeCheckSum(const char *pszSentence, int nLen)
{
    int nChecksumVal = 0;
    int index = 1; // Skip over the '$' and '!' at the begining of the sentence

    while( index < nLen)
    {
        nChecksumVal ^= pszSentence[index];
        ++index;
    }

    return(nChecksumVal);
}

/**
 * @fn     int CSentence::ComputeCheckSum(char *pszSentence)
 * @brief  Function to compute the checksum from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  pX [OUT] Checksum high value
 * @param  pY [OUT] Checksum low value
 * @return Checksum value
 */
int CSentence::ComputeCheckSum(const char *pszSentence, uint8_t *pX, uint8_t *pY)
{
    int nChecksumVal = 0;
    int index = 1; // Skip over the '$' and '!' at the begining of the sentence
    int nLen = strlen(pszSentence);
    uint8_t bX, bY;

    while( index < nLen)
    {
        nChecksumVal ^= pszSentence[index];
        ++index;
    }

    bX = (nChecksumVal >> 4) & 0x0f;
    if (bX > 0x09)
        bX = bX - 0x0a + 'A';
    else
        bX = bX        + '0';

    bY = nChecksumVal & 0x0f;
    if (bY > 0x09)
        bY = bY - 0x0a + 'A';
    else
        bY = bY        + '0';

    *pX = bX;
    *pY = bY;

    return(nChecksumVal);
}

/**
 * @fn     bool CSentence::IsValidCheckSum()
 * @brief  Function to check if the checksum is valid.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @return TRUE if valid, FALSE otherwise
 */
bool CSentence::IsValidCheckSum(const char *pszSentence)
{
    char szChecksum[3];
    int  nCheckSumVal;
    int  nLen = strlen(pszSentence);

    if (pszSentence[nLen-2] == '\r' || pszSentence[nLen-1] == '\n')
    {
        nLen -= 2;
    }

    if(pszSentence[nLen - 3] == '*')
    {
        strncpy(szChecksum, pszSentence+(nLen-2), 2);
        szChecksum[2] = '\0';
        sscanf(szChecksum, "%x", &nCheckSumVal);

        return (ComputeCheckSum(pszSentence, nLen - 3) == nCheckSumVal);
    }

    return false;
}

/**
 * @fn     int32_t CSentence::GetFieldInteger(int nField)
 * @brief  Function to get the integer value from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  nField [IN] Field number
 * @return Integer value
 */
int32_t CSentence::GetFieldInteger(const char *pszSentence, int nField)
{
    int  nResult;
    char szField[100];
    int  nLen;

    nLen = GetFieldString(pszSentence, nField, szField);

    if( nLen == 0 )
        nResult = NMEA_NULL_INTEGER;
    else
        nResult = atoi(szField);
    
    return nResult;
}

/**
 * @fn     int32_t CSentence::GetFieldHexa(int nField)
 * @brief  Function to get the hexadecimal value from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  nField [IN] Field number
 * @return Hexadecimal value
 */
int32_t CSentence::GetFieldHexa(const char *pszSentence, int nField)
{
    int  nResult;
    char szField[100];
    int  nLen;

    nLen = GetFieldString(pszSentence, nField, szField);

    if( nLen == 0 )
        nResult = NMEA_NULL_INTEGER;
    else
        sscanf(szField, "%x", &nResult);
    
    return nResult;
}

/**
 * @fn     double CSentence::GetFieldDouble(int nField)
 * @brief  Function to get the double value from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  nField [IN] Field number
 * @return Double value
 */
double CSentence::GetFieldDouble(const char *pszSentence, int nField)
{
    double dblResult;
    char   szField[100];
    int    nLen;

    nLen = GetFieldString(pszSentence, nField, szField);
    
    if( nLen == 0 )
        dblResult = NMEA_NULL_DOUBLE;
    else
        dblResult = atof(szField);
    
    return dblResult;
}

/**
 * @fn     char CSentence::GetFieldChar(int nField)
 * @brief  Function to get the character value from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  nField [IN] Field number
 * @return Character value
 */
char CSentence::GetFieldChar(const char *pszSentence, int nField)
{
    char chResult;
    char szField[100];
    int  nLen;

    nLen = GetFieldString(pszSentence, nField, szField);
    
    if( nLen == 0 )
        chResult = NMEA_NULL_CHAR;
    else
        chResult = szField[0];
    
    return chResult;
}

/**
 * @fn     int CSentence::GetFieldString(int nField, char *pszField, int nBuffLen)
 * @brief  Function to get the string value from the NMEA sentence.
 * @param  pszSentence [IN] NMEA sentence to be parsed.
 * @param  nField [IN] Field number
 * @param  pszField [OUT] String value
 * @param  nBuffLen [IN] Buffer length
 * @return Length of the string
 */
int32_t CSentence::GetFieldString(const char *pszSentence, int nField, char *pszField, int nBuffLen)
{
    int nFieldCount  = 0;
    int nStartPos    = 0, nLen = 0;
    int nStartFlag   = 0;
    int nSentenceLen = strlen(pszSentence);
    int i;

    bool bTestMode = (pszSentence[0] == '%') ? true : false;

    for( i = 0; i < nSentenceLen; i++ )
    {
        if( pszSentence[i] == ',' || pszSentence[i] == '*' || (bTestMode && pszSentence[i] == ' ') )
            nFieldCount++;

        if( !nStartFlag && nFieldCount == nField )
        {
            nStartPos  = i+1;
            nStartFlag = 1;
        }
        else if( nFieldCount == nField+1 )
        {
            nLen = i - nStartPos;
            break;
        }
    }

    if( nStartFlag )
    {
        if (nBuffLen > 0)   nLen = ((i - nStartPos) > nBuffLen) ? nBuffLen : (i - nStartPos);
        else                nLen = i - nStartPos;
        strncpy(pszField, pszSentence+nStartPos, nLen);
        pszField[nLen] = '\0';
    }
    else
    {
        nLen = 0;
        pszField[0] = '\0';
    }
    
    return nLen;
}


/*************************************************************************/
/**
 * @fn      char *CSentence::ConvertNMEAString(const char *pszText)
 * @brief   Function to convert a string to NMEA format.
 * @param   pszText [IN] Text to be converted
 * @return  Converted NMEA string
 */
char *CSentence::ConvertNMEAString(const char *pszText)
{
    static char szBuf[BUFSIZ];
    char szHex[8];
    int  nLen = strlen(pszText);
    int  nPos = 0;

    szBuf[0] = '\0';

    for( int i = 0; i < nLen; ++i )
    {
        if( IsNMEAReservedCharater(pszText[i]) )
        {
            sprintf(szHex, "^%02X", pszText[i]);
            strcpy(szBuf+nPos, szHex);
            nPos += strlen(szHex);
        }
        else
        {
            szBuf[nPos++] = pszText[i];
        }
    }

    szBuf[nPos] = '\0';

    return szBuf;
}

/*************************************************************************/
/**
 * @fn      char *CSentence::ConvertNormalString(const char *pszText)
 * @brief   Function to convert an NMEA string to normal format.
 * @param   pszText [IN] NMEA text to be converted
 * @return  Converted normal string
 */
char *CSentence::ConvertNormalString(const char *pszText)
{
    static char szBuf[BUFSIZ];
    int  nLen = strlen(pszText);
    int  nPos = 0, i = 0;
    char ch;

    memset(szBuf, 0x00, BUFSIZ);

    while( i < nLen )
    {
        if( pszText[i] == '^' )
        {
            ch  = xtod(pszText[i+1]) * 16;
            ch += xtod(pszText[i+2]);
            szBuf[nPos] = ch;
            ++nPos;
            i += 3;
        }
        else
        {
            szBuf[nPos] = pszText[i];
            ++nPos;
            ++i;
        }
    }

    szBuf[nPos] = '\0';
    return szBuf;
}

/**
 * @fn      char *CSentence::ConvertPosition(char *pLatBuf, char *pLonBuf, double *pLatVal, double *pLonVal)
 * @brief   Function to convert an latitude/longuitude string to position data
 * @param   pLatBuf [IN] latitude string
 * @param   pLonBuf [IN] longuitude string
 * @param   pLatVal [OUT] latitude value
 * @param   pLonVal [OUT] longuitude value
 * @return  Converted normal string
 */
int CSentence::ConvertPosition(char *pLatBuf, char *pLonBuf, double *pLatVal, double *pLonVal)
{
    int    nLen,i;
    int    nTemp;
    double rTmp;
    double rLat, rLon;

    if(strlen(pLatBuf) <= 0 || strlen(pLonBuf) <= 0)
        return 0;

    if (pLatVal == NULL || pLonVal == NULL)
        return 0;

    nLen = strlen((char*)pLatBuf);
    if (nLen > 9)
    {
        nLen = 9;
        pLatBuf[nLen] = 0x00;
    }

    if (nLen < 7 || nLen > 10)
        return 0;

    if (nLen == 10)
    {
        nLen = 9;
        pLatBuf[nLen] = 0x00;
    }

    nTemp = 0;
    for (i = 0 ; i <= 1 ; i++)
    {
        if ((pLatBuf[i] > '9') || (pLatBuf[i] < '0'))
            return 0;

        nTemp = nTemp * 10 + (pLatBuf[i] - '0');
    }
    if(nTemp > 90)
        return 0;

    rLat = (double)nTemp;

    nTemp= 0;
    for (i = 2 ; i <= 3 ; i++)
    {
        if ((pLatBuf[i] > '9') || (pLatBuf[i] < '0'))
            return 0;

        nTemp = nTemp * 10 + (pLatBuf[i] - '0');
    }
    rTmp = ((double)nTemp / 60.0);
    if(rTmp > 1)
        return 0;

    rLat = rLat + rTmp;

    nTemp= 0;
    for (i = 5 ; i < nLen ; i++)
    {
        if ((pLatBuf[i] > '9') || (pLatBuf[i] < '0'))
            return 0;

        nTemp = nTemp * 10 + (pLatBuf[i] - '0');
    }

    if (nLen == 7) rTmp = ((double)nTemp /   6000.0);
    if (nLen == 8) rTmp = ((double)nTemp /  60000.0);
    if (nLen == 9) rTmp = ((double)nTemp / 600000.0);

    if(rTmp > 1)
        return 0;

    rLat += rTmp;

    if (pLatBuf[15] != 'S' && pLatBuf[15] != 'N')
        return 0;

    if (pLatBuf[15] == 'S')
        rLat = -rLat;

    nLen = strlen((char*)pLonBuf);
    if (nLen > 10)
    {
        nLen = 10;
        pLonBuf[nLen] = 0x00;
    }

    if (nLen < 8 || nLen > 11)
        return 0;

    if (nLen == 11)
    {
        nLen = 10;
        pLonBuf[nLen] = 0x00;
    }

    nTemp = 0;
    for (i = 0 ; i <= 2 ; i++)
    {
        if ((pLonBuf[i] > '9') || (pLonBuf[i] < '0'))
            return 0;

        nTemp = nTemp * 10 + (pLonBuf[i] - '0');
    }

    rLon = (double)nTemp;
    if(rLon > 180)
        return 0;

    nTemp= 0;
    for (i = 3 ; i <= 4 ; i++)
    {
        if ((pLonBuf[i] > '9') || (pLonBuf[i] < '0'))
            return 0;

        nTemp = nTemp * 10 + (pLonBuf[i] - '0');
    }

    rTmp = ((double)nTemp / 60.0);
    if(rTmp > 1)
        return 0;

    rLon = rLon + rTmp;

    nTemp= 0;
    for (i = 6 ; i < nLen ; i++)
    {
        if ((pLonBuf[i] > '9') || (pLonBuf[i] < '0'))
            return 0;

        nTemp = nTemp * 10 + (pLonBuf[i] - '0');
    }

    if (nLen == 8) rTmp = ((double)nTemp /  6000.0);
    if (nLen == 9) rTmp = ((double)nTemp /  60000.0);
    if (nLen ==10) rTmp = ((double)nTemp / 600000.0);
    if(rTmp > 1)
        return 0;

    rLon += rTmp;

    if (pLonBuf[15] != 'E' && pLonBuf[15] != 'W')
        return 0;

    if (pLonBuf[15] == 'W')
        rLon = -rLon;

    *pLatVal = rLat;
    *pLonVal = rLon;

    return 1;
}

/*************************************************************************/
/**
 * @fn      bool CSentence::IsNMEAReservedCharater(char ch)
 * @brief   Function to check if a character is reserved in NMEA.
 * @param   ch [IN] Character to check
 * @return  true if reserved, false otherwise
 */
bool CSentence::IsNMEAReservedCharater(char ch)
{
    if( ch == '\x0d' ) return true;
    if( ch == '\x0a' ) return true;
    if( ch == '$' )    return true;
    if( ch == '*' )    return true;
    if( ch == ',' )    return true;
    if( ch == '!' )    return true;
    if( ch == '\\' )   return true;
    if( ch == '^' )    return true;
    if( ch == '~' )    return true;
    if( ch == '\x7f' ) return true;

    return false;
}
