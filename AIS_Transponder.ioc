#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_6
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.ClockPrescaler=ADC_CLOCK_ASYNC_DIV6
ADC1.ClockPrescalerADC3=ADC_CLOCK_ASYNC_DIV6
ADC1.ContinuousConvMode=ENABLE
ADC1.ConversionDataManagement=ADC_CONVERSIONDATA_DMA_CIRCULAR
ADC1.EnableInjectedConversion=DISABLE
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,OffsetSignedSaturation-0\#ChannelRegularConversion,NbrOfConversionFlag,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,OffsetSignedSaturation-1\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode,ClockPrescalerADC3,ClockPrescaler,master,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,OffsetSignedSaturation-2\#ChannelRegularConversion,EnableInjectedConversion,ConversionDataManagement
ADC1.NbrOfConversion=3
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetSignedSaturation-0\#ChannelRegularConversion=DISABLE
ADC1.OffsetSignedSaturation-1\#ChannelRegularConversion=DISABLE
ADC1.OffsetSignedSaturation-2\#ChannelRegularConversion=DISABLE
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.Rank-2\#ChannelRegularConversion=3
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC1.master=1
ADC2.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_2
ADC2.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_15
ADC2.ClockPrescaler=ADC_CLOCK_ASYNC_DIV6
ADC2.ClockPrescalerADC3=ADC_CLOCK_ASYNC_DIV6
ADC2.ContinuousConvMode=ENABLE
ADC2.ConversionDataManagement=ADC_CONVERSIONDATA_DMA_CIRCULAR
ADC2.EnableInjectedConversion=ENABLE
ADC2.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,OffsetSignedSaturation-0\#ChannelRegularConversion,NbrOfConversionFlag,ClockPrescaler,ContinuousConvMode,NbrOfConversion,ClockPrescalerADC3,Resolution,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,OffsetSignedSaturation-1\#ChannelRegularConversion,ConversionDataManagement,EnableInjectedConversion,InjectedRank-3\#ChannelInjectedConversion,InjectedChannel-3\#ChannelInjectedConversion,InjectedSamplingTime-3\#ChannelInjectedConversion,InjectedOffsetNumber-3\#ChannelInjectedConversion,InjectedOffsetSignedSaturation-3\#ChannelInjectedConversion,InjectedOffsetSaturation-3\#ChannelInjectedConversion,InjNumberOfConversion,InjectedConvMode
ADC2.InjNumberOfConversion=1
ADC2.InjectedChannel-3\#ChannelInjectedConversion=ADC_CHANNEL_9
ADC2.InjectedConvMode=AutoInjected
ADC2.InjectedOffsetNumber-3\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC2.InjectedOffsetSaturation-3\#ChannelInjectedConversion=DISABLE
ADC2.InjectedOffsetSignedSaturation-3\#ChannelInjectedConversion=DISABLE
ADC2.InjectedRank-3\#ChannelInjectedConversion=1
ADC2.InjectedSamplingTime-3\#ChannelInjectedConversion=ADC_SAMPLETIME_1CYCLE_5
ADC2.NbrOfConversion=2
ADC2.NbrOfConversionFlag=1
ADC2.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.OffsetSignedSaturation-0\#ChannelRegularConversion=DISABLE
ADC2.OffsetSignedSaturation-1\#ChannelRegularConversion=DISABLE
ADC2.Rank-0\#ChannelRegularConversion=1
ADC2.Rank-1\#ChannelRegularConversion=2
ADC2.Resolution=ADC_RESOLUTION_16B
ADC2.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC2.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC3.AWD3FilteringConfig-Analog\ Watchdog\ 3=ADC3_AWD_FILTERING_NONE
ADC3.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_14
ADC3.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_15
ADC3.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_1
ADC3.Channel-4\#ChannelRegularConversion=ADC_CHANNEL_8
ADC3.ClockPrescaler=ADC_CLOCK_ASYNC_DIV6
ADC3.ClockPrescalerADC3=ADC_CLOCK_ASYNC_DIV6
ADC3.ContinuousConvMode=ENABLE
ADC3.ConversionDataManagement=ADC_CONVERSIONDATA_DMA_CIRCULAR
ADC3.DiscontinuousConvMode=DISABLE
ADC3.EnableInjectedConversion=ENABLE
ADC3.IPParameters=Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,OffsetSignedSaturation-1\#ChannelRegularConversion,NbrOfConversionFlag,ContinuousConvMode,ClockPrescaler,ClockPrescalerADC3,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,OffsetSignedSaturation-2\#ChannelRegularConversion,NbrOfConversion,Rank-3\#ChannelRegularConversion,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,OffsetNumber-3\#ChannelRegularConversion,OffsetSignedSaturation-3\#ChannelRegularConversion,Rank-4\#ChannelRegularConversion,Channel-4\#ChannelRegularConversion,SamplingTime-4\#ChannelRegularConversion,OffsetNumber-4\#ChannelRegularConversion,OffsetSignedSaturation-4\#ChannelRegularConversion,DiscontinuousConvMode,ConversionDataManagement,EnableInjectedConversion,InjectedRank-8\#ChannelInjectedConversion,InjectedChannel-8\#ChannelInjectedConversion,InjectedSamplingTime-8\#ChannelInjectedConversion,InjectedOffsetNumber-8\#ChannelInjectedConversion,InjectedOffsetSignedSaturation-8\#ChannelInjectedConversion,InjectedOffsetSaturation-8\#ChannelInjectedConversion,InjNumberOfConversion,InjectedConvMode,AWD3FilteringConfig-Analog Watchdog 3
ADC3.InjNumberOfConversion=1
ADC3.InjectedChannel-8\#ChannelInjectedConversion=ADC_CHANNEL_13
ADC3.InjectedConvMode=AutoInjected
ADC3.InjectedOffsetNumber-8\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC3.InjectedOffsetSaturation-8\#ChannelInjectedConversion=DISABLE
ADC3.InjectedOffsetSignedSaturation-8\#ChannelInjectedConversion=DISABLE
ADC3.InjectedRank-8\#ChannelInjectedConversion=1
ADC3.InjectedSamplingTime-8\#ChannelInjectedConversion=ADC_SAMPLETIME_1CYCLE_5
ADC3.NbrOfConversion=4
ADC3.NbrOfConversionFlag=1
ADC3.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetNumber-3\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetNumber-4\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetSignedSaturation-1\#ChannelRegularConversion=DISABLE
ADC3.OffsetSignedSaturation-2\#ChannelRegularConversion=DISABLE
ADC3.OffsetSignedSaturation-3\#ChannelRegularConversion=DISABLE
ADC3.OffsetSignedSaturation-4\#ChannelRegularConversion=DISABLE
ADC3.Rank-1\#ChannelRegularConversion=1
ADC3.Rank-2\#ChannelRegularConversion=2
ADC3.Rank-3\#ChannelRegularConversion=3
ADC3.Rank-4\#ChannelRegularConversion=4
ADC3.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC3.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC3.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC3.SamplingTime-4\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
CAD.formats=[]
CAD.pinconfig=Dual
CAD.provider=
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_Spec=MPU_REGION_FULL_ACCESS
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=0x24000000
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=0x30000000
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=0x38000000
CORTEX_M7.BaseAddress_Spec=0x20000000
CORTEX_M7.CPU_DCache=Enabled
CORTEX_M7.CPU_ICache=Enabled
CORTEX_M7.DisableExec_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_INSTRUCTION_ACCESS_ENABLE
CORTEX_M7.DisableExec_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_INSTRUCTION_ACCESS_ENABLE
CORTEX_M7.DisableExec_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_INSTRUCTION_ACCESS_ENABLE
CORTEX_M7.DisableExec_Spec=MPU_INSTRUCTION_ACCESS_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.IPParameters=default_mode_Activation,CPU_ICache,CPU_DCache,Enable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,DisableExec_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,IsShareable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,IsBufferable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,Enable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,Enable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,TypeExtField_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,DisableExec_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,DisableExec_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,BaseAddress_Spec,Size_Spec,SubRegionDisable_Spec,AccessPermission_Spec,DisableExec_Spec,IsShareable_Spec,IsCacheable_Spec,IsCacheable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S
CORTEX_M7.IPParametersWithoutCheck=BaseAddress_S-Cortex_Memory_Protection_Unit_Region1_Settings_S
CORTEX_M7.IsBufferable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_ACCESS_NOT_BUFFERABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsCacheable_Spec=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsShareable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_ACCESS_NOT_SHAREABLE
CORTEX_M7.IsShareable_Spec=MPU_ACCESS_NOT_SHAREABLE
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_REGION_SIZE_512KB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_REGION_SIZE_512KB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_REGION_SIZE_64KB
CORTEX_M7.Size_Spec=MPU_REGION_SIZE_128KB
CORTEX_M7.SubRegionDisable_Spec=0x0
CORTEX_M7.TypeExtField_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_TEX_LEVEL0
CORTEX_M7.default_mode_Activation=1
DAC1.DAC_Channel-DAC_OUT1=DAC_CHANNEL_1
DAC1.DAC_Channel-DAC_OUT2=DAC_CHANNEL_2
DAC1.IPParameters=DAC_Channel-DAC_OUT1,DAC_Channel-DAC_OUT2
Dma.ADC1.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.2.EventEnable=DISABLE
Dma.ADC1.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.2.Instance=DMA1_Stream0
Dma.ADC1.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.2.MemInc=DMA_MINC_ENABLE
Dma.ADC1.2.Mode=DMA_CIRCULAR
Dma.ADC1.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.2.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.2.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.2.Priority=DMA_PRIORITY_LOW
Dma.ADC1.2.RequestNumber=1
Dma.ADC1.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.2.SignalID=NONE
Dma.ADC1.2.SyncEnable=DISABLE
Dma.ADC1.2.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.2.SyncRequestNumber=1
Dma.ADC1.2.SyncSignalID=NONE
Dma.ADC2.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC2.0.EventEnable=DISABLE
Dma.ADC2.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC2.0.Instance=DMA1_Stream1
Dma.ADC2.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC2.0.MemInc=DMA_MINC_ENABLE
Dma.ADC2.0.Mode=DMA_CIRCULAR
Dma.ADC2.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC2.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC2.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC2.0.Priority=DMA_PRIORITY_LOW
Dma.ADC2.0.RequestNumber=1
Dma.ADC2.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC2.0.SignalID=NONE
Dma.ADC2.0.SyncEnable=DISABLE
Dma.ADC2.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC2.0.SyncRequestNumber=1
Dma.ADC2.0.SyncSignalID=NONE
Dma.ADC3.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC3.1.EventEnable=DISABLE
Dma.ADC3.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC3.1.Instance=DMA1_Stream2
Dma.ADC3.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC3.1.MemInc=DMA_MINC_ENABLE
Dma.ADC3.1.Mode=DMA_CIRCULAR
Dma.ADC3.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC3.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC3.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC3.1.Priority=DMA_PRIORITY_LOW
Dma.ADC3.1.RequestNumber=1
Dma.ADC3.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC3.1.SignalID=NONE
Dma.ADC3.1.SyncEnable=DISABLE
Dma.ADC3.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC3.1.SyncRequestNumber=1
Dma.ADC3.1.SyncSignalID=NONE
Dma.Request0=ADC2
Dma.Request1=ADC3
Dma.Request2=ADC1
Dma.RequestsNb=3
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,configTOTAL_HEAP_SIZE,FootprintOK,configUSE_NEWLIB_REENTRANT
FREERTOS.Tasks01=MainTask,2,2048,MainProc,Default,NULL,Dynamic,NULL,NULL;SlotMgrProcTask,2,2048,SlotMgrProc,Default,NULL,Dynamic,NULL,NULL;FreqMgrTask,3,128,FreqMgrProc,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configTOTAL_HEAP_SIZE=38912
FREERTOS.configUSE_NEWLIB_REENTRANT=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C2.IPParameters=Timing
I2C2.Timing=0x307075B1
I2C4.IPParameters=Timing
I2C4.Timing=0x307075B1
KeepUserPlacement=false
MMTAppReg1.MEMORYMAP.AppRegionName=DTCMRAM
MMTAppReg1.MEMORYMAP.ContextName=Cortex-M7NS
MMTAppReg1.MEMORYMAP.CoreName=ARM Cortex-M7
MMTAppReg1.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,ContextName,Name
MMTAppReg1.MEMORYMAP.Name=DTCMRAM
MMTAppReg1.MEMORYMAP.Size=131072
MMTAppReg1.MEMORYMAP.StartAddress=0x20000000
MMTAppReg2.MEMORYMAP.AppRegionName=RAM
MMTAppReg2.MEMORYMAP.ContextName=Cortex-M7NS
MMTAppReg2.MEMORYMAP.CoreName=ARM Cortex-M7
MMTAppReg2.MEMORYMAP.DefaultDataRegion=true
MMTAppReg2.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,ContextName,Name,DefaultDataRegion
MMTAppReg2.MEMORYMAP.Name=RAM
MMTAppReg2.MEMORYMAP.Size=524288
MMTAppReg2.MEMORYMAP.StartAddress=0x24000000
MMTAppReg3.MEMORYMAP.AppRegionName=RAM_D2
MMTAppReg3.MEMORYMAP.ContextName=Cortex-M7NS
MMTAppReg3.MEMORYMAP.CoreName=ARM Cortex-M7
MMTAppReg3.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,ContextName,Name
MMTAppReg3.MEMORYMAP.Name=RAM_D2
MMTAppReg3.MEMORYMAP.Size=294912
MMTAppReg3.MEMORYMAP.StartAddress=0x30000000
MMTAppReg4.MEMORYMAP.AppRegionName=RAM_D3
MMTAppReg4.MEMORYMAP.ContextName=Cortex-M7NS
MMTAppReg4.MEMORYMAP.CoreName=ARM Cortex-M7
MMTAppReg4.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,ContextName,Name
MMTAppReg4.MEMORYMAP.Name=RAM_D3
MMTAppReg4.MEMORYMAP.Size=65536
MMTAppReg4.MEMORYMAP.StartAddress=0x38000000
MMTAppReg5.MEMORYMAP.AppRegionName=ITCMRAM
MMTAppReg5.MEMORYMAP.Cacheability=WTRA
MMTAppReg5.MEMORYMAP.ContextName=Cortex-M7NS
MMTAppReg5.MEMORYMAP.CoreName=ARM Cortex-M7
MMTAppReg5.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,ContextName,Name,Cacheability
MMTAppReg5.MEMORYMAP.Name=ITCMRAM
MMTAppReg5.MEMORYMAP.Size=65536
MMTAppReg5.MEMORYMAP.StartAddress=0x00000000
MMTAppReg6.MEMORYMAP.AP=RO_priv_only
MMTAppReg6.MEMORYMAP.AppRegionName=FLASH
MMTAppReg6.MEMORYMAP.Cacheability=WTRA
MMTAppReg6.MEMORYMAP.ContextName=Cortex-M7NS
MMTAppReg6.MEMORYMAP.CoreName=ARM Cortex-M7
MMTAppReg6.MEMORYMAP.DefaultCodeRegion=true
MMTAppReg6.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,MemType,ContextName,Name,AP,Cacheability,DefaultCodeRegion,ISRRegion,RootBootRegion
MMTAppReg6.MEMORYMAP.ISRRegion=true
MMTAppReg6.MEMORYMAP.MemType=ROM
MMTAppReg6.MEMORYMAP.Name=FLASH
MMTAppReg6.MEMORYMAP.RootBootRegion=true
MMTAppReg6.MEMORYMAP.Size=2097152
MMTAppReg6.MEMORYMAP.StartAddress=0x08000000
MMTAppRegionsCount=6
MMTConfigApplied=false
Mcu.CPN=STM32H743BIT6
Mcu.Family=STM32H7
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=NVIC
Mcu.IP11=RCC
Mcu.IP12=SAI1
Mcu.IP13=SPI1
Mcu.IP14=SYS
Mcu.IP15=TIM2
Mcu.IP16=TIM7
Mcu.IP17=UART4
Mcu.IP18=UART5
Mcu.IP19=UART7
Mcu.IP2=ADC3
Mcu.IP20=UART8
Mcu.IP21=USART1
Mcu.IP22=USART2
Mcu.IP23=USART3
Mcu.IP24=USART6
Mcu.IP3=CORTEX_M7
Mcu.IP4=DAC1
Mcu.IP5=DMA
Mcu.IP6=FREERTOS
Mcu.IP7=I2C2
Mcu.IP8=I2C4
Mcu.IP9=MEMORYMAP
Mcu.IPNb=25
Mcu.Name=STM32H743BITx
Mcu.Package=LQFP208
Mcu.Pin0=PE3
Mcu.Pin1=PE4
Mcu.Pin10=PH0-OSC_IN (PH0)
Mcu.Pin100=VP_SYS_VS_tim1
Mcu.Pin101=VP_TIM2_VS_ClockSourceINT
Mcu.Pin102=VP_TIM7_VS_ClockSourceINT
Mcu.Pin103=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.Pin11=PH1-OSC_OUT (PH1)
Mcu.Pin12=PC0
Mcu.Pin13=PC2_C
Mcu.Pin14=PC3_C
Mcu.Pin15=PH2
Mcu.Pin16=PH3
Mcu.Pin17=PH4
Mcu.Pin18=PH5
Mcu.Pin19=PA3
Mcu.Pin2=PE5
Mcu.Pin20=PA4
Mcu.Pin21=PA5
Mcu.Pin22=PB0
Mcu.Pin23=PB2
Mcu.Pin24=PJ0
Mcu.Pin25=PJ1
Mcu.Pin26=PJ3
Mcu.Pin27=PF11
Mcu.Pin28=PF12
Mcu.Pin29=PF13
Mcu.Pin3=PE6
Mcu.Pin30=PF14
Mcu.Pin31=PF15
Mcu.Pin32=PG0
Mcu.Pin33=PG1
Mcu.Pin34=PE7
Mcu.Pin35=PE8
Mcu.Pin36=PB11
Mcu.Pin37=PH7
Mcu.Pin38=PH8
Mcu.Pin39=PH9
Mcu.Pin4=PC14-OSC32_IN (OSC32_IN)
Mcu.Pin40=PH10
Mcu.Pin41=PH11
Mcu.Pin42=PH12
Mcu.Pin43=PB12
Mcu.Pin44=PB13
Mcu.Pin45=PB14
Mcu.Pin46=PB15
Mcu.Pin47=PD8
Mcu.Pin48=PD10
Mcu.Pin49=PD11
Mcu.Pin5=PC15-OSC32_OUT (OSC32_OUT)
Mcu.Pin50=PD12
Mcu.Pin51=PD13
Mcu.Pin52=PD14
Mcu.Pin53=PD15
Mcu.Pin54=PJ8
Mcu.Pin55=PJ10
Mcu.Pin56=PK0
Mcu.Pin57=PK1
Mcu.Pin58=PK2
Mcu.Pin59=PG2
Mcu.Pin6=PI9
Mcu.Pin60=PG3
Mcu.Pin61=PG4
Mcu.Pin62=PG6
Mcu.Pin63=PG7
Mcu.Pin64=PG8
Mcu.Pin65=PA9
Mcu.Pin66=PA10
Mcu.Pin67=PA11
Mcu.Pin68=PA12
Mcu.Pin69=PH14
Mcu.Pin7=PF1
Mcu.Pin70=PH15
Mcu.Pin71=PI0
Mcu.Pin72=PI2
Mcu.Pin73=PI3
Mcu.Pin74=PC10
Mcu.Pin75=PD0
Mcu.Pin76=PD1
Mcu.Pin77=PD5
Mcu.Pin78=PD6
Mcu.Pin79=PD7
Mcu.Pin8=PF6
Mcu.Pin80=PJ12
Mcu.Pin81=PJ13
Mcu.Pin82=PJ15
Mcu.Pin83=PG9
Mcu.Pin84=PG10
Mcu.Pin85=PG11
Mcu.Pin86=PG14
Mcu.Pin87=PK3
Mcu.Pin88=PG15
Mcu.Pin89=PB5
Mcu.Pin9=PF10
Mcu.Pin90=PB6
Mcu.Pin91=PB7
Mcu.Pin92=PB9
Mcu.Pin93=PE0
Mcu.Pin94=PI4
Mcu.Pin95=PI5
Mcu.Pin96=PI6
Mcu.Pin97=VP_FREERTOS_VS_CMSIS_V1
Mcu.Pin98=VP_SAI1_VP_$IpInstance_SAIA_SAI_BASIC
Mcu.Pin99=VP_SAI1_VP_$IpInstance_SAIB_SAI_BASIC
Mcu.PinsNb=104
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H743BITx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream1_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream2_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SAI1_IRQn=true\:1\:0\:true\:false\:true\:false\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM1_UP_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TIM7_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TimeBase=TIM1_UP_IRQn
NVIC.TimeBaseIP=TIM1
NVIC.UART4_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART5_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART7_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART8_IRQn=true\:5\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=LRCK
PA11.Locked=true
PA11.Signal=I2S2_WS
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=BICK
PA12.Locked=true
PA12.Signal=I2S2_CK
PA3.GPIOParameters=GPIO_Label
PA3.GPIO_Label=R_VSWR
PA3.Signal=ADCx_INP15
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=PWR_CNT
PA4.Signal=COMP_DAC11_group
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=DDS_AJD
PA5.Signal=COMP_DAC12_group
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=DSC_FSK
PB0.Signal=ADCx_INP9
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.Mode=Asynchronous
PB12.Signal=UART5_RX
PB13.Mode=Asynchronous
PB13.Signal=UART5_TX
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=SDTI
PB14.Locked=true
PB14.Signal=I2S2_SDI
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=SDTO
PB15.Locked=true
PB15.Signal=I2S2_SDO
PB2.GPIOParameters=GPIO_Speed,GPIO_Label
PB2.GPIO_Label=Nor_CLK
PB2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB2.Locked=true
PB2.Signal=QUADSPI_CLK
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=DA_SCLK
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=DA_DIN
PB6.Locked=true
PB6.Signal=GPIO_Output
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=DA_NCS
PB7.Locked=true
PB7.Signal=GPIO_Output
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=DDS_LE
PB9.Locked=true
PB9.Signal=GPIO_Output
PC0.GPIOParameters=GPIO_Label
PC0.GPIO_Label=CURRENT_DET
PC0.Locked=true
PC0.Signal=ADCx_INP10
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC14-OSC32_IN\ (OSC32_IN).Mode=LSE-External-Oscillator
PC14-OSC32_IN\ (OSC32_IN).Signal=RCC_OSC32_IN
PC15-OSC32_OUT\ (OSC32_OUT).Mode=LSE-External-Oscillator
PC15-OSC32_OUT\ (OSC32_OUT).Signal=RCC_OSC32_OUT
PC2_C.GPIOParameters=GPIO_Label
PC2_C.GPIO_Label=EX_TX_ENABLE
PC2_C.Locked=true
PC2_C.Signal=GPIO_Input
PC3_C.GPIOParameters=GPIO_Label
PC3_C.GPIO_Label=RF_MAJOR_VER
PC3_C.Mode=IN1-Single-Ended
PC3_C.Signal=ADC3_INP1
PD0.GPIOParameters=GPIO_Label
PD0.GPIO_Label=ALARM_RX
PD0.Locked=true
PD0.Signal=FDCAN1_RX
PD1.GPIOParameters=GPIO_Label
PD1.GPIO_Label=TP1_LED
PD1.Locked=true
PD1.Signal=GPIO_Output
PD10.GPIOParameters=GPIO_Label
PD10.GPIO_Label=LED_3
PD10.Locked=true
PD10.Signal=GPIO_Output
PD11.GPIOParameters=GPIO_Speed,GPIO_Label
PD11.GPIO_Label=Nor_D0
PD11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD11.Locked=true
PD11.Signal=QUADSPI_BK1_IO0
PD12.GPIOParameters=GPIO_Speed,GPIO_Label
PD12.GPIO_Label=Nor_D1
PD12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD12.Locked=true
PD12.Signal=QUADSPI_BK1_IO1
PD13.GPIOParameters=GPIO_Speed,GPIO_Label
PD13.GPIO_Label=Nor_D3
PD13.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD13.Locked=true
PD13.Signal=QUADSPI_BK1_IO3
PD14.GPIOParameters=GPIO_Label
PD14.GPIO_Label=LED_2
PD14.Locked=true
PD14.Signal=GPIO_Output
PD15.GPIOParameters=GPIO_Label
PD15.GPIO_Label=LED_1
PD15.Locked=true
PD15.Signal=GPIO_Output
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PD7.GPIOParameters=GPIO_Label
PD7.GPIO_Label=DDS_DATA
PD7.Mode=TX_Only_Simplex_Unidirect_Master
PD7.Signal=SPI1_MOSI
PD8.Locked=true
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=Debug_RX
PE0.Locked=true
PE0.Mode=Asynchronous
PE0.Signal=UART8_RX
PE3.Mode=SAI_B_SyncSlave
PE3.Signal=SAI1_SD_B
PE4.Mode=SAI_A_MasterWithClock
PE4.Signal=SAI1_FS_A
PE5.Mode=SAI_A_MasterWithClock
PE5.Signal=SAI1_SCK_A
PE6.Mode=SAI_A_MasterWithClock
PE6.Signal=SAI1_SD_A
PE7.GPIOParameters=GPIO_Label
PE7.GPIO_Label=INCOMM_RX
PE7.Locked=true
PE7.Mode=Asynchronous
PE7.Signal=UART7_RX
PE8.GPIOParameters=GPIO_Label
PE8.GPIO_Label=INCOMM_TX
PE8.Mode=Asynchronous
PE8.Signal=UART7_TX
PF1.GPIOParameters=GPIO_Label,GPIO_Pu
PF1.GPIO_Label=CODEC_SCL
PF1.GPIO_Pu=GPIO_PULLUP
PF1.Mode=I2C
PF1.Signal=I2C2_SCL
PF10.GPIOParameters=GPIO_PuPd,GPIO_Label
PF10.GPIO_Label=WING_PTT
PF10.GPIO_PuPd=GPIO_PULLUP
PF10.Locked=true
PF10.Signal=GPIO_Input
PF11.GPIOParameters=GPIO_Label
PF11.GPIO_Label=TEMP
PF11.Mode=IN2-Single-Ended
PF11.Signal=ADC1_INP2
PF12.GPIOParameters=GPIO_Label
PF12.GPIO_Label=PWR_DET
PF12.Mode=IN6-Single-Ended
PF12.Signal=ADC1_INP6
PF13.GPIOParameters=GPIO_Label
PF13.GPIO_Label=F_VSWR
PF13.Mode=IN2-Single-Ended
PF13.Signal=ADC2_INP2
PF14.GPIOParameters=GPIO_Label
PF14.GPIO_Label=MAC_SCL
PF14.Mode=I2C
PF14.Signal=I2C4_SCL
PF15.GPIOParameters=GPIO_Label
PF15.GPIO_Label=MAC_SDA
PF15.Mode=I2C
PF15.Signal=I2C4_SDA
PF6.GPIOParameters=GPIO_Label
PF6.GPIO_Label=RF_MINOR_VER
PF6.Locked=true
PF6.Mode=IN8-Single-Ended
PF6.Signal=ADC3_INP8
PG0.GPIOParameters=GPIO_Label
PG0.GPIO_Label=REMOTE_H_PTT_LED
PG0.Locked=true
PG0.Signal=GPIO_Output
PG1.GPIOParameters=GPIO_Label
PG1.GPIO_Label=WING_H_PTT_LED
PG1.Locked=true
PG1.Signal=GPIO_Output
PG10.GPIOParameters=GPIO_Label
PG10.GPIO_Label=GPS_LOCK_INDI_LED
PG10.Locked=true
PG10.Signal=GPIO_Output
PG11.GPIOParameters=GPIO_Label
PG11.GPIO_Label=DDS_CLK
PG11.Mode=TX_Only_Simplex_Unidirect_Master
PG11.Signal=SPI1_SCK
PG14.Locked=true
PG14.Mode=Asynchronous
PG14.Signal=USART6_TX
PG15.GPIOParameters=GPIO_Label
PG15.GPIO_Label=ERR_INDI_LED
PG15.Locked=true
PG15.Signal=GPIO_Output
PG2.GPIOParameters=GPIO_Label
PG2.GPIO_Label=TX_INDI_LED
PG2.Locked=true
PG2.Signal=GPIO_Output
PG3.GPIOParameters=GPIO_Label
PG3.GPIO_Label=TX_INDI_LED
PG3.Locked=true
PG3.Signal=GPIO_Output
PG4.GPIOParameters=GPIO_Label
PG4.GPIO_Label=PROC1_LED
PG4.Locked=true
PG4.Signal=GPIO_Output
PG6.GPIOParameters=GPIO_Label
PG6.GPIO_Label=PROC2_LED
PG6.Locked=true
PG6.Signal=GPIO_Output
PG7.Mode=SAI_A_MasterWithClock
PG7.Signal=SAI1_MCLK_A
PG8.GPIOParameters=GPIO_Label
PG8.GPIO_Label=PROC3_LED
PG8.Locked=true
PG8.Signal=GPIO_Output
PG9.Locked=true
PG9.Mode=Asynchronous
PG9.Signal=USART6_RX
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PH10.Locked=true
PH10.Signal=S_TIM5_CH1
PH11.GPIOParameters=GPIO_Label
PH11.GPIO_Label=LOOP_EN
PH11.Locked=true
PH11.Signal=GPIO_Output
PH12.GPIOParameters=GPIO_Label
PH12.GPIO_Label=TEST_EN
PH12.Locked=true
PH12.Signal=GPIO_Output
PH14.GPIOParameters=GPIO_Label
PH14.GPIO_Label=A_PDIN
PH14.Locked=true
PH14.Signal=GPIO_Output
PH15.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PH15.GPIO_Label=SP_PWR
PH15.GPIO_PuPd=GPIO_PULLUP
PH15.Locked=true
PH15.PinState=GPIO_PIN_SET
PH15.Signal=GPIO_Output
PH2.GPIOParameters=GPIO_Label
PH2.GPIO_Label=SP_AF_RSSI
PH2.Mode=IN13-Single-Ended
PH2.Signal=ADC3_INP13
PH3.GPIOParameters=GPIO_Label
PH3.GPIO_Label=BB_MAJOR_VER
PH3.Locked=true
PH3.Mode=IN14-Single-Ended
PH3.Signal=ADC3_INP14
PH4.GPIOParameters=GPIO_PuPd,GPIO_Label
PH4.GPIO_Label=BB_MINOR_VER
PH4.GPIO_PuPd=GPIO_NOPULL
PH4.Locked=true
PH4.Mode=IN15-Single-Ended
PH4.Signal=ADC3_INP15
PH5.GPIOParameters=GPIO_Label,GPIO_Pu
PH5.GPIO_Label=CODEC_SDA
PH5.GPIO_Pu=GPIO_PULLUP
PH5.Locked=true
PH5.Mode=I2C
PH5.Signal=I2C2_SDA
PH7.Locked=true
PH7.Signal=SPI5_MISO
PH8.GPIOParameters=GPIO_Label
PH8.GPIO_Label=SPI5_CS
PH8.Locked=true
PH8.Signal=GPIO_Output
PH9.GPIOParameters=PinState,GPIO_Label
PH9.GPIO_Label=H_SP_PWR
PH9.Locked=true
PH9.PinState=GPIO_PIN_SET
PH9.Signal=GPIO_Output
PI0.GPIOParameters=PinState,GPIO_Label
PI0.GPIO_Label=ETH_RESET
PI0.Locked=true
PI0.PinState=GPIO_PIN_SET
PI0.Signal=GPIO_Output
PI2.GPIOParameters=PinState,GPIO_Label
PI2.GPIO_Label=C_F_SP_ON
PI2.Locked=true
PI2.PinState=GPIO_PIN_SET
PI2.Signal=GPIO_Output
PI3.GPIOParameters=PinState,GPIO_Label
PI3.GPIO_Label=C_R_SP_ON
PI3.Locked=true
PI3.PinState=GPIO_PIN_SET
PI3.Signal=GPIO_Output
PI4.GPIOParameters=PinState,GPIO_Label
PI4.GPIO_Label=MAIN_SP_MUTE
PI4.Locked=true
PI4.PinState=GPIO_PIN_SET
PI4.Signal=GPIO_Output
PI5.GPIOParameters=PinState,GPIO_Label
PI5.GPIO_Label=F_SP_MUTE
PI5.Locked=true
PI5.PinState=GPIO_PIN_SET
PI5.Signal=GPIO_Output
PI6.GPIOParameters=PinState,GPIO_Label
PI6.GPIO_Label=R_SP_MUTE
PI6.Locked=true
PI6.PinState=GPIO_PIN_SET
PI6.Signal=GPIO_Output
PI9.Mode=Asynchronous
PI9.Signal=UART4_RX
PJ0.GPIOParameters=GPIO_Label
PJ0.GPIO_Label=CODEC_CAD
PJ0.Locked=true
PJ0.Signal=GPIO_Output
PJ1.GPIOParameters=GPIO_Label
PJ1.GPIO_Label=CODEC_EN
PJ1.Locked=true
PJ1.Signal=GPIO_Output
PJ10.Locked=true
PJ10.Signal=SPI5_MOSI
PJ12.GPIOParameters=PinState,GPIO_Label
PJ12.GPIO_Label=MAIN_DDS_CS
PJ12.Locked=true
PJ12.PinState=GPIO_PIN_SET
PJ12.Signal=GPIO_Output
PJ13.GPIOParameters=PinState,GPIO_Label
PJ13.GPIO_Label=WKR_DDS_CS
PJ13.Locked=true
PJ13.PinState=GPIO_PIN_SET
PJ13.Signal=GPIO_Output
PJ15.GPIOParameters=GPIO_Label
PJ15.GPIO_Label=TX_EN2
PJ15.Locked=true
PJ15.Signal=GPIO_Output
PJ3.GPIOParameters=GPIO_Label
PJ3.GPIO_Label=TP5
PJ3.Locked=true
PJ3.Signal=GPIO_Output
PJ8.GPIOParameters=GPIO_Label
PJ8.GPIO_Label=Debug_TX
PJ8.Mode=Asynchronous
PJ8.Signal=UART8_TX
PK0.GPIOParameters=GPIO_Label
PK0.GPIO_Label=RX_EN
PK0.Locked=true
PK0.Signal=GPIO_Output
PK1.GPIOParameters=GPIO_Label
PK1.GPIO_Label=TX_EN
PK1.Locked=true
PK1.Signal=GPIO_Output
PK2.GPIOParameters=GPIO_Label
PK2.GPIO_Label=MAIN_DDS_LOCK_DETECT
PK2.Locked=true
PK2.Signal=GPIO_Input
PK3.GPIOParameters=GPIO_Label
PK3.GPIO_Label=WKR_DDS_LOCK_DETECT
PK3.Locked=true
PK3.Signal=GPIO_Input
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H743BITx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.12.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x400
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=AIS_Transponder.ioc
ProjectManager.ProjectName=AIS_Transponder
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x800
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_SAI1_Init-SAI1-false-HAL-true,5-MX_DAC1_Init-DAC1-false-HAL-true,6-MX_ADC1_Init-ADC1-false-HAL-true,7-MX_ADC2_Init-ADC2-false-HAL-true,8-MX_ADC3_Init-ADC3-false-HAL-true,9-MX_I2C2_Init-I2C2-false-HAL-true,10-MX_I2C4_Init-I2C4-false-HAL-true,11-MX_TIM7_Init-TIM7-false-HAL-true,12-MX_SPI1_Init-SPI1-false-HAL-true,13-MX_USART1_UART_Init-USART1-false-HAL-true,14-MX_USART2_UART_Init-USART2-false-HAL-true,15-MX_USART3_UART_Init-USART3-false-HAL-true,16-MX_UART4_Init-UART4-false-HAL-true,17-MX_UART5_Init-UART5-false-HAL-true,18-MX_USART6_UART_Init-USART6-false-HAL-true,19-MX_UART7_Init-UART7-false-HAL-true,20-MX_UART8_Init-UART8-false-HAL-true,21-MX_TIM2_Init-TIM2-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
RCC.ADCCLockSelection=RCC_ADCCLKSOURCE_PLL3
RCC.ADCFreq_Value=76800000
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=98304443.359375
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=2
RCC.DIVM2=2
RCC.DIVM3=2
RCC.DIVN1=80
RCC.DIVN2=49
RCC.DIVN3=32
RCC.DIVP1Freq_Value=*********
RCC.DIVP2=6
RCC.DIVP2Freq_Value=98304443.359375
RCC.DIVP3Freq_Value=192000000
RCC.DIVQ1=8
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2=3
RCC.DIVQ2Freq_Value=196608886.71875
RCC.DIVQ3Freq_Value=192000000
RCC.DIVR1Freq_Value=*********
RCC.DIVR2=3
RCC.DIVR2Freq_Value=196608886.71875
RCC.DIVR3=5
RCC.DIVR3Freq_Value=76800000
RCC.EnbaleCSS=false
RCC.FDCANCLockSelection=RCC_FDCANCLKSOURCE_HSE
RCC.FDCANFreq_Value=24000000
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HRTIMFreq_Value=*********
RCC.HSE_VALUE=24000000
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCCLockSelection,ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM2,DIVM3,DIVN1,DIVN2,DIVN3,DIVP1Freq_Value,DIVP2,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1,DIVQ1Freq_Value,DIVQ2,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2,DIVR2Freq_Value,DIVR3,DIVR3Freq_Value,EnbaleCSS,FDCANCLockSelection,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HRTIMFreq_Value,HSE_VALUE,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL1_VCI_Range-AdvancedSettings,PLL1_VCO_SEL,PLL1_VCO_SEL-AdvancedSettings,PLL2FRACN,PLL2_VCI_Range-AdvancedSettings,PLL2_VCO_SEL-AdvancedSettings,PLL3FRACN,PLL3_VCI_Range-AdvancedSettings,PLL3_VCO_SEL-AdvancedSettings,PLLFRACN,PLLSourceVirtual,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1CLockSelection,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123CLockSelection,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value,WatchDogFreq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LTDCFreq_Value=76800000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL1_VCI_Range-AdvancedSettings=RCC_PLL1VCIRANGE_0
RCC.PLL1_VCO_SEL=RCC_PLL1VCOWIDE
RCC.PLL1_VCO_SEL-AdvancedSettings=RCC_PLL1VCOMEDIUM
RCC.PLL2FRACN=1247
RCC.PLL2_VCI_Range-AdvancedSettings=RCC_PLL2VCIRANGE_0
RCC.PLL2_VCO_SEL-AdvancedSettings=RCC_PLL2VCOMEDIUM
RCC.PLL3FRACN=0
RCC.PLL3_VCI_Range-AdvancedSettings=RCC_PLL3VCIRANGE_0
RCC.PLL3_VCO_SEL-AdvancedSettings=RCC_PLL3VCOMEDIUM
RCC.PLLFRACN=0
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1CLockSelection=RCC_SAI1CLKSOURCE_PLL2
RCC.SAI1Freq_Value=98304443.359375
RCC.SAI23Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123CLockSelection=RCC_SPI123CLKSOURCE_PLL2
RCC.SPI123Freq_Value=98304443.359375
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********.15625
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=12000000
RCC.VCOInput2Freq_Value=12000000
RCC.VCOInput3Freq_Value=12000000
RCC.WatchDogFreq_Value=32000
SAI1.AudioFrequency-SAI_A_MasterWithClock=SAI_AUDIO_FREQUENCY_48K
SAI1.BasicDataSize-SAI_A_MasterWithClock=SAI_PROTOCOL_DATASIZE_24BIT
SAI1.BasicDataSize-SAI_B_SyncSlave=SAI_PROTOCOL_DATASIZE_24BIT
SAI1.BasicSlotNumber-SAI_A_MasterWithClock=4
SAI1.BasicSlotNumber-SAI_B_SyncSlave=4
SAI1.ErrorAudioFreq-SAI_A_MasterWithClock=700.0 %
SAI1.FIFOThreshold-SAI_B_SyncSlave=SAI_FIFOTHRESHOLD_EMPTY
SAI1.IPParameters=VirtualProtocol-SAI_A_BASIC,VirtualProtocol-SAI_B_BASIC,Instance-SAI_B_SyncSlave,VirtualMode-SAI_B_SyncSlave,InitProtocol-SAI_B_SyncSlave,MonoStereoMode-SAI_B_SyncSlave,BasicSlotNumber-SAI_B_SyncSlave,BasicDataSize-SAI_B_SyncSlave,FIFOThreshold-SAI_B_SyncSlave,Instance-SAI_A_MasterWithClock,VirtualMode-SAI_A_MasterWithClock,MClockEnable-SAI_A_MasterWithClock,InitProtocol-SAI_A_MasterWithClock,RealAudioFreq-SAI_A_MasterWithClock,ErrorAudioFreq-SAI_A_MasterWithClock,AudioFrequency-SAI_A_MasterWithClock,BasicDataSize-SAI_A_MasterWithClock,OutputDrive-SAI_A_MasterWithClock,OutputDrive-SAI_B_SyncSlave,BasicSlotNumber-SAI_A_MasterWithClock,MonoStereoMode-SAI_A_MasterWithClock,NoDivider-SAI_A_MasterWithClock
SAI1.InitProtocol-SAI_A_MasterWithClock=Enable
SAI1.InitProtocol-SAI_B_SyncSlave=Enable
SAI1.Instance-SAI_A_MasterWithClock=SAI$Index_Block_A
SAI1.Instance-SAI_B_SyncSlave=SAI$Index_Block_B
SAI1.MClockEnable-SAI_A_MasterWithClock=SAI_MASTERCLOCK_ENABLE
SAI1.MonoStereoMode-SAI_A_MasterWithClock=SAI_STEREOMODE
SAI1.MonoStereoMode-SAI_B_SyncSlave=SAI_MONOMODE
SAI1.NoDivider-SAI_A_MasterWithClock=SAI_MCK_OVERSAMPLING_DISABLE
SAI1.OutputDrive-SAI_A_MasterWithClock=SAI_OUTPUTDRIVE_ENABLE
SAI1.OutputDrive-SAI_B_SyncSlave=SAI_OUTPUTDRIVE_ENABLE
SAI1.RealAudioFreq-SAI_A_MasterWithClock=384.001 KHz
SAI1.VirtualMode-SAI_A_MasterWithClock=VM_MASTER
SAI1.VirtualMode-SAI_B_SyncSlave=VM_SLAVE
SAI1.VirtualProtocol-SAI_A_BASIC=VM_BASIC_PROTOCOL
SAI1.VirtualProtocol-SAI_B_BASIC=VM_BASIC_PROTOCOL
SH.ADCx_INP10.0=ADC1_INP10,IN10-Single-Ended
SH.ADCx_INP10.ConfNb=1
SH.ADCx_INP15.0=ADC2_INP15,IN15-Single-Ended
SH.ADCx_INP15.ConfNb=1
SH.ADCx_INP9.0=ADC2_INP9,IN9-Single-Ended
SH.ADCx_INP9.ConfNb=1
SH.COMP_DAC11_group.0=DAC1_OUT1,DAC_OUT1
SH.COMP_DAC11_group.ConfNb=1
SH.COMP_DAC12_group.0=DAC1_OUT2,DAC_OUT2
SH.COMP_DAC12_group.ConfNb=1
SH.S_TIM5_CH1.0=TIM5_CH1
SH.S_TIM5_CH1.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_32
SPI1.CalculateBaudRate=3.072013 MBits/s
SPI1.DataSize=SPI_DATASIZE_8BIT
SPI1.Direction=SPI_DIRECTION_2LINES_TXONLY
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,DataSize
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM7.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM7.IPParameters=Prescaler,Period,AutoReloadPreload
TIM7.Period=5000-1
TIM7.Prescaler=0
UART4.FIFOMode=FIFOMODE_ENABLE
UART4.IPParameters=FIFOMode
UART5.FIFOMode=FIFOMODE_ENABLE
UART5.IPParameters=FIFOMode
UART7.FIFOMode=FIFOMODE_ENABLE
UART7.IPParameters=FIFOMode
UART8.FIFOMode=FIFOMODE_ENABLE
UART8.IPParameters=FIFOMode
USART1.FIFOMode=FIFOMODE_ENABLE
USART1.IPParameters=VirtualMode-Asynchronous,FIFOMode
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.FIFOMode=FIFOMODE_ENABLE
USART2.IPParameters=VirtualMode-Asynchronous,FIFOMode
USART2.VirtualMode-Asynchronous=VM_ASYNC
USART3.FIFOMode=FIFOMODE_ENABLE
USART3.IPParameters=VirtualMode-Asynchronous,FIFOMode
USART3.VirtualMode-Asynchronous=VM_ASYNC
USART6.FIFOMode=FIFOMODE_ENABLE
USART6.IPParameters=VirtualMode-Asynchronous,FIFOMode
USART6.VirtualMode-Asynchronous=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V1.Mode=CMSIS_V1
VP_FREERTOS_VS_CMSIS_V1.Signal=FREERTOS_VS_CMSIS_V1
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_SAI1_VP_$IpInstance_SAIA_SAI_BASIC.Mode=SAI_A_BASIC
VP_SAI1_VP_$IpInstance_SAIA_SAI_BASIC.Signal=SAI1_VP_$IpInstance_SAIA_SAI_BASIC
VP_SAI1_VP_$IpInstance_SAIB_SAI_BASIC.Mode=SAI_B_BASIC
VP_SAI1_VP_$IpInstance_SAIB_SAI_BASIC.Signal=SAI1_VP_$IpInstance_SAIB_SAI_BASIC
VP_SYS_VS_tim1.Mode=TIM1
VP_SYS_VS_tim1.Signal=SYS_VS_tim1
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM7_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM7_VS_ClockSourceINT.Signal=TIM7_VS_ClockSourceINT
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
