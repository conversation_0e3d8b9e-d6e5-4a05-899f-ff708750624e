/**
 * @file    Air.cpp
 * @brief   Air class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Air.h"

/******************************************************************************
* 
* AIR - AIS interrogation Request
*
* $--AIR,xxxxxxxxx,x.x,x,x.x,x,xxxxxxxxx,x.x,x,a,x.x,x.x,x.x*hh<CR><LF>
*        |         |   | |   | |         | | |   |   |   |
*        1         2   3 4   5 6         7 8 9   10  11  12
*
* 1. MMSI of interrogated station-1
* 2. First message number requested from station-1
* 3. Message sub-section
* 4. Second message number requested from station-1
* 5. Message sub-section
* 6. MMSI of interrogated station-2
* 7. Message Number requested from station-2
* 8. Message sub-section
* 9. Channel of interrogation
* 10. Message ID1.1, station-1 reply slot
* 11. Message ID1.2, station-1 reply slot
* 12. Message ID2.1, station-2 reply slot
*
******************************************************************************/
// Define static member variables
uint8_t  CAir::m_nMsgType       = AIR_NONE;
uint32_t CAir::m_dwMMSI1        = 0;
uint32_t CAir::m_dwMsgReq1_1    = 0;
uint32_t CAir::m_dwMsgSubSec1_1 = 0;
uint32_t CAir::m_dwMsgReq1_2    = 0;
uint32_t CAir::m_dwMsgSubSec1_2 = 0;
uint32_t CAir::m_dwMMSI2        = 0;
uint32_t CAir::m_dwMsgReq2      = 0;
uint32_t CAir::m_dwMsgSubSec2   = 0;
char     CAir::m_cChannel       = 0;
uint32_t CAir::m_dwMsgID1_1     = 0;
uint32_t CAir::m_dwMsgID1_2     = 0;
uint32_t CAir::m_dwMsgID2_1     = 0;

CAir::CAir() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAir::Parse(const char *pszSentence)
{
    char  pstrSubData1[32], pstrSubData2[32], pstrSubData3[32];
    int   nSubData1_len, nSubData2_len, nSubData3_len;

    memset(pstrSubData1, 0x00, sizeof(pstrSubData1));
    memset(pstrSubData2, 0x00, sizeof(pstrSubData2));
    memset(pstrSubData3, 0x00, sizeof(pstrSubData3));

    if(CSentence::GetFieldString(pszSentence, 1, pstrSubData1, sizeof(pstrSubData1)) <= 0 
        || !CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("AIR-%d] invalid int num string, first MMSI [%s], ", pstrSubData1);
        return FALSE;
    }

    m_dwMMSI1 = atoi((char*)pstrSubData1);

    if(CSentence::GetFieldString(pszSentence, 2, pstrSubData2, sizeof(pstrSubData2)) <= 0 
        || !CAisLib::IsValidAisIntNumStr(pstrSubData2, TRUE))
    {
        WARNING_LOG("AIR-%d] invalid int num string, MsgID11 [%s], ", pstrSubData2);
        return FALSE;
    }

    m_dwMsgReq1_1 = atoi((char*)pstrSubData2);

    if(CSentence::GetFieldString(pszSentence, 3, pstrSubData3, sizeof(pstrSubData3)) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData3, TRUE))
        {
            WARNING_LOG("AIR] invalid int num string, sub_sec11 [%s], ", pstrSubData3);
            return FALSE;
        }
    }

    m_nMsgType = AIR_ID1_MSG1;


    if(CSentence::GetFieldString(pszSentence, 4, pstrSubData2, sizeof(pstrSubData2)) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData2, TRUE))
        {
            WARNING_LOG("AIR] invalid int num string, MsgID12 [%s], ", pstrSubData2);
            return FALSE;
        }
        m_dwMsgReq1_2 = atoi((char*)pstrSubData2);
        m_nMsgType = AIR_ID1_MSG12;
    }

    if(CSentence::GetFieldString(pszSentence, 5, pstrSubData3, sizeof(pstrSubData3)) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData3, TRUE))
        {
            WARNING_LOG("AIR] invalid int num string, sub_sec12 [%s], ", pstrSubData3);
            return FALSE;
        }
    }

    if(CSentence::GetFieldString(pszSentence, 6, pstrSubData1, sizeof(pstrSubData1)) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("AIR] invalid int num string, second MMSI [%s], ", pstrSubData1);
            return false;
        }

        if(CSentence::GetFieldString(pszSentence, 7, pstrSubData2, sizeof(pstrSubData2)) > 0
            && !CAisLib::IsValidAisIntNumStr(pstrSubData2, TRUE))
        {
            WARNING_LOG("AIR] invalid int num string, MsgID21 [%s], ", pstrSubData2);
            return false;
        }

        if(CSentence::GetFieldString(pszSentence, 8, pstrSubData3, sizeof(pstrSubData3)) > 0
            && !CAisLib::IsValidAisIntNumStr(pstrSubData3, TRUE))
        {
            WARNING_LOG("AIR] invalid int num string, sub_sec21 [%s], ", pstrSubData3);
            return false;
        }

        m_dwMMSI2   = atoi((char*)pstrSubData1);
        m_dwMsgReq2 = atoi((char*)pstrSubData2);

        nSubData1_len = strlen(pstrSubData1);
        nSubData2_len = strlen(pstrSubData2);
        nSubData3_len = strlen(pstrSubData3);
        if((nSubData1_len > 0  && nSubData2_len == 0) ||    // uMMSI2 = yes, bMsgID21 = no
            (nSubData1_len == 0 && nSubData2_len >  0))     // uMMSI2 = no,  bMsgID21 = yes
        {
            WARNING_LOG("AIR-%d] wrong sentence, %d, %d\r\n", nSubData1_len, nSubData2_len);
            return false;
        }

        if(nSubData1_len > 0 && nSubData2_len > 0)
        {
            if(m_nMsgType == AIR_ID1_MSG1)
                m_nMsgType = AIR_ID12_MSG13;
            else if(m_nMsgType == AIR_ID1_MSG12)
                m_nMsgType = AIR_ID12_MSG123;
        }
    }

    return true;
}

/**
 * @brief Make the AIR sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CAir::MakeSentence(char *pszSentence)
{
    return 0;
}