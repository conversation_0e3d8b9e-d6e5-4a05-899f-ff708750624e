#ifndef __REPORTRATEMGR_H__
#define __REPORTRATEMGR_H__

#include "DataType.h"
#include "AllConst.h"

class CReportRateMgr
{
public:
    CReportRateMgr();
    ~CReportRateMgr();

    static std::shared_ptr<CReportRateMgr> getInst() {
        static std::shared_ptr<CReportRateMgr> pInst = std::make_shared<CReportRateMgr>();
        return pInst;
    }

public:
    void    Initialize(void);

	bool    SetReportRateDoubleMode(bool bReportRateDoubleMode);
    bool    IsReportRateForSOTDMA(float fReportIntervalSec);
    bool    IsReportRateForSOTDMA(void);
	void    GetReportInterval(float fNewReportIntervalTotal, int nNewTxChID1, int nNewTxChID2, float *pfReportIntSecTotal, float *pfReportIntSecCh1, float *pfReportIntSecCh2, UINT16 *puStaticIntSecCh1, UINT16 *puStaticIntSecCh2, UINT16 *puLongRangeIntSecCh1, UINT16 *puLongRangeIntSecCh2);
    void    SetReportInterval(float fSecRI);
    void    SetReportInterval(float fNewReportIntervalSecSO, int nNewTxChID1, int nNewTxChID2);
    float   GetReportIntervalSec(void);
    float   GetReportIntSecBySpeed(void);
    float   GetReportIntSecNextShorter(void);
    float   GetReportIntSecNextLonger(void);
    float   GetReportRate(void);
    bool    IsRIValueValid(float fReportIntervalSec);
	bool    IsITDMAEnabled(void);
    bool    CheckCurRRValid(void);
	bool    SetReportRateByITDMA(bool bSet);
    float   CheckAutoModeReportInterval(bool bCheckSpdReduce, bool bChangeRR);

    void    RunPeriodicallyReportRateMgr(void);

private:
    bool    m_bReportRateDoubleMode;
    float   m_fReportIntervalSec;
    float   m_fReportRate;

    bool    m_bFirstCalcRR;

    bool    m_bITDMA;
    DWORD   m_dwHdgDiffAboveTick;
};

#endif//__REPORTRATEMGR_H__
