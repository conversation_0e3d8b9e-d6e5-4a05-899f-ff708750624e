/**
 * @file    Spw.h
 * @brief   Spw header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Sentence.h"

#ifndef __SPW_H__
#define __SPW_H__

#define SPW_NA      0
#define SPW_EPV     1
#define SPW_SSD     2
#define SPW_VSD     3

/******************************************************************************
 * 
 * SPW - Security password sentence
 *
 * $--SPW,ccc,c--c,x,c--c,*hh<CR><LF>
 *         |   |   |  |
 *         1   2   3  4
 *
 * 1. Password protected sentence
 * 2. Unique identifier
 * 3. Password level
 * 4. Password
 *
 ******************************************************************************/
class CSpw : public CSentence
{
public:
    CSpw();

    bool     Parse(const char *pszSentence);
    void     ClearData(void);
    uint8_t  GetCode(void)       { return m_nCode; }
    uint32_t GetMMSI(void)       { return m_dwMMSI; }
    uint8_t  GetPwdLevel(void)   { return m_nPwdLevel; }
    char*    GetPasswordPtr(void){ return m_szPassword; }

    void     RunPeriodically(void);

protected:
    uint8_t     m_nCode;
    uint32_t    m_dwMMSI;
    uint8_t     m_nPwdLevel;
    char        m_szPassword[33];
    uint32_t    m_dwRcvTick;
};

#endif /* __SPW_H__ */

