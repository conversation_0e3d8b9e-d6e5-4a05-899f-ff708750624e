/**
 * @file    ComLib.c
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "AllConst.h"
#include "ComLib.h"

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

int MakeSizeOfDoubleWord(int nSize)
{
    return((nSize + 3) & 0xfffffffc);
}

BOOL RangeCheckBack08(BACK08 *pDataValue,BACK08 nLower,BACK08 nUpper,BACK08 nSetValue)
{
    if (*pDataValue < nLower || *pDataValue > nUpper)
    {
        *pDataValue = nSetValue;
        return TRUE;
    }
    return FALSE;
}
BOOL RangeCheckBack16(BACK16 *pDataValue,BACK16 nLower,BACK16 nUpper,BACK16 nSetValue)
{
    if (*pDataValue < nLower || *pDataValue > nUpper)
    {
        *pDataValue = nSetValue;
        return TRUE;
    }
    return FALSE;
}
BOOL RangeCheckBack32(BACK32 *pDataValue,BACK32 nLower,BACK32 nUpper,BACK32 nSetValue)
{
    if (*pDataValue < nLower || *pDataValue > nUpper)
    {
        *pDataValue = nSetValue;
        return TRUE;
    }
    return FALSE;
}
BOOL RangeCheckBackDD(BACKDD *pDataValue,BACKDD nLower,BACKDD nUpper,BACKDD nSetValue)
{
    if (*pDataValue < nLower || *pDataValue > nUpper)
    {
        *pDataValue = nSetValue;
        return TRUE;
    }
    return FALSE;
}
//==============================================================================
const  DWORD vCrcTable[256] = {
    0x00000000,0x77073096,0xEE0E612C,0x990951BA,0x076DC419,0x706AF48F,
    0xE963A535,0x9E6495A3,0x0EDB8832,0x79DCB8A4,0xE0D5E91E,0x97D2D988,
    0x09B64C2B,0x7EB17CBD,0xE7B82D07,0x90BF1D91,0x1DB71064,0x6AB020F2,
    0xF3B97148,0x84BE41DE,0x1ADAD47D,0x6DDDE4EB,0xF4D4B551,0x83D385C7,
    0x136C9856,0x646BA8C0,0xFD62F97A,0x8A65C9EC,0x14015C4F,0x63066CD9,
    0xFA0F3D63,0x8D080DF5,0x3B6E20C8,0x4C69105E,0xD56041E4,0xA2677172,
    0x3C03E4D1,0x4B04D447,0xD20D85FD,0xA50AB56B,0x35B5A8FA,0x42B2986C,
    0xDBBBC9D6,0xACBCF940,0x32D86CE3,0x45DF5C75,0xDCD60DCF,0xABD13D59,
    0x26D930AC,0x51DE003A,0xC8D75180,0xBFD06116,0x21B4F4B5,0x56B3C423,
    0xCFBA9599,0xB8BDA50F,0x2802B89E,0x5F058808,0xC60CD9B2,0xB10BE924,
    0x2F6F7C87,0x58684C11,0xC1611DAB,0xB6662D3D,0x76DC4190,0x01DB7106,
    0x98D220BC,0xEFD5102A,0x71B18589,0x06B6B51F,0x9FBFE4A5,0xE8B8D433,
    0x7807C9A2,0x0F00F934,0x9609A88E,0xE10E9818,0x7F6A0DBB,0x086D3D2D,
    0x91646C97,0xE6635C01,0x6B6B51F4,0x1C6C6162,0x856530D8,0xF262004E,
    0x6C0695ED,0x1B01A57B,0x8208F4C1,0xF50FC457,0x65B0D9C6,0x12B7E950,
    0x8BBEB8EA,0xFCB9887C,0x62DD1DDF,0x15DA2D49,0x8CD37CF3,0xFBD44C65,
    0x4DB26158,0x3AB551CE,0xA3BC0074,0xD4BB30E2,0x4ADFA541,0x3DD895D7,
    0xA4D1C46D,0xD3D6F4FB,0x4369E96A,0x346ED9FC,0xAD678846,0xDA60B8D0,
    0x44042D73,0x33031DE5,0xAA0A4C5F,0xDD0D7CC9,0x5005713C,0x270241AA,
    0xBE0B1010,0xC90C2086,0x5768B525,0x206F85B3,0xB966D409,0xCE61E49F,
    0x5EDEF90E,0x29D9C998,0xB0D09822,0xC7D7A8B4,0x59B33D17,0x2EB40D81,
    0xB7BD5C3B,0xC0BA6CAD,0xEDB88320,0x9ABFB3B6,0x03B6E20C,0x74B1D29A,
    0xEAD54739,0x9DD277AF,0x04DB2615,0x73DC1683,0xE3630B12,0x94643B84,
    0x0D6D6A3E,0x7A6A5AA8,0xE40ECF0B,0x9309FF9D,0x0A00AE27,0x7D079EB1,
    0xF00F9344,0x8708A3D2,0x1E01F268,0x6906C2FE,0xF762575D,0x806567CB,
    0x196C3671,0x6E6B06E7,0xFED41B76,0x89D32BE0,0x10DA7A5A,0x67DD4ACC,
    0xF9B9DF6F,0x8EBEEFF9,0x17B7BE43,0x60B08ED5,0xD6D6A3E8,0xA1D1937E,
    0x38D8C2C4,0x4FDFF252,0xD1BB67F1,0xA6BC5767,0x3FB506DD,0x48B2364B,
    0xD80D2BDA,0xAF0A1B4C,0x36034AF6,0x41047A60,0xDF60EFC3,0xA867DF55,
    0x316E8EEF,0x4669BE79,0xCB61B38C,0xBC66831A,0x256FD2A0,0x5268E236,
    0xCC0C7795,0xBB0B4703,0x220216B9,0x5505262F,0xC5BA3BBE,0xB2BD0B28,
    0x2BB45A92,0x5CB36A04,0xC2D7FFA7,0xB5D0CF31,0x2CD99E8B,0x5BDEAE1D,
    0x9B64C2B0,0xEC63F226,0x756AA39C,0x026D930A,0x9C0906A9,0xEB0E363F,
    0x72076785,0x05005713,0x95BF4A82,0xE2B87A14,0x7BB12BAE,0x0CB61B38,
    0x92D28E9B,0xE5D5BE0D,0x7CDCEFB7,0x0BDBDF21,0x86D3D2D4,0xF1D4E242,
    0x68DDB3F8,0x1FDA836E,0x81BE16CD,0xF6B9265B,0x6FB077E1,0x18B74777,
    0x88085AE6,0xFF0F6A70,0x66063BCA,0x11010B5C,0x8F659EFF,0xF862AE69,
    0x616BFFD3,0x166CCF45,0xA00AE278,0xD70DD2EE,0x4E048354,0x3903B3C2,
    0xA7672661,0xD06016F7,0x4969474D,0x3E6E77DB,0xAED16A4A,0xD9D65ADC,
    0x40DF0B66,0x37D83BF0,0xA9BCAE53,0xDEBB9EC5,0x47B2CF7F,0x30B5FFE9,
    0xBDBDF21C,0xCABAC28A,0x53B39330,0x24B4A3A6,0xBAD03605,0xCDD70693,
    0x54DE5729,0x23D967BF,0xB3667A2E,0xC4614AB8,0x5D681B02,0x2A6F2B94,
    0xB40BBE37,0xC30C8EA1,0x5A05DF1B,0x2D02EF8D};
//==============================================================================
DWORD GetCrc32(const UCHAR *pData,DWORD dSize)
{
    DWORD i;
    DWORD dCrcValue = 0xffffffff;

    for (i = 0;i < dSize;i++)
         dCrcValue = (dCrcValue >> 8) ^ vCrcTable[(pData[i] ^ dCrcValue) & 0xff];
    dCrcValue ^= 0xffffffff;
    return(dCrcValue);
}
DWORD GetCrc32Cont(const UCHAR *pData,DWORD dSize,DWORD dPrevCRC)
{
    DWORD i;

    for (i = 0;i < dSize;i++)
         dPrevCRC = (dPrevCRC >> 8) ^ vCrcTable[(pData[i] ^ dPrevCRC) & 0xff];
    return(dPrevCRC);
}
DWORD GetUnAlignedLongData(UCHAR *pData)
{
    DWORD dDataX;

    dDataX = pData[3];
    dDataX = (dDataX << 8) | pData[2];
    dDataX = (dDataX << 8) | pData[1];
    dDataX = (dDataX << 8) | pData[0];

    return(dDataX);
}
INT32 PowerOf10(int nPower)
{
    static INT32 vPowerTable[] = {    1,   10,  100, 1000,10000,100000,1000000,10000000,*********,*********0};
    return(vPowerTable[nPower]);
}
void  SwapInt(int *pX,int *pY)
{
    int  nZ;

    nZ  = *pX;
    *pX = *pY;
    *pY = nZ;
}
void  SwapLong(long *pX,long *pY)
{
    long nZ;

    nZ  = *pX;
    *pX = *pY;
    *pY = nZ;
}
void  SwapReal(REAL *pX,REAL *pY)
{
    REAL rZ;

    rZ  = *pX;
    *pX = *pY;
    *pY = rZ;
}
void  SwapFloat(float *pX,float *pY)
{
    float fZ;

    fZ  = *pX;
    *pX = *pY;
    *pY = fZ;
}
int   CirCularDec(int nValue,int nLast)
{
    --nValue;
    if (nValue < 0)
        nValue = nLast - 1;

    return(nValue);
}
int   CirCularInc(int nValue,int nLast)
{
    ++nValue;
    if (nValue >= nLast)
        nValue =  0;

    return(nValue);
}
int   GetCircularSize(int nHead,int nTail,int nSize)
{
    int  nResult;

    if (nHead >= nTail)
        nResult= nHead - nTail;
    else
        nResult= nHead - nTail + nSize;

    return(nResult);
}
int   GetMinInt(int X,int Y)
{
    if (X < Y)
        return(X);

    return(Y);
}
int   GetMaxInt(int X,int Y)
{
    if (X > Y)
        return(X);

    return(Y);
}
int   IsAllSameCharacters(char *pStr,char bChr)
{
    int  i,nLen;

    nLen = strlen((char*)pStr);
    for (i = 0;i < nLen;i++)
         if (pStr[i] != bChr)
             return 0;

    return 1;
}
int   IsAllDigit(char *pData,int nSize,char bFill)
{
    int  i,nRetVal;

    nRetVal = 1;
    for (i = 0;i < nSize;i++)
         if (pData[i] < '0' || pData[i] > '9')
            {
             nRetVal = 0;
             break;
            }

    if (nRetVal == 0)
        if (bFill)
            memset(pData,bFill,nSize);

    return(nRetVal);
}
int   IsAllAscii(char *pData, int nSize, char bFill)
{
    int  i,nRetVal;

    nRetVal = 1;
    for (i = 0;i < nSize;i++)
    {
        if (pData[i] < ' ' || pData[i] > 0x7f)
        {
            nRetVal = 0;
            break;
        }
    }

    if (i < nSize)
    {
        if (bFill)
            memset(&pData[i], bFill, nSize-i);
    }

    return(nRetVal);
}
void  IsOneAscii(char *pData,int nSize,char bFill)
{
    int  i;

    for (i = 0;i < nSize;i++)
        if (pData[i] < ' ' || pData[i] > 0x7f)
            pData[i] = bFill;
}
void  RemoveNullChar(char *pData,int nSize,char bFill)
{
    int  i;

    for (i = 0;i < nSize;i++)
        if (pData[i] == 0x00)
            pData[i] = bFill;
}
int   IsInRangeLong(int nData,int nMin,int nMax)
{
      if (nData >= nMin && nData <= nMax)
          return 1;

      return 0;
}
char *FullAllTrimStr(char *pStr)
{
    char vTemp[1024];
    int  i,nCount=0;

    for (i = 0;i < strlen((char*)pStr);i++)
    {
        if (pStr[i] != ' ')
            vTemp[nCount++] = pStr[i];
    }

    vTemp[nCount] = 0x00;
    strcpy(pStr,vTemp);

    return(pStr);
}
char *RightTrimStr(char *pStr,char bTrimChar)
{
    int  i,nLen;

    nLen = strlen(pStr);
    for (i = (nLen - 1);i >= 0;i--)
        if (pStr[i] == bTrimChar)
            pStr[i] = 0x00;
        else
            break;

    return(pStr);
}
int   StrToInt(char *pStr,int nStart,int nLen)
{
    char vTemp[24];

    memmove(vTemp,pStr + nStart,nLen);
    vTemp[nLen] = 0x00;

    return(atoi(vTemp));
}
char UpperChar(char bData)
{
    if (bData >= 'a' && bData <= 'z')
        bData = bData - 'a' + 'A';

    return(bData);
}
char LowerChar(char bData)
{
    if (bData >= 'A' && bData <= 'Z')
        bData = bData - 'A' + 'a';

    return(bData);
}
void  UpperString(char *pStr)
{
    while (*pStr)
    {
        *pStr = UpperChar(*pStr);
        ++pStr;
    }
}
void  LowerString(char *pStr)
{
    while (*pStr)
    {
        *pStr = LowerChar(*pStr);
        ++pStr;
    }
}
UCHAR HexStrToByte(char *pHexStr)
{
    UCHAR bValue;
    char bTemp;
    int  i;

    bValue = 0;

    for (i = 0; i < 2; i++)
    {
        bTemp = *pHexStr++;
        bTemp = UpperChar(bTemp);

        if (bTemp >= 'A')
            bTemp = bTemp - 'A' + 10;
        else
            bTemp = bTemp - '0';

        bValue = bValue * 16 + bTemp;
    }

    return(bValue);
}
DWORD HexStrToLong(char *pHexStr,int nSize)
{
    DWORD dValue;
    char bTemp;

    dValue = 0;
    while (nSize)
          {
           bTemp = *pHexStr++;
           bTemp = UpperChar(bTemp);
           if (bTemp >= 'A')
               bTemp = bTemp - 'A' + 10;
           else
               bTemp = bTemp - '0';
           dValue = dValue * 16 + bTemp;
           --nSize;
          }

    return(dValue);
}
UCHAR GetHexDigit(int nData,int nFlag)
{
    UCHAR bRst = '0';

    if (nData >= 0 && nData <= 9)
        bRst = nData + '0';
    else
    {
        if (nFlag)
            bRst = nData - 10 + 'A';
        else
            bRst = nData - 10 + 'a';
    }

    return(bRst);
}
char *ByteToBinStr(UCHAR bData)
{
    static char vTemp[12];
    int  i;
    UCHAR  bMask = 0x80;

    for (i = 0;i < 8;i++)
    {
        if (bData & bMask)
            vTemp[i] = '1';
        else
            vTemp[i] = '0';
        bMask >>= 1;
    }

    vTemp[8] = 0x00;

    return(vTemp);
}
char *ByteToHexStr(UCHAR bData,int nFlag)
{
    static char vTemp[12];
    UCHAR  bT;

    bT = (bData >> 4) & 0x0f;
    vTemp[0] = GetHexDigit(bT,nFlag);
    vTemp[1] = GetHexDigit(bData & 0x0f,nFlag);
    vTemp[2] = 0x00;

    return(vTemp);
}
char *ByteArrayToHexStr(BYTE *pData, int nLen, int nFlag, char *pOutBuff)
{
    for(int i = 0 ; i < nLen ; i++)
        strcat(pOutBuff, ByteToHexStr(pData[i], nFlag));

    return pOutBuff;
}
char *WordToHexStr(HWORD wData,int nFlag)
{
    static char vTemp[12];
    UCHAR  bT;

    bT = wData / 256;
    strcpy((char*)vTemp,(char *)ByteToHexStr(bT,nFlag));
    bT = wData & 0xff;
    strcat((char*)vTemp,(char *)ByteToHexStr(bT,nFlag));

    return(vTemp);
}
char *LongToHexStr(DWORD dData,int nFlag)
{
    static char vTemp[12];
    HWORD  wH,wL;

    wH = dData / 65536L;
    wL = dData & 0xffff;

    strcpy((char*)vTemp,(char *)WordToHexStr(wH,nFlag));
    strcat((char*)vTemp,(char *)WordToHexStr(wL,nFlag));

    return(vTemp);
}
int GetReverseBitValue(UCHAR cReverseBitData)
{
    static INT16 vReverDataTableX[256] = {
    //   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
         0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1,     // 00--0f
         8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1,     // 10--1f
         4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1,     // 20--2f
        12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1,     // 30--3f
         2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1,     // 40--4f
        10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1,     // 50--5f
         6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1,     // 60--6f
        14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1,     // 70--7f
         1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1,     // 80--8f
         9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1,     // 90--9f
         5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1,     // a0--af
        13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1,     // b0--bf
         3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1,     // c0--cf
        11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1,     // d0--df
         7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1,     // e0--ef
        15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1};    // f0--ff

      return(vReverDataTableX[cReverseBitData]);
}
//========================================================================
UCHAR CalcNmeaCheckSum(UCHAR *pData, UCHAR *pX, UCHAR *pY)
{
    int   i, nLen;
    UCHAR bX, bY, bCS;

    nLen = strlen((char*)pData);

    bCS  = 0x00;
    for (i = 1;i < nLen;i++)
         bCS ^= pData[i];

    bX = (bCS >> 4) & 0x0f;
    if (bX > 0x09)
        bX = bX - 0x0a + 'A';
    else
        bX = bX        + '0';

    bY = bCS & 0x0f;
    if (bY > 0x09)
        bY = bY - 0x0a + 'A';
    else
        bY = bY        + '0';

    *pX = bX;
    *pY = bY;

    return(bCS);
}
void  MakeNmeaCheckSum(UCHAR *pData, int nAppendCS, int nAppendCrLf)
{
    int   nLen;
    UCHAR bX,bY;

    nLen = strlen((char*)pData);

    CalcNmeaCheckSum(pData,&bX,&bY);

    if (nAppendCS)
       {
        pData[nLen++] = '*';
        pData[nLen++] = bX;
        pData[nLen++] = bY;
       }

    if (nAppendCrLf)
       {
        pData[nLen++] = ASC_CHR_CR;
        pData[nLen++] = ASC_CHR_LF;
       }

    pData[nLen++] = 0x00;
}
//========================================================================
float RemezSinFloat32(float x)
{
    // -π <= x <= +π
    // -π <= x <= +π
    // -π <= x <= +π
    const float s1 = 1.27286784355564790f;
    const float s2 = 0.40516641840920530f;
    const float s3 = 0.77754742428930464f;
    union { float f; DWORD i; } s4 = { 0.22207681739058507f };

    if (x > M_PI_VALUE_F)
        x = x - M_PI_MUL_2_F;

    union { float f; DWORD i; } vx = { x };
    DWORD dSign = vx.i & 0x80000000;

    vx.i &= 0x7FFFFFFF;

    float qpprox = x * (s1 - s2 * vx.f);

    s4.i |= dSign;

    return(qpprox * (s3 + s4.f * qpprox));
}
float RemezCosFloat32(float x)
{
    // 0 <= x <= (2 * π)
    // 0 <= x <= (2 * π)
    // 0 <= x <= (2 * π)

    x += M_PI_DIV_2_F;
    if (x >= M_PI_MUL_2_F)
        x -= M_PI_MUL_2_F;

    return(RemezSinFloat32(x));
}
