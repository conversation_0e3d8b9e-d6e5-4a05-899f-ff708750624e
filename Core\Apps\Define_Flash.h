#ifndef __DEFINE_FLASH_H__
#define __DEFINE_FLASH_H__

//=============================================================================
#define  _FLASH_WORD_               32
//=============================================================================
// Base address of the Flash sectors(Bank1)
#define ADDR_FLASH_BANK0_SECTOR_00  ((uint32_t)0x08000000) // Base @ of Sector 00, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_01  ((uint32_t)0x08020000) // Base @ of Sector 01, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_02  ((uint32_t)0x08040000) // Base @ of Sector 02, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_03  ((uint32_t)0x08060000) // Base @ of Sector 03, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_04  ((uint32_t)0x08080000) // Base @ of Sector 04, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_05  ((uint32_t)0x080A0000) // Base @ of Sector 05, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_06  ((uint32_t)0x080C0000) // Base @ of Sector 06, 128 Kbytes
#define ADDR_FLASH_BANK0_SECTOR_07  ((uint32_t)0x080E0000) // Base @ of Sector 07, 128 Kbytes
// Base address of the Flash sectors(Bank2)
#define ADDR_FLASH_BANK1_SECTOR_00  ((uint32_t)0x08100000) // Base @ of Sector 00, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_01  ((uint32_t)0x08120000) // Base @ of Sector 01, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_02  ((uint32_t)0x08140000) // Base @ of Sector 02, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_03  ((uint32_t)0x08160000) // Base @ of Sector 03, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_04  ((uint32_t)0x08180000) // Base @ of Sector 04, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_05  ((uint32_t)0x081A0000) // Base @ of Sector 05, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_06  ((uint32_t)0x081C0000) // Base @ of Sector 06, 128 Kbytes
#define ADDR_FLASH_BANK1_SECTOR_07  ((uint32_t)0x081E0000) // Base @ of Sector 07, 128 Kbytes
//=============================================================================
#define ADDR_FLASH_END_ADDR         ((uint32_t)0x08200000)

//=============================================================================
#define HW_CONF_BACK_UP_ADDR        ADDR_FLASH_BANK1_SECTOR_04
#define SYS_DATA_BACK_UP_ADDR       ADDR_FLASH_BANK1_SECTOR_05
#define ROS_DATA_BACK_UP_ADDR       ADDR_FLASH_BANK1_SECTOR_06
#define LOG_DATA_BACK_UP_ADDR       ADDR_FLASH_BANK1_SECTOR_07

#define MAIN_APP_START_SECTOR_ID    0
#define MAIN_APP_LAST_SECTOR_ID     3
#define MAIN_APP_NUM_SECTOR         (MAIN_APP_LAST_SECTOR_ID - MAIN_APP_START_SECTOR_ID + 1)
#define MAIN_APP_START_SECTOR_ADDR  (ADDR_FLASH_BANK1_SECTOR_00)
#define MAIN_APP_LAST_SECTOR_ADDR   (ADDR_FLASH_BANK1_SECTOR_03)

#define ADDR_FLASH_BANK_OFFSET      ADDR_FLASH_BANK1_SECTOR_00 - ADDR_FLASH_BANK0_SECTOR_00
//=============================================================================

#endif//__DEFINE_FLASH_H__
