/**
 * @file    Uart.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <memory>
#include "stm32h743xx.h"
#include "SysConst.h"

#ifndef  __UART_H__
#define  __UART_H__

//=============================================================================
#define  UART_NULL_CHAR                  -1
//=============================================================================
#define  UART_RX_DMA_MODE_BUFF_SIZE     (32)          // multiple of 32
#define  UART_TX_DMA_MODE_BUFF_SIZE     (32)          // multiple of 32
//=============================================================================

class cUart
{
public:
    cUart(UINT8 uUartID, DWORD dBaseAddr, int nRxBuffSize, int nTxBuffSize, int nSpeed);
    virtual ~cUart(void);

public:
    int   IsSendingData(void);
    void  SetSendingMode(int nMode);
    void  SetTxBufferClear(void);
    int   IsDataInRxBuffer(void);

    void  ClearRxBuff();
    int   GetComData(void);
    void  PutComData(UCHAR bData);
    void  WriteComStr(const char *pstrData);
    int   GetSpeed();

    virtual int   ReadComData(UCHAR *pData, HWORD wSize);
    virtual void  WriteComData(const UCHAR *pData, HWORD wSize);
    virtual void  SetUartPara(int nSpeed) = 0;
    virtual void  RunUartIsrHandler(void) = 0;
    virtual void  RunUartIsrRxHandler(void) = 0;
    virtual void  RunUartIsrTxHandler(void) = 0;
    virtual DWORD GetStatusRegister(void) = 0;

public:
    UINT8  m_uUartID;

protected:
    DWORD  m_dBaseAddr;
 
    int    m_nRxBuffSize;
    int    m_nTxBuffSize;
    int    m_nSpeed;
 
    DWORD  m_dErCount;
    DWORD  m_dRxCount;
    DWORD  m_dTxCount;
 
    volatile int   m_nRxHead;
    volatile int   m_nRxTail;
    volatile int   m_nTxHead;
    volatile int   m_nTxTail;
    volatile int   m_nSending;

    UCHAR  *m_pRxBuffData;
    UCHAR  *m_pTxBuffData;

};

class cUartSYS : public cUart
{
public:
    cUartSYS(UINT8 uUartID, USART_TypeDef *pBaseAddr, DWORD dSysIrqNo, int nRxBuffSize, int nTxBuffSize, int nSpeed);
    virtual ~cUartSYS(void);

    static std::shared_ptr<cUartSYS> getDebugPort() {
        static std::shared_ptr<cUartSYS> pInstDebug = std::make_shared<cUartSYS>( UARTID_8, UART8, UART8_IRQn, 
                                                                                1024, 1024, 115200
                                                                                );
        return pInstDebug;
    }

public:
    virtual int   ReadComData(UCHAR *pData, HWORD wSize);
    virtual void  WriteComData(const UCHAR *pData, HWORD wSize);
    virtual void  SetUartPara(int nSpeed);
    virtual void  RunUartIsrHandler(void);
    virtual void  RunUartIsrRxHandler(void);
    virtual void  RunUartIsrTxHandler(void);
    virtual DWORD GetStatusRegister(void);

    void  SendPacketDataByDMA(int nRingTail);

    void  SetDmaStreamNoByBaseAddr(DWORD dBaseAddr);
    void  RunDmaSettingForRX(void);
    void  RunDmaSettingForTX(void);

public:
    USART_TypeDef      *m_pBaseAddr;
    UART_HandleTypeDef  m_xUartHand;
    DWORD               m_dSysIrqNo;
};

#endif /*__UART_H__*/

