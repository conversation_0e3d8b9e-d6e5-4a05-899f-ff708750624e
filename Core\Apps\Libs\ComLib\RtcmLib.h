/**
 * @file    RtcmLib.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"

#ifndef  __RTCMLIB_H__
#define  __RTCMLIB_H__

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
UCHAR ReverseEndian8Bit(UCHAR bData);
UCHAR ReverseEndian6Bit(UCHAR bData);
//------------------------------------------------------------------------
UCHAR GetParityD25(UCHAR bDataBit, DWORD dSrcData);
UCHAR GetParityD26(UCHAR nLastBit, DWORD dSrcData);
UCHAR GetParityD27(UCHAR nLastBit, DWORD dSrcData);
UCHAR GetParityD28(UCHAR nLastBit, DWORD dSrcData);
UCHAR GetParityD29(UCHAR nLastBit, DWORD dSrcData);
UCHAR GetParityD30(UCHAR nLastBit, DWORD dSrcData);
//------------------------------------------------------------------------
void  Add01ReverseRTCMSC104Msg(UCHAR *pSrcData, UCHAR *pRtcmData, int nIndex);
void  GetD29D30DataValue(UCHAR *pD29, UCHAR *pD30);
int   MakeMsgDGNSSToRTCMSC104(UCHAR *pDgnssData, int nDgnssLen, UCHAR *pRtcmData);
//========================================================================

#ifdef  __cplusplus
}
#endif

#endif

