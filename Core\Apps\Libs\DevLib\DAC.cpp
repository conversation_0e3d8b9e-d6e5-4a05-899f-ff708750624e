/**
 * @file    Dac.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "DAC.h"

//=============================================================================
cDac::cDac(DAC_TypeDef *pBaseAddr)
{
    __HAL_RCC_DAC12_CLK_ENABLE();

    m_pBaseAddr         = pBaseAddr;

    memset(&m_xDacHand, 0x00, sizeof(m_xDacHand));
    memset(&m_xDacCnfg, 0x00, sizeof(m_xDacCnfg));

    m_xDacHand.Instance = m_pBaseAddr;
    if (HAL_DAC_Init(&m_xDacHand) != HAL_OK)
    {
        while (1);
    }

    m_xDacCnfg.DAC_Trigger      = DAC_TRIGGER_NONE;
    m_xDacCnfg.DAC_OutputBuffer = DAC_OUTPUTBUFFER_ENABLE;
    if (HAL_DAC_ConfigChannel(&m_xDacHand, &m_xDacCnfg, DAC_CHANNEL_1) != HAL_OK)
    {
        while (1);
    }
    if (HAL_DAC_ConfigChannel(&m_xDacHand, &m_xDacCnfg, DAC_CHANNEL_2) != HAL_OK)
    {
        while (1);
    }

    //m_pBaseAddr->DHR12R1 = 0x03ff;
    //m_pBaseAddr->DHR12R2 = 0x0000;
    
    m_pBaseAddr->DHR12R1 = 0x0000;
    m_pBaseAddr->DHR12R2 = 0x03ff;

    __HAL_DAC_ENABLE(&m_xDacHand, DAC_CHANNEL_1);
    __HAL_DAC_ENABLE(&m_xDacHand, DAC_CHANNEL_2);
}

cDac::~cDac(void)
{
}

void  cDac::SetDAC0Data(HWORD wData)
{
    //m_pBaseAddr->DHR12R1 = wData;
    m_pBaseAddr->DHR12R2 = wData;
}

void  cDac::SetDAC1Data(HWORD wData)
{
    //m_pBaseAddr->DHR12R2 = wData;
    m_pBaseAddr->DHR12R1 = wData;
}

void  cDac::RunDACIsrHandler(void)
{
}
//=============================================================================

