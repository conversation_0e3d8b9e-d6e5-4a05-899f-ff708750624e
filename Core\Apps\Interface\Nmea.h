#include "DataType.h"
#include "SysConst.h"
#include "SysLib.h"
#include "GpsLib.h"

#include "Dtm.h"
#include "Gbs.h"
#include "Gga.h"
#include "Gll.h"
#include "Gns.h"
#include "Hdt.h"
#include "Rmc.h"
#include "Rot.h"
#include "Ths.h"
#include "Vbw.h"
#include "Vtg.h"
#include "Zda.h"

#ifndef  __NMEA_H__
#define  __NMEA_H__

#define LEN_NMEA_TALKER  10
#define LEN_NMEA_SENT    10

class cNMEA
{
public:
    cNMEA(int nSensorID);
    virtual ~cNMEA(void);

public:
	virtual bool ProcessGBS(char *pstrCmd);
    virtual bool ProcessGGA(char *pstrCmd);
    virtual bool ProcessGLL(char *pstrCmd);
	virtual bool ProcessGNS(char *pstrCmd);
    virtual bool ProcessRMC(char *pstrCmd);
    virtual bool ProcessVTG(char *pstrCmd, int nCrsVal);
	virtual bool ProcessVBW(char *pstrCmd);
	virtual bool ProcessTHS(char *pstrCmd);
	virtual bool ProcessHDT(char *pstrCmd);
	virtual bool ProcessROT(char *pstrCmd);
	virtual bool ProcessZDA(char *pstrCmd);
    virtual bool ProcessDTM(char *pstrCmd);

protected:
    CDtm    m_hDtm;
    CGbs    m_hGbs;
    CGga    m_hGga;
    CGll    m_hGll;
    CGns    m_hGns;
    CHdt    m_hHdt;
    CRmc    m_hRmc;
    CRot    m_hRot;
    CThs    m_hThs;
    CVbw    m_hVbw;
    CVtg    m_hVtg;
    CZda    m_hZda;
};
#endif
