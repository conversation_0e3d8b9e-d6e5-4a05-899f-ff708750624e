/**
 * @file    AisMsg.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Define_AIS.h"
#include "DataType_AIS.h"

#ifndef  __AISMSG_H__
#define  __AISMSG_H__

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
#define	 AIS_VERSION_INDICATOR               2  // Station compliant with ITU-R M.1371-5 (or later)

#define  AIS_MSG_NO_ID_00                    0  //
#define  AIS_MSG_NO_ID_01                    1  // Position report
#define  AIS_MSG_NO_ID_02                    2  // Position report
#define  AIS_MSG_NO_ID_03                    3  // Position report
#define  AIS_MSG_NO_ID_04                    4  // Base station report
#define  AIS_MSG_NO_ID_05                    5  // static and voyage related data
#define  AIS_MSG_NO_ID_06                    6  // Binary addressed message
#define  AIS_MSG_NO_ID_07                    7  // Binary acknowledgement
#define  AIS_MSG_NO_ID_08                    8  // Binary broadcast message
#define  AIS_MSG_NO_ID_09                    9  // Standard SAR Aircraft Position Report
#define  AIS_MSG_NO_ID_10                   10  // UTC and date inquiry
#define  AIS_MSG_NO_ID_11                   11  // UTC and date response
#define  AIS_MSG_NO_ID_12                   12  // Addressed safety related message
#define  AIS_MSG_NO_ID_13                   13  // Safety related acknowledgement
#define  AIS_MSG_NO_ID_14                   14  // Safety related broadcast message
#define  AIS_MSG_NO_ID_15                   15  // Interrogation
#define  AIS_MSG_NO_ID_16                   16  // Assigned mode command
#define  AIS_MSG_NO_ID_17                   17  // DGNSS broadcast binary message
#define  AIS_MSG_NO_ID_18                   18  // Standard Class B equipment position report
#define  AIS_MSG_NO_ID_19                   19  // Extended Class B equipment position report
#define  AIS_MSG_NO_ID_20                   20  // Data link management message
#define  AIS_MSG_NO_ID_21                   21  // Aid-to-Navigation report
#define  AIS_MSG_NO_ID_22                   22  // Channel management
#define  AIS_MSG_NO_ID_23                   23  // Group assignment command
#define  AIS_MSG_NO_ID_24                   24  // Static data report
#define  AIS_MSG_NO_ID_25                   25  // Single slot binary message
#define  AIS_MSG_NO_ID_26                   26  // Multiple slot binary message
#define  AIS_MSG_NO_ID_27                   27  // Position report for long range applications

#define  AIS_MSG_NO_ID_24_A                 62  // Part A of AIS_MSG_NO_ID_24

#define  AIS_MSG_NO_ID_63                   63
#define  AIS_TESTMODE_MSGID_0101            256 // IEC-61993-2(ed2.0) 10.2 Standard test signal number 2 (TDMA)
#define  AIS_TESTMODE_MSGID_00001111        257 // IEC-61993-2(ed2.0) 10.3 Standard test signal number 3 (TDMA)
#define  AIS_TESTMODE_MSGID_PRBS_ONE        258 // IEC-61993-2(ed2.0) 10.4 Standard test signal number 4 (PRBS) MKD menu : "Fixed Msg."
#define  AIS_TESTMODE_MSGID_PRBS_CLUSTER    269 // IEC-61993-2(ed2.0) 10.5 Standard test signal number 5 (PRBS) MKD menu : "Random Data"
#define  AIS_TESTMODE_MSGID_UNMOD_CARRIER   260 // internal test mode message, it's not VDL message
#define  AIS_RXMSGID_MIN                    0
#define  AIS_RXMSGID_MAX                    63
#define  AIS_MSG_NO_ID_UNDEFINED            64

#define	 PI_MSGID_70                        70  // unstructured binary data message 25 from ABM or BBM message through PI
#define  PI_MSGID_71                        71  // unstructured binary data message 26 from ABM or BBM message through PI

//========================================================================

//=============================================================================
typedef  struct 
{
    HWORD  wMsgChNO;
    HWORD  wMsgFrameNO;
    HWORD  wMsgSlotNO;
    HWORD  wMsgBitsLen;
    HWORD  wMsgByteLen;
    INT8   nNumMsgSlot;

    DWORD  dMsgID;
    DWORD  dRptInd;
    DWORD  dSrcMMSI;
}xAISMSG00;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dNavStatus;
    int    nROT;
    DWORD  dSOG;
    DWORD  dPosAcc;
    POS_ALLF xFullPosX;
    DWORD  dCOG;
    DWORD  dHDG;
    DWORD  dTimeStamp;
    DWORD  dManoeuvre;
    DWORD  dSpareX;
    DWORD  dRAIM;
    DWORD  dComStateSyncState;                   // Msg1/2/3
    DWORD  dComStateTimeOut;                     // Msg1/2
    DWORD  dComStateSubMsg;                      // Msg1/2

    DWORD  dComStateSlotInc;                     // Msg3
    DWORD  dComStateNoSlots;                     // Msg3
    DWORD  dComStateKeepFlag;                    // Msg3
}xAISMSG01;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dYear;
    DWORD  dMonth;
    DWORD  dDay;
    DWORD  dHour;
    DWORD  dMin;
    DWORD  dSec;
    DWORD  dPosAcc;
    POS_ALLF xFullPosX;
    DWORD  dEPFD;
    DWORD  dwTxCtrlLR;
    DWORD  dSpareX;
    DWORD  dRAIM;
    DWORD  dComStateSyncState;
    DWORD  dComStateTimeOut;
    DWORD  dComStateSubMsg;
}xAISMSG04;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dVersion;
    DWORD  dIMO;
    char   vCallSign[LEN_MAX_CALLSIGN + 1];
    char   vShipName[LEN_MAX_SHIP_NAME + 2];
    DWORD  dShipType;
    xANTPOS xAntPos;
    DWORD  dEPFS;
    DWORD  dETA;
    DWORD  dETAMonth;
    DWORD  dETADay;
    DWORD  dETAHour;
    DWORD  dETAMinute;
    DWORD  dDraught;
    char   vDest[LEN_MAX_DESTINATION + 2];
    DWORD  dHasDTE;
    DWORD  dSpareX;
}xAISMSG05;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSqncNO;
    DWORD  dDestID;
    DWORD  dRetransmit;
    DWORD  dSpareX;
    DWORD  dDAC;                                 // Designated Area Code
    DWORD  dFID;                                 // Function Identifier
    DWORD  dDataSize;
    UCHAR  pDataBuff[115 + 5];                   // max 936 bits
}xAISMSG06;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dDestID1;
    DWORD  dSqncNO1;
    DWORD  dDestID2;                             // 0=NULL
    DWORD  dSqncNO2;
    DWORD  dDestID3;                             // 0=NULL
    DWORD  dSqncNO3;
    DWORD  dDestID4;                             // 0=NULL
    DWORD  dSqncNO4;
}xAISMSG07;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSqncNO;
    DWORD  dDestID;
    DWORD  dRetransmit;
    DWORD  dSpareX;
    DWORD  dDAC;                                 // Designated Area Code
    DWORD  dFID;                                 // Function Identifier
    DWORD  dDataSize;
    UCHAR  pDataBuff[121 + 5];                   // max 968 bits
}xAISMSG08;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dAltitude;
    DWORD  dSOG;
    DWORD  dPosAcc;
    POS_ALLF xFullPosX;
    DWORD  dCOG;
    DWORD  dTimeStamp;
    DWORD  dAltSensor;
    DWORD  dSpareX;
    DWORD  dDTE;
    DWORD  dSpareY;
    DWORD  dAssignedMode;
    DWORD  dRAIM;
    DWORD  dComStateSelector;                    // 0=SOTDMA,1=ITDMA
    DWORD  dComStateSyncState;                   // SOTDMA/ITDMA
    DWORD  dComStateTimeOut;                     // SOTDMA
    DWORD  dComStateSubMsg;                      // SOTDMA

    DWORD  dComStateSlotInc;                     // ITDMA
    DWORD  dComStateNoSlots;                     // ITDMA
    DWORD  dComStateKeepFlag;                    // ITDMA
}xAISMSG09;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dDestID;
    DWORD  dSpareY;
}xAISMSG10;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dDestID;
    DWORD  dSpareY;
}xAISMSG11;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSqncNO;
    DWORD  dDestID;
    DWORD  dRetransmit;
    DWORD  dSpareX;
    DWORD  dDataSize;
    UCHAR  pDataBuff[156 + 4];                   // max 156
}xAISMSG12;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;
    
    DWORD  dSpareX;
    DWORD  dDataSize;
    UCHAR  pDataBuff[156 + 4];                   // max 156
}xAISMSG14;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareW;
    DWORD  dDestID1;
    DWORD  dMsgID11;
    DWORD  dSltOF11;
    DWORD  dSpareX;
    DWORD  dMsgID12;
    DWORD  dSltOF12;

    DWORD  dSpareY;
    DWORD  dDestID2;
    DWORD  dMsgID21;
    DWORD  dSltOF21;
    DWORD  dSpareZ;
}xAISMSG15;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dDestIDA;
    DWORD  dOffsetA;
    DWORD  dIncrementA;

    DWORD  dDestIDB;
    DWORD  dOffsetB;
    DWORD  dIncrementB;
    DWORD  dSpareY;
}xAISMSG16;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD    dSpareX;
    POS_ALLH xAllPosX;
    DWORD    dSpareY;
    DWORD    dRtcmMsgType;                         // ITU-R M.823
    WORD     wZcount;
    BYTE     bHealth;
    DWORD    dDataSize;
    UCHAR    pDataBuff[92 + 4];                    // max 92
}xAISMSG17;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dSOG;
    DWORD  dPosAcc;
    POS_ALLF xFullPosX;
    DWORD  dCOG;
    DWORD  dHDG;
    DWORD  dTimeStamp;

    DWORD  dSpareY;
    DWORD  dUnitFlag;                            // 0=SOTDMA,1="CS" unit
    DWORD  dDispFlag;                            // 0=none,1=Equipped with display
    DWORD  dDscFlag;                             // 0=none,1=Equipped with DSC
    DWORD  dBandFlag;                            // 0=none,1=capable of whole marine band
    DWORD  dMsg22Flag;                           // 0=none,1=freq management via MSG22
    DWORD  dModeFlag;                            // 0=autonomous,1=assigned mode
    DWORD  dRAIM;

    DWORD  dComStateSelector;                    // 0=SOTDMA,1=ITDMA
    DWORD  dComStateSyncState;                   // SOTDMA/ITDMA
    DWORD  dComStateTimeOut;                     // SOTDMA
    DWORD  dComStateSubMsg;                      // SOTDMA

    DWORD  dComStateSlotInc;                     // ITDMA
    DWORD  dComStateNoSlots;                     // ITDMA
    DWORD  dComStateKeepFlag;                    // ITDMA
}xAISMSG18;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dSOG;
    DWORD  dPosAcc;
    POS_ALLF xFullPosX;
    DWORD  dCOG;
    DWORD  dHDG;
    DWORD  dTimeStamp;

    DWORD  dSpareY;
    char   vShipName[LEN_MAX_SHIP_NAME + 2]; // 2=null
    DWORD  dShipType;
    xANTPOS xAntPos;
    DWORD  dEPFS;
    DWORD  dRAIM;
    DWORD  dDTE;
    DWORD  dModeFlag;                            // 0=autonomous,1=assigned mode
    DWORD  dSpareZ;
}xAISMSG19;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dOffsetNo1;
    DWORD  dNoOfSlot1;
    DWORD  dTimeOut1;
    DWORD  dIncrement1;

    DWORD  dOffsetNo2;
    DWORD  dNoOfSlot2;
    DWORD  dTimeOut2;
    DWORD  dIncrement2;

    DWORD  dOffsetNo3;
    DWORD  dNoOfSlot3;
    DWORD  dTimeOut3;
    DWORD  dIncrement3;

    DWORD  dOffsetNo4;
    DWORD  dNoOfSlot4;
    DWORD  dTimeOut4;
    DWORD  dIncrement4;
    DWORD  dSpareY;
}xAISMSG20;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dAtonType;
    char   vAtonName[LEN_MAX_SHIP_NAME + 2]; // 2=null
    DWORD  dPosAcc;
    POS_ALLF xFullPosX;
    xANTPOS xAntPos;
    DWORD  dEPFS;
    DWORD  dTimeStamp;

    DWORD  dOffPosInd;
    DWORD  dAtonStatus;
    DWORD  dRAIM;
    DWORD  dVirtualAton;
    DWORD  dModeFlag;                            // 0=autonomous,1=assigned mode
    DWORD  dSpareX;

    char   vExtnName[14 + 2];                    // 2=null
    DWORD  dSpareY;
}xAISMSG21;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    DWORD  dChannelA;
    DWORD  dChannelB;
    DWORD  dTxRxMode;
    DWORD  dPower;                               // 0=high,1=low

    POS_ALLH xAllPos1;
    POS_ALLH xAllPos2;
    DWORD  dDestID1;
    DWORD  dDestID2;

    DWORD  dBroadOrAddr;                         // 0=broadcast,1=addressed
    DWORD  dBandwidthA;                          // 0=25KHz,1=12.5KHz
    DWORD  dBandwidthB;                          // 0=25KHz,1=12.5KHz
    DWORD  dTrZoneSize;

    DWORD  dSpareY;
}xAISMSG22;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dSpareX;
    POS_ALLH xAllPos1;
    POS_ALLH xAllPos2;

    DWORD  dStnType;
    DWORD  dShipType;

    DWORD  dSpareY;
    DWORD  dTxRxMode;
    DWORD  dRptInterval;
    DWORD  dQuietTime;

    DWORD  dSpareZ;
}xAISMSG23;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dPartNumber;
    char   vShipName[LEN_MAX_SHIP_NAME + 2]; // 2=null , PART-A

    DWORD  dShipType;                             //          PART-B
    char   vVendorID[LEN_MAX_VENDORID + 1]; // 1=null , PART-B
    char   vCallSign[LEN_MAX_CALLSIGN + 1]; // 1=null , PART-B
    xANTPOS xAntPos;                              //          PART-B
    DWORD  dEPFS;                                 //          PART-B

    DWORD  dSpareX;                               //          PART-B
}xAISMSG24;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dDestInd;                             // 0=Broad/1=Addressed
    DWORD  dBinFlag;                             // 0=unstructured,1=binary data
    DWORD  dDestID;
    DWORD  dSpareX;
    DWORD  dDataSize;
    UCHAR  pDataBuff[16 + 4];                    // max 16
}xAISMSG25;
//========================================================================
typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dDestInd;                             // 0=Broad/1=Addressed
    DWORD  dBinFlag;                             // 0=unstructured,1=binary data
    DWORD  dDestID;
    DWORD  dSpareX;

    DWORD  dDataSize;
    UCHAR  pDataBuff[125 + 5];                   // max 125

    DWORD  dComStateSelector;                    // 0=SOTDMA,1=ITDMA
    DWORD  dComStateSyncState;                   // SOTDMA/ITDMA
    DWORD  dComStateTimeOut;                     // SOTDMA
    DWORD  dComStateSubMsg;                      // SOTDMA

    DWORD  dComStateSlotInc;                     // ITDMA
    DWORD  dComStateNoSlots;                     // ITDMA
    DWORD  dComStateKeepFlag;                    // ITDMA
}xAISMSG26;

typedef  struct
{
    xAISMSG00 xRxCOM;

    DWORD  dPosAcc;
    DWORD  dRAIM;
    DWORD  dNavStatus;
    
    POS_ALLH xFullPosX;
    DWORD  dSOG;
    DWORD  dCOG;
    DWORD  dPosLatency;
    DWORD  dSpareZ;
}xAISMSG27;

typedef  struct
{
    xAISMSG00 xRxCOM;
    DWORD  dDataSize;
    UCHAR  pDataBuff[156 + 4];                   // max 156
} xAISMSG_RESERVED;
//========================================================================
typedef  union
{
    xAISMSG00  xMsg00;
    xAISMSG01  xMsg01;
    xAISMSG04  xMsg04;  // same with Msg11
    xAISMSG05  xMsg05;
    xAISMSG06  xMsg06;
    xAISMSG07  xMsg07;
    xAISMSG08  xMsg08;
    xAISMSG09  xMsg09;
    xAISMSG10  xMsg10;
    xAISMSG12  xMsg12;
    xAISMSG14  xMsg14;
    xAISMSG15  xMsg15;
    xAISMSG16  xMsg16;
    xAISMSG17  xMsg17;
    xAISMSG18  xMsg18;
    xAISMSG19  xMsg19;
    xAISMSG20  xMsg20;
    xAISMSG21  xMsg21;
    xAISMSG22  xMsg22;
    xAISMSG23  xMsg23;
    xAISMSG24  xMsg24;
    xAISMSG25  xMsg25;
    xAISMSG26  xMsg26;
    xAISMSG27  xMsg27;
    xAISMSG_RESERVED  xMsgReserved;
}xAISMSG99;

#ifdef  __cplusplus
}
#endif

#endif

