/**
 * @file    Vbw.cpp
 * @brief   Vbw class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Vbw.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"
#include "Ship.h"

/******************************************************************************
 * 
 * VBW - 
 *
 * $--VBW,x.x,x.x,A,x.x,x.x,A,x.x,A,x.x,A*hh<CR><LF>
 *         |   |  |  |   |  |  |  |  |  |
 *         1   2  3  4   5  6  7  8  9  10
 *
 * 1. Longitudinal water speed
 * 2. Transverse water speed
 * 3. Status: water speed, A = data valid, V = data invalid
 * 4. Longitudinal ground speed
 * 5. Transverse ground speed
 * 6. Status: ground speed, A = data valid, V = data invalid
 * 7. <PERSON> transverse water speed
 * 8. Status: stern water speed, A = data valid, V = data invalid
 * 9. <PERSON> transverse ground speed
 * 10. Status: stern ground speed, A = data valid, V = data inv
 * 
 ******************************************************************************/
CVbw::CVbw() : CSentence()
{
    ClearData();
}

void CVbw::ClearData(void)
{
    m_nSpdData = NMEA_SOG_NULL;
    m_nVelData = NMEA_SOG_NULL;
    m_nCrsData = NMEA_COG_NULL;
    m_dwCrsValidTick = 0;
    m_dwSpdValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CVbw::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    double rLonGroundSpeed;         // Longitudinal ground speed (see Note 1), knots
    double rTransGroundSpeed;       // Transverse ground speed (see Note 1), knots

#if 0
    GetFieldString(pszSentence, 1, pstrTmp);     // Longitudinal water speed
    GetFieldString(pszSentence, 2, pstrTmp);     // Transverse water speed
    GetFieldString(pszSentence, 3, pstrTmp);     // Status: water speed, A = data valid, V = data invalid
#endif

    GetFieldString(pszSentence, 4, pstrTmp);     // Longitudinal ground speed
    if (strlen(pstrTmp) > 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, FALSE))
        {
            return false;
        }

        rLonGroundSpeed = atof(pstrTmp);

        GetFieldString(pszSentence, 5, pstrTmp); // Transverse ground speed
        if (strlen(pstrTmp) > 0)
        {
            if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, FALSE))
            {
                return false;
            }

            rTransGroundSpeed = atof(pstrTmp);

            GetFieldString(pszSentence, 6, pstrTmp); // Status: ground speed, A = data valid, V = data invalid
            if(pstrTmp[0] == 'A')
            {
                int nSOG = sqrt(rLonGroundSpeed * rLonGroundSpeed + rTransGroundSpeed * rTransGroundSpeed) * NMEA_SCALE_SOG;
                m_nSpdData = nSOG;
                m_dwSpdValidTick = SysGetSystemTimer();

                int nHdgVal = cShip::getOwnShipInst()->xDynamicData.nHDG;

                if(nHdgVal != NMEA_HDG_NULL)
                {
                    double rTheta = 0;
                    double rTheta1 = 0;
                    if(rTransGroundSpeed != 0)
                    {
                        rTheta = RadianToDegree(atan2(rLonGroundSpeed, rTransGroundSpeed));
                        rTheta1 = 90 - rTheta;
                    }
                    int nNewCOG = (TO_DEG360(rTheta1 + (double)nHdgVal / NMEA_SCALE_HDT)) * NMEA_SCALE_COG;

                    m_nCrsData = nNewCOG;
                    m_dwCrsValidTick = SysGetSystemTimer();
                }
            }
        }
    }

    return true;
}

/**
 * @brief Check received course data is valid or not
 */
bool CVbw::IsValidCourseData(void)
{
    return (m_dwCrsValidTick && SysGetDiffTimeMili(m_dwCrsValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Check received speed data is valid or not
 */
bool CVbw::IsValidSpeedData(void)
{
    return (m_dwSpdValidTick && SysGetDiffTimeMili(m_dwSpdValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Get received course data
 */
int CVbw::GetCourse(void)
{
    return m_nCrsData;
}

/**
 * @brief Get received speed data
 */
int CVbw::GetSpeed(void)
{
    return m_nSpdData;
}

/**
 * @brief Get received speed data
 */
int CVbw::GetVelocity(void)
{
    return m_nVelData;
}

/**
 * @brief Call function periodically
 */
void CVbw::RunPeriodically(void)
{
    if (m_dwCrsValidTick && SysGetDiffTimeMili(m_dwCrsValidTick) < NMEA_COG_LASTDATA_STAYMS)
    {
        m_dwCrsValidTick = 0;
        m_nCrsData = NMEA_COG_NULL;
    }

    if (m_dwSpdValidTick && SysGetDiffTimeMili(m_dwSpdValidTick) < NMEA_SOG_LASTDATA_STAYMS)
    {
        m_dwSpdValidTick = 0;
        m_nSpdData = NMEA_SOG_NULL;
    }
}
