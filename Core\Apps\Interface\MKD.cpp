#include <stdlib.h>
#include <string.h>
#include "main.h"
#include "SysConst.h"
#include "SysLib.h"
#include "Uart.h"
#include "Nmea.h"
#include "LayerNetwork.h"
#include "SetupMgr.h"
#include "LongRange.h"
#include "VdlTxMgr.h"
#include "AisMsg.h"
//#include "ChannelMgr.h"
#include "BuiltInTestMgr.h"
#include "TestModeMgr.h"
#include "SensorMgr.h"
#include "Timer.h"
#include "GnssInternal.h"
#include "RosMgr.h"
#include "FlashMem.h"
#include "LayerPhysical.h"
#include "RosMgr.h"
#include "AisModem.h"
#include "SensorMgr.h"
#include "AlarmThing.h"
#include "AisModem.h"
#include "Ship.h"
#include "ExtDisp.h"
#include "Pilot.h"
#include "Txt.h"
#include "MKD.h"

//-----------------------------------------------------------------------------
// Global variables
//-----------------------------------------------------------------------------
#define PROTOCOL_OVERHEAD    3

CMKD::CMKD(void) : CPI(HIGHSPD_PORTID_0, 0, new cUartSYS(UARTID_8, UART8, UART8_IRQn, 2048, 4096, 115200))
{
    m_dwRcvSecVFD = 0;
}

CMKD::~CMKD()
{
}

void CMKD::SendInitCmd()
{
    SendAllSetupData();
}

void CMKD::SendAllACAACStoPI()
{
    CPI::SendAllACAACStoPI();
    SendTXTtoPI(TXT_ID_ENDOF_ROSLIST);
}

void CMKD::SendPowerMode()
{
    char pstrSycBuff[32];

    sprintf((char*)pstrSycBuff, "$AIINL,PWR,%01d", CLayerPhysical::getInst()->GetTxPowerMode());
    CSentence::AddSentenceTail(pstrSycBuff);

    DEBUG_LOG("send PWR : %d\r\n", CLayerPhysical::getInst()->GetTxPowerMode());

    SendOutStr(pstrSycBuff);
}

void CMKD::ProcRespSendAllSetupData(char *pstrCmd)
{
    //------------------
    // $AIINL,LDR,
    //------------------

    char pstrSubData1[RX_MAX_DATA_SIZE];
    CSentence::GetFieldString(pstrCmd, 2, pstrSubData1, sizeof(pstrSubData1));
    CSetupMgr::getInst()->SetSetupLoadError(false);
}

void CMKD::SendRequestAllSetupData()
{
    char pstrSycBuff[32];

    strcpy((char*)pstrSycBuff, "$AIINL,LDR,FF");
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);

    RunSystemDelayMs(500);
}

void CMKD::SendOutNMEA(char *pstrCmd)
{
    if( (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableRMC && !strncmp((char*)&pstrCmd[3], "RMC", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableGSV && !strncmp((char*)&pstrCmd[3], "GSV", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableGLL && !strncmp((char*)&pstrCmd[3], "GLL", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableVTG && !strncmp((char*)&pstrCmd[3], "VTG", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableGBS && !strncmp((char*)&pstrCmd[3], "GBS", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableGGA && !strncmp((char*)&pstrCmd[3], "GGA", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableZDA && !strncmp((char*)&pstrCmd[3], "ZDA", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToMKD.bEnableGSA && !strncmp((char*)&pstrCmd[3], "GSA", 3)))
    {
        SendOutStr(pstrCmd);
    }

#ifdef __ENABLE_EXT_DISP__
    // enable/disable option to send NMEA to Ext.
    if( (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableRMC && !strncmp((char*)&pstrCmd[3], "RMC", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableGSV && !strncmp((char*)&pstrCmd[3], "GSV", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableGLL && !strncmp((char*)&pstrCmd[3], "GLL", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableVTG && !strncmp((char*)&pstrCmd[3], "VTG", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableGBS && !strncmp((char*)&pstrCmd[3], "GBS", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableGGA && !strncmp((char*)&pstrCmd[3], "GGA", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableZDA && !strncmp((char*)&pstrCmd[3], "ZDA", 3)) ||
        (CSetupMgr::getInst()->m_sNmeaToEXT.bEnableGSA && !strncmp((char*)&pstrCmd[3], "GSA", 3)))
    {
        CEXTDISP::getInst()->SendOutStr(pstrCmd);
    }
#endif
}

BYTE CMKD::GetDTEFlag()
{
    //----------------------------------------
    // 0 = DTE(Data Terminal Equipment) ready
    // 1 = DTE not available
    //----------------------------------------
    //-----------------------------------------------------------------
    // refer to IEC-61993-2 ********.2
    // refer to IEC-61993-2 6.11.1 Minimum keyboard and display (MKD)
    // IEC-61993-2 ******** (Ivan test report p.206)
    //---------------------------------------------------------------
    return CheckTimeoutHBT();
}

BOOL CMKD::CheckDTEFlag()
{
    //-----------------------------------------------------------------
    // refer to IEC-61993-2 ********.2
    //-----------------------------------------------------------------
    static BOOL bOldDTE = FALSE;
    BOOL bCurDTE = GetDTEFlag();

    if(bCurDTE != bOldDTE)
    {
        CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();

        bOldDTE = bCurDTE;
        return TRUE;
    }
    return FALSE;
}

void CMKD::ProcAliveMsgVFD(void)
{
    //-----------------------------------------------
    // alive message from MKD with 1 second interval
    //-----------------------------------------------
    m_dwRcvSecVFD = cTimerSys::getInst()->GetCurTimerSec();
}

void CMKD::ProcChangeMMSI(char *pstrSentence)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];
    UINT  uData = 0;

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    uData = atoi((char*)pstrSubData1);

    if(cShip::getOwnShipInst()->GetOwnShipMMSI() != uData)
    {
        if(CAisLib::IsValidMMSI(uData))
        {
            CLayerNetwork::getInst()->ChangeMMSI(uData);
            CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();
            CLayerNetwork::getInst()->SetOpPhase(OPPHASE_MONITOR_VDL);
            CSetupMgr::getInst()->ReserveToSaveSysConfigData();
        }
    }
}

void CMKD::ProcChangeIMO(char *pstrSentence)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];
    UINT  uData = 0;

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    uData = atoi((char*)pstrSubData1);

    if(CAisLib::IsValidImo(uData))
    {
        if(cShip::getOwnShipInst()->GetOwnShipIMO() != uData)
        {
            cShip::getOwnShipInst()->SetOwnShipIMO(uData);
            CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();
            CSetupMgr::getInst()->ReserveToSaveSysConfigData();
        }
    }
}

void CMKD::SendFirmwareVersion()
{
    //-----------------------
    // Send version info
    //-----------------------
    char pstrVersion[RX_MAX_DATA_SIZE];

    sprintf(pstrVersion, "$AIINL,VER,M,%s,%s", STR_VERSION_SW, STR_RELEASE_DATE);
    CSentence::AddSentenceTail(pstrVersion);
    SendOutStr(pstrVersion);
}

void CMKD::SendDataTxSWR()
{
    char pstrSycBuff[32];

    sprintf((char*)pstrSycBuff, "$AIINL,SWR,%d,%d", 
            CLayerPhysical::getInst()->GetFwdPowerAdValue(), 
            CLayerPhysical::getInst()->GetRefPowerAdValue());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

void CMKD::SendSetupNMEA()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,SGD,%04X", CSetupMgr::getInst()->GetSetupNmeaOutToMKD());
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

// Enable/Disable option to send NMEA to Ext.
void CMKD::SendSetupNMEAforEXT()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,SGE,%04X", CSetupMgr::getInst()->GetSetupNmeaOutToEXT());
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

void CMKD::ProcessACA(char *pstrCmd)
{
    //----------------------------------------------------------------------
    // ACA : AIS channel assignment message
    // ex) $SxACA,....   (x : index number)
    // MKD 에서 ACA 를 보낼때 항상 index 를 8 로 하도록 수정함
    //----------------------------------------------------------------------
    char pstrSubData1[30], pstrSubData2[10];
    xROSDATA xRosData;
    xROADATA xRoaData;
    int nRosIndex;

    CROSMgr::getInst()->ClearOneRosData(&xRosData, &xRoaData);

    CSentence::GetFieldString(pstrCmd, 0, pstrSubData1, sizeof(pstrSubData1));
    pstrSubData2[0] = pstrSubData1[2];
    pstrSubData2[1] = '\0';
    nRosIndex = atoi(pstrSubData2);

    CROSMgr::getInst()->ClearOneRosData(&xRosData, &xRoaData);

    CSentence::GetFieldString(pstrCmd, 17, pstrSubData1, sizeof(pstrSubData1));

    xRosData.bRosSource = CAisLib::GetROSSourceCharToInfo(pstrSubData1[0]);

    xRosData.dSrcMMSI = AIS_AB_MMSI_NULL;

    // NE: Latitude (not null parameter?) - When Command is Address? Default ROS(null ?)
    CSentence::GetFieldString(pstrCmd, 2, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pstrCmd, 3, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        DEBUG_LOG("ACA-in-MKD] ignore, NE latitude invalid\r\n");
    }
    xRosData.xPointNE.xPosF.fLAT = CAisLib::GetLatitude(pstrSubData1, pstrSubData2[0]);

    // NE: Longitude (not null parameter?)
    CSentence::GetFieldString(pstrCmd, 4, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pstrCmd, 5, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        DEBUG_LOG("ACA-in-MKD] ignore, NE longitude invalid\r\n");
    }
    xRosData.xPointNE.xPosF.fLON = CAisLib::GetLongitude(pstrSubData1, pstrSubData2[0]);

    CAisLib::CalcGridLowPosByFLOAT(&xRosData.xPointNE);

    if(!CAisLib::IsValidAisGridPOS(&xRosData.xPointNE.xPosG))
    {
        DEBUG_LOG("ACA-in-MKD] ignore, NE pos invalid, f : %.4f,%.4f -> g : %d,%d\r\n",
            xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT, xRosData.xPointNE.xPosG.nLON, xRosData.xPointNE.xPosG.nLAT);
    }

    // SW: Latitude (not null parameter?)
    CSentence::GetFieldString(pstrCmd, 6, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pstrCmd, 7, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        DEBUG_LOG("ACA-in-MKD] ignore, SW latitude invalid\r\n");
    }
    xRosData.xPointSW.xPosF.fLAT = CAisLib::GetLatitude(pstrSubData1, pstrSubData2[0]);

    // SW: Longitude (not null parameter?)
    CSentence::GetFieldString(pstrCmd, 8, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pstrCmd, 9, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        DEBUG_LOG((char*)"ACA-in-MKD] ignore, SW longitude invalid\r\n");
    }
    xRosData.xPointSW.xPosF.fLON = CAisLib::GetLongitude(pstrSubData1, pstrSubData2[0]);

    CAisLib::CalcGridLowPosByFLOAT(&xRosData.xPointSW);
    if(!CAisLib::IsValidAisGridPOS(&xRosData.xPointSW.xPosG))
    {
        DEBUG_LOG("ACA-in-MKD] ignore, SW pos invalid, f : %.4f,%.4f -> g : %d,%d\r\n",
            xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT, xRosData.xPointSW.xPosG.nLON, xRosData.xPointSW.xPosG.nLAT);
    }

    // Transition Zone Size(1~8nm)
    CSentence::GetFieldString(pstrCmd, 10, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, TRzone size invalid\r\n");
        return;
    }

    xRosData.bTrZoneSize = atoi(pstrSubData1);
    if(xRosData.bTrZoneSize < TRZONE_SIZE_MIN || xRosData.bTrZoneSize > TRZONE_SIZE_MAX)
    {
        WARNING_LOG("ACA-in-MKD] ignore, wrong TRzone size, %d\r\n", xRosData.bTrZoneSize);
        return;
    }

    CROSMgr::getInst()->CalcOneRoaDataByROS(&xRosData, &xRoaData);

    // Channel, Bandwidth
    CSentence::GetFieldString(pstrCmd, 11, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, CH-A invalid\r\n");
        return;
    }
    xRosData.wChannelNoA = atoi(pstrSubData1);

    CSentence::GetFieldString(pstrCmd, 12, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, bandwidth-A invalid\r\n");
        return;
    }
    xRosData.bBandwidthA = atoi(pstrSubData1);

    CSentence::GetFieldString(pstrCmd, 13, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, CH-B invalid\r\n");
        return;
    }
    xRosData.wChannelNoB = atoi(pstrSubData1);

    CSentence::GetFieldString(pstrCmd, 14, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, bandwidth-B invalid\r\n");
        return;
    }
    xRosData.bBandwidthB = atoi(pstrSubData1);

    if(xRosData.bBandwidthA != CH_BW_25KHZ || xRosData.bBandwidthB != CH_BW_25KHZ)
    {
        DEBUG_LOG("ACA-in-MKD] bandwidth pstrCmd invalid but continue, %d, %d\r\n", xRosData.bBandwidthA, xRosData.bBandwidthB);

        //bandwidth value should always be set to 25kHz. But the sentence should not be ignored!
        xRosData.bBandwidthA = CH_BW_25KHZ;
        xRosData.bBandwidthB = CH_BW_25KHZ;
    }

    // Tx/Rx mode control
    CSentence::GetFieldString(pstrCmd, 15, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, TRX mode invalid\r\n");
        return;
    }
    xRosData.bTxRxMode = atoi(pstrSubData1);

    if(xRosData.bTxRxMode < TRXMODE_MIN || xRosData.bTxRxMode > TRXMODE_MAX)
    {
        WARNING_LOG("ACA-in-MKD] ignore, TRX mode pstrCmd, %d\r\n", xRosData.bTxRxMode);
        return;
    }

    // Power level control
    CSentence::GetFieldString(pstrCmd, 16, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in-MKD] ignore, power invalid\r\n");
        return;
    }
    xRosData.bTxPower = atoi(pstrSubData1);

    if(xRosData.bTxPower > AIS_TX_POWER_MAXVALUE)
    {
        WARNING_LOG("ACA-in-MKD] ignore, power pstrCmd, %d\r\n", xRosData.bTxPower);
        return;
    }

    if(CROSMgr::getInst()->IsRosIndexValid_Ext(nRosIndex))
    {
        if(CROSMgr::getInst()->IsRosDataDefaultSetting(&xRosData))
        {
            DEBUG_LOG("ACA-in-MKD] default setting, erase, #%d, s:%d\r\n", nRosIndex, cTimerSys::getInst()->GetCurTimerSec());

            CROSMgr::getInst()->ClearOneRosData(nRosIndex);
            SendAllACAACStoPI();
            CSetupMgr::getInst()->ReserveToSaveRosConfigData();
            return;
        }
    }

    // Set Current UTC.
    xRosData.bValidMode = MODE_VAL_ON;
    xRosData.xRcvTime   = cShip::getOwnShipInst()->xSysTime;
    xRosData.dwRcvSysSec= cTimerSys::getInst()->GetCurTimerSec();

    DEBUG_LOG("ACA-in-MKD] idx:%d(%d), from:%d, %09d, NE:%.2f,%.2f(%d,%d) SW:%.2f,%.2f(%d,%d), pos:%.2f,%.2f, ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d, %d-%d-%d,%02d:%02d:%02d\r\n",
        nRosIndex, CROSMgr::getInst()->IsRosInUse(nRosIndex),
        xRosData.bRosSource, xRosData.dSrcMMSI,
        xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT, xRosData.xPointNE.xPosL.nLON, xRosData.xPointNE.xPosL.nLAT,
        xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT, xRosData.xPointSW.xPosL.nLON, xRosData.xPointSW.xPosL.nLAT,
        cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
        xRosData.wChannelNoA, xRosData.wChannelNoB,
        xRosData.bTxRxMode, xRosData.bTxPower, xRosData.bTrZoneSize,
        xRosData.xRcvTime.xDate.nYear, xRosData.xRcvTime.xDate.nMon, xRosData.xRcvTime.xDate.nDay,
        xRosData.xRcvTime.xTime.nHour, xRosData.xRcvTime.xTime.nMin, xRosData.xRcvTime.xTime.nSec);

    CROSMgr::getInst()->UpdateRosData(&xRosData, nRosIndex);
}

void CMKD::ProcSetupUartPortMon(char *pstrSentence)
{
    //------------------------------------------------------------------------------------------------
    // $AIINL,MON,x,y
    // x (%c): 'E'-External display port, 'L'-LongRange port, 'P'-Pilot port
    // y (%d): %d: 1= 해당명령 수신시 sub-cpu 는 각 해당 센서의 입력을 그대로 MKD 로 전달한다.
    //             0= 전달을 멈춘다.
    //------------------------------------------------------------------------------------------------

    char pstrSubData1[RX_MAX_DATA_SIZE];
    char pstrSubData2[RX_MAX_DATA_SIZE];

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    char chPortID = pstrSubData1[0];

    CSentence::GetFieldString(pstrSentence, 3, pstrSubData2, sizeof(pstrSubData2));
    BOOL bEnable = (pstrSubData2[0] == '1');

    switch(chPortID)
    {
#ifdef __ENABLE_PILOT__
    case MON_PORTID_CH_PILOT:
        CPILOT::getInst()->EnableSendRcvDataToMKD(bEnable);
        break;
#endif
#ifdef __ENABLE_EXT_DISP__
    case MON_PORTID_CH_EXTDISP:
        CEXTDISP::getInst()->EnableSendRcvDataToMKD(bEnable);
        break;
#endif
    case MON_PORTID_CH_LR:
        CLongRange::getInst()->EnableSendRcvDataToMKD(bEnable);
        break;
    }
}

void CMKD::ProcUartPortTxTest(char *pstrSentence)
{
    //------------------------------------------------------------------------------------------------
    // $AIINL,TST,x,SYTEST
    // x (%c): 'E'-External display port, 'L'-LongRange port, 'P'-Pilot port
    //------------------------------------------------------------------------------------------------

    char pstrSubData1[RX_MAX_DATA_SIZE];
    char pstrSubData2[RX_MAX_DATA_SIZE];

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    char chPortID = pstrSubData1[0];

    switch(chPortID)
    {
#ifdef __ENABLE_PILOT__
    case MON_PORTID_CH_PILOT:
        CPILOT::getInst()->SendOutStr(pstrSentence);
        break;
#endif
#ifdef __ENABLE_EXT_DISP__
    case MON_PORTID_CH_EXTDISP:
        CEXTDISP::getInst()->SendOutStr(pstrSentence);
        break;
#endif
    case MON_PORTID_CH_LR:
        CLongRange::getInst()->SendOutStr(pstrSentence);
        break;
    }
}

/**
 * @brief Process the Software upgrade command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcessSUG(char *pstrCmd)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];
    CSentence::GetFieldString(pstrCmd, 2, pstrSubData1, sizeof(pstrSubData1));

    //if(pstrSubData1[0] == 'U')
    //{
    //    PrepFirmwareUpgrade();
    //}
}

/**
 * @brief Process the AIQ command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcessAIQ(char *pstrCmd)
{
    //---------------------------------------------------------
    // AIQ : Query of the other messages from AIS
    // $--AIQ,xxx
    // xxx : queried sentence like ACA, SSD, VSD, TRL, VER, TXT
    //---------------------------------------------------------

    char pstrSubID[8];
    CSentence::GetFieldString(pstrCmd, 1, pstrSubID, sizeof(pstrSubID));

    if(!strncmp((char*)pstrSubID, "VER", 3))
    {
        SendFirmwareVersion();
        return;
    }
    if(!strncmp((char*)pstrSubID, "ACA", 3))
    {
        SendAllACAACStoPI();
        return;
    }
    else if(!strncmp((char*)pstrSubID, "BAU", 3))
    {
        SendSetupUartPortSpeed();
    }
    if(!strncmp((char*)pstrSubID, "SWR", 3))
    {
        SendDataTxSWR();
        return;
    }
    CPI::ProcessAIQ(pstrCmd);
}

/**
 * @brief Process the Intellian command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcessINL(char *pstrCmd)
{
    char pstrSubID[8];
    CSentence::GetFieldString(pstrCmd, 1, pstrSubID, sizeof(pstrSubID));

    // Request time infomation to PI(MKD)
    if(!strncmp(pstrSubID, "002", 3))   {SendSysDateTime();                         return;}
    if(!strncmp(pstrSubID, "106", 3))   {ProcChangeMMSI(pstrCmd);                   return;}
    if(!strncmp(pstrSubID, "107", 3))   {ProcChangeMMSI(pstrCmd);                   return;}

    // Enable/Disable LoopBack Command
    if(!strncmp(pstrSubID, "LBP", 3))   {ProcRunComLoopBackCmd(pstrCmd);            return;}

    if(!strncmp(pstrSubID, "LDR", 3))   {ProcRespSendAllSetupData(pstrCmd);         return;}

    if(!strncmp(pstrSubID, "MON", 3))   {ProcSetupUartPortMon(pstrCmd);             return;}
    if(!strncmp(pstrSubID, "TTT", 3))   {ProcUartPortTxTest(pstrCmd);               return;}

    if(!strncmp(pstrSubID, "VFD", 3))   {ProcAliveMsgVFD();                         return;}

    if(!strncmp(pstrSubID, "AIQ", 3))   {ProcessINLAIQ(pstrCmd);                    return;}

    if(!strncmp(pstrSubID, "BAU", 3))   {ProcSetupUartSpeed(pstrCmd);               return;}

    // LR Config 명령를 수신
    if(!strncmp(pstrSubID, "CLR", 3))   {ProcSetupLongRange(pstrCmd);               return;}

    if(!strncmp(pstrSubID, "LR1", 3))   {ProcSetupLongRangeCH1(pstrCmd);            return;}
    if(!strncmp(pstrSubID, "LR2", 3))   {ProcSetupLongRangeCH2(pstrCmd);            return;}

    // Addr Msg Retry 회수 변경
    if(!strncmp(pstrSubID, "TXR", 3))   {ProcSetupAddrMsgTxRetryCnt(pstrCmd);       return;}

    // Reset AIS MKD Backup Data
    if(!strncmp(pstrSubID, "AMI", 3))   {ProcRunFactoryReset(pstrCmd);              return;}
    // EPFS Config
    if(!strncmp(pstrSubID, "EPS", 3))   {ProcSetupEPFS(pstrCmd);                    return;}

    if(!strncmp(pstrSubID, "SGD", 3))   {ProcSetupEnableNMEA(pstrCmd);              return;}
    // Enable/Disable option to send NMEA to Ext.
    if(!strncmp(pstrSubID, "SGE", 3))   {ProcSetupEnableNMEAforEXT(pstrCmd);        return;}
    // Enable/Disable Message 17 for DGNSS
    if(!strncmp(pstrSubID, "IDM", 3))   {ProcSetupEnableDgnssFromMsg17(pstrCmd);    return;}

    if(!strncmp(pstrSubID, "SBA", 3))   {ProcSetupEnableSBAS(pstrCmd);              return;}

    if(!strncmp(pstrSubID, "SIL", 3))   {ProcSetupEnableSilentMode(pstrCmd);        return;}

    if(!strncmp(pstrSubID, "EEPS", 3))  {ProcSetupEnableExtEPFS(pstrCmd);           return;}
    if(!strncmp(pstrSubID, "EHDG", 3))  {ProcSetupEnableHeading(pstrCmd);           return;}
    if(!strncmp(pstrSubID, "EROT", 3))  {ProcSetupEnableROT(pstrCmd);               return;}
    if(!strncmp(pstrSubID, "EALR14", 6)){ProcSetupEnableALR14(pstrCmd);             return;}
    if(!strncmp(pstrSubID, "SSA", 3))   {ProcSetupUserKeySSA(pstrCmd);              return;}
    if(!strncmp(pstrSubID, "EDI", 3))   {ProcSetupExtDimInt(pstrCmd);               return;}
    if(!strncmp(pstrSubID, "PLA", 3))   {ProcSetupPilotPortAccessLevel(pstrCmd);    return;}

    CPI::ProcessINL(pstrCmd);
}

/**
 * @brief Process the intellian AIQ command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcessINLAIQ(char *pstrSentence)
{
    //-------------------
    // $AIINL,AIQ,XXX,
    // ex) $AIINL,AIQ,BAU
    //-------------------
    char pstrSubData1[RX_MAX_DATA_SIZE];
    char pstrSubData2[RX_MAX_DATA_SIZE];

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));

    if(!strncmp((char*)pstrSubData1, "MON", 3))
    {
        // $AIINL,AIQ,MON,%c
        CSentence::GetFieldString(pstrSentence, 3, pstrSubData2, sizeof(pstrSubData2));

#ifdef __ENABLE_EXT_DISP__
        if(pstrSubData1[0] == 'E')
            CEXTDISP::getInst()->SendSetupUartPortMon();
        else
#endif
        if(pstrSubData1[0] == 'L')
            CLongRange::getInst()->SendSetupUartPortMon();
        return;
    }

    if(!strncmp((char*)pstrSubData1, "SBA", 3))
    {
        SendSetupEnableSBAS();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "EPS", 3))
    {
        SendSetupEPFS();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "EEPS", 4))
    {
        SendSetupEnableExtEPFS();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "EHDG", 4))
    {
        SendSetupEnableHeading();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "EROT", 4))
    {
        SendSetupEnableROT();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "EALR14", 6))
    {
        SendSetupEnableALR14();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "SSA", 3))
    {
        SendSetupUserKeySSA();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "EDI", 3))
    {
        SendSetupExtDimInt();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "PLA", 3))
    {
        SendSetupPilotPortAccessLevel();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "IDM", 3))
    {
        SendSetupEnableDgnssFromMsg17();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "SSD", 3))            // MKD로 SSD sentence 전송
    {
        SendSSDToPI(STR_TALKER_AI);
        return;
    }
    if(!strncmp((char*)pstrSubData1, "VSD", 3))
    {
        SendVSDToPI(STR_TALKER_AI);
        return;
    }
    if(!strncmp((char*)pstrSubData1, "VER", 3))
    {
        SendFirmwareVersion();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "PWR", 3))
    {
        SendPowerMode();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "ALR", 3))
    {
        CSentence::GetFieldString(pstrSentence, 3, pstrSubData1, sizeof(pstrSubData1));
        CSentence::GetFieldString(pstrSentence, 4, pstrSubData2, sizeof(pstrSubData2));

        int nAlrStartID = -1;
        int nAlrEndID = -1;
        if(strlen(pstrSubData1) > 0)
        {
            nAlrStartID = atoi(pstrSubData1);
            if(nAlrStartID == 90 || nAlrStartID == 119)
            {
//                 if(MKD_CONNECT == src_pos_data)
//                     SendDataToNTPort((uchar *)pstrSentence); /* bypass to NT */
            }
            if(nAlrStartID > 35)
                nAlrStartID = 0;
        }
        if(strlen(pstrSubData2) > 0)
        {
            nAlrEndID = atoi(pstrSubData2);
            nAlrEndID = MIN(nAlrEndID, 35);
        }
        if(nAlrStartID >= 0)
        {
            if(nAlrEndID < 0)
            {
                nAlrEndID = nAlrStartID;
            }
            SendINLALRToPI(nAlrStartID, nAlrEndID);
        }
        return;
    }

    if(!strncmp((char*)pstrSubData1, "BAU", 3))    // "$DUAIQ,SER*20\r\n"을 수신하고, NT로 Bypass 및 src_pos_data로 응답.
    {
        SendSetupUartPortSpeed();
        return;
    }
    if(!strncmp((char*)pstrSubData1, "SGD", 3))
    {
        SendSetupNMEA();
        return;
    }
    // Added enable/disable option to send NMEA to Ext.
    if(!strncmp((char*)pstrSubData1, "SGE", 3))
    {
        SendSetupNMEA();
        return;
    }

    CPI::ProcessINLAIQ(pstrSentence);
}

/**
 * @brief Process the intellian command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupUartSpeed(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // Ex) $AIINL,BAU,%04X*hh
    // %04X 의 각 2비트는 00:4800, 01:38400, 02:auto 을 표시
    // bit [1..0] : SENSOR 1
    // bit [3..2] : SENSOR 2
    // bit [5..4] : SENSOR 3
    // bit [7..6] : Long range
    // bit [9..8] : External
    //----------------------------------------------------------------------------------------
    char pstrBaudData[RX_MAX_DATA_SIZE];
    UINT uBaudrate;

    CSentence::GetFieldString(pstrSentence, 2, pstrBaudData, sizeof(pstrBaudData));
    uBaudrate = CAisLib::HexaStrToDecimal((char*)pstrBaudData, 4);

    if(PORT_BAUDMASK_MIN <= uBaudrate && uBaudrate <= PORT_BAUDMASK_MAX)
    {
        if(uBaudrate != CSetupMgr::getInst()->GetPortSpeedBitmap())
        {
            CSetupMgr::getInst()->SetPortSpeedBitmap(uBaudrate);
            SetAllUartBaudRate();
        }
    }
}

/**
 * @brief Send the UART port speed to the PI
 * @return None
 */
void CMKD::SendSetupUartPortSpeed()
{
    //--------------------------------------
    // 각 포트 속도 설정값을 MKD로 전송
    //--------------------------------------

    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,BAU,%04X", CSetupMgr::getInst()->GetPortSpeedBitmap());
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Process the long range command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupLongRange(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // 현재 설정되어있는 Long Range Info를 전송.
    // $AIINL,CLR,x,y,z
    //
    // x : %d: 1= 자동 응답(Auto Response), 0= 수동 응답(Manual Response)
    // y : %d: 1= Enable Broadcast,        0= Disable Broadcast
    // z : %08x 기존 프로토콜 유지,
    //----------------------------------------------------------------------------------------

    char pstrSubData[RX_MAX_DATA_SIZE];
    int     nTmp;
    UINT uLongRangeCfg;

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData, sizeof(pstrSubData));
    nTmp = atoi(pstrSubData);
    if(CSetupMgr::getInst()->CheckSetupValueOnOff(nTmp) && CSetupMgr::getInst()->GetLongRangeAutoReply() != nTmp)
    {
        CSetupMgr::getInst()->SetLongRangeAutoReply(nTmp);
    }

    CSentence::GetFieldString(pstrSentence, 3, pstrSubData, sizeof(pstrSubData));
    nTmp = atoi(pstrSubData);
    if(CSetupMgr::getInst()->CheckSetupValueOnOff(nTmp) && CSetupMgr::getInst()->GetLongRangeTxEnableMsg27() != nTmp)
    {
        CSetupMgr::getInst()->SetLongRangeTxEnableMsg27(nTmp);
    }

    CSentence::GetFieldString(pstrSentence, 4, pstrSubData, sizeof(pstrSubData));
    uLongRangeCfg = CAisLib::HexaStrToDecimal((char*)pstrSubData, 8);

    if(CSetupMgr::getInst()->GetLongRangeCfg() != uLongRangeCfg)
    {
        CSetupMgr::getInst()->SetLongRangeCfg(uLongRangeCfg);
        CLongRange::getInst()->SetLongRangeConfig(CSetupMgr::getInst()->GetLongRangeAutoReply(), CSetupMgr::getInst()->GetLongRangeCfg());
    }
}

/**
 * @brief Send the long range configuration to the PI
 * @return None
 */
void CMKD::SendSetupLongRange()
{
    //----------------------------------------------------------------------------------------
    // 현재 설정되어있는 Long Range Info를 전송.
    // $AIINL,CLR,x,y,z
    //
    // x : %d: 1= 자동 응답(Auto Response), 0= 수동 응답(Manual Response)
    // y : %d: 1= Enable Broadcast,        0= Disable Broadcase
    // z : %08x 기존 프로토콜 유지,
    //----------------------------------------------------------------------------------------

#ifdef __ENABLE_LONGRANGE__
    char pstrSycBuff[64];
    sprintf((char*)pstrSycBuff, "$AIINL,CLR,%d,%d,%08x",
            CSetupMgr::getInst()->GetLongRangeAutoReply(), CSetupMgr::getInst()->GetLongRangeTxEnableMsg27(), CSetupMgr::getInst()->GetLongRangeCfg());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
#endif
}

/**
 * @brief Process the long range CH1 command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupLongRangeCH1(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // 현재 설정되어있는 Long Range Info를 전송.
    // $AIINL,LR1,x
    //
    // x : %d: Long range CH1 number
    //----------------------------------------------------------------------------------------

    char pstrLrData[RX_MAX_DATA_SIZE];
    int  nTmp;

    CSentence::GetFieldString(pstrSentence, 2, pstrLrData, sizeof(pstrLrData));
    nTmp = atoi((char*)pstrLrData);

    if(nTmp == 0 || nTmp == AIS_DEFAULT_TXLR_CH_1)
    {
        if(CSetupMgr::getInst()->GetLongRangeCh1() != nTmp)
        {
            CSetupMgr::getInst()->ChangeLongRangeCH1(nTmp);
        }
    }
}

/**
 * @brief Send the long range CH1 configuration to the PI
 * @return None
 */
void CMKD::SendSetupLongRangeCH1()
{
    //----------------------------------------------------------------------------------------
    // 현재 설정되어있는 Long Range Info를 전송.
    // $AIINL,LR2,x
    //
    // x : %d: Long range CH-1 채널 번호, 0 또는 75
    //----------------------------------------------------------------------------------------

#ifdef __ENABLE_LONGRANGE__
    char pstrSycBuff[32];
    sprintf((char*)pstrSycBuff, "$AIINL,LR1,%d", CSetupMgr::getInst()->GetLongRangeCh1());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
#endif
}

/**
 * @brief Process the long range CH2 command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupLongRangeCH2(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // 현재 설정되어있는 Long Range Info를 전송.
    // $AIINL,LR2,x
    //
    // x : %d: Long range CH2 number
    //----------------------------------------------------------------------------------------

    char pstrLrData[RX_MAX_DATA_SIZE];
    int  nTmp;

    CSentence::GetFieldString(pstrSentence, 2, pstrLrData, sizeof(pstrLrData));
    nTmp = atoi((char*)pstrLrData);

    if(nTmp == 0 || nTmp == AIS_DEFAULT_TXLR_CH_2)
    {
        if(CSetupMgr::getInst()->GetLongRangeCh2() != nTmp)
        {
            CSetupMgr::getInst()->ChangeLongRangeCH1(nTmp);
        }
    }
}

/**
 * @brief Send the long range CH2 configuration to the PI
 * @return None
 */
void CMKD::SendSetupLongRangeCH2()
{
    //----------------------------------------------------------------------------------------
    // 현재 설정되어있는 Long Range Info를 전송.
    // $AIINL,LR2,x
    //
    // x : %d: Long range CH-2 채널 번호, 0 또는 76
    //----------------------------------------------------------------------------------------

#ifdef __ENABLE_LONGRANGE__
    char pstrSycBuff[32];
    sprintf((char*)pstrSycBuff, "$AIINL,LR2,%d", CSetupMgr::getInst()->GetLongRangeCh2());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
#endif
}

/**
 * @brief Process the address message retry count command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupAddrMsgTxRetryCnt(char *pstrSentence)
{
    char  pstrSubData1[RX_MAX_DATA_SIZE];
    BYTE  nTxRetryCnt;

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    nTxRetryCnt = atoi(pstrSubData1);

    if(nTxRetryCnt > ADDR_TX_RETRYCNT_DFLT)
        return;

    if(CSetupMgr::getInst()->GetAddrMsgTxRetryCnt() != nTxRetryCnt)
    {
        CSetupMgr::getInst()->SetAddrMsgRetransmitCount(nTxRetryCnt);
        SendAddrMsgRetransmitCount();
    }
}

/**
 * @brief Send the address message retry count to the PI
 * @return None
 */
void CMKD::SendAddrMsgRetransmitCount()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,TXR,%d", CSetupMgr::getInst()->GetAddrMsgTxRetryCnt());
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send the factory reset done notification to the PI
 * @return None
 */
void CMKD::SendNotifyFactoryResetDone()
{
    //----------------------------------------------
    // Notify to MKD that Factory reset is done.
    //----------------------------------------------
    char pstrBuff[RX_MAX_DATA_SIZE];
    strcpy(pstrBuff, (char*)"$AIINL,AMI,DONE");
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send all the setup data to the PI
 * @return None
 */
void CMKD::SendAllSetupData()
{
    // Send MMSI and IMO
    SendOwnShipMMSI();
    SendOwnShipIMO();

    // Send Static and Voyage Data
    SendSSDToPI(STR_TALKER_AI);
    SendVSDToPI(STR_TALKER_AI);

    SendSetupShowTestingSART();
    SendSetupEnableSilentMode();

    RunSystemDelayMs(1);

    SendSetupUartPortSpeed();
    SendSetupNMEA();

    RunSystemDelayMs(1);

    SendSetupEnableSBAS();
    SendSetupLongRange();
    SendSetupLongRangeCH1();
    SendSetupLongRangeCH2();
    SendSetupEPFS();

    RunSystemDelayMs(1);

    SendSetupEnableExtEPFS();
    SendSetupEnableHeading();
    SendSetupEnableROT();
    SendSetupEnableALR14();
    SendSetupUserKeySSA();
    SendSetupExtDimInt();
    SendSetupPilotPortAccessLevel();

    SendSetupEnableDgnssFromMsg17();
    SendPowerMode();
    SendFirmwareVersion();
    SendAddrMsgRetransmitCount();

    RunSystemDelayMs(1);

    SendAllACAACStoPI();
}

/**
 * @brief Process the factory reset command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcRunFactoryReset(char *pstrSentence)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    if(strcmp((char*)pstrSubData1, MASTER_PASSWORD) != 0)
        return;

    CSentence::GetFieldString(pstrSentence, 3, pstrSubData1, sizeof(pstrSubData1));

    if(pstrSubData1[0] != '2' && pstrSubData1[0] != '3')
    {
        return;
    }

    RunSystemDelayMs(100);

    switch(pstrSubData1[0])
    {
    case '2':
        CSetupMgr::getInst()->FactoryResetAll();
        break;
    case '3':
        CSetupMgr::getInst()->FactoryResetMode3();
        break;
    }

    SendAllSetupData();
    RunSystemDelayMs(1000);
    SendNotifyFactoryResetDone();

    RunSystemDelayMs(2000);
    NVIC_SystemReset();
}

/**
 * @brief Process the SBAS enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableSBAS(char *pstrSentence)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];

    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    bool bEnableSBAS = (pstrSubData1[0] == '1') ? true : false;

    if(CSetupMgr::getInst()->GetEnableSBAS() != bEnableSBAS)
    {
        CSetupMgr::getInst()->SetEnableSBAS(bEnableSBAS);
        CSensorMgr::getInst()->SetInternalGnssConfig(CSetupMgr::getInst()->GetIntGnssType(), CSetupMgr::getInst()->GetEnableSBAS());
    }
}

/**
 * @brief Send the SBAS enable/disable status to the PI
 * @return None
 */
void CMKD::SendSetupEnableSBAS()
{
    char pstrSycBuff[32];

    sprintf((char*)pstrSycBuff, "$AIINL,SBA,%d", CSetupMgr::getInst()->GetEnableSBAS());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the EPFS configuration command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEPFS(char *pstrSentence)
{
    int nIntEpfs = CSentence::GetFieldInteger(pstrSentence, 2);
    int nExtEpfs = CSentence::GetFieldInteger(pstrSentence, 3);

    if(!CSetupMgr::getInst()->CheckIntGnssCfgValue(nIntEpfs))
        nIntEpfs = INT_GNSS_CFG_IDX_DFLT;
    if(!CSetupMgr::getInst()->CheckExtGnssCfgValue(nExtEpfs))
        nExtEpfs = EXT_GNSS_CFG_IDX_DFLT;

    if(CSetupMgr::getInst()->GetIntGnssType() != nIntEpfs || CSetupMgr::getInst()->GetExtGnssType() != nExtEpfs)
    {
        if((CSensorMgr::getInst()->GetAntType() == EPFD_ANTENNA_TYPE_INT && CSetupMgr::getInst()->GetIntGnssType() != nIntEpfs) ||
            (CSensorMgr::getInst()->GetAntType() == EPFD_ANTENNA_TYPE_EXT && CSetupMgr::getInst()->GetExtGnssType() != nExtEpfs))
        {
            CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();
        }

        // Set internal/external gnss type
        CSetupMgr::getInst()->SetIntGnssType(nIntEpfs);
        CSetupMgr::getInst()->SetExtGnssType(nExtEpfs);

        // Set internal gnss config
        CSensorMgr::getInst()->SetInternalGnssConfig(CSetupMgr::getInst()->GetIntGnssType(), CSetupMgr::getInst()->GetEnableSBAS());
    }
}

/**
 * @brief Send the EPFS configuration to the PI
 * @return None
 */
void CMKD::SendSetupEPFS()
{
    char pstrSycBuff[32];

    int nIntEpfs = CSetupMgr::getInst()->GetIntGnssType();
    int nExtEpfs = CSetupMgr::getInst()->GetExtGnssType();

    memset(pstrSycBuff, 0x00, sizeof(pstrSycBuff));
    sprintf((char*)pstrSycBuff, "$AIINL,EPS,%d,%d", nIntEpfs, nExtEpfs);
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the external EPFS enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableExtEPFS(char *pstrSentence)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];

    memset(pstrSubData1, 0x00, sizeof(pstrSubData1));
    CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    int nEnable = atoi(pstrSubData1);

    if((nEnable == 0 || nEnable == 1) && CSetupMgr::getInst()->GetEnableExtEPFS() != nEnable)
    {
        CSetupMgr::getInst()->SetEnableExtEPFS(nEnable);
        gpAlarmExtEpfsLost->ResetAlarmCheckStat();

        CSensorMgr::getInst()->SetEnableEPFS(nEnable);
    }
}

/**
 * @brief Send the external EPFS enable/disable status to the PI
 * @return None
 */
void CMKD::SendSetupEnableExtEPFS()
{
    char pstrSycBuff[32];

    memset(pstrSycBuff, 0x00, sizeof(pstrSycBuff));
    sprintf((char*)pstrSycBuff, "$AIINL,EEPS,%d", CSetupMgr::getInst()->GetEnableExtEPFS());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the external heading enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableHeading(char *pstrSentence)
{
    int nEnable = CSentence::GetFieldInteger(pstrSentence, 2);

    if((nEnable == 0 || nEnable == 1) && CSetupMgr::getInst()->GetEnableExtHeading() != nEnable)
    {
        CSetupMgr::getInst()->SetEnableExtHeading(nEnable);
    }
}

/**
 * @brief Send the external heading enable/disable status to the PI
 * @return None
 */
void CMKD::SendSetupEnableHeading()
{
    char pstrSycBuff[32];

    memset(pstrSycBuff, 0x00, sizeof(pstrSycBuff));
    sprintf((char*)pstrSycBuff, "$AIINL,EHDG,%d", CSetupMgr::getInst()->GetEnableExtHeading());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the external ROT enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableROT(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // ROT 정보 enable/disable
    //----------------------------------------------------------------------------------------
    //---------------------
    // $AIINL,EROT,1*45
    //---------------------

    int nEnable = CSentence::GetFieldInteger(pstrSentence, 2);

    if((nEnable == 0 || nEnable == 1) && CSetupMgr::getInst()->GetEnableExtROT() != nEnable)
    {
        CSetupMgr::getInst()->SetEnableExtROT(nEnable);
    }
}

/**
 * @brief Send the external ROT enable/disable status to the PI
 * @return None
 */
void CMKD::SendSetupEnableROT()
{
    char pstrSycBuff[32];

    memset(pstrSycBuff, 0x00, sizeof(pstrSycBuff));
    sprintf((char*)pstrSycBuff, "$AIINL,EROT,%d", CSetupMgr::getInst()->GetEnableExtROT());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the ALR 14 enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableALR14(char *pstrSentence)
{
    int nEnable = CSentence::GetFieldInteger(pstrSentence, 2);
    if((nEnable == 0 || nEnable == 1) && CSetupMgr::getInst()->GetEnableAlert14() != nEnable)
    {
        CSetupMgr::getInst()->SetEnableAlr14(nEnable);
    }
}

/**
 * @brief Send the ALR 14 enable/disable status to the PI
 * @return None
 */
void CMKD::SendSetupEnableALR14()
{
    char pstrSycBuff[32];

    memset(pstrSycBuff, 0x00, sizeof(pstrSycBuff));
    sprintf((char*)pstrSycBuff, "$AIINL,EALR14,%d", CSetupMgr::getInst()->GetEnableAlert14());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the silent mode enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */ 
void CMKD::ProcSetupEnableSilentMode(char *pstrSentence)
{
    int nEnable = CSentence::GetFieldInteger(pstrSentence, 2);
    if((nEnable == 0 || nEnable == 1) && CSetupMgr::getInst()->GetEnableSilentMode() != nEnable)
    {
        CSetupMgr::getInst()->SetEnableSilentMode(nEnable);
    }
}

/**
 * @brief Process the SSA pre-shared key command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupUserKeySSA(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // SSA pre-shared key 설정
    //----------------------------------------------------------------------------------------
    //---------------------
    // $AIINL,SSA,%s*45
    //---------------------
    char pstrSubData1[RX_MAX_DATA_SIZE];

    memset(pstrSubData1, 0x00, sizeof(pstrSubData1));
    int nLen = CSentence::GetFieldString(pstrSentence, 2, pstrSubData1, sizeof(pstrSubData1));

    if(CSetupMgr::getInst()->CheckUserKeySSA(pstrSubData1, nLen))
    {
        int nLen = strlen(pstrSubData1);
        if(nLen >= MAX_SSA_KEY_LEN)
            nLen = MAX_SSA_KEY_LEN;

        CSetupMgr::getInst()->SetUserKeySSA(pstrSubData1, nLen);
    }
}

/**
 * @brief Send the SSA pre-shared key to the PI
 * @return None
 */
void CMKD::SendSetupUserKeySSA()
{
    char pstrSycBuff[32];

    memset(pstrSycBuff, 0x00, sizeof(pstrSycBuff));
    sprintf((char*)pstrSycBuff, "$AIINL,SSA,%d", (char*)CSetupMgr::getInst()->GetUserKeySSA());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the external dimension and internal dimension command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupExtDimInt(char *pstrSentence)
{
    xANTPOS sAntPos;
    memset(&sAntPos, 0x00, sizeof(xANTPOS));

    sAntPos.wA = CSentence::GetFieldInteger(pstrSentence, 2);
    sAntPos.wB = CSentence::GetFieldInteger(pstrSentence, 3);
    sAntPos.wC = CSentence::GetFieldInteger(pstrSentence, 4);
    sAntPos.wD = CSentence::GetFieldInteger(pstrSentence, 5);

    if(    sAntPos.wA != CSetupMgr::getInst()->GetExtendAntennaPosA()
        || sAntPos.wB != CSetupMgr::getInst()->GetExtendAntennaPosB()
        || sAntPos.wC != CSetupMgr::getInst()->GetExtendAntennaPosC()
        || sAntPos.wD != CSetupMgr::getInst()->GetExtendAntennaPosD())
    {
        CSetupMgr::getInst()->SetExtendAntennaPos(sAntPos);
    }
}

/**
 * @brief Send the external dimension and internal dimension to the PI
 * @return None
 */
void CMKD::SendSetupExtDimInt()
{
    char pstrSycBuff[64];

    sprintf((char*)pstrSycBuff, "$AIINL,EDI,%d,%d,%d,%d",
            CSetupMgr::getInst()->GetExtendAntennaPosA(), 
            CSetupMgr::getInst()->GetExtendAntennaPosB(), 
            CSetupMgr::getInst()->GetExtendAntennaPosC(), 
            CSetupMgr::getInst()->GetExtendAntennaPosD());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the pilot port access level command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupPilotPortAccessLevel(char *pstrSentence)
{
    //----------------------------------------------------------------------------------------
    // Pilot port restricted mode enable/disable
    //----------------------------------------------------------------------------------------

    int nEnable = CSentence::GetFieldInteger(pstrSentence, 2);
    if((nEnable == 0 || nEnable == 1) && CSetupMgr::getInst()->GetPilotPortRestricted() != nEnable)
    {
        CSetupMgr::getInst()->SetPilotPortRestricted(nEnable);
    }
}

/**
 * @brief Send the pilot port access level to the PI
 * @return None
 */
void CMKD::SendSetupPilotPortAccessLevel()
{
    char pstrSycBuff[32];

    sprintf((char*)pstrSycBuff, "$AIINL,PLA,%d", CSetupMgr::getInst()->GetPilotPortRestricted());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the NMEA enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableNMEA(char *pstrSentence)
{
    //------------------------------------------------------------------------------------------------------------------------
    // Description : Send GPS Data - Ver.1.02
    //  - $AIINL,SGD,xxxx*hh
    //
    //     .xxxx : 0000 0000 0111 1111
    //     .filed: ZDA,GGA,GBS,VTG,GLL,GSV,RMC
    //
    //   - Query Sentence: $AIINL,AIQ,SGD*hh
    //   - (주의): AIS-50B에서는 기존방식대로, AIS-50N에서는 하나의 Sentence로 통합해서 Control하는 방법으로 변경.<2009.10.29>
    //   - 현재 UBlox Core Board에서 수신되는 GPS Sentence는 플로터에서 설정하도록 되어 있음.
    //------------------------------------------------------------------------------------------------------------------------
    char pstrSubData[32];
    CSentence::GetFieldString(pstrSentence, 2, pstrSubData, sizeof(pstrSubData));
    DWORD dwBitmapData = strtol(pstrSubData, NULL, 16);

    CSetupMgr::getInst()->SetSetupNmeaOutToMKD(dwBitmapData);
}

/**
 * @brief Process the NMEA enable/disable command for external device from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableNMEAforEXT(char *pstrSentence)
{
    //------------------------------------------------------------------------------------------------------------------------
    // Description : Send GPS Data - Ver.1.02
    //  - $AIINL,SGE,xxxx*hh
    //
    //     .xxxx : 0000 0000 0111 1111
    //     .filed: ZDA,GGA,GBS,VTG,GLL,GSV,RMC
    //
    //   - Query Sentence: $AIINL,AIQ,SGE*hh
    //   - (주의): AIS-50B에서는 기존방식대로, AIS-50N에서는 하나의 Sentence로 통합해서 Control하는 방법으로 변경.<2009.10.29>
    //   - 현재 UBlox Core Board에서 수신되는 GPS Sentence는 플로터에서 설정하도록 되어 있음.
    //------------------------------------------------------------------------------------------------------------------------
    char pstrSubData[32];
    CSentence::GetFieldString(pstrSentence, 2, pstrSubData, sizeof(pstrSubData));
    DWORD dwBitmapData = strtol(pstrSubData, NULL, 16);
    
    CSetupMgr::getInst()->SetSetupNmeaOutToEXT(dwBitmapData);
}

/**
 * @brief Process the DGNSS enable/disable command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcSetupEnableDgnssFromMsg17(char *pstrSentence)
{
    char pstrSubData1[RX_MAX_DATA_SIZE];
    char pstrSubData2[RX_MAX_DATA_SIZE];

    int nData = CSentence::GetFieldInteger(pstrSentence, 2);
    if(nData == 1 || nData == 0)
    {
        if(CSetupMgr::getInst()->GetEnableDgnssByMsg17() != nData)
        {
            CSetupMgr::getInst()->SetEnableDgnssByMsg17(nData);
            SendSetupEnableDgnssFromMsg17();
        }
    }
}

/**
 * @brief Send the DGNSS enable/disable status to the PI
 * @return None
 */
void CMKD::SendSetupEnableDgnssFromMsg17()
{
    char pstrSycBuff[32];

    sprintf((char*)pstrSycBuff, "$AIINL,IDM,%01d", CSetupMgr::getInst()->GetEnableDgnssByMsg17());
    CSentence::AddSentenceTail(pstrSycBuff);
    SendOutStr(pstrSycBuff);
}

/**
 * @brief Send the BITE result to the PI
 * @return None
 */
void CMKD::SendOutResultBITE()
{
    char pstrRespBuff[32];

    sprintf((char*)pstrRespBuff, "#AIINL,BIT,%04X", CTestModeMgr::getInst()->m_wErrorCodeBITE);
    CSentence::AddSentenceTail(pstrRespBuff);
    SendOutStr(pstrRespBuff);
}

/**
 * @brief Send the loopback test result to the PI
 * @return None
 */
void CMKD::SendOutTxLoopbackRcvCnt()
{
    char pstrRespBuff[32];

    sprintf((char*)pstrRespBuff, "#AIINL,LBT,%04X,%04X", CTestModeMgr::getInst()->m_nTxLoopbackRcvCntCH1, CTestModeMgr::getInst()->m_nTxLoopbackRcvCntCH2);
    CSentence::AddSentenceTail(pstrRespBuff);
    SendOutStr(pstrRespBuff);
}

/**
 * @brief Send the DC voltage to the PI
 * @return None
 */
void CMKD::SendOutVoltageDC()
{
    char pstrRespBuff[32];
    WORD wData;
    wData = CBuiltInTestMgr::getInst()->GetDcVoltageAdcData();
    sprintf((char*)pstrRespBuff, "#AIINL,DCV,%04X", wData);
    CSentence::AddSentenceTail(pstrRespBuff);
    SendOutStr(pstrRespBuff);
}

/**
 * @brief Process the loopback test command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CMKD::ProcRunComLoopBackCmd(char *pstrCmd)
{
    //-----------------------------------------
    // $AIINL,LBP,%d
    //-----------------------------------------
    //  %d : Loop-back test를 실행할 타겟 포트
    //         0 : Sensor1
    //         1 : Sensor2
    //         2 : Sensor3
    //         3 : Long - Range Port
    //         4 : External display Port
    //         5 : Pilot Port
    //-----------------------------------------
    char pstrPort[8];
    char pstrData[RX_MAX_DATA_SIZE];

    int  nPortID = CSentence::GetFieldInteger(pstrCmd, 2);

    memcpy(pstrData, pstrCmd, strlen(pstrCmd));
    CSentence::AddSentenceTail(pstrData);

    // Send loop-back command to corresponding port
    switch(nPortID)
    {
    case LOOPBACK_PORT_ID_SENSOR1:
    case LOOPBACK_PORT_ID_SENSOR2:
    case LOOPBACK_PORT_ID_SENSOR3:
#ifdef __ENABLE_EXT_DISP__
        CEXTDISP::getInst()->SendOutStr(pstrData);
#endif
        break;
    case LOOPBACK_PORT_ID_LR:
        CLongRange::getInst()->SendOutStr(pstrData);
        break;
#ifdef __ENABLE_EXT_DISP__
    case LOOPBACK_PORT_ID_EXTDISP:
        CEXTDISP::getInst()->SendOutStr(pstrData);
        break;
#endif
#ifdef __ENABLE_PILOT__
    case LOOPBACK_PORT_ID_PILOT:
        CPILOT::getInst()->SendOutStr(pstrData);
        break;
#endif
    default:
        break;
    }
}

/**
 * @brief Process the data
 * @param pUartDbgP The UART port to be processed
 * @return None
 */
void CMKD::ProcessData(cUart *pUartDbgP)
{
    return CPI::ProcessData(pUartDbgP);
}

/**
 * @brief Process the sentences
 * @param pstrCmd The sentence to be parsed
 * @return None
 */
int CMKD::ProcessSentence(char *pstrCmd)
{
    // Check if the checksum is valid
    if (!CSentence::IsValidCheckSum(pstrCmd))
    {
        return 0;
    }

    char pstrTalkerID[LEN_NMEA_TALKER];
    char pstrSentID[LEN_NMEA_SENT];
    CSentence::GetTalkerSenderID((char*)pstrCmd, pstrTalkerID, pstrSentID);

         if(!strncmp(pstrSentID, "SUG", 3)) {ProcessSUG(pstrCmd);   return 1;}
    else if(!strncmp(pstrSentID, "ACA", 3)) {ProcessACA(pstrCmd);   return 1;}
    else if(!strncmp(pstrSentID, "INL", 3)) {ProcessINL(pstrCmd);   return 1;}
    else if(!strncmp(pstrSentID, "AIQ", 3)) {ProcessAIQ(pstrCmd);   return 1;}

    return CPI::ProcessSentence(pstrCmd);
}

/**
 * @brief Run MKD periodically
 * @return None
 */
void CMKD::RunPeriodicallyMkd()
{
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 1)
    {
        CheckDTEFlag();

        if(cTimerSys::getInst()->GetTimeDiffSec(0) < 30)
        {
            SendOutVoltageDC();
            SendOutResultBITE();
        }

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }

    CPI::RunPeriodicallyPI();
}
