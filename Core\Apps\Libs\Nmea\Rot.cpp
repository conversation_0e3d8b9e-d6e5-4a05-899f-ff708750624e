/**
 * @file    Rot.cpp
 * @brief   Rot class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Rot.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"
#include "Pilot.h"

#define LATENCY_ROT_SEND_TO_PILOTPORT        950            // less than 1sec

/******************************************************************************
 * 
 * ROT - 
 *
 * $--ROT, x.x,A*hh<CR><LF>
 *          |  |
 *          1  2
 * 
 * 1. Rate of turn, °/min, "-" = bow turns to port
 * 2. Status: A = data valid, V = data invalid
 * 
 ******************************************************************************/
CRot::CRot() : CSentence()
{
    ClearData();
}

void CRot::ClearData(void)
{
    m_nRotData = NMEA_ROT_NULL;
    m_nRotSrcType = ROTSCR_NONE;
    m_dwRcvTick = 0;
    m_ndwLatencyROT = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CRot::Parse(const char *pszSentence)
{
    char pstrTalkerID[8];
    char pstrTmp[128];
    int  nROT;

    GetFieldString(pszSentence, 1, pstrTmp);         // Rate of turn
    if (strlen(pstrTmp) > 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, FALSE))
        {
            return false;
        }

        nROT    = (int)(atof(pstrTmp) * NMEA_SCALE_ROT);
        GetFieldString(pszSentence, 2, pstrTmp);     // Status
        if (pstrTmp[0] == 'A')
        {
            m_nRotData = nROT;
            m_dwRcvTick = SysGetSystemTimer();

            GetTalkerID(pszSentence, pstrTalkerID);
            m_nRotSrcType = (strncmp(pstrTalkerID, "TI", 2) ? ROTSCR_NOT_TI : ROTSCR_TI);

        #ifdef __ENABLE_PILOT__
            // [IEC 61993-2, Ed.3, clause 19.5.10.2]
            // Confirm that the heading and rate of turn values from sources used for position reports are output at the pilot port.
            // [IEC 61993-2, Ed.3, clause 19.7.2]
            // Confirm that heading and rate of turn sentences are output with a latency of less than 200ms.
            // Confirm that heading is output at a rate of 5 sentences per second and rate of turn is output at a rate of 1 sentence per second.
            if (SysGetDiffTimeMili(m_ndwLatencyROT) >= LATENCY_ROT_SEND_TO_PILOTPORT)
            {
                CPILOT::getInst()->SendOutStr(pszSentence);
                m_ndwLatencyROT = SysGetSystemTimer();
            }
        #endif
        }
    }

    return true;
}

/**
 * @brief Check received heading data is valid or not
 */
bool CRot::IsValidRotData(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_DATA_VALID_TIMEMS)
    {
        return true;
    }

    return false;
}

/**
 * @brief Get source type for rot data
 */
int CRot::GetRotSrcType(void)
{
    return m_nRotSrcType;
}

/**
 * @brief Get rot data
 */
int CRot::GetRot(void)
{
    return m_nRotData;
}

/**
 * @brief Call function periodically
 */
void CRot::RunPeriodically(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_ROT_LASTDATA_STAYMS)
    {
        ClearData();
    }
}
