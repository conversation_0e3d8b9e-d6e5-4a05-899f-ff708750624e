/**
 * @file    Adc.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <memory>
#include "stm32h7xx_hal.h"
#include "DataType.h"
#include "SysConst.h"

#ifndef  __ADC_H__
#define  __ADC_H__

//=============================================================================
#define  ADC_0_DSC_RX_CH_NO         (0)

#define  ADC_1_CURR_MON_CH_NO       (0)
#define  ADC_1_12VOL_MON_CH_NO      (1)
#define  ADC_1_GMSK_RXA_CH_NO       (16)

#define  ADC_2_FWD_VSWR_CH_NO       (0)
#define  ADC_2_REV_VSWR_CH_NO       (1)

#define  ADC_3_BB_VER1_CH_NO        (0)
#define  ADC_3_BB_VER2_CH_NO        (1)
#define  ADC_3_RF_VER1_CH_NO        (2)
#define  ADC_3_RV_VER2_CH_NO        (3)
#define  ADC_3_GMSK_RXB_CH_NO       (16)

//---------------------------------- Current         Power
static const DWORD vAdc1RegChNo[] = {ADC_CHANNEL_10, ADC_CHANNEL_6};
static const DWORD vAdc1InjChNo[] = {ADC_CHANNEL_2};    // GMSK RX2

//---------------------------------- FWR_VSWR       REV_VSWR
static const DWORD vAdc2RegChNo[] = {ADC_CHANNEL_2, ADC_CHANNEL_15};
////static const DWORD vAdc2InjChNo[] = {ADC_CHANNEL_9};    // GMSK RX1

//---------------------------------- BB ver1         BB ver2         RF ver1        RF ver2
static const DWORD vAdc3RegChNo[] = {ADC_CHANNEL_14, ADC_CHANNEL_15, ADC_CHANNEL_1, ADC_CHANNEL_8};
static const DWORD vAdc3InjChNo[] = {ADC_CHANNEL_13};   // GMSK RX1

//=============================================================================
class CAdc
{
public:
	CAdc(ADC_TypeDef *pBaseAddr, int nNoOfRegular, const DWORD *pRegularChNo, int nNoOfInjected, const DWORD *pInjectedChNo);
    virtual ~CAdc(void);

    static std::shared_ptr<CAdc> getInstAdc1() {
        static std::shared_ptr<CAdc> pInstAdc0 = std::make_shared<CAdc>(ADC1, 2, vAdc1RegChNo, 1, vAdc1InjChNo);
        return pInstAdc0;
    }

    static std::shared_ptr<CAdc> getInstAdc2() {
        static std::shared_ptr<CAdc> pInstAdc1 = std::make_shared<CAdc>(ADC2, 2, vAdc2RegChNo, 0, (const DWORD *)NULL);
        return pInstAdc1;
    }

    static std::shared_ptr<CAdc> getInstAdc3() {
        static std::shared_ptr<CAdc> pInstAdc2 = std::make_shared<CAdc>(ADC3, 4, vAdc3RegChNo, 1, vAdc3InjChNo);
        return pInstAdc2;
    }

public:
    void  SetChannelEnable(int nChNo);
    HWORD GetADCData(DWORD dChNo);
    void  RunADCIsrHandler(void);

public:
    DWORD                           m_vRegDmaAdData[16];

protected:
    ADC_TypeDef                    *m_pBaseAddr;
    ADC_HandleTypeDef               m_xAdcHand;
    ADC_ChannelConfTypeDef          m_xAdcCnfg;
    ADC_InjectionConfTypeDef        m_xAdcInjt;
 
    int                             m_nNoOfRegular;        // Regular conversion channel count
    DWORD                           m_vRegularChNo[16];    // Regular conversion max 16 channel
    int                             m_nNoOfInjected;       // Injected conversion channel count
    DWORD                           m_vInjectedChNo[4];    // Injected conversion max 4 channel
};
//=============================================================================

#endif /*__ADC_H__*/

