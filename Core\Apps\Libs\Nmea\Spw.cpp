/**
 * @file    Spw.cpp
 * @brief   Spw class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Spw.h"
#include "SysLib.h"
#include "Predef.h"
#include "Ship.h"

/******************************************************************************
 * 
 * SPW - Security password sentence
 *
 * $--SPW,ccc,c--c,x,c--c,*hh<CR><LF>
 *         |   |   |  |
 *         1   2   3  4
 *
 * 1. Password protected sentence
 * 2. Unique identifier
 * 3. Password level
 * 4. Password
 *
 ******************************************************************************/
CSpw::CSpw() : CSentence()
{
    ClearData();
}

void CSpw::ClearData(void)
{
    m_nCode = SPW_NA;
    m_dwMMSI = 0;
    m_nPwdLevel = 0;
    m_dwRcvTick = 0;
    memset(m_szPassword, 0x00, sizeof(m_szPassword));
}
    
/**
 * @brief Parse the sentence
 */
bool CSpw::Parse(const char *pszSentence)
{
    char pstrTmp[8];

    memset(pstrTmp, 0x00, sizeof(pstrTmp));
    GetFieldString(pszSentence, 1, pstrTmp);

    if(!strncmp(pstrTmp, "EPV", 3))
        m_nCode = SPW_EPV;
    else if(!strncmp(pstrTmp, "SSD", 3))
        m_nCode = SPW_SSD;
    else if(!strncmp(pstrTmp, "VSD", 3))
        m_nCode = SPW_VSD;
    else
        return false;

    m_dwMMSI = GetFieldInteger(pszSentence, 2);
    if(m_dwMMSI != cShip::getOwnShipInst()->GetOwnShipMMSI())
    {
        ClearData();
        return false;
    }

    m_nPwdLevel = GetFieldInteger(pszSentence, 3);

    GetFieldString(pszSentence, 4, m_szPassword);
    if(strlen(m_szPassword) <= 0 || strcmp(m_szPassword, MASTER_PASSWORD))
    {
        ClearData();
        return false;
    }

    m_dwRcvTick = SysGetSystemTimer();

    return true;
}

/**
 * @brief Call function periodically
 */
void CSpw::RunPeriodically(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_HDG_LASTDATA_STAYMS)
    {
        ClearData();
    }
}