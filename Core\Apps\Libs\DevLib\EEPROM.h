/**
 * @file    Eeprom.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef  __EEPROM_H__
#define  __EEPROM_H__

#include <memory>
#include "SysConst.h"
#include "DevMem.h"
#include "SPI.h"

#define  BACK_HW_CONFIG_ADDR        (0x0000)
#define  BACK_SYS_DATA_ADDR         (0x1000)
#define  BACK_ROS_DATA_ADDR         (0x2000)
#define  BACK_LOG_DATA_ADDR         (0x3000)


class CEEPROM : public CDevMem
{
public:
    CEEPROM();
    ~CEEPROM();

    static std::shared_ptr<CEEPROM> getInst() {
        static std::shared_ptr<CEEPROM> pInst = std::make_shared<CEEPROM>();
        return pInst;
    }

private:
    BOOL    EEPROM_IsBusy(void);
    void    EEPROM_WriteEnable(void);
    void    EEPROM_WriteDisable(void);

public:
	virtual DWORD GetSectorSize(DWORD dAddr) { return 0; };
    virtual int   EraseSector(DWORD dAddr, int nSecCnt) { return 0; };
    virtual int   WriteData(DWORD dAddr, void *pData, int nSize);
    virtual int   ReadData(DWORD dAddr, void *pData, int nSize);

public:
    cSpiSYS  *m_pSPI4;
};

#endif

