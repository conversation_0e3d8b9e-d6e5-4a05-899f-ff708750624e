#ifndef __FRAMEMAPMGR_H__
#define __FRAMEMAPMGR_H__

#include "DataType.h"
#include "AllConst.h"
#include "AisMsg.h"
#include "AisLib.h"
#include "ChannelMgr.h"
#include "VdlRxMgr.h"

class CChannelMgr;

class CFrameMapMgr : public CAisLib
{
public:
    CFrameMapMgr(CChannelMgr *pChannel);
    ~CFrameMapMgr();

public:
    void    ClearFrameMap();
    void    ShiftFrameMap(INT16 nShiftOffset);
    WORD    GetShiftedMapSlotID(WORD wMapSlotID);
    WORD    GetShiftedMapSlotID(WORD wFrameID, WORD wSlotID);
    WORD    GetSlotIdFromFrameSlotID(WORD wMapSlotID);

    FRAMEMAP_SLOTDATA* GetSlotDataPtr(WORD wFrSlotID);
    void    ChangeMMSIInFrameMap(UINT uNewMMSI);

protected:
    void    ClearFrameMapSlotData(FRAMEMAP_SLOTDATA *pSlotPtr);
    void    ClearFrameMapSlotData(int nFrSlotID);
    void    SetFrameMapSlotData(bool bCheckProtect, WORD wFrSlotID, INT8 nNumSlots, UINT uMMSI, 
                            BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                            UINT16 uMsgID, bool bStartSlot, UINT uSlotOffset, bool bItdmaKeepFlag, bool bProtectAssignedModeSlot, 
                            BYTE nDataIdx, bool bFAtoSO, WORD wTxChNum=AIS_CH_NUM_NONE);
public:
    void    SetFrameMapOneMsg(bool bCheckProtect, WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, 
                            BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme, 
                            UINT16 uMsgID, UINT16 uSlotOffset, bool bItdmaKeepFlag, bool bProtectAssignedModeSlot, 
                            BYTE nDataIdx, bool bFAtoSO);
    void    SetFrameMapOneMsgColumn(bool bCheckProtect, const WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, 
                            BYTE bSlotStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme, 
                            UINT16 uMsgID, UINT16 uSlotOffset, bool bItdmaKeepFlag, bool bProtectAssignedModeSlot, 
                            BYTE nDataIdx, bool bFAtoSO);

    void    FreeFrameMapSlotRowByCnt(WORD wStartSlotID, int nNumSlot);
    void    FreeFrameMapSlotRowByRange(WORD wStartSlotID, WORD wEndSlotID);
    int     FreeFrameMapOneMsg(WORD wFrSlotID);
    void    FreeFrameMapMsgColRow(const WORD wFrSlotIDToFree, int uNumSlot, UINT uMMSI);

    void    UpdateFrameMapSOTDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotNo, INT8 nNumSlot, UINT uMMSI, 
                                DWORD dwSyncState, DWORD dwTimeOut, DWORD dwSubMsg);
    void    UpdateFrameMapITDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotNo, INT8 nNumSlot, UINT uMMSI, 
                                DWORD dwSyncState, DWORD dwSlotInc, DWORD dwNumSlot, DWORD dwKeepFlag, BYTE cSlotStatus);
    void    UpdateFrameMapFATDMA(bool bCheckBastStStat, UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotID, UINT uMMSI, 
                                DWORD dwOffset, DWORD dwNumSlot, DWORD dwTimeOut, int nIncrement, bool bFAtoSO=FALSE);

    bool    UpdateAssignedSlotsFATDMAtoSOTDMA(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, 
                                            WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt);
    bool    UpdateAssignedSlotsSOTDMA(WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt);
    void    RestoreAssginedSlotsSOTDMAtoFATDMA(UINT uBaseStMMSI, WORD wStartSlotID);
    WORD    GetInternalAllocatedSlot(WORD wStartSlot, INT16 nNumSlotToScan, BYTE bTdmaScheme,
                                    int nMsgID=AIS_MSG_NO_ID_UNDEFINED, bool bExceptTmoZeroSlot=FALSE);
    WORD    GetInternalAllocatedSlot_SO(WORD wStartSI, int nSizeSI, 
                                    int nMsgID=AIS_MSG_NO_ID_UNDEFINED, bool bExceptTmoZeroSlot=FALSE, bool bFindMsg3SO=FALSE);
    WORD    GetInternalAllocatedSlot_IT(WORD wStartSI, int nSizeSI, 
                                    int nMsgID=AIS_MSG_NO_ID_UNDEFINED, bool bExceptTmoZeroSlot=FALSE);
    WORD    GetSoTdmaSlotForItdmaTx(WORD wStartSI, int nSizeSI);

    bool    IsIntTxReservedSlot(FRAMEMAP_SLOTDATA *pSlotData);
    bool    IsInternalAllocSlot(FRAMEMAP_SLOTDATA *pSlotPtr);
    bool    IsInternalAllocSlotSO(FRAMEMAP_SLOTDATA *pSlotPtr, bool bCheckMsgID=FALSE);
    bool    IsSlotAvailableForMMSI(WORD wFrSlotID, int nNumSlot, UINT uMMSI);
    void    SetLastTxFrameAndFree(WORD wTxSlotId, int nNumSlotCol=NUM_SLOT_PER_FRAME, bool bCheckMsgID=FALSE);

    void    SetLastTxSlotWithRange(int nStartSlot, int nEndSlot, bool bExceptBorder);

    void    FrChangeFrMapPosMsgMode(WORD wFrSlotIDStart, int nOpMode);
    void    FrChangePosMsgModeTimeOut(WORD wFrSlotIDStart, int nNumSlotsToCheck, int nTimeOut);

    static  bool CheckSlotIntAlloc(FRAMEMAP_SLOTDATA *pSlot);
    bool    CheckSlotIntAlloc(WORD wFrSlotID);

    static  bool CheckSlotExtAlloc(FRAMEMAP_SLOTDATA *pSlot);
    bool    CheckSlotExtAlloc(WORD wFrSlotID);

    void    PrepToChgCh(WORD wFrSlotIDStart);

private:
    CChannelMgr       *m_pChannel;
    FRAMEMAP_SLOTDATA *m_pFrameMap;
    INT16              m_nFrameMapStartSlotID;

public:
    static FRAMEMAP_SLOTDATA *m_pMsgMapOneFrame;
};
#endif//__FRAMEMAPMGR_H__
