/**
 * @file    Txt.h
 * @brief   Txt header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __TXT_H__
#define __TXT_H__

//----------------------------------------------------------------------------------------------------------------------
// refer to IEC-61993-2 6.10.2.1
// TXT-sentences with "alarm numbers" greater than 099 cannot be followed by TXT-sentences containing additional
// information by using the TXT-sentence's "text identifier". The "text identifier" is limited to the range of 01 to 99.
// Additional numbers may be used by the manufacturer for other purposes but shall be in the range 051 to 099.
//----------------------------------------------------------------------------------------------------------------------
#define TXT_ID_EXT_DGNSS            21      //"AIS: external DGNSS in use",                     /* $AIINL,TXT,021,A */
#define TXT_ID_EXT_GNSS             22      //"AIS: external GNSS in use",                      /* $AIINL,TXT,022,A */
#define TXT_ID_INT_DGNSS_BEACON     23      //"AIS: internal DGNSS in use(beacon)",             /* $AIINL,TXT,023,A */
#define TXT_ID_INT_DGNSS_MSG17      24      //"AIS: internal DGNSS in use(message 17)",         /* $AIINL,TXT,024,A */
#define TXT_ID_INT_GNSS             25      //"AIS: internal GNSS in use",                      /* $AIINL,TXT,025,A */
#define TXT_ID_NO_SENSOR_POS        26      // 
#define TXT_ID_EXT_SOG_COG          27      //"AIS: external SOG/COG in use",                   /* $AIINL,TXT,027,A */
#define TXT_ID_INT_SOG_COG          28      //"AIS: internal SOG/COG in use",                   /* $AIINL,TXT,028,A */
#define TXT_ID_HEADING_VALID        31      //"AIS: Heading valid",                             /* $AIINL,TXT,031,A */
#define TXT_ID_ROT_INDICATOR        33      //"AIS: Rate of Turn Indicator in use",             /* $AIINL,TXT,033,A */
#define TXT_ID_OTHER_ROT_SRC        34      //"AIS: Other ROT source in use",                   /* $AIINL,TXT,034,A */
#define TXT_ID_CH_MNG_CHANGE        36      //"AIS: Channel management parameters changed",     /* $AIINL,TXT,036,A */
// refer to IEC-61993-2:2018 ED3.0 6.10.2.4  
#define TXT_ID_LOW_PWR_MODE_ACT     37      //"AIS: Low power tanker mode active",              /* $AIINL,TXT,037,A */
#define TXT_ID_LOW_PWR_MODE_INACT   38      //"AIS: Low power tanker mode inactive",            /* $AIINL,TXT,038,A */
#define TXT_ID_ASSIGNED_MODE        40      //"AIS: Assigned mode by Message 16",               /* $AIINL,TXT,040,A */
#define TXT_ID_DATALINK_MNG_MODE    41      //"AIS: Data link management mode by message 20",   /* $AIINL,TXT,041,A */
#define TXT_ID_CH_MNG_MODE          42      //"AIS: Channel management mode by message 22",     /* $AIINL,TXT,042,A */
#define TXT_ID_GRP_ASSGNED_MODE     43      //"AIS: Group assignment mode by message 23 ",      /* $AIINL,TXT,043,A */
#define TXT_ID_RTN_DEFAULT_MODE     44      //"AIS: Returned to default operations",            /* $AIINL,TXT,044,A */

//============================================================================
// User defined TXT
// Security Log ID :  90~104, 119
#define TXTID_SECURITYLOG_MIN       90
#define TXTID_SECURITYLOG_MAX       119       
#define TXT_ID_ENDOF_ROSLIST        120     //"AIS: End of ACA list"
#define TXT_ID_INT_GNSS_SBAS        121     //"AIS: internal GNSS in use with SBAS"             /* $AIINL,TXT,121,A */

/******************************************************************************
 * 
 * TXT - Text Transmission
 *
 * $--TXT,xx,xx,xx,c--c*hh<CR><LF>
 *        |  |  |   |
 *        1  2  3   4
 *
 * 1. Total number of sentences, 01 to 99
 * 2. Sentence number, 01 to 99
 * 3. Text identifier
 * 4. Text message(maximum : 61 characters)
 *
 ******************************************************************************/
class CTxt : public CSentence
{
public:
    CTxt();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the text string pointer
     * @param nTxtID The text identifier
     * @return The text string pointer
     */
    static char* GetTxtStringPtr(int nTxtID);

    /**
     * @brief Check the text status
     * @param nTxtID The text identifier
     * @return True if the text is valid, false otherwise
     */
    static bool CheckTxtStat(int nTxtID);

    /**
     * @brief Make the TXT sentence
     * @param pszSentence The sentence to be made
     * @param nTxtID The text identifier
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, int nTxtID);
};

#endif /* __TXT_H__ */

