#include <string.h>
#include "DataType.h"
#include "AllConst.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "ComLib.h"
#include "Nmea.h"
#include "Ship.h"
#include "DataBackMgr.h"
#include "AisLib.h"
#include "Ublox.h"
#include "SetupMgr.h"
#include "AisLib.h"
#include "GpsLib.h"
#include "Timer.h"
#include "MKD.h"
#include "GnssInternal.h"

CGnssInternal::CGnssInternal(INT8 nSensorID, cUart *pUartPort) : cGpsBoard(nSensorID, pUartPort)
{
	CAisLib::SetDefaultSysDateTime(&m_sGnssDateTime);

    m_uMsg17BaseStId    = AIS_AB_MMSI_NULL;
    m_wMsg17RefStZcount = DGNSS_ZCOUNT_MAX;
    m_bMsg17RefStHealth = DGNSS_HEALTH_UNHEALTHY;
    m_dwDgnssByMsg17Sec = 0;
}

CGnssInternal::~CGnssInternal(void)
{
}

void CGnssInternal::InitializeRcvr()
{
    SetEnableEPFS(TRUE);
    UBX_InitRcvr((cGpsBoard*)this);
    UBX_CfgRcvrGnss((cGpsBoard*)this, CSetupMgr::getInst()->GetIntGnssType(), CSetupMgr::getInst()->GetEnableSBAS());
}

int CGnssInternal::GetTypeOfEPFS()
{
    return AIS_EPFD_INT_GNSS;
}

void CGnssInternal::SetGnssConfig(BYTE bGnssConfig, BOOL bEnableSBAS)
{
    //-------------------------------------------------------
    // bGnssConfig : GNSS_CFG_GPS_ONLY..GNSS_CFG_GPS_GLONASS
    //-------------------------------------------------------
    UBX_CfgRcvrGnss((cGpsBoard*)this, bGnssConfig, bEnableSBAS);
}

BOOL CGnssInternal::IsDgnssCorrectedByMsg17(void)
{
    return (IsDgnssCorrected() && m_uMsg17BaseStId != AIS_AB_MMSI_NULL);
}

BOOL CGnssInternal::IsDgnssCorrectedByBeacon(void)
{
    return FALSE;
}

void CGnssInternal::SetDgnssDataSrcBaseSt(UINT uBaseMMSI, POS_ALLH *psRefStPos, WORD wZcount, BYTE bHealth)
{
    m_uMsg17BaseStId    = uBaseMMSI;
    m_wMsg17RefStZcount    = wZcount;
    m_bMsg17RefStHealth    = bHealth;
    m_dwDgnssByMsg17Sec = cTimerSys::getInst()->GetCurTimerSec();

    if(psRefStPos)
        m_sMsg17RefStPos = *psRefStPos;
    else
    	CAisLib::ClearHalfPosToNULL(&m_sMsg17RefStPos);

    static UINT uOldBaseMMSI = AIS_AB_MMSI_NULL;
    if(m_uMsg17BaseStId != uOldBaseMMSI)
    {
        DEBUG_LOG("rcv-MSG17] SetDgnssBaseSt, %09d, Z:%d, health:%d, pos:%.4f,%.4f, s: %d\r\n",
            m_uMsg17BaseStId, m_wMsg17RefStZcount, m_bMsg17RefStHealth, m_sMsg17RefStPos.xPosF.fLAT, m_sMsg17RefStPos.xPosF.fLON, m_dwDgnssByMsg17Sec);

        uOldBaseMMSI = m_uMsg17BaseStId;
    }
}

void CGnssInternal::CheckDgnssRefStLifeTime()
{
    if(m_uMsg17BaseStId != AIS_AB_MMSI_NULL && cTimerSys::getInst()->GetTimeDiffSec(m_dwDgnssByMsg17Sec) > DGNSS_TIMEOUT_SEC)
    {
        DEBUG_LOG("rcv-MSG17] SetDgnssBaseSt, time-out, DGNSS fix: %d, %09d, staySec: %d, s:%d\r\n",
                IsDgnssCorrectedByMsg17(), m_uMsg17BaseStId, m_dwDgnssByMsg17Sec, cTimerSys::getInst()->GetCurTimerSec());

        SetDgnssDataSrcBaseSt(AIS_AB_MMSI_NULL);
    }
}

BOOL CGnssInternal::IsMsg17RefStAvailable(xAISMSG17 *pRxMsg17)
{
    //----------------------------------------------------------------------------------------------------------------------
    // IEC-61993-2 ********
    // IEC-61993-2 6.2.2
    // the internal GNSS receiver shall be capable of being differentially corrected, at least by evaluation of Message 17.
    // Where DGNSS corrections are received from multiple sources, the DGNSS corrections from the nearest DGNSS reference
    // station should be used taking into account the Z count, and the health of the DGNSS reference station.
    //----------------------------------------------------------------------------------------------------------------------
#define IsHealthGood(bHealth)   (bHealth != DGNSS_HEALTH_UNHEALTHY)
#define IsZcountValid(wZcount)  (wZcount <= DGNSS_ZCOUNT_MAX)
#define IsDistanceGood(fDist)    TRUE

    if(!pRxMsg17)
        return FALSE;

    POS_ALLH *psNewRefStPos = &(pRxMsg17->xAllPosX);
    WORD wNewZcount = pRxMsg17->wZcount;
    BYTE bNewHealth = pRxMsg17->bHealth;

    BOOL bAcceptMsg = FALSE;
    FLOAT fDistOld = AIS_DIST_NM_INVALID;
    FLOAT fDistNew = CGps::GetDistanceByFLOAT(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON,
                                        psNewRefStPos->xPosF.fLAT, psNewRefStPos->xPosF.fLON, DIST_UNIT_NM);

    BOOL bCompareSt = pRxMsg17->xRxCOM.dSrcMMSI != AIS_AB_MMSI_NULL && m_uMsg17BaseStId != AIS_AB_MMSI_NULL && pRxMsg17->xRxCOM.dSrcMMSI != m_uMsg17BaseStId;
    if(bCompareSt)
    {
        //if(psNewRefStPos->xPosF.fLAT != AIS_FLOAT_LAT_NULL_VAL && psNewRefStPos->xPosF.fLON != AIS_FLOAT_LON_NULL_VAL)
        {
            if(m_uMsg17BaseStId != AIS_AB_MMSI_NULL)
                fDistOld = CGps::GetDistanceByFLOAT(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON,
                                            m_sMsg17RefStPos.xPosF.fLAT, m_sMsg17RefStPos.xPosF.fLON, DIST_UNIT_NM);

            if(IsHealthGood(bNewHealth))    // if new DGNSS reference station's health is working
            {
                if(bNewHealth < m_bMsg17RefStHealth)
                {
                    bAcceptMsg = TRUE;
                }
                else if(bNewHealth == m_bMsg17RefStHealth)
                {
                    if(IsZcountValid(wNewZcount))
                    {
                        if(IsZcountValid(m_wMsg17RefStZcount))
                        {
                            if(wNewZcount < m_wMsg17RefStZcount)
                            {
                                bAcceptMsg = TRUE;
                            }
                            else if(wNewZcount == m_wMsg17RefStZcount)
                            {
                                if(fDistNew != AIS_DIST_NM_INVALID)
                                {
                                    if(fDistOld == AIS_DIST_NM_INVALID)
                                        bAcceptMsg = IsDistanceGood(fDistNew);
                                    else
                                        bAcceptMsg = (fDistNew < fDistOld);
                                }
                            }
                        }
                        else
                        {
                            bAcceptMsg = TRUE;
                        }
                    }
                }
            }
        }
    }
    else
    {
        bAcceptMsg = IsHealthGood(bNewHealth) && IsZcountValid(wNewZcount) && IsDistanceGood(fDistNew);
    }

    DEBUG_LOG("rcv-MSG17] IsMsg17RefStAvailable, accept: %d, elapSec: %d, compare: %d, BaseSt : %09d -> %09d, Z: %d(%d) -> %d(%d), health: %d(%d) -> %d(%d), dist: %.1f(%d) -> %.1f(%d), pos:%.4f,%.4f -> %.4f,%.4f, s:%d\r\n",
            bAcceptMsg, cTimerSys::getInst()->GetTimeDiffSec(m_dwDgnssByMsg17Sec), bCompareSt, m_uMsg17BaseStId, pRxMsg17->xRxCOM.dSrcMMSI,
            m_wMsg17RefStZcount, IsZcountValid(m_wMsg17RefStZcount), wNewZcount, IsZcountValid(wNewZcount),
            m_bMsg17RefStHealth, IsHealthGood(m_bMsg17RefStHealth), bNewHealth, IsHealthGood(bNewHealth), fDistOld, IsDistanceGood(fDistOld), fDistNew, IsDistanceGood(fDistNew),
            m_sMsg17RefStPos.xPosF.fLON, m_sMsg17RefStPos.xPosF.fLAT, psNewRefStPos->xPosF.fLON, psNewRefStPos->xPosF.fLAT,
            cTimerSys::getInst()->GetCurTimerSec());

    return bAcceptMsg;
}

void CGnssInternal::SetUtcDate(int nYear,int nMonth,int nDay)
{
    cGpsBoard::SetUtcDate(nYear, nMonth, nDay);
    CAisLib::GetSysDateTime(&m_sGnssDateTime, &m_xUtcDate, &m_xUtcTime);
}

void CGnssInternal::SetUtcTime(int nHour,int nMin,int nSec)
{
    cGpsBoard::SetUtcTime(nHour, nMin, nSec);
    CAisLib::GetSysDateTime(&m_sGnssDateTime, &m_xUtcDate, &m_xUtcTime);
}

BOOL CGnssInternal::IsRcvrBackupDateTimeValid()
{
    return m_sGnssDateTime.nValid;
}

void CGnssInternal::UpdatePosByPriority()
{
	cGpsBoard::UpdatePosByPriority();
}

int CGnssInternal::ProcessSentence(char *pstrCmd)
{
    char pstrTmp[RX_MAX_DATA_SIZE];
    int nLen;
    strncpy(pstrTmp, pstrCmd, (nLen = MIN(strlen(pstrCmd), RX_MAX_DATA_SIZE-1)));
    pstrTmp[nLen] = '\0';

    cGpsBoard::ProcessSentence(pstrCmd);

    CMKD::getInst()->SendOutNMEA(pstrTmp);
}

void CGnssInternal::RunPeriodicallyGps()
{
    cGpsBoard::RunPeriodicallyGps();

    CheckDgnssRefStLifeTime();
}
