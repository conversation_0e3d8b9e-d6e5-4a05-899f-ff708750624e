/**
 * @file    Aca.cpp
 * @brief   Aca class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "RosMgr.h"
#include "Ship.h"
#include "Timer.h"
#include "Aca.h"

/******************************************************************************
 *
 * ACA - AIS Regional Channel Assignment Message
 *                                                                      16  18
 *                                                                      |   |
 * $--ACA,x,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a,x,xxxx,x,xxxx,x,x,x,a,x,hhmmss.ss*hh<CR><LF>
 *        |    |    |    |     |    |    |    |     | |  |   |  |   | |   |      |
 *        1    2    3    4     5    6    7    8     9 10 11  12 13 14 15  17     19
 *
 * 1.  Sequence Number, 0 to 9
 * 2.  Region Northeast corner latitude
 * 3.  N/S - 'N' or 'S'
 * 4.  Region Northeast corner longitude
 * 5.  E/W - 'E' or 'W'
 * 6.  Region Southwest corner latitude
 * 7.  N/S - 'N' or 'S'
 * 8.  Region Southwest corner longitude
 * 9.  E/W - 'E' or 'W'
 * 10. Transition Zone Size
 * 11. Channel A
 * 12. Channel A bandwidth
 * 13. Channel B
 * 14. Channel B bandwidth
 * 15. Tx/Rx mode control
 * 16. Power level control
 * 17. Information source
 * 18. In-Use Flag
 * 19. Time of "in-use" change
 *
 ******************************************************************************/
// Define static member variables
int8_t CAca::m_nSequentialId = 0;

CAca::CAca() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAca::Parse(const char *pszSentence)
{
    char pstrSubData1[30], pstrSubData2[10];
    xROSDATA xRosData;
    xROADATA xRoaData;
    int nRosIndex;

    nRosIndex = ROS_IDX_NULL;

    CROSMgr::getInst()->ClearOneRosData(&xRosData, &xRoaData);

    xRosData.bRosSource = ROS_SRC_PI;
    xRosData.dSrcMMSI = AIS_AB_MMSI_NULL;

    // NE: Latitude (not null parameter?) - When Command is Address? Default ROS(null ?)
    CSentence::GetFieldString(pszSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pszSentence, 3, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        WARNING_LOG("ACA-in] ignore, NE latitude invalid\r\n");
        return false;
    }

    xRosData.xPointNE.xPosF.fLAT = CAisLib::GetLatitude(pstrSubData1, pstrSubData2[0]);

    // NE: Longitude (not null parameter?)
    CSentence::GetFieldString(pszSentence, 4, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pszSentence, 5, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        WARNING_LOG("ACA-in] ignore, NE longitude invalid\r\n");
        return false;
    }

    xRosData.xPointNE.xPosF.fLON = CAisLib::GetLongitude(pstrSubData1, pstrSubData2[0]);

    CAisLib::CalcGridLowPosByFLOAT(&xRosData.xPointNE);

    if(!CAisLib::IsValidAisGridPOS(&xRosData.xPointNE.xPosG))
    {
        WARNING_LOG("ACA-in] ignore, NE pos invalid, f : %.4f,%.4f -> g : %d,%d\r\n",
                xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT, xRosData.xPointNE.xPosG.nLON, xRosData.xPointNE.xPosG.nLAT);
        return false;
    }

    // SW: Latitude (not null parameter?)
    CSentence::GetFieldString(pszSentence, 6, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pszSentence, 7, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        WARNING_LOG("ACA-in] ignore, SW latitude invalid\r\n");
        return false;
    }

    xRosData.xPointSW.xPosF.fLAT = CAisLib::GetLatitude(pstrSubData1, pstrSubData2[0]);

    // SW: Longitude (not null parameter?)
    CSentence::GetFieldString(pszSentence, 8, pstrSubData1, sizeof(pstrSubData1));
    CSentence::GetFieldString(pszSentence, 9, pstrSubData2, sizeof(pstrSubData2));
    if(!strlen(pstrSubData1) || !strlen(pstrSubData2)) // Check NULL
    {
        WARNING_LOG("ACA-in] ignore, SW longitude invalid\r\n");
        return false;
    }

    xRosData.xPointSW.xPosF.fLON = CAisLib::GetLongitude(pstrSubData1, pstrSubData2[0]);

    CAisLib::CalcGridLowPosByFLOAT(&xRosData.xPointSW);
    if(!CAisLib::IsValidAisGridPOS(&xRosData.xPointSW.xPosG))
    {
        WARNING_LOG("ACA-in] ignore, SW pos invalid, f : %.4f,%.4f -> g : %d,%d\r\n",
                xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT, xRosData.xPointSW.xPosG.nLON, xRosData.xPointSW.xPosG.nLAT);
        return false;
    }

    // Transition Zone Size(1~8nm) - CalcROADataToROS()함수에서 bTrZoneSize을 쓰므로, 먼저 읽어온다.
    CSentence::GetFieldString(pszSentence, 10, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1))
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("ACA-in] ignore, invalid int num string, TRzone size [%s], ", pstrSubData1);
            return false;
        }
        xRosData.bTrZoneSize = atoi(pstrSubData1);                // ACA Sentence에 의한 bTrZoneSize은 1을 더하지 않는다.
    }
    else
        xRosData.bTrZoneSize = TRZONE_SIZE_DFLT;

    if(xRosData.bTrZoneSize < TRZONE_SIZE_MIN || xRosData.bTrZoneSize > TRZONE_SIZE_MAX)
    {
        WARNING_LOG("ACA-in] ignore, wrong TRzone size, %d\r\n", xRosData.bTrZoneSize);
        return false;
    }

    CROSMgr::getInst()->CalcOneRoaDataByROS(&xRosData, &xRoaData);

    // Channel, Bandwidth
    CSentence::GetFieldString(pszSentence, 11, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in] ignore, CH-A invalid\r\n");
        return false;
    }
    if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("ACA-in] invalid int num string, CH-A size [%s], ", pstrSubData1);
        return false;
    }
    xRosData.wChannelNoA = atoi(pstrSubData1);

    CSentence::GetFieldString(pszSentence, 12, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in] ignore, bandwidth-A invalid\r\n");
        return false;
    }
    if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("ACA-in] invalid int num string, Bandwidth-A [%s], ", pstrSubData1);
        return false;
    }

    xRosData.bBandwidthA = atoi(pstrSubData1);

    if(!CAisLib::IsValidBandwidth(xRosData.bBandwidthA))
    {
        WARNING_LOG("ACA-in] ignore, bandwidth pszSentence invalid, %d\r\n", xRosData.bBandwidthA);
        return false;
    }

    CSentence::GetFieldString(pszSentence, 13, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in] ignore, CH-B invalid\r\n");
        return false;
    }
    if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("ACA-in] invalid int num string, CH-B size [%s], ", pstrSubData1);
        return false;
    }

    xRosData.wChannelNoB = atoi(pstrSubData1);

    if(!CAisLib::IsValidBandwidth(xRosData.bBandwidthB))
    {
        WARNING_LOG("ACA-in] ignore, bandwidth pszSentence invalid, %d\r\n", xRosData.bBandwidthB);
        return false;
    }

    CSentence::GetFieldString(pszSentence, 14, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in] ignore, bandwidth-B invalid\r\n");
        return false;
    }
    if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("ACA-in] invalid int num string, Bandwidth-B [%s], ", pstrSubData1);
        return false;
    }

    xRosData.bBandwidthB = atoi(pstrSubData1);

    if(xRosData.bBandwidthA != CH_BW_25KHZ || xRosData.bBandwidthB != CH_BW_25KHZ)
    {
        WARNING_LOG("ACA-in] bandwidth pszSentence invalid, %d, %d\r\n", xRosData.bBandwidthA, xRosData.bBandwidthB);
        xRosData.bBandwidthA = CH_BW_25KHZ;
        xRosData.bBandwidthB = CH_BW_25KHZ;
    }

    // Tx/Rx mode control
    CSentence::GetFieldString(pszSentence, 15, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in] ignore, TRX mode invalid\r\n");
        return false;
    }

    if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("ACA-in] invalid int num string, TRX mode [%s], ", pstrSubData1);
        return false;
    }

    xRosData.bTxRxMode = atoi(pstrSubData1);

    if(xRosData.bTxRxMode < TRXMODE_MIN || xRosData.bTxRxMode > ACA_TRXMODE_MAX)
    {
        WARNING_LOG("ACA-in] ignore, TRX mode pszSentence, %d\r\n", xRosData.bTxRxMode);
        return false;
    }

    // Power level control
    CSentence::GetFieldString(pszSentence, 16, pstrSubData1, sizeof(pstrSubData1));
    if(!strlen(pstrSubData1))
    {
        WARNING_LOG("ACA-in] ignore, power invalid\r\n");
        return false;
    }

    if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
    {
        WARNING_LOG("ACA-in] invalid int num string, power [%s], ", pstrSubData1);
        return false;
    }

    xRosData.bTxPower = atoi(pstrSubData1);

    if(xRosData.bTxPower > AIS_TX_POWER_MAXVALUE)
    {
        WARNING_LOG("ACA-in] ignore, power pszSentence, %d\r\n", xRosData.bTxPower);
        return false;
    }

    // Set Current UTC.
    xRosData.bValidMode = MODE_VAL_ON;
    xRosData.xRcvTime   = cShip::getOwnShipInst()->xSysTime;
    xRosData.dwRcvSysSec= cTimerSys::getInst()->GetCurTimerSec();

    INFO_LOG("ACA-in] idx:%d(%d), from:%d, %09d, NE:%.2f,%.2f SW:%.2f,%.2f, pos:%.2f,%.2f, ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d\r\n",
        nRosIndex, CROSMgr::getInst()->IsRosInUse(nRosIndex),
        xRosData.bRosSource, xRosData.dSrcMMSI,
        xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT,
        xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT,
        cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
        xRosData.wChannelNoA, xRosData.wChannelNoB,
        xRosData.bTxRxMode, xRosData.bTxPower, xRosData.bTrZoneSize);

    CROSMgr::getInst()->UpdateRosData(&xRosData, nRosIndex);

    return true;
}

/**
 * @brief Make the ACA sentence
 * @param pszSentence The sentence to be made
 * @param psRosData The ROS data
 * @param nRosIndex The ROS index
 * @return The length of the sentence
 */
int32_t CAca::MakeSentence(char *pszSentence, xROSDATA *psRosData, int nRosIndex)
{
    char  temp[RX_MAX_DATA_SIZE];
    UINT   uPosDeg = 0;
    UINT   uPosMin = 0;
    UINT   uPosMinRem = 0;

    BOOL bInUse = CROSMgr::getInst()->IsRosInUse(nRosIndex);

    sprintf(pszSentence, "$AIACA,%01d,", GetSequentialId());

    if(CAisLib::IsValidAisGridPOS(&psRosData->xPointNE.xPosG) && CAisLib::IsValidAisGridPOS(&psRosData->xPointSW.xPosG))
    {
    	CAisLib::ConvertPosDataToDMM(psRosData->xPointNE.xPosG.nLAT, &uPosDeg, &uPosMin, &uPosMinRem, MIN_10000);
        if(psRosData->xPointNE.xPosG.nLAT < 0)
            sprintf(temp, "%02d%02d.%01d,S,", uPosDeg, uPosMin, uPosMinRem);
        else
            sprintf(temp, "%02d%02d.%01d,N,", uPosDeg, uPosMin, uPosMinRem);
        strcat(pszSentence, temp);

        CAisLib::ConvertPosDataToDMM(psRosData->xPointNE.xPosG.nLON, &uPosDeg, &uPosMin, &uPosMinRem, MIN_10000);
        if(psRosData->xPointNE.xPosG.nLON < 0)
            sprintf(temp, "%03d%02d.%01d,W,", uPosDeg, uPosMin, uPosMinRem);
        else
            sprintf(temp, "%03d%02d.%01d,E,", uPosDeg, uPosMin, uPosMinRem);
        strcat(pszSentence, temp);

        CAisLib::ConvertPosDataToDMM(psRosData->xPointSW.xPosG.nLAT, &uPosDeg, &uPosMin, &uPosMinRem, MIN_10000);
        if(psRosData->xPointSW.xPosG.nLAT < 0)
            sprintf(temp, "%02d%02d.%01d,S,", uPosDeg, uPosMin, uPosMinRem);
        else
            sprintf(temp, "%02d%02d.%01d,N,", uPosDeg, uPosMin, uPosMinRem);
        strcat(pszSentence, temp);

        CAisLib::ConvertPosDataToDMM(psRosData->xPointSW.xPosG.nLON, &uPosDeg, &uPosMin, &uPosMinRem, MIN_10000);
        if(psRosData->xPointSW.xPosG.nLON < 0)
            sprintf(temp, "%03d%02d.%01d,W,", uPosDeg, uPosMin, uPosMinRem);
        else
            sprintf(temp, "%03d%02d.%01d,E,", uPosDeg, uPosMin, uPosMinRem);
        strcat(pszSentence, temp);
    }
    else
        strcat(pszSentence, ",,,,,,,,");

    sprintf(temp, "%01d,%04d,%01d,%04d,%01d,%01d,%01d,%c,",
            psRosData->bTrZoneSize, psRosData->wChannelNoA, psRosData->bBandwidthA,
            psRosData->wChannelNoB, psRosData->bBandwidthB, psRosData->bTxRxMode,
            psRosData->bTxPower, CAisLib::GetROSSourceInfoToChar(psRosData->bRosSource));
    strcat(pszSentence, temp);

    if(bInUse)
    {
        sprintf(temp, "1,%02d%02d%02d", 
                CROSMgr::getInst()->m_xRosInUseTime.xTime.nHour, 
                CROSMgr::getInst()->m_xRosInUseTime.xTime.nMin, 
                CROSMgr::getInst()->m_xRosInUseTime.xTime.nSec);
        strcat(pszSentence, temp);
    }
    else
    {
        strcat(pszSentence, "0,");
    }

    CSentence::AddSentenceTail(pszSentence);

    // Increase the sequential message identifier
    //IncSequentialId();

    return strlen(pszSentence);
}




