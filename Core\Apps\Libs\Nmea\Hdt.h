/**
 * @file    Hdt.h
 * @brief   Hdt header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __HDT_H__
#define __HDT_H__

/******************************************************************************
 * 
 * HDT - 
 *
 * $--HDT, x.x,T*hh<CR><LF>
 *          |__|
 *          1
 * 
 * 1. Heading, degrees true
 * 
 ******************************************************************************/
class CHdt : public CSentence
{
public:
    CHdt();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidHeadingData(void);
    int  GetHeading(void);

    void RunPeriodically(void);

protected:
    int     m_nHdgData;
    DWORD   m_dwRcvTick;
};

#endif /* __HDT_H__ */

