/**
 * @file    Rot.h
 * @brief   Rot header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __ROT_H__
#define __ROT_H__

#define ROTSCR_NONE        -1
#define ROTSCR_TI           0       // Turn indicator talker ID "TI"
#define ROTSCR_NOT_TI       1       // Turn indicator talker ID is not "TI"
#define ROTSCR_HDG          2       // no turn indicator, ROT is calculated from heading

/******************************************************************************
 * 
 * ROT - 
 *
 * $--ROT, x.x,A*hh<CR><LF>
 *          |  |
 *          1  2
 * 
 * 1. Rate of turn, °/min, "-" = bow turns to port
 * 2. Status: A = data valid, V = data invalid
 * 
 ******************************************************************************/
class CRot : public CSentence
{
public:
    CRot();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidRotData(void);
    int  GetRotSrcType(void);
    int  GetRot(void);

    void RunPeriodically(void);

protected:
    int     m_nRotSrcType;
    int     m_nRotData;
    DWORD   m_dwRcvTick;
    DWORD   m_ndwLatencyROT;
};

#endif /* __ROT_H__ */

