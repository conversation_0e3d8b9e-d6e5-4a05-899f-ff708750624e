/**
 * @file    DscModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef __DSCMODEM_H__
#define __DSCMODEM_H__

#include <memory>
#include "SysConst.h"
#include "FskModem.h"

//-----------------------------------------------------------------------------
// DSC Format Specifier
//-----------------------------------------------------------------------------
#define DSC_FRMT_VTS_GEOGRAPHY            103
#define DSC_FRMT_INDIVIDUAL               120
#define DSC_FRMT_NONE                       0
//-----------------------------------------------------------------------------
#define DSC_CATEGORY_VTS                  103            // Category "information", a safety call related to VTS operation
#define DSC_CATEGORY_NONE                   0
//-----------------------------------------------------------------------------
#define DSC_EOS_CHR_117                   117
#define DSC_EOS_CHR_127                   127
//-----------------------------------------------------------------------------
#define DSC_MSGID_VTS_EXP                 104            // Symbols for Message contents of VTS DSC calls
//-----------------------------------------------------------------------------
#define DSC_EXP_TX_POWER                    1        // (followed by 1 symbol)
#define DSC_EXP_PRI_REGION_CH               9        // (followed by 3 symbols) - single-channel operation
#define DSC_EXP_SEC_REGION_CH              10        // (followed by 3 symbols) - Set Tx/Rx mode  (M.1371-3, p.58)
#define DSC_EXP_GUARD_REGION_CH            11        // (followed by 3 symbols) - Set only Rx Mode
#define DSC_EXP_NE_CORNER_REGION           12        // (followed by 6 symbols)
#define DSC_EXP_SW_CORNER_REGION           13        // (followed by 6 symbols)
//=============================================================================
#define DSC_CHAR_BIT_SIZE                  10
#define DSC_PHASE_CHAR_SIZE                16
#define DSC_PHASE_BIT_SIZE               (DSC_PHASE_CHAR_SIZE * DSC_CHAR_BIT_SIZE)
//=============================================================================
#define DSC_DX_RX_SIZE                    256
//=============================================================================
#define DSC_RX_STATUS_PHASING               0
#define DSC_RX_STATUS_RUNNING               1
//=============================================================================
#define DSC_RX_EOS_FOUND_NONE               0
#define DSC_RX_EOS_FOUND_DX                 1
#define DSC_RX_EOS_FOUND_RX                 2
//=============================================================================
#define DSC_MAX_DSC_EXP_MSG                 4
//=============================================================================
#define DSC_NEWS_NE                         0
#define DSC_NEWS_NW                         1
#define DSC_NEWS_SE                         2
#define DSC_NEWS_SW                         3
#define DSC_GROUP_BY_CRS                    4        // Group call by course
//=============================================================================
#define DSC_CMD_VALID_MASK         0x80000000
//=============================================================================

//=============================================================================
#define DSC_MAX_BUFF_SIZE                   4
//=============================================================================

//=============================================================================
typedef  struct _xDscCMD
{
    DWORD   dCmd;                                // DSC_CMD_VALID_MASK + (00 -- 14)
    UCHAR   vVal[12];
} xDscCMD;
//-----------------------------------------------------------------------------
typedef  struct _xDscPOS
{
    int   nGridLat;
    int   nGridLon;
} xDscPOS;
//-----------------------------------------------------------------------------
typedef  struct _xDscGeo
{
    xDscPOS xNE_Pos;
    xDscPOS xSW_Pos;
} xDscGeo;
//-----------------------------------------------------------------------------
typedef  struct _xDscAllMsg
{
    UCHAR    bFormat;

    DWORD    dTgtMMSI;                            // valid in case of (bFormat == DSC_FRMT_INDIVIDUAL)

    xDscGeo  xGeoData;                            // valid in case of (bFormat == DSC_FRMT_VTS_GEOGRAPHY)
    UINT16   uGroupCourse;                        // heading
    UINT8    uGroupShipType;

    UCHAR    bCategory;
    DWORD    dSrcMMSI;

    UINT8    uTxPower;                        // AIS_TX_POWER_HIGH..AIS_TX_POWER_LOW 
    UINT16   uPrimaryChID;
    UINT16   uPrimaryBandwidth;
    UINT16   uSecondaryChID;
    UINT16   uSecondaryBandwidth;
    UINT16   uGuardRegionChID;
    UINT16   uGuardRegionBandwidth;

    xDscPOS  sRosNE;
    xDscPOS  sRosSW;
} xDscAllMsg;
//=============================================================================

//=============================================================================
class cDscModem
{
public:
    cDscModem(std::shared_ptr<cFskModem> pFskModemP);
    virtual ~cDscModem(void);

public:
    int   CombineDscCharX(UCHAR *pRecv, HWORD *pDscChar);
    int   CombineDscCharY(UCHAR *pRecv, HWORD *pDscChar, int nStart, int nMaxSize);
    HWORD CalcDscEccValue(HWORD wData);
    int   IsDscDxCharacter(HWORD wChar);
    int   IsDscRxCharacter(HWORD wChar);
    int   CountDscFormat(HWORD wFormat, HWORD *pData, int nSize);
 
    void  ProcessRcvData();
    void  RunPhaseProcess(UCHAR nBitData);
    void  RunDataProcess(UCHAR nBitData);
 
    void  CheckEndReceiving(HWORD wDscChar);
    int   RunCommonEndChk(int nDxStart, int nOffsetZ);
 
    void  CheckRcvDscMessage(int nDxStart);
 
    void  ClearDscMsg(xDscAllMsg *psDscMsg);
    void  RcvIndividualMsg(UCHAR *pDscData, int nDscLen);
    void  RcvVtsGeogrphMsg(UCHAR *pDscData, int nDscLen);
    BOOL  RcvCommonDscMsg(xDscAllMsg *pDscMsg, UCHAR *pDscData, int nDscLen);
 
    DWORD GetDscMMSI(UCHAR *pDscData);
    int   GetDscGroupSpecifier(xDscAllMsg *psDscMsg, UCHAR *pDscData);
    BOOL  GetDscGeoData(xDscGeo *pGeoData, UCHAR *pDscData);
    int   GetDscGeoGridLat(UCHAR *pDscData, int nNEWS, int nUnit);
    int   GetDscGeoGridLon(UCHAR *pDscData, int nNEWS, int nUnit);
    int   GetDscGeoGridHori(UCHAR *pDscData);
    int   GetDscGeoGridVert(UCHAR *pDscData);
    HWORD GetDscWord(UCHAR *pDscData);
    void  GetDscCoordinateX(UCHAR *pDscData, xDscPOS *pDscPos);
 
    int   CheckGridLatRange(int nGridLat);
    int   CheckGridLonRange(int nGridLon);
 
    void  RunMessageReceivingEnd(void);

protected:
    std::shared_ptr<cFskModem> m_pFskModemP;
 
    int    m_nDscRxStatus;
    DWORD  m_dTotalRxBitCnt;
 
    int    m_nDscRxBitCnt;
    HWORD  m_wDscRxLastWord;
    HWORD  m_wDscRxLastChar;
 
    int    m_nEosFoundModeX;                        // 0=not-found, 1=DX, 2=RX
    int    m_nCharsAfterEOS;
    int    m_nEosFoundDxPos;
 
    int    m_nPhaseCntr;
    UCHAR  *m_vPhaseData;
    int    m_nPhaseIndx;
 
    int    m_nDxRxSize;
    HWORD  *m_vDxRxData;
 
    int    m_nDxSize;
    HWORD  *m_vDxData;
 
    int    m_nRxSize;
    HWORD  *m_vRxData;
 
    int    m_nRealSize;
    UCHAR  *m_vRealData;
 
    int    m_nRecvPacketP;
    UCHAR  *m_vRecvPacketD;
 
    UCHAR  m_bRecvFormatSpec;
};

#endif /*__DSCMODEM_H__*/
