#include "LayerNetwork.h"
#include "RosMgr.h"
#include "SetupMgr.h"
#include "LongRange.h"
#include "Ship.h"
#include "Timer.h"
#include "SysLog.h"
#include "ReportRateMgr.h"

CReportRateMgr::CReportRateMgr()
{
    Initialize();
}

CReportRateMgr::~CReportRateMgr()
{
}

/** 
 * @brief Initialize the report rate manager
 */
void CReportRateMgr::Initialize(void)
{
    m_fReportIntervalSec    = NOMINAL_REPORT_10SEC;
    m_fReportRate           = REPORT_RATE_DFLT;
    m_bReportRateDoubleMode = false;

    m_bFirstCalcRR          = true;
    m_bITDMA                = false;
    m_dwHdgDiffAboveTick    = 0;
}

/** 
 * @brief Set report rate double mode
 * @param bReportRateDoubleMode Report rate double mode
 * @return true if report rate double mode is changed, false otherwise
 */
bool CReportRateMgr::SetReportRateDoubleMode(bool bReportRateDoubleMode)
{
    //-----------------------------------------------------------------------------
    // ITU-R M.1371-5 4.1.5 Transitional mode operations near regional boundaries
    // Additionally, for multichannel operations as specified in § 4.1.2, 
    // except when the reporting interval has been assigned by Message 16, 
    // when operating in this mode, the reporting interval should be doubled 
    // and shared between the two channels (alternate transmission mode).

    bool bRet = (m_bReportRateDoubleMode != bReportRateDoubleMode);
    m_bReportRateDoubleMode = bReportRateDoubleMode;
    return bRet;
}

/** 
 * @brief Check if report rate is for SOTDMA
 * @param fReportIntervalSec Report interval in seconds
 * @return true if report rate is for SOTDMA, false otherwise
 */
bool CReportRateMgr::IsReportRateForSOTDMA(float fReportIntervalSec)
{
    // If report interval is over 30 seconds, it is for ITDMA,
    // otherwise it is for SOTDMA
    return (fReportIntervalSec <= NOMINAL_REPORT_30SEC);
}

/** 
 * @brief Check if report rate is for SOTDMA
 * @return true if report rate is for SOTDMA, false otherwise
 */
bool CReportRateMgr::IsReportRateForSOTDMA(void)
{
    return IsReportRateForSOTDMA(m_fReportIntervalSec);
}

/** 
 * @brief Get report interval for each channel
 * @param fNewReportIntervalTotal Total report interval
 * @param nNewTxChID1 Tx channel ID 1
 * @param nNewTxChID2 Tx channel ID 2
 * @param pfReportIntSecTotal Total report interval
 * @param pfReportIntSecCh1 Report interval for channel 1
 * @param pfReportIntSecCh2 Report interval for channel 2
 * @param puStaticIntSecCh1 Static report interval for channel 1
 * @param puStaticIntSecCh2 Static report interval for channel 2
 * @param puLongRangeIntSecCh1 Long range report interval for channel 1
 * @param puLongRangeIntSecCh2 Long range report interval for channel 2
 */
void CReportRateMgr::GetReportInterval( float fNewReportIntervalTotal, int nNewTxChID1, int nNewTxChID2, 
                                        float *pfReportIntSecTotal, float *pfReportIntSecCh1, float *pfReportIntSecCh2, 
                                        UINT16 *puStaticIntSecCh1, UINT16 *puStaticIntSecCh2, 
                                        UINT16 *puLongRangeIntSecCh1, UINT16 *puLongRangeIntSecCh2)
{
    *pfReportIntSecCh1 = 0;
    *pfReportIntSecCh2 = 0;

    *puStaticIntSecCh1 = 0;
    *puStaticIntSecCh2 = 0;

    // Tx on both channels
    if(nNewTxChID1 != AIS_CH_NUM_NONE && nNewTxChID2 != AIS_CH_NUM_NONE)
    {
        // Tx will be alternate on both channels
        fNewReportIntervalTotal = MAX(fNewReportIntervalTotal, NOMINAL_REPORT_MIN);
        *pfReportIntSecTotal = fNewReportIntervalTotal;

        *pfReportIntSecCh1 = *pfReportIntSecCh2 = fNewReportIntervalTotal * 2.0f;
        *puStaticIntSecCh1 = *puStaticIntSecCh2 = STATIC_REPORT_INTSEC * 2;
    }
    else
    {
        // Tx on only one channel
        // Tx on one channel have to transmit as report rate
        fNewReportIntervalTotal = MAX(fNewReportIntervalTotal, NOMINAL_REPORT_MIN);
        *pfReportIntSecTotal = fNewReportIntervalTotal;

        if(nNewTxChID1 != AIS_CH_NUM_NONE)
        {
            *pfReportIntSecCh1 = fNewReportIntervalTotal;
            *puStaticIntSecCh1 = STATIC_REPORT_INTSEC;
        }
        else if(nNewTxChID2 != AIS_CH_NUM_NONE)
        {
            *pfReportIntSecCh2= fNewReportIntervalTotal;
            *puStaticIntSecCh2 = STATIC_REPORT_INTSEC;
        }
    }

    // if report rate double mode, divide static report interval by 2
    if(m_bReportRateDoubleMode)
    {
        *puStaticIntSecCh1 = *puStaticIntSecCh1 >> 1;
        *puStaticIntSecCh2 = *puStaticIntSecCh2 >> 1;
    }

    // Long range should always transmit every 3 minutes on two channels (CH 75, 76)!
    *puLongRangeIntSecCh1 = *puLongRangeIntSecCh2 = LR_REPORT_INTERVAL_SEC * 2;   
}

/** 
 * @brief Calculate report interval
 * @param fSecRI Report interval in seconds
 */
void CReportRateMgr::SetReportInterval(float fSecRI)
{
    float fReportIntSecCh1, fReportIntSecCh2;
    UINT16 uStaticIntSecCh1, uStaticIntSecCh2;
    UINT16 uLongRangeIntSecCh1, uLongRangeIntSecCh2;

    // Get report interval for each channel
    GetReportInterval(fSecRI, CLayerNetwork::getInst()->GetChPrimary()->GetTxChannelNumber(), CLayerNetwork::getInst()->GetChSecondary()->GetTxChannelNumber(), 
                      &m_fReportIntervalSec, &fReportIntSecCh1, &fReportIntSecCh2, &uStaticIntSecCh1, &uStaticIntSecCh2, &uLongRangeIntSecCh1, &uLongRangeIntSecCh2);

    DEBUG_LOG("SetReportInterval] setRI:%.1f, double:%d, RI : %.1f, chRI : %.2f,%.2f, staticRI : %d,%d, LRRI : %d,%d\r\n",
            fSecRI, m_bReportRateDoubleMode, m_fReportIntervalSec, 
            fReportIntSecCh1, fReportIntSecCh2, uStaticIntSecCh1, uStaticIntSecCh2, uLongRangeIntSecCh1, uLongRangeIntSecCh2);

    m_fReportRate = 60.0f / m_fReportIntervalSec;
    int nTotalNI = CAisLib::GetNIfromRR(m_fReportRate);

    // Set report interval for each channel
    CLayerNetwork::getInst()->GetChPrimary()->SetChReportInterval(nTotalNI, fReportIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetChReportInterval(nTotalNI, fReportIntSecCh2);

    // static report interval
    bool bStaticRRchg = (uStaticIntSecCh1 != CLayerNetwork::getInst()->GetChPrimary()->m_dwStaticReportIntervalSec ||
                        uStaticIntSecCh2 != CLayerNetwork::getInst()->GetChSecondary()->m_dwStaticReportIntervalSec);
    CLayerNetwork::getInst()->GetChPrimary()->SetStaticReportIntervalSec(uStaticIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetStaticReportIntervalSec(uStaticIntSecCh2);

    if(bStaticRRchg)
        CLayerNetwork::getInst()->InitStaticReportSec();

    // long range report interval
    bool bLongRangeRRchg = (uLongRangeIntSecCh1 != CLayerNetwork::getInst()->GetChPrimary()->m_dwLongRangeReportIntervalSec ||
                            uLongRangeIntSecCh2 != CLayerNetwork::getInst()->GetChSecondary()->m_dwLongRangeReportIntervalSec);
    CLayerNetwork::getInst()->GetChPrimary()->SetLongRangeReportInterval(uLongRangeIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetLongRangeReportInterval(uLongRangeIntSecCh2);

    if(bLongRangeRRchg)
        CLayerNetwork::getInst()->InitLongRangeReportSec();

    DEBUG_LOG("SetReportInterval] RI : %.2f, chRR : %.2f(%.2f), chRI : %.2f(%.2f), staticRI : %d(%d)\r\n",
            m_fReportIntervalSec,
            CLayerNetwork::getInst()->GetChPrimary()->m_fChReportRate, CLayerNetwork::getInst()->GetChSecondary()->m_fChReportRate,
            CLayerNetwork::getInst()->GetChPrimary()->m_fChReportIntervalSec, CLayerNetwork::getInst()->GetChSecondary()->m_fChReportIntervalSec,
            uStaticIntSecCh1, uStaticIntSecCh2);

    m_bFirstCalcRR = false;
}

/** 
 * @brief Set new report interval
 * @param fNewReportIntervalSecSO New report interval in seconds
 * @param nNewTxChID1 New Tx channel ID 1
 * @param nNewTxChID2 New Tx channel ID 2
 */
void CReportRateMgr::SetReportInterval(float fNewReportIntervalSecSO, int nNewTxChID1, int nNewTxChID2)
{
    float fReportIntSecCh1, fReportIntSecCh2;
    UINT16 uStaticIntSecCh1, uStaticIntSecCh2;
    UINT16 uLongRangeIntSecCh1, uLongRangeIntSecCh2;

    // Get report interval for each channel
    GetReportInterval(fNewReportIntervalSecSO, nNewTxChID1, nNewTxChID2,
                      &m_fReportIntervalSec, &fReportIntSecCh1, &fReportIntSecCh2, &uStaticIntSecCh1, &uStaticIntSecCh2, &uLongRangeIntSecCh1, &uLongRangeIntSecCh2);

    m_fReportRate = 60.0f / m_fReportIntervalSec;

    // Set new report interval for each channel
    CLayerNetwork::getInst()->GetChPrimary()->SetChNewReportIntervalSec(fReportIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetChNewReportIntervalSec(fReportIntSecCh2);

    // static report interval for each channel
    CLayerNetwork::getInst()->GetChPrimary()->SetStaticReportIntervalSec(uStaticIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetStaticReportIntervalSec(uStaticIntSecCh2);

    // long range report interval for each channel
    CLayerNetwork::getInst()->GetChPrimary()->SetLongRangeReportInterval(uLongRangeIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetLongRangeReportInterval(uLongRangeIntSecCh2);

    DEBUG_LOG("SetNewReportInt] RI : %.2f, txCH : %d(%d), chRI : %.2f(%.2f), staticRI : %d(%d), LRRI : %d(%d)\r\n",
            m_fReportIntervalSec, nNewTxChID1, nNewTxChID2, fReportIntSecCh1, fReportIntSecCh2, 
            uStaticIntSecCh1, uStaticIntSecCh2, uLongRangeIntSecCh1, uLongRangeIntSecCh2);
}

/** 
 * @brief Get report interval in seconds
 * @return Report interval in seconds
 */
float CReportRateMgr::GetReportIntervalSec(void)
{
    return m_fReportIntervalSec;
}

/** 
 * @brief Get report interval by speed
 * @return Report interval in seconds
 */
float CReportRateMgr::GetReportIntSecBySpeed(void)
{
    const int nSOG = cShip::getOwnShipInst()->xDynamicData.nSOG;

    //-----------------------------------------------------------------------------
    // ITU-R 1371-5 Ann.2 4.2.1 Reporting interval (TABLE1)
    // Ship at anchor or moored and not moving faster than 3 knots      3 min(1)
    // Ship at anchor or moored and moving faster than 3 knots          10 s(1)
    // Ship 0-14 knots                                                  10 s(1)
    // Ship 0-14 knots and changing course                              3 1/3 s(1)
    // Ship 14-23 knots                                                 6 s(1)
    // Ship 14-23 knots and changing course                             2 s
    // Ship > 23 knots                                                  2 s
    // Ship > 23 knots and changing course                              2 s
    if (CSetupMgr::getInst()->IsAisAClass())
    {
        // SOG is not available
        if(nSOG == NMEA_SOG_NULL)
        {
            // if NavStatus is Anchor/Moored, SOG is not available
            if(IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus))
                return  NOMINAL_REPORT_180SEC;  // 3 min

            return NOMINAL_REPORT_10SEC;        // 10 sec
        }

        // if NavStatus is Anchor/Moored, speed is not moving faster than 3 knots
        if (   IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus) 
            && cShip::getOwnShipInst()->xDynamicData.nSOG <= SOG_THRESHOLD_DONTMOVE)
        {
            return NOMINAL_REPORT_180SEC;       // 3 min
        }

        // if NavStatus is Anchor/Moored, speed is moving faster than 3 knots
        if (   IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus) 
            && cShip::getOwnShipInst()->xDynamicData.nSOG > SOG_THRESHOLD_DONTMOVE)
        {
            return NOMINAL_REPORT_10SEC;        // 10 sec
        }

        if(nSOG < 140)
            return NOMINAL_REPORT_10SEC;        // below 14 knots, 10 sec
        if(nSOG < 230)
            return NOMINAL_REPORT_6SEC;         // below 23 knots, 6 sec

        return NOMINAL_REPORT_2SEC;             // elsewhere, 2 sec
    }

    //-----------------------------------------------------------------------------
    // ITU-R 1371-5 Ann.2 4.2.1 Reporting interval (TABLE2)
    // Class B “SO” shipborne mobile equipment not moving faster than 2 knots   3 min
    // Class B “SO” shipborne mobile equipment moving 2−14 knots                30 s
    // Class B “SO” shipborne mobile equipment moving 14−23 knots               15 s
    // Class B “SO” shipborne mobile equipment moving >23 knots                 5 s
    if(nSOG == NMEA_SOG_NULL || nSOG < 20)  // below 2 knots, 3 min
        return NOMINAL_REPORT_180SEC;
    if(nSOG < 140)                          // below 14 knots, 30 sec
        return NOMINAL_REPORT_30SEC;
    if(nSOG < 230)                          // below 23 knots, 15 sec
        return NOMINAL_REPORT_15SEC;
    return NOMINAL_REPORT_5SEC;             // elsewhere, 5 sec
}

/** 
 * @brief Get next shorter report interval
 * @return Next shorter report interval in seconds
 */
float CReportRateMgr::GetReportIntSecNextShorter(void)
{
    if (CSetupMgr::getInst()->IsAisAClass())
    {
        if(m_fReportIntervalSec <= NOMINAL_REPORT_6SEC)
            return NOMINAL_REPORT_2SEC; // 2 sec
        if(m_fReportIntervalSec <= NOMINAL_REPORT_10SEC)
            return NOMINAL_REPORT_6SEC; // 6 sec
        return NOMINAL_REPORT_10SEC;    // 10 sec
    }

    // Class B
    if(m_fReportIntervalSec <= NOMINAL_REPORT_15SEC)
        return NOMINAL_REPORT_5SEC;     // 5 sec
    if(m_fReportIntervalSec <= NOMINAL_REPORT_30SEC)
        return NOMINAL_REPORT_15SEC;    // 15 sec
    return NOMINAL_REPORT_30SEC;        // 10 sec
}

/** 
 * @brief Get next longer report interval
 * @return Next longer report interval in seconds
 */
float CReportRateMgr::GetReportIntSecNextLonger(void)
{
    if (CSetupMgr::getInst()->IsAisAClass())
    {
        if(m_fReportIntervalSec <= NOMINAL_REPORT_2SEC)
            return NOMINAL_REPORT_6SEC; // 6 sec
        if(m_fReportIntervalSec <= NOMINAL_REPORT_6SEC)
            return NOMINAL_REPORT_10SEC;// 10 sec
        return NOMINAL_REPORT_180SEC;   // 3 min
    }

    // Class B
    if(m_fReportIntervalSec <= NOMINAL_REPORT_5SEC)
        return NOMINAL_REPORT_15SEC;    // 15 sec
    if(m_fReportIntervalSec <= NOMINAL_REPORT_15SEC)
        return NOMINAL_REPORT_30SEC;    // 30 sec
    return NOMINAL_REPORT_180SEC;       // 3 min
}

/** 
 * @brief Get report rate
 * @return Report rate
 */
float CReportRateMgr::GetReportRate(void)
{
    return m_fReportRate;
}

/** 
 * @brief Check if report interval is valid
 * @param fReportIntervalSec Report interval in seconds
 * @return true if report interval is valid, false otherwise
 */
bool CReportRateMgr::IsRIValueValid(float fReportIntervalSec)
{
    return ((NOMINAL_REPORT_MIN <= fReportIntervalSec) && (fReportIntervalSec <= NOMINAL_REPORT_MAX));
}

bool CReportRateMgr::IsITDMAEnabled(void)
{
    return m_bITDMA;
}

/** 
 * @brief Check if current report rate is valid
 * @return true if current report rate is valid, false otherwise
 */
bool CReportRateMgr::CheckCurRRValid(void)
{
    return ((m_fReportRate > 0) && IsRIValueValid(m_fReportIntervalSec));
}

/** 
 * @brief Set report rate by ITDMA
 * @param bOn true to enable, false to disable
 * @return true if successful, false otherwise
 */
bool CReportRateMgr::SetReportRateByITDMA(bool bOn)
{
    // if already in the desired state, return false
    if (m_bITDMA == bOn)
        return false;

    if (bOn)
    {
        //------------------------------------------------------------
        // ITU-R 1371-5 Ann.2 4.2.1 Reporting interval (TABLE1)
        // Ship 0-14 knots and changing course (10 sec => 3 1/3 sec)
        // Ship 14-23 knots and changing course (6 sec => 2 sec)
        if (m_fReportIntervalSec > NOMINAL_REPORT_2SEC)
        {
            CLayerNetwork::getInst()->GetChPrimary()->SetReportRateByITDMA(true);
            CLayerNetwork::getInst()->GetChSecondary()->SetReportRateByITDMA(true);
        }
    }
    else
    {
        // Restore report rate
        CLayerNetwork::getInst()->GetChPrimary()->SetReportRateByITDMA(false);
        CLayerNetwork::getInst()->GetChSecondary()->SetReportRateByITDMA(false);
    }

    m_bITDMA = bOn;
    return true;
}

/** 
 * @brief Check auto mode report interval
 * @param bCheckSpdReduce Check speed reduce
 * @param bDoChangeRR Do change report rate
 * @return Report interval in seconds
 */
float CReportRateMgr::CheckAutoModeReportInterval(bool bCheckSpdReduce, bool bDoChangeRR)
{
    static float fSpeedReduceIntSec = 0;
    static DWORD dwSpeedReduceStartSec = 0;
    static float fOldReportIntSecBySpeed = 0;

    float fReportIntSecBySpeed = 0;
    float fFinalRIsecSO = -1;

    // if Ownship semaphore mode, report interval is 2 seconds
    if(CSyncMgr::getInst()->IsOwnShipSemaphoreMode())
    {
        fFinalRIsecSO = 2;
    }
    else
    {
        //----------------------------------------------------------------------------------------------------
        // ITU-R 1371-5 Ann.2 4.3 Changing reporting interval
        // ITU-R 1371-5 Ann.2 *******
        // When an increase in speed results in a higher Rr (see Tables 1 and 2 in Annex 1) than 
        // the currently used Rr, the station should increase the Rr using the algorithm described in § 3.3.5. 
        // When a station has maintained a speed, which should result in an Rr lower than the currently used Rr, 
        // the station should reduce Rr when this state has persisted for three (3) min.
        //----------------------------------------------------------------------------------------------------
        // Ivan test report p.136 IEC-61993-2 ********
        // Ivan test report p.141 IEC-61993-2 ********
        // Ivan test report p.305 IEC-61993-2 ********.1
        // Ivan test report p.628 IEC-61993-2 16.6.7.9.1 TT16-6-7-9-Ed2.scn
        //----------------------------------------------------------------------------------------------------
        fReportIntSecBySpeed = GetReportIntSecBySpeed();
        fFinalRIsecSO = fReportIntSecBySpeed;

        if (bCheckSpdReduce)
        {
            // Increase Reporting Interval when speed is reduced
            if (fReportIntSecBySpeed > m_fReportIntervalSec)
            {
                if (fReportIntSecBySpeed > fOldReportIntSecBySpeed)
                {
                    fSpeedReduceIntSec = fReportIntSecBySpeed;
                    // Save the time when the station starts to increase the reporting interval.
                    dwSpeedReduceStartSec= cTimerSys::getInst()->GetCurTimerSec();
                }

                if (fSpeedReduceIntSec > 0 && fReportIntSecBySpeed == fSpeedReduceIntSec)
                {
                    // When a station has maintained a speed, which should result in an Rr lower than the currently used Rr, 
                    // the station should reduce Rr when this state has persisted for three (3) min.
                    if (cTimerSys::getInst()->GetTimeDiffSec(dwSpeedReduceStartSec) < 180)
                    {
                        // keep reporting interval until 3 min after the reporting interval has been increased
                        fFinalRIsecSO = m_fReportIntervalSec;
                    }
                }
            }
            // Decrease Reporting Interval when speed is increased
            else if (fReportIntSecBySpeed < m_fReportIntervalSec)
            {
                fSpeedReduceIntSec = 0;
            }
        }

        //-------------------------------------------------------------------------------------------------------
        // ITU-R 1371-5 Ann.2 ******* Changing course (applicable to Class A shipborne mobile equipment, only)
        // The higher Rr should be maintained by using ITDMA to complement SOTDMA scheduled
        // transmissions in order to derive the desired Rr. When 5° is exceeded, the reporting interval should
        // be decreased beginning with a broadcast within the next 150 slots (see § *******.1) using either
        // a scheduled SOTDMA slot, or a RATDMA access slot (see § *******).
        if (CSetupMgr::getInst()->IsAisAClass() && IsReportRateForSOTDMA())
        {
            bool bCourseExceed5 =  (cShip::getOwnShipInst()->xDynamicData.nHDG != NMEA_HDG_NULL)
                                    && (cShip::getOwnShipInst()->xDynamicData.fHdgAvg != NMEA_HDG_NULL)
                                    && (CAisLib::GetDiffAbsYaw(cShip::getOwnShipInst()->xDynamicData.fHdgAvg, 
                                        cShip::getOwnShipInst()->xDynamicData.nHDG) >= 5);

            if (bCourseExceed5)
            {
                // Decrease report rate about 30 percent
                SetReportRateByITDMA(true);
                m_dwHdgDiffAboveTick = SysGetSystemTimer();
            }
            else
            {
                //-----------------------------------------------------------------------------------------------------
                // ITU-R 1371-5 Ann.2 ******* Changing course (applicable to Class A shipborne mobile equipment, only)
                // The increased Rr should be maintained until the difference between the mean value of heading 
                // and present heading has been less than 5s for more than 20s.
                if (m_bITDMA)
                {
                    // if HDG is invalid, stop temporary use of ITDMA
                    if (cShip::getOwnShipInst()->xDynamicData.nHDG == NMEA_HDG_NULL ||
                        SysGetDiffTimeScnd(m_dwHdgDiffAboveTick) > 20)
                    {
                        // Restore report rate
                        SetReportRateByITDMA(false);
                    }
                }
            }
        }
    }

    //---------------------------------------------------------------------------
    // ITU-R M.1371-5 4.1.5 Transitional mode operations near regional boundaries
    // Additionally, for multichannel operations as specified in § 4.1.2, 
    // except when the reporting interval has been assigned by Message 16, 
    // when operating in this mode, the reporting interval should be doubled 
    // and shared between the two channels (alternate transmission mode).
    if(m_bReportRateDoubleMode)
        fFinalRIsecSO /= 2;

    // backup report interval by speed
    fOldReportIntSecBySpeed = fReportIntSecBySpeed;

    if(bDoChangeRR && fFinalRIsecSO > 0 && (!CheckCurRRValid() || fFinalRIsecSO != m_fReportIntervalSec))
    {
        if(CLayerNetwork::getInst()->CheckAssignedModeRunning())
        {
            //-----------------------------------------------------------------------------------------
            // IEC-61993-2 14.3.2.3.2
            // The EUT shall revert to Message 1 or 3 in autonomous mode with the autonomous reporting interval 
            // - after a period of 4 min to 8 min, or 
            // - if a change of course, speed and NavStatus require a shorter autonomous reporting interval.
            if(fFinalRIsecSO < m_fReportIntervalSec)
            {
                CLayerNetwork::getInst()->RunReturnToAutoMode();
            }
        }
        else
        {
            if(CLayerNetwork::getInst()->IsChangingReportRateAvailable())
            {
                // Change Report Rate
                CLayerNetwork::getInst()->RunPhaseChangeReportRate( CROSMgr::getInst()->m_sChSetup.nTxRxMode, 
                                                                    CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, 
                                                                    CROSMgr::getInst()->m_sChSetup.uChannelIdA, 
                                                                    CROSMgr::getInst()->m_sChSetup.uChannelIdB, 
                                                                    fFinalRIsecSO, 
                                                                    CLayerNetwork::getInst()->m_nOpMode, 
                                                                    CLayerNetwork::getInst()->m_nOpMode, 
                                                                    CLayerNetwork::getInst()->m_bAssignedModeBy);
            }
        }
    }

    return fFinalRIsecSO;
}

/** 
 * @brief Run periodically report rate manager
 */
void CReportRateMgr::RunPeriodicallyReportRateMgr(void)
{
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > 1)
    {
        bool bCheckSpdReduce= (CLayerNetwork::getInst()->m_nOpPhase == OPPHASE_ROUTINE);
        bool bDoChangeRR    = (CLayerNetwork::getInst()->m_nOpPhase == OPPHASE_ROUTINE);
        CheckAutoModeReportInterval(bCheckSpdReduce, bDoChangeRR);

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}