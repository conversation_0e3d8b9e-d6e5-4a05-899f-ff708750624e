/**
 * @file    Ack.h
 * @brief   Ack header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ACK_H__
#define __ACK_H__

/******************************************************************************
 * 
 * ACK - Acknowledge alarm
 *
 * $--ACK,xxx*hh<CR><LF>
 *        |
 *        1
 *
 * 1. Unique alarm number (identifier) at alarm source
 *
 ******************************************************************************/
class CAck : public CSentence
{
public:
    CAck();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the ACK sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

    /**
     * @brief Get the alarm number
     * @return The alarm number
     */
    static int32_t GetAlarmNum(void) { return m_nAlarmNum; }

public:
    static int32_t m_nAlarmNum;
};

#endif /* __ACK_H__ */

