/**
 * @file    Alr.h
 * @brief   Alr header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ALR_H__
#define __ALR_H__

/******************************************************************************
 * 
 * ALR - Set alarm state
 *
 * $--ALR,hhmmss.ss,xxx,A,A,c--c*hh<CR><LF>
 *         |         |  | |   |
 *         1         2  3 4   5
 *
 * 1. Time of alarm condition change, UTC
 * 2. Local alarm number(identifier)[identification number of alarm source]
 * 3. Alarm condition (A=threshold exceeded, V=not exceeded)
 * 4. <PERSON><PERSON>'s acknowledge state, A=acknowledged V=unacknowledge
 * 5. <PERSON><PERSON>'s description text
 *
 ******************************************************************************/
class CAlr : public CSentence
{
public:
    CAlr();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the ALR sentence
     * @param pszSentence The sentence to be made
     * @param nAlrID The alarm ID
     * @param pAlrTime The alarm time
     * @param nAlrStat The alarm status
     * @param pstrAlarmTxt The alarm text
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, int nAlrID, SYS_DATE_TIME *pAlrTime, _tagBAMAlertStat nAlrStat, char *pstrAlarmTxt);

    /**
     * @brief Make the ALR sentence
     * @param pszSentence The sentence to be made
     * @param pAlarmThing The alarm thing structure
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, CAlarmThing *pAlarmThing);
};

#endif /* __ALR_H__ */

