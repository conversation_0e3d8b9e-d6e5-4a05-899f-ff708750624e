#include <stdlib.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Timer.h"
#include "Ship.h"
#include "ReportRateMgr.h"
#include "VdlRxMgr.h"
#include "AisModem.h"
#include "UserDirMgr.h"
#include "ChannelMgr.h"
#include "LayerNetwork.h"
#include "SetupMgr.h"
#include "SlotMgr.h"
#include "SensorMgr.h"
#include "FrameMapMgr.h"

FRAMEMAP_SLOTDATA *CFrameMapMgr::m_pMsgMapOneFrame = NULL;

CFrameMapMgr::CFrameMapMgr(CChannelMgr *pChannel)
{
    m_pChannel =  pChannel;
    m_pFrameMap = (FRAMEMAP_SLOTDATA*)SysAllocMemory(sizeof(FRAMEMAP_SLOTDATA) * NUM_SLOT_FRAMEMAP);

    if(!m_pMsgMapOneFrame)
    {
        m_pMsgMapOneFrame = (FRAMEMAP_SLOTDATA*)SysAllocMemory(sizeof(FRAMEMAP_SLOTDATA) * NUM_SLOT_PER_FRAME);
    }

    m_nFrameMapStartSlotID = 0;

    ClearFrameMap();
}

CFrameMapMgr::~CFrameMapMgr()
{
}

/**
 * @brief Clear frame map
 */
void CFrameMapMgr::ClearFrameMap(void)
{
    memset(m_pFrameMap, 0, sizeof(FRAMEMAP_SLOTDATA) * NUM_SLOT_FRAMEMAP);
}

/**
 * @brief Shift slot id in frame map
 * @param nShiftOffset Number of slots to shift
 */
void CFrameMapMgr::ShiftFrameMap(INT16 nShiftOffset)
{
    int nOldStartSlotID = m_nFrameMapStartSlotID;

    m_nFrameMapStartSlotID -= nShiftOffset;
    if(abs(m_nFrameMapStartSlotID) >= NUM_SLOT_FRAMEMAP)
    {
        m_nFrameMapStartSlotID %= NUM_SLOT_FRAMEMAP;
    }

    DEBUG_LOG("ShiftFrameMap] shift : %d, offset : %d -> %d\r\n", nShiftOffset, nOldStartSlotID, m_nFrameMapStartSlotID);
}

/**
 * @brief Get shifted slot id in frame map
 * @param wMapSlotID Slot id in frame map
 * @return Shifted slot id in frame map
 */
WORD CFrameMapMgr::GetShiftedMapSlotID(WORD wMapSlotID)
{
    return FrameMapSlotIdAdd(wMapSlotID, m_nFrameMapStartSlotID);
}

/**
 * @brief Get shifted slot id in frame map
 * @param wFrameID Frame id in frame map
 * @param wSlotID Slot id in frame map
 * @return Shifted slot id in frame map
 */
WORD CFrameMapMgr::GetShiftedMapSlotID(WORD wFrameID, WORD wSlotID)
{
    WORD wMapSlotID = GetFrameMapSlotID(wFrameID, wSlotID);
    return GetShiftedMapSlotID(wMapSlotID);
}

/**
 * @brief Get slot id from frame slot id
 * @param wMapSlotID Slot id in frame map
 * @return Slot id in frame map
 */
WORD CFrameMapMgr::GetSlotIdFromFrameSlotID(WORD wMapSlotID)
{
    if(wMapSlotID == SLOTID_NONE)
        return SLOTID_NONE;

    return (wMapSlotID % NUM_SLOT_PER_FRAME);
}

/**
 * @brief Get slot data pointer from frame slot id
 * @param wFrSlotID Frame slot id
 * @return Slot data pointer
 */
FRAMEMAP_SLOTDATA* CFrameMapMgr::GetSlotDataPtr(WORD wFrSlotID)
{
    WORD wMapSlot = SLOTID_NONE;
    if(wFrSlotID != SLOTID_NONE)
    {
        wMapSlot = GetShiftedMapSlotID(wFrSlotID);

        if(CheckValidFrameMapSlotID(wMapSlot))
            return &m_pFrameMap[wMapSlot];
    }

    return NULL;
}

/**
 * @brief Check if slot is internal allocated
 * @param pIntSlot Slot data pointer
 * @return true if internal allocated, false otherwise
 */
bool CFrameMapMgr::CheckSlotIntAlloc(FRAMEMAP_SLOTDATA *pIntSlot)
{
    return (cShip::getOwnShipInst()->IsOwnShipMMSISet() && pIntSlot->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI());
}

/**
 * @brief Check if slot is internal allocated
 * @param wFrSlotID Frame slot id
 * @return true if internal allocated, false otherwise
 */
bool CFrameMapMgr::CheckSlotIntAlloc(WORD wFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(!pSlotPtr)
        return false;

    return CheckSlotIntAlloc(pSlotPtr);
}

/**
 * @brief Check if slot is external allocated
 * @param pSlotPtr Slot data pointer
 * @return true if external allocated, false otherwise
 */
bool CFrameMapMgr::CheckSlotExtAlloc(FRAMEMAP_SLOTDATA *pSlotPtr)
{
    return (pSlotPtr->uMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI != cShip::getOwnShipInst()->GetOwnShipMMSI());
}

/**
 * @brief Check if slot is external allocated
 * @param wFrSlotID Frame slot id
 * @return true if external allocated, false otherwise
 */
bool CFrameMapMgr::CheckSlotExtAlloc(WORD wFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(!pSlotPtr)
        return false;

    return CheckSlotExtAlloc(pSlotPtr);
}

/**
 * @brief Change MMSI in frame map
 * @param uNewMMSI New MMSI
 */
void CFrameMapMgr::ChangeMMSIInFrameMap(UINT uNewMMSI)
{
    if(cShip::getOwnShipInst()->IsOwnShipMMSISet())
    {
        for(int i = 0 ; i <= MAX_SLOT_FRAMEMAP ; i++)
        {
            if(m_pFrameMap[i].uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())
                m_pFrameMap[i].uMMSI = uNewMMSI;
        }
    }
}

/**
 * @brief Set frame map slot data
 * @param bCheckProtect Check if slot is protected
 * @param wFrSlotID Frame slot id
 * @param nNumSlots Number of slots
 * @param uMMSI MMSI
 * @param bStatus Slot status
 * @param nSlotTimeOut Slot time out
 * @param bAccessScheme Access scheme
 * @param bCommStateScheme Communication state scheme
 * @param uMsgID Message id
 * @param bStartSlot Start slot
 * @param uSlotOffset Slot offset
 * @param bItdmaKeepFlag ITDMA keep flag
 * @param bProtectAssignedModeSlot Protect assigned mode slot
 * @param nDataIdx Data index
 * @param bFAtoSO FA to SO flag
 * @param wTxChNum Tx channel number
 */
void CFrameMapMgr::SetFrameMapSlotData( bool bCheckProtect, WORD wFrSlotID, INT8 nNumSlots, UINT uMMSI, 
                                        BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                        UINT16 uMsgID, bool bStartSlot, UINT uSlotOffset, bool bItdmaKeepFlag, 
                                        bool bProtectAssignedModeSlot, BYTE nDataIdx, bool bFAtoSO, WORD wTxChNum)
{
    if(wFrSlotID == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(!pSlotPtr)
        return;

    WORD wSlotID = GetSlotIdFromFrameSlotID(wFrSlotID);

    if(wTxChNum == AIS_CH_NUM_NONE)
        wTxChNum = m_pChannel->GetTxChannelNumber();

    if(bCheckProtect)
    {
        if (   uMMSI != AIS_AB_MMSI_NULL && uMMSI != pSlotPtr->uMMSI 
            && cShip::getOwnShipInst()->IsOwnShipMMSISet() 
            && pSlotPtr->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())
        {
            if(    uMsgID == AIS_MSG_NO_ID_20 
                && IsValidMMSI_BaseSt(uMMSI) 
                && (CUserDirMgr::getInst()->IsBaseStationWithin120NM(uMMSI) || CSensorMgr::getInst()->IsGnssLost()))
            {
                WARNING_LOG("SetMap] Error! Own Slot is stolen by BS,Msg-20, S: %d(%d), %09d -> %09d, M : %d, TMO : %d -> %d, s:%d\r\n",
                    wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, pSlotPtr->nSlotTimeOut, nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
            }
            else
            {
                WARNING_LOG("SetMap] WARN! Own Slot is about to be stolen but ignored, S: %d(%d), %09d -> %09d, M : %d -> %d, TMO : %d, s:%d\r\n",
                    wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, uMsgID, pSlotPtr->nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
                return;
            }
        }

        if(    uMMSI != AIS_AB_MMSI_NULL 
            && uMMSI != pSlotPtr->uMMSI 
            && IsValidMMSI_BaseSt(pSlotPtr->uMMSI) 
            && pSlotPtr->bCommStateScheme == TDMA_FATDMA)
        {
            ERROR_LOG("SetMap] Error! FATDMA slot is about to overwitten by other BaseST but ignore, S: %d(%d), %09d -> %09d, M : %d -> %d, TMO : %d, s:%d\r\n",
                wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, uMsgID, pSlotPtr->nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
            return;
        }
    }

    if ((pSlotPtr->uMsgID == AIS_MSG_NO_ID_20 || pSlotPtr->bFAtoSO) 
        && uMMSI != AIS_AB_MMSI_NULL 
        && uMMSI != pSlotPtr->uMMSI 
        && cShip::getOwnShipInst()->IsOwnShipMMSISet()
        && uMMSI != cShip::getOwnShipInst()->GetOwnShipMMSI() 
        && pSlotPtr->uMMSI != cShip::getOwnShipInst()->GetOwnShipMMSI())
    {
        ERROR_LOG("SetMap] xxxxxxxxxxxxxxxxxxxx Error! MSG-20 or FAtoSO slot is about to overwitten but ignore, S: %d(%d), %09d -> %09d, M : %d -> %d, FA: %d -> %d, TMO : %d -> %d, s:%d\r\n",
                wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, uMsgID, pSlotPtr->bFAtoSO, bFAtoSO, pSlotPtr->nSlotTimeOut, nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
        return;
    }

    pSlotPtr->uMMSI             = uMMSI;
    pSlotPtr->nDataIdx          = nDataIdx;
    pSlotPtr->bSlotStat         = bStatus;
    pSlotPtr->nSlotTimeOut      = nSlotTimeOut;
    pSlotPtr->bAccessScheme     = bAccessScheme;
    pSlotPtr->bCommStateScheme  = bCommStateScheme;
    pSlotPtr->uMsgID            = uMsgID;
    pSlotPtr->uNumSlots         = nNumSlots;
    pSlotPtr->bStartSlot        = bStartSlot;
    pSlotPtr->uSlotOffset       = uSlotOffset;
    pSlotPtr->bItdmaKeepFlag    = bItdmaKeepFlag;
    pSlotPtr->bFAtoSO           = bFAtoSO;
    pSlotPtr->wTxChNum          = wTxChNum;
}

/**
 * @brief Clear frame map slot data
 * @param pSlotPtr Slot data pointer
 */
void CFrameMapMgr::ClearFrameMapSlotData(FRAMEMAP_SLOTDATA *pSlotPtr)
{
    if(!pSlotPtr)
        return;

    pSlotPtr->uMMSI             = AIS_AB_MMSI_NULL;
    pSlotPtr->bSlotStat         = SLOTSTAT_FREE;
    pSlotPtr->nSlotTimeOut      = 0;
    pSlotPtr->bAccessScheme     = POS_REPORT_UNSCHEDULED;
    pSlotPtr->bCommStateScheme  = TDMA_NONE;
    pSlotPtr->uMsgID            = AIS_MSG_NO_ID_UNDEFINED;
    pSlotPtr->uNumSlots         = 0;
    pSlotPtr->bStartSlot        = false;
    pSlotPtr->uSlotOffset       = 0;
    pSlotPtr->bItdmaKeepFlag    = false;
    pSlotPtr->nDataIdx          = 0;
    pSlotPtr->bFAtoSO           = false;
    pSlotPtr->wTxChNum          = AIS_CH_NUM_NONE;
}

/**
 * @brief Clear frame map slot data
 * @param nFrSlotID Frame slot id
 */
void CFrameMapMgr::ClearFrameMapSlotData(int nFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(nFrSlotID);
    ClearFrameMapSlotData(pSlotPtr);
}

/**
 * @brief Set frame map one message
 * @param bCheckProtect Check if slot is protected
 * @param wFrSlotID Frame slot id
 * @param nNumSlot Number of slots
 * @param uMMSI MMSI
 * @param bStatus Slot status
 * @param nSlotTimeOut Slot time out
 * @param bAccessScheme Access scheme
 * @param bCommStateScheme Communication state scheme
 * @param uMsgID Message id
 * @param uSlotOffset Slot offset
 * @param bItdmaKeepFlag ITDMA keep flag
 * @param bProtectAssignedModeSlot Protect assigned mode slot
 * @param nDataIdx Data index
 * @param bFAtoSO FA to SO flag
 */
void CFrameMapMgr::SetFrameMapOneMsg(bool bCheckProtect, WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, 
                                    BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                    UINT16 uMsgID, UINT16 uSlotOffset, bool bItdmaKeepFlag, bool bProtectAssignedModeSlot, 
                                    BYTE nDataIdx, bool bFAtoSO)
{
    SetFrameMapSlotData(bCheckProtect, wFrSlotID, nNumSlot, uMMSI, bStatus, nSlotTimeOut, bAccessScheme, bCommStateScheme,
                        uMsgID, true, uSlotOffset, bItdmaKeepFlag, bProtectAssignedModeSlot, nDataIdx, bFAtoSO);
    wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, 1);

    for(int i = 1 ; i < nNumSlot ; i++)
    {
        SetFrameMapSlotData(bCheckProtect, wFrSlotID, nNumSlot, uMMSI, bStatus, nSlotTimeOut, bAccessScheme, bCommStateScheme,
                            uMsgID, false, uSlotOffset, bItdmaKeepFlag, bProtectAssignedModeSlot, nDataIdx, bFAtoSO);
        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, 1);
    }
}

/**
 * @brief Set frame map one message column
 * @param bCheckProtect Check if slot is protected
 * @param wFrSlotID Frame slot id
 * @param nNumSlot Number of slots
 * @param uMMSI MMSI
 * @param bSlotStatus Slot status
 * @param nSlotTimeOut Slot time out
 * @param bAccessScheme Access scheme
 * @param bCommStateScheme Communication state scheme
 * @param uMsgID Message id
 * @param uSlotOffset Slot offset
 * @param bItdmaKeepFlag ITDMA keep flag
 * @param bProtectAssignedModeSlot Protect assigned mode slot
 * @param nDataIdx Data index
 * @param bFAtoSO FA to SO flag
 */
void CFrameMapMgr::SetFrameMapOneMsgColumn(bool bCheckProtect, const WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, 
                                            BYTE bSlotStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                            UINT16 uMsgID, UINT16 uSlotOffset, bool bItdmaKeepFlag, bool bProtectAssignedModeSlot, 
                                            BYTE nDataIdx, bool bFAtoSO)
{
    if(wFrSlotID == SLOTID_NONE)
        return;

    if(nNumSlot <= 0)
        return;

    WORD wSlotID = wFrSlotID;
    while(nSlotTimeOut >= 0)
    {
        SetFrameMapOneMsg(bCheckProtect, wSlotID, nNumSlot, uMMSI, bSlotStatus, nSlotTimeOut, bAccessScheme, bCommStateScheme,
                            uMsgID, uSlotOffset, bItdmaKeepFlag, bProtectAssignedModeSlot, nDataIdx, bFAtoSO);

        wSlotID = FrameMapSlotIdAdd(wSlotID, NUM_SLOT_PER_FRAME);
        nSlotTimeOut--;
    }
}

/**
 * @brief Free frame map slot row by count
 * @param wStartSlotID Start slot id
 * @param nNumSlot Number of slots
 */
void CFrameMapMgr::FreeFrameMapSlotRowByCnt(WORD wStartSlotID, int nNumSlot)
{
    FRAMEMAP_SLOTDATA *pSlotPtr;

    for(int i = 0 ; i < nNumSlot ; i++)
    {
        pSlotPtr = GetSlotDataPtr(wStartSlotID);
        ClearFrameMapSlotData(pSlotPtr);
        wStartSlotID = FrameMapSlotIdAdd(wStartSlotID, 1);
    }
}

/**
 * @brief Free frame map slot row by range
 * @param wStartSlotID Start slot id
 * @param wEndSlotID End slot id
 */
void CFrameMapMgr::FreeFrameMapSlotRowByRange(WORD wStartSlotID, WORD wEndSlotID)
{
    int nCnt = FrameMapGetDiffSlotID(wStartSlotID, wEndSlotID);
    FreeFrameMapSlotRowByCnt(wStartSlotID, nCnt);
}

/**
 * @brief Free frame map one message
 * @param wFrSlotID Frame slot id
 * @return Number of slots freed
 */
int CFrameMapMgr::FreeFrameMapOneMsg(WORD wFrSlotID)
{
    if(wFrSlotID == SLOTID_NONE)
        return 1;

    // Modified to clear 1 slot at a time regardless of bStartSlot when the slot is not allocated by own vessel.
    // Otherwise, slots that should not be included in actual transmission candidates may be included as transmission slot candidates.
    FRAMEMAP_SLOTDATA *pStartSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(pStartSlotPtr->bStartSlot && pStartSlotPtr->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())
    {
        FreeFrameMapSlotRowByCnt(wFrSlotID, pStartSlotPtr->uNumSlots);
        return pStartSlotPtr->uNumSlots;
    }

    ClearFrameMapSlotData(pStartSlotPtr);
    return 1;
}

/**
 * @brief Free frame map message column row
 * @param wFrSlotIDToFree Frame slot id to free
 * @param nNumColToCheck Number of columns to check
 * @param uMMSI MMSI
 */
void CFrameMapMgr::FreeFrameMapMsgColRow(const WORD wFrSlotIDToFree, int nNumColToCheck, UINT uMMSI)
{
    FRAMEMAP_SLOTDATA *pSlotPtr;

    if(wFrSlotIDToFree == SLOTID_NONE)
        return;

    WORD wColSlotID = wFrSlotIDToFree;
    int nTmo;
    for(int i = 0 ; i < nNumColToCheck ; i++)
    {
        WORD wRowSlotID = wColSlotID;
        for(int j = 0 ; j < NUM_FRAME_FRAMEMAP ; j++)
        {
            pSlotPtr = GetSlotDataPtr(wRowSlotID);
            nTmo = pSlotPtr->nSlotTimeOut;

            if(uMMSI == AIS_AB_MMSI_NULL || pSlotPtr->uMMSI == uMMSI)  // 연속 프레임에서 모두 찾아 해제해야한다.
            {
                FreeFrameMapOneMsg(wRowSlotID);
            }
            if(nTmo <= 0)
                break;

            wRowSlotID = FrameMapSlotIdAdd(wRowSlotID, NUM_SLOT_PER_FRAME);
        }
        wColSlotID = FrameMapSlotIdAdd(wColSlotID, 1);
    }
}

/**
 * @brief Update frame map SOTDMA
 * @param uMsgID Message id
 * @param wFrameID Frame id
 * @param wRcvSlotID Received slot id
 * @param nNumSlot Number of slots
 * @param uMMSI MMSI
 * @param dwSyncState Synchronization state
 * @param dwTimeOut Time out
 * @param dwSubMsg Sub message
 */
void CFrameMapMgr::UpdateFrameMapSOTDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotID, INT8 nNumSlot, 
                                        UINT uMMSI, DWORD dwSyncState, DWORD dwTimeOut, DWORD dwSubMsg)
{
    WORD wFrSlotID = GetFrameMapSlotID(wFrameID, wRcvSlotID);

    if(uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())
    {
        ERROR_LOG("FrMapSO-ERROR] ERROR! Rcved my msg %d, M : %d, S : %d\r\n", uMMSI, uMsgID, wRcvSlotID);
        return;
    }

    if(dwTimeOut == 0)
    {
        WORD uNewFrSlotID = FrameMapSlotIdAdd(wFrSlotID, dwSubMsg);                                                    // to offset to the slot in which transmission will occur during the next frame.
        SetFrameMapOneMsgColumn(true, uNewFrSlotID, nNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC, TMO_MIN, POS_REPORT_SCHEDULED,
                                TDMA_SOTDMA, uMsgID, 0, false, false, 0, false);                                    // 다음 프레임에서의 송신슬롯의 타임아웃을 아직 모르므로 TMO_MIN 로 설정한다!
    }
    else
    {
        dwTimeOut = MIN(dwTimeOut, TMO_MAX);
        SetFrameMapOneMsgColumn(true, wFrSlotID, nNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC, dwTimeOut, POS_REPORT_SCHEDULED,
                                (uMsgID == AIS_MSG_NO_ID_04 ? TDMA_FATDMA : TDMA_SOTDMA), uMsgID, 0, false, false, 0, false);    // 기지국의 송신에 사용된 슬롯은 FATDMA 슬롯으로 간주한다!
    }
}

/**
 * @brief Update frame map ITDMA
 * @param uMsgID Message id
 * @param wFrameID Frame id
 * @param wRcvSlotID Received slot id
 * @param nNumSlot Number of slots
 * @param uMMSI MMSI
 * @param dwSyncState Synchronization state
 * @param dwSlotInc Slot increment
 * @param dwNumSlot Number of slots
 * @param dwKeepFlag Keep flag
 * @param cSlotStatus Slot status
 */
void CFrameMapMgr::UpdateFrameMapITDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotID, INT8 nNumSlot, UINT uMMSI, 
                                        DWORD dwSyncState, DWORD dwSlotInc, DWORD dwNumSlot, DWORD dwKeepFlag, BYTE cSlotStatus)
{
    const UINT8 uSlotTimeOut = 0;        // one more frame;

    WORD wFrSlotID = GetFrameMapSlotID(wFrameID, wRcvSlotID);

    if(uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())
    {
        ERROR_LOG("FrMapIT-ERROR] ERROR! Rcved my msg %d, M : %d, S : %d\r\n", uMMSI, uMsgID, wRcvSlotID);
        return;
    }

    if(dwKeepFlag)
        SetFrameMapOneMsgColumn(true, wFrSlotID, nNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC, uSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_ITDMA, uMsgID, 0, false, false, 0, false);

    if(dwSlotInc > 0)
    {
        int nSlotOffset = dwSlotInc;
        UINT8 uNumNextTxSlot = 0;
        if(dwNumSlot <= 4)
            uNumNextTxSlot = dwNumSlot + 1;
        else if(dwNumSlot <= 7)
        {
            uNumNextTxSlot = dwNumSlot - 4;
            nSlotOffset += MAX_ITDMA_SLOT_OFFSET;
        }
        WORD wNextReservedSlot = FrameMapSlotIdAdd(wFrSlotID, nSlotOffset);
        SetFrameMapOneMsgColumn(true, wNextReservedSlot, uNumNextTxSlot, uMMSI, SLOTSTAT_EXT_ALLOC, uSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_ITDMA, uMsgID, 0, false, false, 0, false);
    }
}

/**
 * @brief Update frame map FATDMA
 * @param bCheckBastStStat Check base station status
 * @param uMsgID Message id
 * @param wRcvFrameID Received frame id
 * @param wRcvSlotID Received slot id
 * @param uMMSI MMSI
 * @param dwOffset Offset
 * @param dwNumSlot Number of slots
 * @param dwTimeOut Time out
 * @param nIncrement Increment
 * @param bFAtoSO FA to SO flag
 */
void  CFrameMapMgr::UpdateFrameMapFATDMA(bool bCheckBastStStat, UINT16 uMsgID, WORD wRcvFrameID, WORD wRcvSlotID, UINT uMMSI, 
                                        DWORD dwOffset, DWORD dwNumSlot, DWORD dwTimeOut, int nIncrement, bool bFAtoSO)
{
    if(bCheckBastStStat)
    {
        if(!IsValidMMSI_BaseSt(uMMSI))
        {
            INFO_LOG("UpdateFrameMapFATDMA] ignored, not BS %09d\r\n", uMMSI);
            return;
        }

        if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(uMMSI) && !CSensorMgr::getInst()->IsGnssLost())
        {
            INFO_LOG("UpdateFrameMapFATDMA] ignored, 120NM : %d, OwnPosLost : %d\r\n", CUserDirMgr::getInst()->IsBaseStationWithin120NM(uMMSI), CSensorMgr::getInst()->IsGnssLost());
            return;
        }
    }

    int nTdmaScheme = TDMA_FATDMA;
    int nAccessScheme = POS_REPORT_SCHEDULED;
    if(bFAtoSO)
    {
        if(uMsgID == AIS_MSG_NO_ID_05)
        {
            nAccessScheme    = POS_REPORT_UNSCHEDULED;
            nTdmaScheme        = TDMA_RATDMA;
        }
        else
        {
            nAccessScheme    = POS_REPORT_SCHEDULED;
            nTdmaScheme        = TDMA_SOTDMA;
        }
    }

    WORD wRcvFrSlot = GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wNextReservedSlot = FrameMapSlotIdAdd(wRcvFrSlot, dwOffset);
    INT16 nResCnt = 0;

    if(nIncrement == 0)
        nResCnt = 1;
    else
    {
        nResCnt = NUM_SLOT_PER_FRAME / (int)nIncrement;
    }

    if(bFAtoSO)
    {
        for(int i = 0 ; i < nResCnt ; i++)
        {
            if(IsSlotAvailableForMMSI(wNextReservedSlot, dwNumSlot, uMMSI))
            {
                SetFrameMapOneMsgColumn(false, wNextReservedSlot, dwNumSlot, 
                                        cShip::getOwnShipInst()->GetOwnShipMMSI(), 
                                        SLOTSTAT_INT_ALLOC, dwTimeOut, nAccessScheme, 
                                        nTdmaScheme, uMsgID, 0, false, false, 0, bFAtoSO);
            }
            wNextReservedSlot = FrameMapSlotIdAdd(wNextReservedSlot, nIncrement);
        }
    }
    else
    {
        for(int i = 0 ; i < nResCnt ; i++)
        {
            SetFrameMapOneMsgColumn(true, wNextReservedSlot, dwNumSlot, uMMSI, 
                                    SLOTSTAT_EXT_ALLOC, dwTimeOut, POS_REPORT_SCHEDULED, 
                                    TDMA_FATDMA, uMsgID, 0, true, false, 0, bFAtoSO);
            wNextReservedSlot = FrameMapSlotIdAdd(wNextReservedSlot, nIncrement);
        }
    }
}

/**
 * @brief Update assigned slots FATDMA to SOTDMA
 * @param uBaseStMMSI Base station MMSI
 * @param wRcvFrameID Received frame id
 * @param wRcvSlotID Received slot id
 * @param wOffset Offset
 * @param nIncrement Increment
 * @param nTimeOutCnt Time out count
 * @return True if success, false otherwise
 */
bool CFrameMapMgr::UpdateAssignedSlotsFATDMAtoSOTDMA(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, 
                                                    WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt)
{
    //-------------------------------------------------------------------------------------------------------------------------------------------------------
    // 슬롯할당 메시지 수신 시 호출됨
    // 할당명령에 의해 지정된 슬롯들을(이 슬롯들은  메시지 20 으로 기지국에 할당되어있는 FATDMA 슬롯일수도 있고 아닐수도 있다) 내부 SOTDMA 할당 슬롯으로 변경
    //-------------------------------------------------------------------------------------------------------------------------------------------------------

    int nFailCnt = 0;
    WORD wRcvFrSlotID = GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wFrSlotID = FrameMapSlotIdAdd(wRcvFrSlotID, wOffset);

    if(wFrSlotID == SLOTID_NONE)
        return false;

    int nLoopCnt = NUM_SLOT_PER_FRAME / nIncrement;
    if(NUM_SLOT_PER_FRAME % nIncrement)
        nLoopCnt++;

    DEBUG_LOG("FAtoSO] mmsi : %09d, rcvSlot : %d,%d (%d), off : %d, inc : %d, startSlot : %d(%d), cnt : %d\r\n",
        uBaseStMMSI, wRcvFrameID, wRcvSlotID, wRcvFrSlotID, wOffset, nIncrement, wFrSlotID, GetSlotIdFromFrameSlotID(wFrSlotID), nLoopCnt);

    bool bFAtoSO;
    FRAMEMAP_SLOTDATA *pCurSlot;
    FRAMEMAP_SLOTDATA *pNextSlot;

    for(int i = 0 ; i < nLoopCnt-1 ; i++)
    {
        pCurSlot = GetSlotDataPtr(wFrSlotID);
        pNextSlot= GetSlotDataPtr(FrameMapSlotIdAdd(wFrSlotID, nIncrement));
        bFAtoSO = (pCurSlot->bSlotStat == SLOTSTAT_EXT_ALLOC && pCurSlot->uMMSI == uBaseStMMSI && pCurSlot->bCommStateScheme == TDMA_FATDMA);

        SetFrameMapOneMsgColumn(false, wFrSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_02, 0, false, false, 0, bFAtoSO);

        if(!IsInternalAllocSlotSO(pNextSlot))
            SetFrameMapOneMsg(true, wFrSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, nIncrement, true, false, 0, false);

        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, nIncrement);
    }

    pCurSlot = GetSlotDataPtr(wFrSlotID);
    bFAtoSO = (pCurSlot->bSlotStat == SLOTSTAT_EXT_ALLOC && pCurSlot->uMMSI == uBaseStMMSI && pCurSlot->bCommStateScheme == TDMA_FATDMA);
    bool bLastExisting = IsInternalAllocSlotSO(pCurSlot);

    SetFrameMapOneMsgColumn(false, wFrSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_02, 0, true, false, 0, bFAtoSO);
    if(!bLastExisting)
        SetFrameMapOneMsg(true, wFrSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                            POS_REPORT_SCHEDULED, TDMA_ITDMA, AIS_MSG_NO_ID_03, 0, true, false, 0, false);

    return (nFailCnt <= 0);
}

/**
 * @brief Restore assigned slots SOTDMA to FATDMA
 * @param uBaseStMMSI Base station MMSI
 * @param wStartSlotID Start slot id
 */
void CFrameMapMgr::RestoreAssginedSlotsSOTDMAtoFATDMA(UINT uBaseStMMSI, WORD wStartSlotID)
{
    //-----------------------------------------------------------------------------------------------
    // 슬롯할당모드에 의해 사용된 SOTDMA 슬롯들을 원래 FATDMA 할당한 기지국의 것으로 변경
    //-----------------------------------------------------------------------------------------------

    FRAMEMAP_SLOTDATA *pSlotPtr;
    WORD wFrSlotID = wStartSlotID;
    UINT uTmpSlotID;

    if(uBaseStMMSI == AIS_AB_MMSI_NULL)
        return;

    if(wStartSlotID == SLOTID_NONE)
        return;

    for(int i = 0 ; i < NUM_SLOT_PER_FRAME ; i++)
    {
        pSlotPtr = GetSlotDataPtr(wFrSlotID);

        if(    pSlotPtr->bSlotStat == SLOTSTAT_INT_ALLOC 
            && pSlotPtr->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() 
            && pSlotPtr->bAccessScheme == POS_REPORT_SCHEDULED 
            && pSlotPtr->bCommStateScheme == TDMA_SOTDMA 
            && pSlotPtr->uMsgID == AIS_MSG_NO_ID_02)
        {
            if(pSlotPtr->bFAtoSO)
                SetFrameMapOneMsgColumn(false, wFrSlotID, 1, uBaseStMMSI, SLOTSTAT_EXT_ALLOC, 0,
                                        POS_REPORT_SCHEDULED, TDMA_FATDMA, AIS_MSG_NO_ID_20, 0, false, false, 0, false);
        }

        wFrSlotID  = FrameMapSlotIdAdd(wFrSlotID, 1);
    }
}

/**
 * @brief Update assigned slots SOTDMA
 * @param wRcvFrameID Received frame id
 * @param wRcvSlotID Received slot id
 * @param wOffset Offset
 * @param nIncrement Increment
 * @param nTimeOutCnt Time out count
 * @return True if success, false otherwise
 */
bool CFrameMapMgr::UpdateAssignedSlotsSOTDMA(WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt)
{
    //-----------------------------------------------------------------------------------------------
    // 슬롯할당 메시지 수신 시 호출됨
    //-----------------------------------------------------------------------------------------------

    int nFailCnt = 0;
    WORD wRcvFrSlotID = GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wFrSlotID = FrameMapSlotIdAdd(wRcvFrSlotID, wOffset);

    if(wFrSlotID == SLOTID_NONE)
        return false;

    int nLoopCnt = NUM_SLOT_PER_FRAME / nIncrement;
    if(NUM_SLOT_PER_FRAME % nIncrement)
    {
        nLoopCnt++;
    }

    DEBUG_LOG("AssignedSO] rcvSlot : %d,%d (%d), off : %d, inc : %d, startSlot : %d(%d), cnt : %d\r\n",
            wRcvFrameID, wRcvSlotID, wRcvFrSlotID, wOffset, nIncrement, wFrSlotID, nLoopCnt);

    for(int i = 0 ; i < nLoopCnt ; i++)
    {
        SetFrameMapOneMsgColumn(false, wFrSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_02, 0, false, false, 0, false);

        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, nIncrement);
    }
    return (nFailCnt <= 0);
}

/**
 * @brief Get internal allocated slot
 * @param wStartSlot Start slot
 * @param nNumSlotToScan Number of slots to scan
 * @param bTdmaScheme TDMA scheme
 * @param nMsgID Message id
 * @param bExceptTmoZeroSlot Except TMO zero slot
 * @return Internal allocated slot
 */
WORD CFrameMapMgr::GetInternalAllocatedSlot(WORD wStartSlot, INT16 nNumSlotToScan, BYTE bTdmaScheme, 
                                            int nMsgID, bool bExceptTmoZeroSlot)
{
    FRAMEMAP_SLOTDATA *pSlot;
    WORD wOutputSlot = SLOTID_NONE;
    WORD wSlot = wStartSlot;

    if(wStartSlot == SLOTID_NONE)
        return SLOTID_NONE;

    if(!cShip::getOwnShipInst()->IsOwnShipMMSISet())
        return SLOTID_NONE;

    for(int i = 0 ; i < nNumSlotToScan ; i++)
    {
        if((pSlot = GetSlotDataPtr(wSlot)))
        {
            if( pSlot->bStartSlot 
                && (nMsgID == AIS_MSG_NO_ID_UNDEFINED || pSlot->uMsgID == nMsgID)
                && pSlot->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() 
                && pSlot->bSlotStat == SLOTSTAT_INT_ALLOC
                && (bTdmaScheme == TDMA_NONE || pSlot->bCommStateScheme == bTdmaScheme)
                && (!bExceptTmoZeroSlot || pSlot->nSlotTimeOut > 0))
            {
                wOutputSlot = wSlot;
                break;
            }
        }

        wSlot = FrameMapSlotIdAdd(wSlot, 1);
    }

    return wOutputSlot;
}

/**
 * @brief Get internal allocated slot for SO
 * @param wStartSI Start slot
 * @param nSizeSI Size of slot
 * @param nMsgID Message id
 * @param bExceptTmoZeroSlot Except TMO zero slot
 * @param bFindMsg3SO Find MSG-3 SO
 * @return Internal allocated slot
 */
WORD CFrameMapMgr::GetInternalAllocatedSlot_SO(WORD wStartSI, int nSizeSI, int nMsgID, 
                                                bool bExceptTmoZeroSlot, bool bFindMsg3SO)
{
    WORD wOutputSlot = SLOTID_NONE;

    if(nSizeSI > MAX_SIZE_SI)
    {
        INFO_LOG("GetIntSlot_SO] too big sizeSI : %d\r\n", nSizeSI);
        nSizeSI = MAX_SIZE_SI;
    }

    // Get preallocated SO slot with same message id
    wOutputSlot = GetInternalAllocatedSlot(wStartSI, nSizeSI, TDMA_SOTDMA, nMsgID, bExceptTmoZeroSlot);

    // If no preallocated SO slot with same message id, get preallocated SO slot with MSG-3
    if(wOutputSlot == SLOTID_NONE && bFindMsg3SO && (nMsgID == AIS_MSG_NO_ID_01 || nMsgID == AIS_MSG_NO_ID_02))
    {
        wOutputSlot = GetInternalAllocatedSlot(wStartSI, nSizeSI, TDMA_SOTDMA, AIS_MSG_NO_ID_03, bExceptTmoZeroSlot);
    }

    return wOutputSlot;
}

/**
 * @brief Get internal allocated slot for IT
 * @param wStartSI Start slot
 * @param nSizeSI Size of slot
 * @param nMsgID Message id
 * @param bExceptTmoZeroSlot Except TMO zero slot
 * @return Internal allocated slot
 */
WORD CFrameMapMgr::GetInternalAllocatedSlot_IT(WORD wStartSI, int nSizeSI, int nMsgID, bool bExceptTmoZeroSlot)
{
    return GetInternalAllocatedSlot(wStartSI, nSizeSI, TDMA_ITDMA, nMsgID, bExceptTmoZeroSlot);
}

/**
 * @brief Get SO TDMA slot for ITDMA Tx
 * @param wStartSI Start slot
 * @param nSizeSI Size of slot
 * @return SO TDMA slot
 */
WORD CFrameMapMgr::GetSoTdmaSlotForItdmaTx(WORD wStartSI, int nSizeSI)
{
    if(wStartSI == SLOTID_NONE)
        return SLOTID_NONE;

    const INT8 nPosMsgID = GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    return GetInternalAllocatedSlot_SO(wStartSI, nSizeSI, nPosMsgID, true);
}

/**
 * @brief Check if slot is internal Tx reserved
 * @param pSlotData Slot data pointer
 * @return true if internal Tx reserved, false otherwise
 */
bool CFrameMapMgr::IsIntTxReservedSlot(FRAMEMAP_SLOTDATA *pSlotData)
{
    if(pSlotData->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() && pSlotData->bStartSlot)
        return true;
    return false;
}

/**
 * @brief Check if slot is available for MMSI
 * @param wFrSlotID Frame slot id
 * @param nNumSlot Number of slots
 * @param uMMSI MMSI
 * @return true if available, false otherwise
 */
bool CFrameMapMgr::IsSlotAvailableForMMSI(WORD wFrSlotID, int nNumSlot, UINT uMMSI)
{
    FRAMEMAP_SLOTDATA *pSlot = GetSlotDataPtr(wFrSlotID);
    for(int i = 0 ; i < nNumSlot ; i++)
    {
        if(pSlot->uMMSI != AIS_AB_MMSI_NULL && pSlot->uMMSI != uMMSI)
        {
            INFO_LOG("Map-FA] IsSlotAvailableForMMSI, ignore, MMSI mismatch %09d -> %09d\r\n",
                    uMMSI, pSlot->uMMSI);
            return false;
        }
    }
    return true;
}

/**
 * @brief Check if slot is internal allocated
 * @param pSlotPtr Slot data pointer
 * @return true if internal allocated, false otherwise
 */
bool CFrameMapMgr::IsInternalAllocSlot(FRAMEMAP_SLOTDATA *pSlotPtr)
{
    return (pSlotPtr && pSlotPtr->uMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI());
}

/**
 * @brief Check if slot is internal allocated for SO
 * @param pSlotPtr Slot data pointer
 * @param bCheckMsgID Check message id
 * @return true if internal allocated for SO, false otherwise
 */
bool CFrameMapMgr::IsInternalAllocSlotSO(FRAMEMAP_SLOTDATA *pSlotPtr, bool bCheckMsgID)
{
    return (pSlotPtr && pSlotPtr->uMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() &&
            (!bCheckMsgID || (pSlotPtr->uMsgID == AIS_MSG_NO_ID_01 || pSlotPtr->uMsgID == AIS_MSG_NO_ID_02)) &&
            pSlotPtr->bCommStateScheme == TDMA_SOTDMA && pSlotPtr->bAccessScheme == POS_REPORT_SCHEDULED);
}

/**
 * @brief Set last Tx frame and free
 * @param wFrSlotIDStart Start frame slot id
 * @param nNumColToCheck Number of columns to check
 * @param bCheckMsgID Check message id
 */
void CFrameMapMgr::SetLastTxFrameAndFree(WORD wFrSlotIDStart, int nNumColToCheck, bool bCheckMsgID)
{
    //---------------------------------------------------------------------------------
    // SOTDMA 내부할당된 해당 메시지 송신 슬롯을 모두 찾아 time out 을 0 으로 만든다.
    //---------------------------------------------------------------------------------

    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;

    for(int i = 0 ; i < nNumColToCheck ; i++)
    {
        pSlotPtr = GetSlotDataPtr(uMapSlotID);
        if(IsInternalAllocSlot(pSlotPtr))
        {
            DEBUG_LOG("SetLast] chkMsg: %d, s:%d,%d, M:%d, tmo:%d, CH:%d, off:%d\r\n",
                    bCheckMsgID, uMapSlotID, GetSlotIdFromFrameSlotID(uMapSlotID), 
                    pSlotPtr->uMsgID, pSlotPtr->nSlotTimeOut, pSlotPtr->wTxChNum, pSlotPtr->uSlotOffset);

            uTmpSlotID = FrameMapSlotIdAdd(uMapSlotID, NUM_SLOT_PER_FRAME);
            if(pSlotPtr->uMsgID == AIS_MSG_NO_ID_03 && pSlotPtr->nSlotTimeOut > 0)
            {
                FRAMEMAP_SLOTDATA *pTmpSlot = GetSlotDataPtr(uTmpSlotID);
                if( (pTmpSlot->nSlotTimeOut == pSlotPtr->nSlotTimeOut-1)
                    && (pTmpSlot->uMsgID == AIS_MSG_NO_ID_01 || pTmpSlot->uMsgID == AIS_MSG_NO_ID_02))
                {
                    pSlotPtr->uMsgID = pTmpSlot->uMsgID;
                    pSlotPtr->uSlotOffset    = 0;
                }
            }

            FreeFrameMapMsgColRow(uTmpSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI());

            pSlotPtr->bItdmaKeepFlag= false;
            pSlotPtr->nSlotTimeOut  = 0;
        }

        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }
}

/**
 * @brief Set last Tx slot with range
 * @param nStartSlot Start slot
 * @param nEndSlot End slot
 * @param bExceptBorder Except border
 */
void CFrameMapMgr::SetLastTxSlotWithRange(int nStartSlot, int nEndSlot, bool bExceptBorder)
{
    int nClearStartSlot = nStartSlot;
    int nClearEndSlot   = nEndSlot;
    if(bExceptBorder)
    {
        nClearStartSlot = FrameMapSlotIdAdd(nClearStartSlot, 1);
        nClearEndSlot   = FrameMapSlotIdAdd(nClearEndSlot, -1);
    }
    int nNumCols = FrameMapGetDiffSlotID(nClearStartSlot, nClearEndSlot);

    if(nStartSlot == SLOTID_NONE || nEndSlot == SLOTID_NONE)
        return;

    SetLastTxFrameAndFree(nClearStartSlot, nNumCols, true);
}

/**
 * @brief Change frame map position message mode
 * @param wFrSlotIDStart Start frame slot id
 * @param nOpMode Operation mode
 */
void CFrameMapMgr::FrChangeFrMapPosMsgMode(WORD wFrSlotIDStart, int nOpMode)
{
    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    const int NEW_POSMSG = GetScheduledPosReportMsgID(nOpMode);

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;
    int nCntRow;

    for(int i = 0 ; i < (NUM_SLOT_PER_FRAME*2) ; i++)
    {
        uTmpSlotID = FrameMapSlotIdAdd(uMapSlotID, i);
        for(int nCntRow = 0 ; nCntRow < TMO_MAX ; nCntRow++)
        {
            pSlotPtr = GetSlotDataPtr(uTmpSlotID);
            if(!IsInternalAllocSlotSO(pSlotPtr))
                break;

            pSlotPtr->uMsgID = NEW_POSMSG;
            if(pSlotPtr->nSlotTimeOut <= 0)
                break;

            uTmpSlotID = FrameMapSlotIdAdd(uTmpSlotID, NUM_SLOT_PER_FRAME);
            nCntRow++;
        }
    }
}

/**
 * @brief Change position message mode time out
 * @param wFrSlotIDStart Start frame slot id
 * @param nNumSlotsToCheck Number of slots to check
 * @param nTimeOut Time out
 */
void CFrameMapMgr::FrChangePosMsgModeTimeOut(WORD wFrSlotIDStart, int nNumSlotsToCheck, int nTimeOut)
{
    //---------------------------------------------------------------------------------
    // SOTDMA 내부할당된 해당 메시지 송신 슬롯을 모두 찾아 mode, time out 을 바꾼다.
    //---------------------------------------------------------------------------------

    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;
    int nCntRow;

    for(int i = 0 ; i < nNumSlotsToCheck ; i++)
    {
        pSlotPtr = GetSlotDataPtr(uMapSlotID);
        if(IsInternalAllocSlotSO(pSlotPtr))
        {
            FreeFrameMapMsgColRow(uMapSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI());
            SetFrameMapOneMsgColumn(true, uMapSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), 
                                    SLOTSTAT_INT_ALLOC, nTimeOut, pSlotPtr->bAccessScheme, 
                                    pSlotPtr->bCommStateScheme, pSlotPtr->uMsgID, 0, false, false, 0, 
                                    pSlotPtr->bFAtoSO);
        }

        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }
}

/**
 * @brief Prepare to change channel
 * @param wFrSlotIDStart Start frame slot id
 */
void CFrameMapMgr::PrepToChgCh(WORD wFrSlotIDStart)
{
    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;

    memset(m_pMsgMapOneFrame, 0, sizeof(FRAMEMAP_SLOTDATA) * NUM_SLOT_PER_FRAME);

    //-----------------------------------------------------------------------------------------------
    // Find all SOTDMA internally allocated message transmission slots, set their timeout to 0, 
    // and initialize externally allocated messages.
    // Recycle internally allocated message slots with timeout set to 0 to use as transmission slots.
    //-----------------------------------------------------------------------------------------------
    for(int i = 0 ; i < NUM_SLOT_PER_FRAME ; i++)
    {
        pSlotPtr = GetSlotDataPtr(uMapSlotID);
        if(IsInternalAllocSlot(pSlotPtr))
        {
            DEBUG_LOG("PrepToChgCh] s:%d,%d, M:%d, tmo:%d\r\n",
                uMapSlotID, GetSlotIdFromFrameSlotID(uMapSlotID), pSlotPtr->uMsgID, pSlotPtr->nSlotTimeOut);

            uTmpSlotID = FrameMapSlotIdAdd(uMapSlotID, NUM_SLOT_PER_FRAME);
            if(pSlotPtr->uMsgID == AIS_MSG_NO_ID_03 && pSlotPtr->nSlotTimeOut > 0)
            {
                FRAMEMAP_SLOTDATA *pTmpSlot = GetSlotDataPtr(uTmpSlotID);
                if (   (pTmpSlot->nSlotTimeOut == pSlotPtr->nSlotTimeOut-1)
                    && (pTmpSlot->uMsgID == AIS_MSG_NO_ID_01 || pTmpSlot->uMsgID == AIS_MSG_NO_ID_02))
                {
                    pSlotPtr->uMsgID = pTmpSlot->uMsgID;
                    pSlotPtr->uSlotOffset    = 0;
                }
            }

            memcpy(&m_pMsgMapOneFrame[i], pSlotPtr, sizeof(FRAMEMAP_SLOTDATA));
        }

        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }

    ClearFrameMap();

    uMapSlotID = wFrSlotIDStart;
    for(int i = 0 ; i < NUM_SLOT_PER_FRAME ; i++)
    {
        pSlotPtr = &m_pMsgMapOneFrame[i];
        if(IsInternalAllocSlot(pSlotPtr))
        {
            SetFrameMapOneMsg(true, uMapSlotID, pSlotPtr->uNumSlots, 
                            cShip::getOwnShipInst()->GetOwnShipMMSI(), pSlotPtr->bSlotStat, 0,
                            pSlotPtr->bAccessScheme, pSlotPtr->bCommStateScheme,
                            pSlotPtr->uMsgID, pSlotPtr->uSlotOffset, false, false, 0, pSlotPtr->bFAtoSO);
        }
        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }
}
