#ifndef __VDLRXMSG_H__
#define __VDLRXMSG_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AisMsg.h"
#include "AisLib.h"
#include "ChannelMgr.h"
#include "VdlTxMgr.h"
#include "RxModem.h"

class CChannelMgr;

class CVdlRxMgr : public CAisLib
{
public:
    CVdlRxMgr(CChannelMgr *pChannel);
    ~CVdlRxMgr();

public:
	/**
     * @brief Get AIS messages field data 
	 **/
    void    GetMsg00FieldData(void);
    void    GetMsg01FieldData(void);
    void    GetMsg04FieldData(void);
    void    GetMsg05FieldData(void);
    void    GetMsg06FieldData(void);
    void    GetMsg07FieldData(void);
    void    GetMsg08FieldData(void);
    void    GetMsg09FieldData(void);
    void    GetMsg10FieldData(void);
    void    GetMsg12FieldData(void);
    void    GetMsg14FieldData(void);
    void    GetMsg15FieldData(void);
    void    GetMsg16FieldData(void);
    void    GetMsg17FieldData(void);
    void    GetMsg18FieldData(void);
    void    GetMsg19FieldData(void);
    void    GetMsg20FieldData(void);
    void    GetMsg21FieldData(void);
    void    GetMsg22FieldData(void);
    void    GetMsg23FieldData(void);
    void    GetMsg24FieldData(void);
    void    GetMsg25FieldData(void);
    void    GetMsg26FieldData(void);
    void    GetMsg27FieldData(void);
    void    GetMsgReservedFieldData(void);

	/**
     * @brief Process AIS messages
	 **/
    bool    ProcessCommon(void);
    bool    ProcessRxMSG_01_02_03(void);
    bool    ProcessRxMSG_04_11(void);
    bool    ProcessRxMSG_SingleSlotAddrACK(void);
    bool    ProcessRxMSG_05(void);
    bool    ProcessRxMSG_06(void);
    bool    ProcessRxMSG_Addressed(UINT dwDestMMSI, UINT8 uAckMsgID, DWORD dwSeqNum);
    bool    ProcessRxMSG_07_13(void);
    void    ProcessRxMSG_MultiSlotAddrACK(UINT dwDestMMSI, DWORD dwSeqNum);
    bool    ProcessRxMSG_08(void);
    bool    ProcessRxMSG_09(void);
    bool    ProcessRxMSG_10(void);
    bool    ProcessRxMSG_12(void);
    bool    ProcessRxMSG_14(void);
    bool    ProcessRxMSG_15(void);
    bool    ProcessRxMSG_15Sub(UINT uDestMMSI, int nReqMsgID, int nSlotOff);
    bool    ProcessRxMSG_16(void);
    bool    ProcessRxMSG_16Sub(DWORD dSrcMMSI, DWORD dDstMMSI, DWORD dOffset, DWORD dwIncrement);
    bool    ProcessRxMSG_17(void);
    bool    ProcessRxMSG_18(void);
    bool    ProcessRxMSG_19(void);
    bool    ProcessRxMSG_20(void);
    bool    ProcessRxMSG_20Sub(DWORD dwOffset, DWORD dwNumSlots, DWORD dwTimeOut, DWORD dwIncrement);
    bool    ProcessRxMSG_21(void);
    bool    ProcessRxMSG_22(void);
    bool    ProcessRxMSG_22Broadcast(void);
    bool    ProcessRxMSG_22Addressed(void);
    bool    ProcessRxMSG_23(void);
    bool    ProcessRxMSG_24(void);
    bool    ProcessRxMSG_25(void);
    bool    ProcessRxMSG_26(void);
    bool    ProcessRxMSG_27(void);
    bool    ProcessRxMSG_Reserved(void);

public:
    void    GetRxAisMsgNoID(void);
    int     GetMsg23ReportIntervalSec(UINT8 uFieldData);

    bool    ProcessRxOneMSG();
    bool    RunProcessRxMgr();

public:
    CChannelMgr    *m_pChannel;

    HWORD          	m_wRxPacketBitC;
    HWORD          	m_wRxPacketSize;
    UCHAR          *m_pRxPacketData;
    UCHAR          	m_bRxAisMsgNoID;
    UCHAR          	m_bRxAisMsgRPTI;

    xAisRxRawForm  *m_pRxRawForm;
    xAISMSG99       m_xRxAisMsg;
};
#endif//__VDLRXMSG_H__
