/**
 * @file    Ssd.cpp
 * @brief   Ssd class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <stdio.h>
#include "SysLog.h"
#include "PI.h"
#include "MKD.h"
#include "SetupMgr.h"
#include "SensorMgr.h"
#include "Ship.h"
#include "LayerNetwork.h"
#include "Ssd.h"

/******************************************************************************
 * 
 * SSD - Ship Static Data
 *
 * $--SSD,c--c,c--c,xxx,xxx,xx,xx,c,aa*hh<CR><LF>
 *         |    |    |   |  |  |  | | 
 *         1    2    3   4  5  6  7 8
 *
 * 1. Ship's Call Sign, 1 to 7 characters
 * 2. Ship's Name, 1 to 20 characters
 * 3. Pos. ref. point distance, "A," from bow, 0 to 511 meters
 * 4. Pos. ref., "B," from stern, 0 to 511 meters
 * 5. Pos. ref., "C," from port beam, 0 to 63 meters
 * 6. Pos. ref., "D," from starboard beam, 0 to 63 meters
 * 7. DTE indicator flag
 * 8. Source identifier
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
CSsd::CSsd() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 */
bool CSsd::Parse(const char *pszSentence)
{
    STATIC_DATA xStaticData = cShip::getOwnShipInst()->xStaticData;
    char    pstrSubData1[RX_MAX_DATA_SIZE];
    int     nParam;
    char    pstrCallSign[LEN_MAX_CALLSIGN + 1];
    char    pstrShipName[LEN_MAX_SHIP_NAME + 1];
    int     nAntType;

    BOOL    bCfgChged = FALSE;
    BOOL    bChgStaticMsgData = FALSE;

    //--------------------------------------------------------------------------
    // $AISSD,ABCDEFK,RES-TEST,096,000,00,00,0,AI  ==> Internal antenna position
    // $AISSD,ABCDEFK,RES-TEST,031,032,33,34,0,--  ==> External antenna position
    //--------------------------------------------------------------------------
    CSentence::GetFieldString(pszSentence, 8, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
        nAntType = (strncmp(pstrSubData1, "AI", 2) ? EPFD_ANTENNA_TYPE_EXT : EPFD_ANTENNA_TYPE_INT);
    else
        nAntType = EPFD_ANTENNA_TYPE_NONE;

    CSentence::GetFieldString(pszSentence, 1, pstrSubData1, sizeof(pstrSubData1));
    if(strlen((char*)pstrSubData1) > 0)            // null : not changed.(IEC 61993-2, p.97)
    {
        memset(pstrCallSign, 0x00, sizeof(pstrCallSign));
        if(!CAisLib::ConvertNormalString(pstrSubData1, pstrCallSign, LEN_MAX_CALLSIGN))
        {
            WARNING_LOG("SSD] Convert error, callsign, before [%s]\r\n", pstrSubData1);
            return false;
        }
        if(strlen(pstrCallSign) > LEN_MAX_CALLSIGN)
        {
            WARNING_LOG("SSD] invalid callsign, too big [%s], len : %d", pstrCallSign, strlen(pstrCallSign));
            return false;
        }

        CAisLib::CheckValidChar(pstrCallSign, LEN_MAX_CALLSIGN);

        if(!CAisLib::IsSameAisStr(xStaticData.vCallSign, pstrCallSign))
        {
            strcpy(xStaticData.vCallSign, pstrCallSign);
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    CSentence::GetFieldString(pszSentence, 2, pstrSubData1, sizeof(pstrSubData1));
    if(strlen((char*)pstrSubData1) > 0)            // null : not changed.(IEC 61993-2, p.97)
    {
        memset(pstrShipName, 0x00, sizeof(pstrShipName));
        if(!CAisLib::ConvertNormalString(pstrSubData1, pstrShipName, LEN_MAX_SHIP_NAME))
        {
            WARNING_LOG("SSD] Convert error, shipName, before [%s]\r\n", pstrSubData1);
            return false;
        }
        if(strlen(pstrShipName) > LEN_MAX_SHIP_NAME)
        {
            WARNING_LOG("SSD] invalid shipName, too big [%s], len : %d", pstrShipName, strlen(pstrShipName));
            return false;
        }

        CAisLib::CheckValidChar(pstrShipName, LEN_MAX_SHIP_NAME);
        if(!CAisLib::IsSameAisStr(xStaticData.vShipName, pstrShipName))
        {
            strcpy(xStaticData.vShipName, pstrShipName);
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    // Dimension of ship/reference for position.
    CSentence::GetFieldString(pszSentence, 3, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("SSD] invalid int num string, dim [%s], ", pstrSubData1);
            return false;
        }

        nParam = atoi((char*)pstrSubData1);
        if(0 <= nParam && nParam <= AIS_GNSS_ANT_POS_A_MAX)
        {
            if(nAntType == EPFD_ANTENNA_TYPE_EXT)
            {
                if(CSetupMgr::getInst()->GetExtAntennaPosA() != nParam)
                {
                    CSetupMgr::getInst()->SetExtAntennaPosA(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
            else    // EPFD_ANTENNA_TYPE_INT
            {
                if(CSetupMgr::getInst()->GetIntAntennaPosA() != nParam)
                {
                    CSetupMgr::getInst()->SetIntAntennaPosA(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
        }
        else
        {
            WARNING_LOG("SSD] AntA wrong data %d\r\n", nParam);
            return false;
        }
    }

    CSentence::GetFieldString(pszSentence, 4, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("ACK] invalid int num string, wA [%s], ", pstrSubData1);
            return false;
        }

        nParam = atoi((char*)pstrSubData1);
        if(0 <= nParam && nParam <= AIS_GNSS_ANT_POS_B_MAX)
        {
            if(nAntType == EPFD_ANTENNA_TYPE_EXT)
            {
                if(CSetupMgr::getInst()->GetExtAntennaPosB() != nParam)
                {
                    CSetupMgr::getInst()->SetExtAntennaPosB(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
            else    // EPFD_ANTENNA_TYPE_INT
            {
                if(CSetupMgr::getInst()->GetIntAntennaPosB() != nParam)
                {
                    CSetupMgr::getInst()->SetIntAntennaPosB(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
        }
        else
        {
            WARNING_LOG("SSD] AntB wrong data %d\r\n", nParam);
            return false;
        }
    }

    CSentence::GetFieldString(pszSentence, 5, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("ACK] invalid int num string, wC [%s], ", pstrSubData1);
            return false;
        }

        nParam = atoi((char*)pstrSubData1);
        if(0 <= nParam && nParam <= AIS_GNSS_ANT_POS_C_MAX)
        {
            if(nAntType == EPFD_ANTENNA_TYPE_EXT)
            {
                if(CSetupMgr::getInst()->GetExtAntennaPosC() != nParam)
                {
                    CSetupMgr::getInst()->SetExtAntennaPosC(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
            else    // EPFD_ANTENNA_TYPE_INT
            {
                if(CSetupMgr::getInst()->GetIntAntennaPosC() != nParam)
                {
                    CSetupMgr::getInst()->SetIntAntennaPosC(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
        }
        else
        {
            WARNING_LOG("SSD] AntC wrong data %d\r\n", nParam);
            return false;
        }
    }

    CSentence::GetFieldString(pszSentence, 6, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("ACK] invalid int num string, wD [%s], ", pstrSubData1);
            return false;
        }

        nParam = atoi((char*)pstrSubData1);
        if(0 <= nParam && nParam <= AIS_GNSS_ANT_POS_D_MAX)
        {
            if(nAntType == EPFD_ANTENNA_TYPE_EXT)
            {
                if(CSetupMgr::getInst()->GetExtAntennaPosD() != nParam)
                {
                    CSetupMgr::getInst()->SetExtAntennaPosD(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
            else    // EPFD_ANTENNA_TYPE_INT
            {
                if(CSetupMgr::getInst()->GetIntAntennaPosD() != nParam)
                {
                    CSetupMgr::getInst()->SetIntAntennaPosD(nParam);
                    bChgStaticMsgData = TRUE;
                    bCfgChged = TRUE;
                }
            }
        }
        else
        {
            WARNING_LOG("PI-SSD] AntD wrong data %d\r\n", nParam);
            return false;
        }
    }

    CSentence::GetFieldString(pszSentence, 7, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            WARNING_LOG("ACK] invalid int num string, DTE [%s], ", pstrSubData1);
            return false;
        }

        nParam = atoi((char*)pstrSubData1);
        if(nParam == DTE_READY || nParam == DTE_NOT_AVAIL)
        {
            if(xStaticData.nDTE != nParam)
            {
                xStaticData.nDTE = nParam;
                bCfgChged = TRUE;
            }
        }
        else
        {
            WARNING_LOG("SSD] DTE wrong data %d\r\n", nParam);
            return false;
        }
    }

    // Static Data 변경시, 1분내에 Msg 5번을 전송.(비주기적 Msg 5)
    if(bCfgChged)
    {
        CSensorMgr::getInst()->UpdateOwnShipAntPos();

        if(memcmp(&xStaticData, &(cShip::getOwnShipInst()->xStaticData), sizeof(STATIC_DATA)))
            bChgStaticMsgData = TRUE;

        cShip::getOwnShipInst()->xStaticData = xStaticData;
        CSetupMgr::getInst()->ReserveToSaveSysConfigData();

        if(bChgStaticMsgData)
        {
            CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();

            CMKD::getInst()->SendSSDToPI(STR_TALKER_AI);
        }
    }

    return true;
}

/**
 * @brief Make the sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CSsd::MakeSentence(char *pszSentence, char *pszTalker, int nAntType)
{
    char szCallSign[LEN_MAX_CALLSIGN+1];
    char szShipName[LEN_MAX_SHIP_NAME+1];
    xANTPOS* pAntPos;

    memset(szCallSign, 0, LEN_MAX_CALLSIGN+1);
    strcpy(szCallSign, cShip::getOwnShipInst()->xStaticData.vCallSign);

    memset(szShipName, 0, LEN_MAX_SHIP_NAME+1);
    strcpy(szShipName, cShip::getOwnShipInst()->xStaticData.vShipName);

    if(nAntType == EPFD_ANTENNA_TYPE_INT)
    {
        // Int.Antenna
        pAntPos = CSetupMgr::getInst()->GetIntAntennaPos();
    }
    else
    {
        // Ext.Antenna
        pAntPos = CSetupMgr::getInst()->GetExtAntennaPos();
    }

    sprintf(pszSentence, "$%sSSD,%s,%s,%03d,%03d,%02d,%02d,%1d,%s",
            (pszTalker == nullptr) ? STR_TALKER_AI : (char*)pszTalker,
            (strlen(szCallSign) <= 0) ? "@@@@@@@" : szCallSign,
            (strlen(szShipName) <= 0) ? "@@@@@@@@@@@@@@@@@@@@" : CAisLib::ConvertNMEAString(szShipName),
            pAntPos->wA, pAntPos->wB, pAntPos->wC, pAntPos->wD,
            cShip::getOwnShipInst()->xStaticData.nDTE,
            (nAntType == EPFD_ANTENNA_TYPE_INT) ? "AI" : "GN"
            );

    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}
