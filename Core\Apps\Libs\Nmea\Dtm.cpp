/**
 * @file    Dtm.cpp
 * @brief   Dtm class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Dtm.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * DTM - 
 *
 * $--DTM,ccc,a,x.x,a,x.x,a,x.x,ccc*hh<CR><LF>
 *         |  |  |__|  |__|  |   | 
 *         1  2  3     4     5   6
 * 
 * 1. Local datum
 * 2. Local datum subdivision code
 * 3. Lat offset, min, N/S
 * 4. Lon offset, min, E/W
 * 5. Altitude offset, m
 * 6. Reference datum
 * 
 ******************************************************************************/
CDtm::CDtm() : CSentence()
{
    ClearData();
}

void CDtm::ClearData(void)
{
	m_nDatumLoc = DATUM_WGS84;
    m_nDatumRef = DATUM_WGS84;
    m_dwRcvTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CDtm::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    int  nTrue;

    GetFieldString(pszSentence, 1, pstrTmp);         // Local datum
    m_nDatumLoc = (strcmp(pstrTmp, "W84") ? DATUM_NOT_WGS84 : DATUM_WGS84);

#if 0
    GetFieldString(pszSentence, 2, pstrTmp);         // Local datum subdivision code
    GetFieldString(pszSentence, 3, pstrTmp);         // Lat offset, min
    GetFieldString(pszSentence, 4, pstrTmp);         // N/S
    GetFieldString(pszSentence, 5, pstrTmp);         // Lon offset, min
    GetFieldString(pszSentence, 6, pstrTmp);         // E/W
    GetFieldString(pszSentence, 7, pstrTmp);         // Altitude offset, m
#endif

    GetFieldString(pszSentence, 8, pstrTmp);         // Reference datum
    m_nDatumRef = strcmp(pstrTmp, "W84") ? DATUM_NOT_WGS84 : DATUM_WGS84;

    m_dwRcvTick = SysGetSystemTimer();

    return true;
}

bool CDtm::IsValidDatumLoc(void)
{
    return (m_nDatumLoc == DATUM_WGS84);
}

bool CDtm::IsValidDatumRef(void)
{
    return (m_nDatumRef == DATUM_WGS84);
}
