/**
 * @file    DataType_AIS.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef __DATATYPE_AIS_H__
#define __DATATYPE_AIS_H__

#include <stdbool.h>
#include "AllConst.h"
#include "DataType.h"

#pragma  pack(1)
typedef struct
{
    UINT    uMMSI               :30;

    BYTE    bSlotStat           :2;     // SLOTSTAT_FREE ~ SLOTSTAT_EXT_ALLOC

    UINT16  uMsgID              :9;     // AIS_MSG_NO_ID_00 ~ AIS_MSG_NO_ID_UNDEFINED
    bool    bStartSlot          :1;     // TRUE : it's start slot of message

    BYTE    bAccessScheme       :2;     // POS_REPORT_NONE ~ POS_REPORT_UNSCHEDULED
    BYTE    bCommStateScheme    :3;     // TDMA_SOTDMA ~ TDMA_NONE

    INT8    nSlotTimeOut        :3;     // ITU_R M.1371 3.3.7.2.2 (0 ~ 7)
    UINT16  uSlotOffset         :14;    // ITU_R M.1371 3.3.7.2.3 (14bit)
    UINT8   uNumSlots           :3;     // ITU_R M.1371 3.3.7.3.2 (0 ~ 7)
    bool    bItdmaKeepFlag      :1;     // ITU_R M.1371 3.3.7.3.2 (0 ~ 1)

    bool    bFAtoSO             :1;

    WORD    wTxChNum            :12;
    BYTE    nDataIdx            :7;
} FRAMEMAP_SLOTDATA;

typedef  struct
{
    INT8    nTxRxMode;
    INT8    nTxRxModeBy;

    UINT8   uTxPower;
    UINT16  uChannelIdA;
    UINT16  uChannelIdB;
    UINT8   uBandwidthA;
    UINT8   uBandwidthB;
} CH_SETUP;
#pragma pack()

typedef struct __LOG_DATA
{
    int             nEventLogID;
    SYS_DATE_TIME   sEventTime;
    SYS_DATE_TIME   sEventEndTime;
    bool            bEventTimeUTC;
    int             nLogFlag;
    int             nDurationMin;

    union {
        int            nMMSI;
    };
} EVENTLOG_DATA;

// refer NMEA-0184 "LRF", function request field
typedef struct __LONGRANGE_INFO
{
    UINT16        uLRFuncA;     // Ship's:name, call sign, and IMO number
    UINT16        uLRFuncB;     // date and time of message composition
    UINT16        uLRFuncC;     // Position
    UINT16        uLRFuncE;     // Course over ground(COG)
    UINT16        uLRFuncF;     // Speed over ground(SOG)
    UINT16        uLRFuncI;     // Destination and Estimated Time of Arrival(ETA)
    UINT16        uLRFuncO;     // Draught
    UINT16        uLRFuncP;     // Ship/Cargo
    UINT16        uLRFuncU;     // Ship's:length, breadth, type
    UINT16        uLRFuncW;     // Persons on board
} LONGRANGE_INFO;

typedef struct __LONGRANGE_TIME
{
    int              nBaseStMMSI;
    SYS_DATE_TIME    sCallDateTime;
} LR_SHIP_LOG;

typedef struct __TDMA_UNSCHE_INFO
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nTxMsgID;           // 전송하고자 하는 AIS Message number(VDL 05,...)
    BYTE    uNumTxSlot;         // 1~5 slot
    int     nSizeSI;
    int     nTimeOutSlot;
} TDMA_UNSCHE_INFO;

typedef struct __TDMA_UNSCHE15_INFO
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nTxMsgID;

    BYTE    bAirType;           // AIR_NONE..AIR_ID12_MSG123

    UINT    uMMSI1;
    BYTE    bMsgID11;
    BYTE    bMsgID12;

    UINT    uMMSI2;
    BYTE    bMsgID21;

    INT16   nNumDataBits;       // 88 ~ 160 bits
} TDMA_UNSCHE15_INFO;

typedef struct __TDMA_UNSCHE_ADDRMSG
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nTxMsgID;           // 전송하고자 하는 AIS Message number
    BYTE    uNumTxSlot;         // 1~5 slot

    UINT32  uDestMMSI;
    INT8    nTxSeqNum;          // 0~3 (한개의 uDestMMSI로 총 4개까지 Addr Msg가 전송가능)

    BYTE    nTxRetryCnt;        // 전송후, 응답없으면 1증가후 재전송.(Addr.Msg는 최초전송 + Retry 3회= 총 4회까지 가능)

    int     nTxSlot;

    INT16   nNumDataBits;
    char    pMsgBinData[MAX_ADDRMSG_DATA+10];
    char    pstrEncAisciiData[SIZE_BUFF_ABM+10];
} TDMA_UNSCHE_ADDRMSG;

typedef struct __TDMA_UNSCHE_ACKMSG
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nTxMsgID;

    UINT    uDestMMSI;          // nTxMsgID=7,13일 때만 사용
    INT8    nTxSeqNum;          // nTxMsgID=7,13일 때만 사용

    WORD    wTxStartSI;
} TDMA_UNSCHE_ACKMSG;

typedef struct
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nAirMsgID;
    INT16   nTxMsgID;
    UINT    uDestMMSI;          // nTxMsgID=7,13일 때만 사용
} TDMA_ONESLOT_ADDRMSG;

typedef struct __TDMA_UNSCHE_BROADMSG
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    UINT16  nTxMsgID;
    BYTE    uNumTxSlot;         // 1~5 slot

    INT8    nTxSeqNum;          // 0~9

    INT16   nNumDataBits;
    char    pMsgBinData[MAX_BROADMSG_DATA];
    char    pstrEncAisciiData[SIZE_BUFF_BBM];
} TDMA_UNSCHE_BROADMSG;

typedef struct
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nABMMsgID;
    INT16   nTxMsgID;           // VDL Message number
    BYTE    uNumTxSlot;         // 1~5 slot

    UINT32  uDestMMSI;
    INT8    nTxSeqNum;          // 0~3 (한개의 uDestMMSI로 총 4개까지 Addr Msg가 전송가능)

    INT16   nNumDataBits;       // 현재까지 저장되어있는 addr msg data bit수
    char    pMsgBinData[MAX_ADDRMSG_DATA];
    char    pstrEncAisciiData[SIZE_BUFF_ABM];

    BYTE    bBinDataFlag;
} TDMA_UNSCHE_MSG25;

typedef struct
{
    bool    bReserveTx;
    bool    bAllocSlot;
    int     nCurSetSlotID;      // bReserveTx 가 TRUE 가 되는 순간의 slot 번호

    INT16   nBBMMsgID;
    INT16   nTxMsgID;           // 전송하고자 하는 AIS Message number
    BYTE    uNumTxSlot;         // 1~5 slot

    UINT32  uDestMMSI;
    INT8    nTxSeqNum;          // 0~3 (한개의 uDestMMSI로 총 4개까지 Addr Msg가 전송가능)

    INT16   nNumDataBits;       // 현재까지 저장되어있는 addr msg data bit수
    char    pMsgBinData[MAX_ADDRMSG_DATA];
    char    pstrEncAisciiData[SIZE_BUFF_BBM];

    BYTE    bBinDataFlag;
} TDMA_UNSCHE_MSG26;

//============================================================================
typedef  struct
{
    HWORD  wChNo;
    DWORD  dFreq;
} xCHNLFREQ;
//============================================================================
typedef  struct
{
    HWORD    wMsgFrameNO;
    DWORD    dSlotNoCounter;
    DWORD    dSampleCounter;
    HWORD    wRxByteSize;
    HWORD    wRxBitsSize;
    UCHAR    vRxRawData[AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX];
} xAisRxRawForm;
//============================================================================
typedef  struct
{
    INT16 wA;        // bow
    INT16 wB;        // stern
    INT16 wC;        // port
    INT16 wD;        // starboard
} xANTPOS;
//============================================================================
typedef  struct
{
    DWORD   dMMSI;
    DWORD   dwImoID;
    char    vCallSign[LEN_MAX_CALLSIGN + 1];
    char    vShipName[LEN_MAX_SHIP_NAME + 2];
    char    vVendorID[LEN_MAX_VENDORID + 1];

    INT16   wAntType;
    xANTPOS sAntPos;
    INT8    nDTE;
} STATIC_DATA;
//----------------------------------------------------------------------------
typedef struct 
{
    POS_GRFP    sShipPosLastValid;
    int         nFixMode;
    int         nSOG;
    int         nCOG;
    BYTE        bPosAccFlag;
    BYTE        bRaimFlag;
    int         nTimeStamp;

    int         nHDG;
    float       fHdgAvg;
    int         nROT;
} DYNAMIC_DATA;

typedef struct __ETA_DATA
{
    int nMonth;
    int nDay;
    int nHour;
    int nMin;
    int nSec;
} ETA_DATA;

typedef struct
{
    UINT16      uShipType;
    UINT16      uDraught;
    UINT16      uPerson;
    char        pstrDestination[LEN_MAX_DESTINATION+1];

    ETA_DATA    xETA;

    UINT8       uNavStatus;
    UINT16      uManoeuvre;
} NAV_DATA;
//============================================================================

#endif  /*__DATATYPE_AIS_H__*/
