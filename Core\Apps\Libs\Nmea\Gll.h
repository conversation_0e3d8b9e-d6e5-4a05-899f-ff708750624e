/**
 * @file    Gll.h
 * @brief   Gll header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __GLL_H__
#define __GLL_H__

/******************************************************************************
 * 
 * GLL - 
 *
 * $--GLL,llll.ll,a,yyyyy.yy,a,hhmmss.ss,A,a*hh<CR><LF>
 *           |----|     |----|     |     | |
 *           1          2          3     4 5
 *
 * 1. Latitude, N/S
 * 2. Longitude, E/W
 * 3. UTC of position
 * 4. Status A=data valid V=data invalid
 * 5. Mode indicator
 * 
 ******************************************************************************/
class CGll : public CSentence
{
public:
    CGll();
    void ClearData(void);
    
    bool Parse(const char *pszSentence);
    bool IsValidPosData(void);

    UINT8 GetPosModeIndi(void);
    DWORD GetPosModeTick(void);

    double GetLatVal(void);
    double GetLonVal(void);

    SYS_TIME GetUtcTime(void);

    void RunPeriodically(void);

protected:
    SYS_TIME m_xUtcTime;
    double  m_rRcvLatVal;
    double  m_rRcvLonVal;

    UINT8   m_uPosModeIndi;

    DWORD   m_dwPosModeTick;
    DWORD   m_dwPosValidTick;
};

#endif /* __GLL_H__ */

