/**
 * @file    Rmc.cpp
 * @brief   Rmc class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Rmc.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * RMC - 
 *
 * $--RMC,hhmmss,A,llll.ll,a,yyyyy.yy,a,x.x.x.x,xxxxxx,x.x,a,a,a*hh<CR><LF>
 *          |    |    |         |        |   |     |    |  |   |
 *          1    2    3         4        5   6     7    8  9   10
 *
 * 1. UTC of positon fix
 * 2. Status A=data valid V = navigation receiver warning
 * 3. Latitude, N/S
 * 4. Longitude, E/W
 * 5. Speed over ground, knots
 * 6. Course over ground, degrees true
 * 7. Date: dd/mm/yy
 * 8. Magnetic variation, degrees, E/W
 * 9. Mode indicator
 * 10. Navigational status
 * 
 ******************************************************************************/
CRmc::CRmc() : CSentence()
{
    ClearData();
}

void CRmc::ClearData(void)
{
	CAisLib::SetDefaultSysDate(&m_xUtcDate);
	CAisLib::SetDefaultSysTime(&m_xUtcTime);

    m_rRcvLatVal = 0.0;
    m_rRcvLonVal = 0.0;

    m_nSpdData = NMEA_SOG_NULL;
    m_nVelData = NMEA_SOG_NULL;
    m_nCrsData = NMEA_COG_NULL;

    m_uPosModeIndi = POS_MODE_NONE;

    m_dwPosModeTick = 0;
    m_dwSpdValidTick = 0;
    m_dwCrsValidTick = 0;
    m_dwPosValidTick = 0;
    m_dwUtcValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CRmc::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    int32_t nHour,nMin,nSec;
    int32_t nDay,nMonth,nYear;
    int32_t nSpd,nVel,nCrs;
    uint8_t bFixed;
    uint8_t bValid;
    char vLatBuf[32];
    char vLonBuf[32];
    char vSpdBuf[32];
    char vCrsBuf[32];
    uint8_t bTimeStampValid = false;
    uint8_t bPosValid = false;

    GetFieldString(pszSentence, 1, pstrTmp);
    nHour   = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
    nMin    = (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
    nSec    = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';

    bTimeStampValid = (strlen(pstrTmp) >= 6 && CAisLib::IsValidAisSysTime(nHour, nMin, nSec));
    if(bTimeStampValid)
    {
        m_xUtcTime.nHour = nHour;
        m_xUtcTime.nMin  = nMin;
        m_xUtcTime.nSec  = nSec;
    }
    else
    {
    	CAisLib::SetDefaultSysTime(&m_xUtcTime);
    }

    GetFieldString(pszSentence, 2, pstrTmp);
    bValid  = pstrTmp[0];

    bFixed = false;
    if (bValid == 'A' || bValid == 'D')
    {
        GetFieldString(pszSentence, 3, pstrTmp);         // Lat
        memmove(vLatBuf,pstrTmp,12);
        vLatBuf[12] = 0x00;

        GetFieldString(pszSentence, 4, pstrTmp);         // N or S
        vLatBuf[15] = pstrTmp[0];

        GetFieldString(pszSentence, 5, pstrTmp);         // Lon
        memmove(vLonBuf,pstrTmp,12);
        vLonBuf[12] = 0x00;

        GetFieldString(pszSentence, 6, pstrTmp);         // E or W
        vLonBuf[15] = pstrTmp[0];

        GetFieldString(pszSentence, 7, pstrTmp);         // speed
        memmove(vSpdBuf,pstrTmp,12);

        if(strlen(pstrTmp) > 0 && !CAisLib::IsValidAisFloatNumStr(pstrTmp, true))
        {
            return false;
        }

        GetFieldString(pszSentence, 8, pstrTmp);         // course
        memmove(vCrsBuf,pstrTmp,12);

        if(strlen(pstrTmp) > 0 && !CAisLib::IsValidAisFloatNumStr(pstrTmp, false))
        {
            return false;
        }
    }

    GetFieldString(pszSentence, 9, pstrTmp);             // ddmmyy
    if (strlen(pstrTmp) >= 6)
    {
        nDay  = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
        nMonth= (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
        nYear = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';
        nYear+= 2000;

        if(CAisLib::IsValidAisSysDate(nYear, nMonth, nDay))
        {
            m_xUtcDate.nYear = nYear;
            m_xUtcDate.nMon  = nMonth;
            m_xUtcDate.nDay  = nDay;
            m_dwUtcValidTick = SysGetSystemTimer();
        }
    }

    if (bValid == 'A' || bValid == 'D')
    {
        GetFieldString(pszSentence, 12, pstrTmp);        // Mode indicator

        if(strlen(pstrTmp) > 0)
        {
            m_uPosModeIndi = CGps::ParsePosModeIndicator(pstrTmp[0]);
            m_dwPosModeTick = SysGetSystemTimer();
        }
        else
        {
            m_uPosModeIndi = (pstrTmp[0] == 'A' ? POS_MODE_AUTO : POS_MODE_DIFFERENTIAL);
            m_dwPosModeTick = SysGetSystemTimer();
        }

        if(bTimeStampValid && CGps::IsModeIndicatorTrustable(m_uPosModeIndi))
        {
            bFixed = true;

            if(CSentence::ConvertPosition(vLatBuf, vLonBuf, &m_rRcvLatVal, &m_rRcvLonVal))
            {
                bPosValid = true;
                m_dwPosValidTick = SysGetSystemTimer();

                // RS Scenario BAM-6_4_2-Ed3.scn and Legend BAM-6_4_2-Ed3.leg
                // RMC센텐스에 COG/SOG항목에 NULL이 입력되어 수신한 경우 COG/SOG 업데이트 하지 않고,
                // Alert 3119(Missing COG/SOG) Caution 발생하도록 수정.
                if (strlen(vSpdBuf) > 0)
                {
                    nSpd = (int)(atof((char*)vSpdBuf) * NMEA_SCALE_SOG);
                    if (nSpd < 0 || nSpd > 9999)
                    {
                        ;
                    }
                    else
                    {
                        nVel = (int)(nSpd * 1.852);
                        m_dwSpdValidTick = SysGetSystemTimer();
                        m_nSpdData = nSpd;
                        m_nVelData = nVel;
                    }
                }

                if (strlen(vCrsBuf) > 0)
                {
                    nCrs = (int)(atof((char*)vCrsBuf) * NMEA_SCALE_COG);

                    // Ublox chip 버전에 따라 COG가 음수로 입력되는 경우가 발생함.
                    // 음수인 경우 360도를 더해서 정상 처리 하도록 수정함.
                    if      (nCrs < 0 )      nCrs += 3600;
                    else if (nCrs > 3600)    nCrs -= 3600;

                    m_dwCrsValidTick = SysGetSystemTimer();
                    m_nCrsData = nCrs;
                }
            }
        }
    }

    return (bFixed);
}

/**
 * @brief Check received position data is valid or not
 */
bool CRmc::IsValidPosData(void)
{
    return (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Check received speed data is valid or not
 */
bool CRmc::IsValidSpeedData(void)
{
    return (m_dwSpdValidTick && SysGetDiffTimeMili(m_dwSpdValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Check received course data is valid or not
 */
bool CRmc::IsValidCourseData(void)
{
    return (m_dwCrsValidTick && SysGetDiffTimeMili(m_dwCrsValidTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Check received UTC data is valid or not
 */
bool CRmc::IsValidUtcData(void)
{
    return (m_dwUtcValidTick && SysGetDiffTimeMili(m_dwUtcValidTick) < NMEA_UTC_LASTDATA_STAYMS);
}

/**
 * @brief Get position mode indicatior
 */
UINT8 CRmc::GetPosModeIndi(void)
{
    return m_uPosModeIndi;
}

/**
 * @brief Get position mode tick counter
 */
DWORD CRmc::GetPosModeTick(void)
{
    return m_dwPosValidTick;
}

/**
 * @brief Get latitude positon
 */
double CRmc::GetLatVal(void)
{
    return m_rRcvLatVal;
}

/**
 * @brief Get longitude position
 */
double CRmc::GetLonVal(void)
{
    return m_rRcvLonVal;
}

/**
 * @brief Get UTC date
 */
SYS_DATE CRmc::GetUtcDate(void)
{
    return m_xUtcDate;
}

/**
 * @brief Get UTC time
 */
SYS_TIME CRmc::GetUtcTime(void)
{
    return m_xUtcTime;
}

/**
 * @brief Get received course data
 */
int CRmc::GetCourse(void)
{
    return m_nCrsData;
}

/**
 * @brief Get received speed data
 */
int CRmc::GetSpeed(void)
{
    return m_nSpdData;
}

/**
 * @brief Get received speed data
 */
int CRmc::GetVelocity(void)
{
    return m_nVelData;
}

/**
 * @brief Call function periodically
 */
void CRmc::RunPeriodically(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosValidTick = 0;
    }

    if (m_dwPosModeTick && SysGetDiffTimeMili(m_dwPosModeTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosModeTick = 0;
        m_uPosModeIndi = 0;
    }

    if (m_dwSpdValidTick && SysGetDiffTimeMili(m_dwSpdValidTick) < NMEA_SOG_LASTDATA_STAYMS)
    {
        m_dwSpdValidTick = 0;
        m_nSpdData = NMEA_SOG_NULL;
    }

    if (m_dwCrsValidTick && SysGetDiffTimeMili(m_dwCrsValidTick) < NMEA_COG_LASTDATA_STAYMS)
    {
        m_dwCrsValidTick = 0;
        m_nCrsData = NMEA_COG_NULL;
    }

    if (m_dwUtcValidTick && SysGetDiffTimeMili(m_dwUtcValidTick) < NMEA_UTC_LASTDATA_STAYMS)
    {
        m_dwUtcValidTick = 0;
    }
}
