/**
 * @file    Ssd.h
 * @brief   Ssd header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Sentence.h"

#ifndef __SSD_H__
#define __SSD_H__

/******************************************************************************
 * 
 * SSD - Ship Static Data
 *
 * $--SSD,c--c,c--c,xxx,xxx,xx,xx,c,aa*hh<CR><LF>
 *         |    |    |   |  |  |  | | 
 *         1    2    3   4  5  6  7 8
 *
 * 1. Ship's Call Sign, 1 to 7 characters
 * 2. Ship's Name, 1 to 20 characters
 * 3. Pos. ref. point distance, "A," from bow, 0 to 511 meters
 * 4. Pos. ref., "B," from stern, 0 to 511 meters
 * 5. Pos. ref., "C," from port beam, 0 to 63 meters
 * 6. Pos. ref., "D," from starboard beam, 0 to 63 meters
 * 7. DTE indicator flag
 * 8. Source identifier
 *
 ******************************************************************************/
class CSsd : public CSentence
{
public:
    CSsd();

    static bool    Parse(const char *pszSentence);
    static int32_t MakeSentence(char *pszSentence, char *pszTalker, int nAntType);
};

#endif /* __SSD_H__ */

