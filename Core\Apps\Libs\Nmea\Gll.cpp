/**
 * @file    Gll.cpp
 * @brief   Gll class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Gll.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * GLL - 
 *
 * $--GLL,llll.ll,a,yyyyy.yy,a,hhmmss.ss,A,a*hh<CR><LF>
 *           |----|     |----|     |     | |
 *           1          2          3     4 5
 *
 * 1. Latitude, N/S
 * 2. Longitude, E/W
 * 3. UTC of position
 * 4. Status A=data valid V=data invalid
 * 5. Mode indicator
 * 
 ******************************************************************************/
CGll::CGll() : CSentence()
{
    ClearData();
}

void CGll::ClearData(void)
{
	CAisLib::SetDefaultSysTime(&m_xUtcTime);

    m_rRcvLatVal = 0.0;
    m_rRcvLonVal = 0.0;

    m_uPosModeIndi = POS_MODE_NONE;

    m_dwPosModeTick = 0;
    m_dwPosValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CGll::Parse(const char *pszSentence)
{
    char *pSource;
    char pstrTmp[128];
    bool bFixed = 0;
    bool bValid;
    int  nHour,nMin,nSec;
    char vLatBuf[32];
    char vLonBuf[32];

    GetFieldString(pszSentence, 1, pstrTmp);     // Latitude
    memmove(vLatBuf,pstrTmp,12); vLatBuf[12] = 0x00;

    GetFieldString(pszSentence, 2, pstrTmp);     // N or S
    vLatBuf[15] = pstrTmp[0];

    GetFieldString(pszSentence, 3, pstrTmp);     // Longitude
    memmove(vLonBuf,pstrTmp,12); vLonBuf[12] = 0x00;

    GetFieldString(pszSentence, 4, pstrTmp);     // E or W
    vLonBuf[15] = pstrTmp[0];

    GetFieldString(pszSentence, 5, pstrTmp);     // UTC of position
    nHour   = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
    nMin    = (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
    nSec    = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';

    BOOL bTimeStampValid = (strlen(pstrTmp) >= 6 && CAisLib::IsValidAisSysTime(nHour, nMin, nSec));
    if(bTimeStampValid)
    {
        m_xUtcTime.nHour = nHour;
        m_xUtcTime.nMin    = nMin;
        m_xUtcTime.nSec    = nSec;
    }
    else
    {
    	CAisLib::SetDefaultSysTime(&m_xUtcTime);
    }

    GetFieldString(pszSentence, 6, pstrTmp);     // Status ('A','V')
    bValid  = pstrTmp[0];

    bFixed = false;
    if (bTimeStampValid && (bValid == 'A' || bValid == 'D'))
    {
        bFixed = true;

        if(ConvertPosition(vLatBuf, vLonBuf, &m_rRcvLatVal, &m_rRcvLonVal))
        {
            m_dwPosValidTick = SysGetSystemTimer();

            GetFieldString(pszSentence, 7, pstrTmp);
            if(strlen(pstrTmp) > 0)
            {
                m_uPosModeIndi = CGps::ParsePosModeIndicator(pstrTmp[0]);                     // '0','1','2'
                m_dwPosModeTick = SysGetSystemTimer();
            }
            else
            {
                // In case mode indicator is not supported
                m_uPosModeIndi = (bValid == 'D' ? POS_MODE_DIFFERENTIAL : POS_MODE_AUTO);
                m_dwPosModeTick = SysGetSystemTimer();
            }
        }
    }

    return (bFixed);
}

/**
 * @brief Check received position data is valid or not
 */
bool CGll::IsValidPosData(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_DATA_VALID_TIMEMS)
    {
        return true;
    }

    return false;
}

/**
 * @brief Get position mode indicatior
 */
UINT8 CGll::GetPosModeIndi(void)
{
    return m_uPosModeIndi;
}

/**
 * @brief Get position mode tick counter
 */
DWORD CGll::GetPosModeTick(void)
{
    return m_dwPosValidTick;
}

/**
 * @brief Get latitude positon
 */
double CGll::GetLatVal(void)
{
    return m_rRcvLatVal;
}

/**
 * @brief Get longitude position
 */
double CGll::GetLonVal(void)
{
    return m_rRcvLonVal;
}

/**
 * @brief Get UTC time
 */
SYS_TIME CGll::GetUtcTime(void)
{
    return m_xUtcTime;
}

/**
 * @brief Call function periodically
 */
void CGll::RunPeriodically(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosValidTick = 0;
    }

    if (m_dwPosModeTick && SysGetDiffTimeMili(m_dwPosModeTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosModeTick = 0;
        m_uPosModeIndi = 0;
    }
}
