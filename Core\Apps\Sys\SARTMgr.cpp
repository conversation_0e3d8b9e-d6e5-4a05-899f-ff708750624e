#include <string.h>
#include <SartMgr.h>

#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "VdlRxMgr.h"
#include "AlarmMgr.h"
#include "Timer.h"
#include "AlarmThing.h"
#include "SetupMgr.h"
#include "SartMgr.h"

#define SART_TYPE_TESTING       0            // AIS-SART on testing
#define SART_TYPE_TYPEAPP       1            // AIS-SART on type approval testing
#define SART_TYPE_ACTIVE        2            // Active AIS-SART
#define SART_TYPE_UNKNOWN      -1

#define SART_TIMEOUT_SEC_CNT        1080                        // 18 min == 1080 sec, IEC 61993-2 6.11
#define SART_NO_TEXT_RCV_SEC_CNT    (SART_TIMEOUT_SEC_CNT+1)


CSartMgr::CSartMgr()
{
    m_pSartList = (SART_DATA*)SysAllocMemory(sizeof(SART_DATA) * NUM_SARTS);

    for(int i = 0 ; i < NUM_SARTS ; i++)
    {
        ClearSartData(i);
    }
}

CSartMgr::~CSartMgr()
{

}

int CSartMgr::FindSART(UINT uMMSI)
{
    for(int i = 0 ; i < NUM_SARTS ; i++)
    {
        if(m_pSartList[i].uMMSI == uMMSI)
            return i;
    }
    return -1;
}

int CSartMgr::FindEmptySlot()
{
    for(int i = 0 ; i < NUM_SARTS ; i++)
    {
        if(m_pSartList[i].uMMSI == AIS_AB_MMSI_NULL)
            return i;
    }
    return FindOldestSART();
}

int CSartMgr::FindOldestSART()
{
    int nIndex = -1;
    DWORD dwMaxElapSec = 0;
    DWORD dwElapSec;
    for(int i = 0 ; i < NUM_SARTS ; i++)
    {
        dwElapSec = cTimerSys::getInst()->GetTimeDiffSec(m_pSartList[i].dwPosRcvSec);
        if(dwElapSec > dwMaxElapSec)
        {
            nIndex = i;
            dwMaxElapSec = dwElapSec;
        }
    }
    return nIndex;
}

INT8 CSartMgr::GetTypeOfSART(UINT uMMSI)
{
    int nIndex;
    if((nIndex = FindSART(uMMSI)) >= 0)
        return m_pSartList[nIndex].nType;
    return SART_TYPE_UNKNOWN;
}

BOOL CSartMgr::IsTestingSART(UINT uMMSI)
{
    return (GetTypeOfSART(uMMSI) == SART_TYPE_TESTING);
}

void CSartMgr::ClearSartData(INT8 nIndex)
{
    m_pSartList[nIndex].uMMSI       = AIS_AB_MMSI_NULL;
    m_pSartList[nIndex].nType       = SART_TYPE_UNKNOWN;
    m_pSartList[nIndex].nAlarmStatus= BAM_ALERT_STAT_NORMAL;
    m_pSartList[nIndex].dwTextRcvSec= 0;
    m_pSartList[nIndex].dwPosRcvSec = 0;
}

void CSartMgr::SetDataSART(INT8 nIndex, UINT uMMSI, INT8 nType)
{
    m_pSartList[nIndex].uMMSI       = uMMSI;
    m_pSartList[nIndex].nType       = nType;
    m_pSartList[nIndex].dwPosRcvSec = cTimerSys::getInst()->GetCurTimerSec();

    DEBUG_LOG("SART-set] #%d, %09d, type:%d\r\n", nIndex, uMMSI, nType);
}

void CSartMgr::AddSART(UINT uMMSI, INT8 nType)
{
    int nIndex;
    if((nIndex = FindSART(uMMSI)) >= 0)
    {
        SetDataSART(nIndex, uMMSI, nType);
    }
    else
    {
        nIndex = FindEmptySlot();
        SetDataSART(nIndex, uMMSI, nType);
    }
}

void CSartMgr::RemoveSARTwithMMSI(UINT uMMSI)
{
    int nIndex;
    if((nIndex = FindSART(uMMSI)) >= 0)
        ClearSartData(nIndex);
}

void CSartMgr::CheckTypeOfSART(UINT uMMSI, char *pstr)
{
    if(!strcmp(pstr, "SART ACTIVE"))
    {
        int nIndex;
        if((nIndex = FindSART(uMMSI)) >= 0)
        {
            if(m_pSartList[nIndex].nType == SART_TYPE_ACTIVE)
                m_pSartList[nIndex].dwTextRcvSec = cTimerSys::getInst()->GetCurTimerSec();
        }
    }
    else if(!strcmp(pstr, "SART TEST"))
    {
        int nIndex;
        if((nIndex = FindSART(uMMSI)) >= 0)
        {
            if(m_pSartList[nIndex].nType == SART_TYPE_TYPEAPP || m_pSartList[nIndex].nType == SART_TYPE_TESTING)
                m_pSartList[nIndex].dwTextRcvSec = cTimerSys::getInst()->GetCurTimerSec();
        }
    }
}

void CSartMgr::CheckAndAddSART(UINT uMMSI, int nNavStatus)
{
    //--------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to ITU-R-1371-5 Annex1 2.1.6 Automatic identification system search and rescue transmitter
    // The Messages 1 and 14 should use a user ID 970xxyyyy (where xx = manufacturer ID 01 to 99;
    // yyyy = the sequence number 0000 to 9999) and Navigational Status 14 when active, and Navigational Status 15 when under test.
    // Other devices using AIS technology such as man overboard (MOB) devices and emergency
    // position indicating radio beacons (EPIRBs) should not be subsets of AIS-SART stations, because
    // these devices do not conform with all the requirements for these stations.
    // The Message 14 should have the following content:
    // When active: SART ACTIVE
    // Under test: SART TEST
    //--------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.11.1 Minimum keyboard and display (MKD)
    // - Active AIS-SART: confirm that user IDs of Message 1 lead with 97, Message 1 NavStatus is 14.
    // - Testing AIS-SART : confirm that user IDs of Messages 1 and 14 lead with 97, Message 1 NavStatus is 15, Message 14 text is "SART TEST"
    // - Type approval testing AIS-SART: confirm that user IDs of Messages 1 and 14 lead with 97000, Message 1 NavStatus is 15 and Message 14 text is "SART TEST".
    //--------------------------------------------------------------------------------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 18.1.5 Behaviour of NavStatus 14 reception
    // This test verifies the correct behaviour of the received Message 1 with NavStatus 14
    // Navigational status 14 = AIS-SART (active), MOB-AIS, EPIRB-AIS
    // => 새로 수신한 스테이션의 NavStatus 가 14 이면 : ALR 14 발생
    //--------------------------------------------------------------------------------------------------------------------------------------------------------------

    if(CAisLib::IsValidMMSI_SART(uMMSI))
    {
        int nType = SART_TYPE_UNKNOWN;
        if(nNavStatus == AIS_NAV_STATUS_AIS_SART)
            nType = SART_TYPE_ACTIVE;
        else if(nNavStatus == AIS_NAV_STATUS_UNDEFINED)
        {
            if(MMSI_MIN_TYPEAPPMODE_SART <= uMMSI && uMMSI <= (DWORD)MMSI_MAX_TYPEAPPMODE_SART)
                nType = SART_TYPE_TYPEAPP;
            else
                nType = SART_TYPE_TESTING;
        }

        if(nType != SART_TYPE_UNKNOWN)
        {
            AddSART(uMMSI, nType);
            gpAlarmActiveSART->UpdateAlarmStatus(TRUE);
        }
    }
}

void CSartMgr::SetActiveSartAlarmAcked()
{
    for(int i = 0 ; i < NUM_SARTS ; i++)
    {
        switch(m_pSartList[i].nAlarmStatus)
        {
            case BAM_ALERT_STAT_ACTIVE_UNACK:
            case BAM_ALERT_STAT_ACTIVE_SILEN:
            case BAM_ALERT_STAT_RESPON_TRANS:
                m_pSartList[i].nAlarmStatus = BAM_ALERT_STAT_ACTIVE_ACKED;
                break;
            case BAM_ALERT_STAT_RECTIF_UNACK:
                m_pSartList[i].nAlarmStatus = BAM_ALERT_STAT_NORMAL;
                break;
        }
    }
}

BOOL CSartMgr::CheckAlarmNewActiveSART()
{
    int nNumActive = 0;

    for(int i = 0 ; i < NUM_SARTS ; i++)
    {
        if(m_pSartList[i].nType == SART_TYPE_ACTIVE ||
            (CSetupMgr::getInst()->GetShowTestingSART() && (m_pSartList[i].nType == SART_TYPE_TYPEAPP || m_pSartList[i].nType == SART_TYPE_TESTING)))
        {
            if(m_pSartList[i].nAlarmStatus == BAM_ALERT_STAT_NORMAL || m_pSartList[i].nAlarmStatus == BAM_ALERT_STAT_RECTIF_UNACK)
            {
                m_pSartList[i].nAlarmStatus = BAM_ALERT_STAT_ACTIVE_UNACK;

                if(gpAlarmActiveSART->GetAlarmStatus() == BAM_ALERT_STAT_ACTIVE_ACKED)
                {
                    gpAlarmActiveSART->SetAlarmStatus(BAM_ALERT_STAT_ACTIVE_UNACK, TRUE);
                }
            }
            nNumActive++;
        }
    }

    return (nNumActive > 0);
}

void CSartMgr::RunPeriodicallySART()
{
    //----------------------
    // IEC 61993-2 6.11
    // IEC 61993-2 18.1.5.3
    //----------------------
    static DWORD dwCheckTick = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckTick) > 2)
    {
        for(int i = 0 ; i < NUM_SARTS ; i++)
        {
            if(m_pSartList[i].uMMSI != AIS_AB_MMSI_NULL)
            {
                DWORD dwElapRcv = cTimerSys::getInst()->GetTimeDiffSec(m_pSartList[i].dwPosRcvSec);
                INFO_LOG("SART] check time-out, #%d, %09d, rcvSec:%d, rcvElap:%d, rcvTextSec:%d, alr:%d, s:%d\r\n",
                    i, m_pSartList[i].uMMSI, m_pSartList[i].dwPosRcvSec, dwElapRcv, m_pSartList[i].dwTextRcvSec, m_pSartList[i].nAlarmStatus, cTimerSys::getInst()->GetCurTimerSec());

                if(dwElapRcv > SART_TIMEOUT_SEC_CNT)
                {
                    INFO_LOG("SART] timeOut, %09d, rcvSec:%d, elap:%d\r\n",
                            m_pSartList[i].uMMSI, m_pSartList[i].dwPosRcvSec, dwElapRcv);
                    ClearSartData(i);
                }
            }
        }

        dwCheckTick = cTimerSys::getInst()->GetCurTimerSec();
    }
}
