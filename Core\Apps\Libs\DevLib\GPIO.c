/**
 * @file    Gpio.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "SysConst.h"
#include "AllConst.h"
#include "SysLib.h"
#include "GPIO.h"

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

//========================================================================
void  SysSetGPIOxBitData(GPIO_TypeDef *pGPIO, int nPinNo, DWORD dSetVal)
{
    if (dSetVal)
        pGPIO->BSRR = 0x01UL << nPinNo;
    else
        pGPIO->BSRR = 0x01UL << (nPinNo + 16);
}

DWORD SysGetGPIOxBitData(GPIO_TypeDef *pGPIO, int nPinNo)
{
    if (pGPIO->IDR & (0x01UL << nPinNo))
        return 1;

    return 0;
}
//========================================================================

