#include <memory>
#include <stdlib.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "FlashMem.h"
#include "EEPROM.h"
#include "RosMgr.h"
#include "ComLib.h"
#include "Ship.h"
#include "AisMsg.h"
#include "LongRange.h"
#include "Timer.h"
#include "MKD.h"
#include "EventLogMgr.h"
#include "SensorMgr.h"
#include "DataBackMgr.h"
#include "BuiltInTestMgr.h"
#include "LayerNetwork.h"
#include "LayerPhysical.h"
#include "SysOpStatus.h"
#include "SetupMgr.h"

//#define __SAVE_NMEAOUT_SETUP__            // Save m_sNmeaToMKD data to flash memory

//-----------------------------------
//-----------------------------------
#define SETUP_NMEABMP_RMC        0x01
#define SETUP_NMEABMP_GSV        0x02
#define SETUP_NMEABMP_GLL        0x04
#define SETUP_NMEABMP_VTG        0x08
#define SETUP_NMEABMP_GBS        0x10
#define SETUP_NMEABMP_GGA        0x20
#define SETUP_NMEABMP_ZDA        0x40
#define SETUP_NMEABMP_GSA        0x80
//-----------------------------------


#define SIZE_LR_TIME    (sizeof(LR_SHIP_LOG)*MAX_MMSI_LR_REQ)

#define SIZE_SETUPDATA_BUFF    2048

CSetupMgr::CSetupMgr()
{
#ifdef __USE_INTERNAL_FLASH_FOR_BACKUP__
    m_pDataBackHw           = new CDataBackMgr(BACK_DEVICE_FLASH, HW_CONF_BACK_UP_ADDR);
    m_pDataBackSys          = new CDataBackMgr(BACK_DEVICE_FLASH, SYS_DATA_BACK_UP_ADDR);
    m_pDataBackRos          = new CDataBackMgr(BACK_DEVICE_FLASH, ROS_DATA_BACK_UP_ADDR);
    m_pDataBackLog          = new CDataBackMgr(BACK_DEVICE_FLASH, LOG_DATA_BACK_UP_ADDR);
#else
    m_pDataBackHw           = new CDataBackMgr(BACK_DEVICE_EEPROM, BACK_HW_CONFIG_ADDR);
    m_pDataBackSys          = new CDataBackMgr(BACK_DEVICE_EEPROM, BACK_SYS_DATA_ADDR);
    m_pDataBackRos          = new CDataBackMgr(BACK_DEVICE_EEPROM, BACK_ROS_DATA_ADDR);
    m_pDataBackLog          = new CDataBackMgr(BACK_DEVICE_EEPROM, BACK_LOG_DATA_ADDR);
#endif

    m_bSetupLoadError       = false;
    m_bSetupDataChgSys      = false;
    m_bSetupDataChgROS      = false;
    m_bSetupDataChgLog      = false;
    m_bSetupDataChgHwConfig = false;

    m_pSetupDataBuff        = (UCHAR *)SysAllocMemory(sizeof(UCHAR) * SIZE_SETUPDATA_BUFF);
#ifdef __ENABLE_SETUP_SAVEVERIFY__
    m_pSetupVerifyBuff      = (UCHAR *)SysAllocMemory(sizeof(UCHAR) * SIZE_SETUPDATA_BUFF);
#endif

    InitHwConfigData();
    InitSysConfigData();

    m_bAddrMsgRetransmitCntChg = false;

    m_sNmeaToMKD.bEnableRMC = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableGSV = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableGLL = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableVTG = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableGBS = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableGGA = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableZDA = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableGNS = MODE_VAL_OFF;
    m_sNmeaToMKD.bEnableGSA = MODE_VAL_OFF;

    m_sNmeaToEXT.bEnableRMC = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableGSV = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableGLL = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableVTG = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableGBS = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableGGA = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableZDA = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableGNS = MODE_VAL_OFF;
    m_sNmeaToEXT.bEnableGSA = MODE_VAL_OFF;
}

CSetupMgr::~CSetupMgr()
{
}

/**
 * @brief Initialize the hardware configuration data to default values.
 * @param None
 * @return None
 */
void CSetupMgr::InitHwConfigData(void)
{
    m_sHwConfigData.wPowerLevelCh1_High   = DEFAULT_TX_PW_HIGH;
    m_sHwConfigData.wPowerLevelCh1_Low    = DEFAULT_TX_PW_LOW;
    m_sHwConfigData.wPowerLevelCh2_High   = DEFAULT_TX_PW_HIGH;
    m_sHwConfigData.wPowerLevelCh2_Low    = DEFAULT_TX_PW_LOW;
    m_sHwConfigData.wTcxoLevel            = DEFAULT_TCXO_LEVEL;
    m_sHwConfigData.wVswrLimit            = DEFAULT_VSWR_REFADC;

    m_sHwConfigData.wDcOffset             = DEFAULT_DC_OFFSET;
    m_sHwConfigData.wDcShift              = DEFAULT_DC_SHIFT;
}

/**
 * @brief Save the hardware configuration data to the backup device.
 * @param None
 * @return The size of the saved data.
 */
int CSetupMgr::SaveHwConfigData(void)
{
    int    nSize;
    UCHAR  *pTemp = m_pSetupDataBuff;

    // Clear buffer
    memset(pTemp, 0x00, SIZE_SETUPDATA_BUFF);

    // Data header
    memcpy(pTemp, SETUP_HEADER, SETUP_HEADER_LEN);
    pTemp += SETUP_HEADER_LEN;

    // Data
    memcpy(pTemp, &m_sHwConfigData , sizeof(RF_HW_CONFIG)); 
    pTemp += sizeof(RF_HW_CONFIG);

    // Checksum
    DWORD *pdwChecksum = (DWORD*)pTemp;
    nSize = pTemp - m_pSetupDataBuff;
    *pdwChecksum = GetCrc32(m_pSetupDataBuff, nSize);
    nSize += SETUP_CHECKSUM_LEN;

    // Make size of double word
    nSize = MakeSizeOfDoubleWord(nSize);

    // Write to backup device
    m_pDataBackHw->BackDataSegmentWrite(m_pSetupDataBuff, nSize);

#ifdef __ENABLE_SETUP_SAVEVERIFY__
    m_pDataBackHw->BackDataSegmentRead(m_pSetupVerifyBuff, nSize);
    if(!CompareSetupData(m_pSetupVerifyBuff, m_pSetupDataBuff, nSize))
    {
        DEBUG_LOG("SaveHwConfigData] Verify Error, size: %d\r\n", nSize);
        bRet = FALSE;
    }
#endif

    return nSize;
}

/**
 * @brief Load the hardware configuration data from the backup device.
 * @param None
 * @return True if the data is loaded successfully, false otherwise
 */
bool CSetupMgr::LoadHwConfigData(void)
{
    bool bRet = false;
    int nReadSize = sizeof(RF_HW_CONFIG) + SETUP_HEADER_LEN + SETUP_CHECKSUM_LEN;
    nReadSize = MakeSizeOfDoubleWord(nReadSize);

    if (m_pDataBackHw->BackDataSegmentRead(m_pSetupDataBuff, nReadSize) >= nReadSize)
    {
        if(VerifySetupData(m_pSetupDataBuff, sizeof(RF_HW_CONFIG) + SETUP_HEADER_LEN))
        {
            UCHAR  *pTemp = m_pSetupDataBuff;
            pTemp += SETUP_HEADER_LEN;

            memmove(&m_sHwConfigData, pTemp, sizeof(RF_HW_CONFIG));
            pTemp += sizeof(RF_HW_CONFIG);

            DEBUG_LOG("LoadHwConfigData] PWR1-H : %d, PWR1-L : %d, PWR2-H : %d, PWR2-H : %d, TCXO : %d, VSWR : %d\r\n",
                    m_sHwConfigData.wPowerLevelCh1_High,
                    m_sHwConfigData.wPowerLevelCh1_Low,
                    m_sHwConfigData.wPowerLevelCh2_High,
                    m_sHwConfigData.wPowerLevelCh2_Low,
                    m_sHwConfigData.wTcxoLevel, 
                    m_sHwConfigData.wVswrLimit);

            VerifyHwConfigData();
            bRet = true;
        }
        else
        {
            WARNING_LOG("LoadHwConfigData] Verify Error, size: %d\r\n", nReadSize);
        }
    }

    if (!bRet)
    {
        InitHwConfigData();
        ReserveToSaveHwConfigData();
    }

    return bRet;
}

/**
 * @brief Verify the hardware configuration data.
 * @param None
 * @return None
 */
void CSetupMgr::VerifyHwConfigData(void)
{
    BOOL bLoadError = FALSE;

    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wPowerLevelCh1_High   )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_TX_PW_HIGH))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wPowerLevelCh1_Low    )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_TX_PW_LOW))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wPowerLevelCh2_High   )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_TX_PW_HIGH))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wPowerLevelCh2_Low    )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_TX_PW_LOW))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wTcxoLevel            )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_TCXO_LEVEL))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wVswrLimit            )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_VSWR_REFADC))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wDcOffset             )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_DC_OFFSET))
        bLoadError = TRUE;
    if(RangeCheckBack16((BACK16*)(&(m_sHwConfigData.wDcShift              )), 0,  SYS_ADC_VALUE_MAX,  DEFAULT_DC_OFFSET))
        bLoadError = TRUE;

    CBuiltInTestMgr::getInst()->SetEEPROMError(bLoadError);
}


/**
 * @brief Initialize the system configuration data to default values.
 * @param None
 * @return None
 */
void CSetupMgr::InitSysConfigData(void)
{
    cShip::getOwnShipInst()->ClearOwnShipInfo();

    // Clear all system configuration data
    memset(&m_sSysConfigData, 0, sizeof(m_sSysConfigData));

    // Set default positioning configuration data.
    m_sSysConfigData.bEnableExtEPFS        = true;
    m_sSysConfigData.bEnableExtHeading     = true;
    m_sSysConfigData.bEnableExtROT         = true;
    m_sSysConfigData.bEnableAlr14          = true;
    m_sSysConfigData.bPilotPortRestricted  = false;

    // Set default gnss antenna type
    m_sSysConfigData.nIntGnssType  = INT_GNSS_CFG_IDX_DFLT;
    m_sSysConfigData.nExtGnssType  = EXT_GNSS_CFG_IDX_DFLT;
    m_sSysConfigData.bEnableSBAS   = MODE_VAL_OFF;

    // Set default port speed
    m_sSysConfigData.uPortSpeedBitmap = PORT_BAUDMASK_DFLT;

    m_sSysConfigData.bEnableDgnssByMsg17 = false;
    m_sSysConfigData.nAddrMsgTxRetryCnt  = ADDR_TX_RETRYCNT_DFLT;

    // Set default long range configuration
    m_sSysConfigData.bLongRangeTxEnableMsg27= DEFAULT_LR_TX;
    m_sSysConfigData.bLongRangeAutoReply    = DEFAULT_LR_AUTOREPLY;
    m_sSysConfigData.dwLongRangeCfg         = LR_CFG_DEFAULT;
    m_sSysConfigData.dwLongRangeCh1         = AIS_DEFAULT_TXLR_CH_1;
    m_sSysConfigData.dwLongRangeCh2         = AIS_DEFAULT_TXLR_CH_2;

    // Clear long range time data
    for(int i = 0 ; i < MAX_MMSI_LR_REQ ; i++)
    {
        m_sSysConfigData.pLongRangeTime[i].nBaseStMMSI = AIS_AB_MMSI_NULL;
        CAisLib::SetDefaultSysDateTime(&(m_sSysConfigData.pLongRangeTime[i].sCallDateTime));
    }
}

/**
 * @brief Save the system configuration data to the backup device.
 * @param None
 * @return The size of the saved data.
 */
int CSetupMgr::SaveSysConfigData(void)
{
    int    nSize;
    UCHAR  *pTemp = m_pSetupDataBuff;
    std::shared_ptr<cShip> pOwnShip = cShip::getOwnShipInst();

    // Clear buffer
    memset(pTemp, 0x00, SIZE_SETUPDATA_BUFF);

    // Data header
    memcpy(pTemp, SETUP_HEADER, SETUP_HEADER_LEN);
    pTemp += SETUP_HEADER_LEN;

    // Static data
    memmove(pTemp, &(pOwnShip->xStaticData), sizeof(STATIC_DATA));
    pTemp += sizeof(STATIC_DATA);

    // Navigation data
    memmove(pTemp, &(pOwnShip->xNavData), sizeof(NAV_DATA));
    pTemp += sizeof(NAV_DATA);

    // System configuration Data
    memcpy(pTemp, &m_sSysConfigData , sizeof(SYS_CONFIG)); 
    pTemp += sizeof(SYS_CONFIG);

    // Checksum
    DWORD *pdwChecksum = (DWORD*)pTemp;
    nSize = pTemp - m_pSetupDataBuff;
    *pdwChecksum = GetCrc32(m_pSetupDataBuff, nSize);
    nSize += SETUP_CHECKSUM_LEN;

    // Make size of double word
    nSize = MakeSizeOfDoubleWord(nSize);

    // Write to backup device
    m_pDataBackSys->BackDataSegmentWrite(m_pSetupDataBuff, nSize);

#ifdef __ENABLE_SETUP_SAVEVERIFY__
    m_pDataBackSys->BackDataSegmentRead(m_pSetupVerifyBuff, nSize);
    if(!CompareSetupData(m_pSetupVerifyBuff, m_pSetupDataBuff, nSize))
    {
        DEBUG_LOG("SaveSysConfigData] Verify Error, size: %d\r\n", nSize);
        bRet = FALSE;
    }
#endif

    return nSize;
}

/**
 * @brief Load the system configuration data from the backup device.
 * @param None
 * @return True if the data is loaded successfully, false otherwise
 */
bool CSetupMgr::LoadSysConfigData(void)
{
    bool bRet = false;
    std::shared_ptr<cShip> pOwnShip = cShip::getOwnShipInst();
    int nDataSize = sizeof(STATIC_DATA) + sizeof(NAV_DATA) + sizeof(SYS_CONFIG);
    int nReadSize = nDataSize + SETUP_HEADER_LEN + SETUP_CHECKSUM_LEN;
    nReadSize = MakeSizeOfDoubleWord(nReadSize);

    if (m_pDataBackSys->BackDataSegmentRead(m_pSetupDataBuff, nReadSize) >= nReadSize)
    {
        if(VerifySetupData(m_pSetupDataBuff, nDataSize + SETUP_HEADER_LEN))
        {
            UCHAR  *pTemp = m_pSetupDataBuff;
            pTemp += SETUP_HEADER_LEN;

            memmove(&(pOwnShip->xStaticData), pTemp, sizeof(STATIC_DATA));
            pTemp += sizeof(STATIC_DATA);
            memmove(&(pOwnShip->xNavData), pTemp, sizeof(NAV_DATA));
            pTemp += sizeof(NAV_DATA);
            memmove(&m_sSysConfigData, pTemp, sizeof(SYS_CONFIG));
            pTemp += sizeof(SYS_CONFIG);

            VerifySysConfigData();
            bRet = true;
        }
        else
        {
            WARNING_LOG("LoadHwConfigData] Verify Error, size: %d\r\n", nReadSize);
        }
    }

    if (!bRet)
    {
        InitSysConfigData();
        ReserveToSaveSysConfigData();
    }

    CSensorMgr::getInst()->UpdateOwnShipAntPos();
    return bRet;
}

/**
 * @brief Verify the system configuration data.
 * @param None
 * @return None
 */
void CSetupMgr::VerifySysConfigData(void)
{
    BOOL bLoadError = FALSE;
    std::shared_ptr<cShip> pOwnShipData = cShip::getOwnShipInst();

    // Check MMSI & IMO
    DWORD dMMSI = pOwnShipData->GetOwnShipMMSI();
    DWORD dwIMO = pOwnShipData->GetOwnShipIMO();
    RangeCheckBackDD(&dMMSI, AIS_AB_MMSI_START, AIS_AB_MMSI_LAST, AIS_AB_MMSI_NULL);
    RangeCheckBackDD(&dwIMO, AIS_IMOID_VALID_MIN, AIS_IMOID_VALID_MAX, AIS_IMOID_DFLT);

    // Check ship type, draught, person
    if(RangeCheckBack16((BACK16*)(&(pOwnShipData->xNavData.uShipType)),    AIS_SHIPTYPE_MIN,   AIS_SHIPTYPE_MAX,   AIS_SHIPTYPE_DFLT))
        bLoadError |= TRUE;
    if(RangeCheckBack16((BACK16*)(&(pOwnShipData->xNavData.uDraught)),     AIS_DRAUGHT_MIN,    AIS_DRAUGHT_MAX,    AIS_DRAUGHT_DFLT))
        bLoadError |= TRUE;
    if(RangeCheckBack16((BACK16*)(&(pOwnShipData->xNavData.uPerson)),      AIS_PERSON_MIN,     AIS_PERSON_MAX,     AIS_PERSON_DFLT))
        bLoadError |= TRUE;

    // Check ETA
    if(RangeCheckBack32((BACK32*)(&(pOwnShipData->xNavData.xETA.nMonth)),  ETA_MONTH_MIN,      ETA_MONTH_MAX,      ETA_MONTH_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack32((BACK32*)(&(pOwnShipData->xNavData.xETA.nDay)),    ETA_DAY_MIN,        ETA_DAY_MAX,        ETA_DAY_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack32((BACK32*)(&(pOwnShipData->xNavData.xETA.nHour)),   ETA_HOUR_MIN,       ETA_HOUR_MAX,       ETA_HOUR_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack32((BACK32*)(&(pOwnShipData->xNavData.xETA.nMin)),    ETA_MIN_MIN,        ETA_MIN_MAX,        ETA_MIN_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack32((BACK32*)(&(pOwnShipData->xNavData.xETA.nSec)),    ETA_SEC_MIN,        ETA_SEC_MAX,        ETA_SEC_NULL))
        bLoadError |= TRUE;

    // Check navigation status, manoeuvre
    if(RangeCheckBack16((BACK16*)(&(pOwnShipData->xNavData.uNavStatus)),   AIS_NAV_STATUS_MIN, AIS_NAV_STATUS_MAX, AIS_NAV_STATUS_DFLT))
        bLoadError |= TRUE;
    if(RangeCheckBack16((BACK16*)(&(pOwnShipData->xNavData.uManoeuvre)),   AIS_MANOEUVRE_MIN,  AIS_MANOEUVRE_MAX,  AIS_MANOEUVRE_DFLT))
        bLoadError |= TRUE;

    // Check ship name, vendor ID, call sign, destination
    pOwnShipData->xStaticData.vShipName[LEN_MAX_SHIP_NAME] = '\0';
    pOwnShipData->xStaticData.vVendorID[LEN_MAX_VENDORID] = '\0';
    pOwnShipData->xStaticData.vCallSign[LEN_MAX_CALLSIGN] = '\0';
    pOwnShipData->xNavData.pstrDestination[LEN_MAX_DESTINATION] = '\0';

    CAisLib::CheckValidChar(pOwnShipData->xStaticData.vCallSign, LEN_MAX_CALLSIGN);
    CAisLib::CheckValidChar(pOwnShipData->xStaticData.vShipName, LEN_MAX_SHIP_NAME);
    CAisLib::CheckValidChar(pOwnShipData->xStaticData.vVendorID, LEN_MAX_VENDORID);
    CAisLib::CheckValidChar(pOwnShipData->xNavData.pstrDestination, LEN_MAX_DESTINATION);

    //-------------------------------------------------------------------------------------
    // Check port speed
    if(RangeCheckBack32((BACK32*)(&m_sSysConfigData.uPortSpeedBitmap),     PORT_BAUDMASK_MIN,  PORT_BAUDMASK_MAX,      PORT_BAUDMASK_DFLT))
        bLoadError |= TRUE;

    // Check external sensor enable
    if(RangeCheckBack08((BACK08*)(&m_sSysConfigData.bEnableExtEPFS),       FALSE,        TRUE,        TRUE))
        bLoadError |= TRUE;
    if(RangeCheckBack08((BACK08*)(&m_sSysConfigData.bEnableExtHeading),    FALSE,        TRUE,        TRUE))
        bLoadError |= TRUE;
    if(RangeCheckBack08((BACK08*)(&m_sSysConfigData.bEnableExtROT),        FALSE,        TRUE,        TRUE))
        bLoadError |= TRUE;
    // Check alert 14
    if(RangeCheckBack08((BACK08*)(&m_sSysConfigData.bEnableAlr14),         FALSE,        TRUE,        TRUE))
        bLoadError |= TRUE;
    // Check DGNSS by msg17
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.bEnableDgnssByMsg17),  MODE_VAL_OFF,       MODE_VAL_ON,            MODE_VAL_ON))
        bLoadError |= TRUE;

    // Check internal antenna position
    if(RangeCheckBack16(&(m_sSysConfigData.sIntAntPos.wA), AIS_GNSS_ANT_POS_A_NULL, AIS_GNSS_ANT_POS_A_MAX, AIS_GNSS_ANT_POS_A_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sIntAntPos.wB), AIS_GNSS_ANT_POS_B_NULL, AIS_GNSS_ANT_POS_B_MAX, AIS_GNSS_ANT_POS_B_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sIntAntPos.wC), AIS_GNSS_ANT_POS_C_NULL, AIS_GNSS_ANT_POS_C_MAX, AIS_GNSS_ANT_POS_C_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sIntAntPos.wD), AIS_GNSS_ANT_POS_D_NULL, AIS_GNSS_ANT_POS_D_MAX, AIS_GNSS_ANT_POS_D_NULL))
        bLoadError |= TRUE;

    if(m_sSysConfigData.sIntAntPos.wA == AIS_GNSS_ANT_POS_A_NULL 
        || m_sSysConfigData.sIntAntPos.wB == AIS_GNSS_ANT_POS_B_NULL 
        || m_sSysConfigData.sIntAntPos.wC == AIS_GNSS_ANT_POS_C_NULL 
        || m_sSysConfigData.sIntAntPos.wD == AIS_GNSS_ANT_POS_D_NULL)
    {
        m_sSysConfigData.sIntAntPos.wA = AIS_GNSS_ANT_POS_A_NULL;
        m_sSysConfigData.sIntAntPos.wB = AIS_GNSS_ANT_POS_B_NULL;
        m_sSysConfigData.sIntAntPos.wC = AIS_GNSS_ANT_POS_C_NULL;
        m_sSysConfigData.sIntAntPos.wD = AIS_GNSS_ANT_POS_D_NULL;
    }

    // Check external antenna position
    if(RangeCheckBack16(&(m_sSysConfigData.sExtAntPos.wA), AIS_GNSS_ANT_POS_A_NULL, AIS_GNSS_ANT_POS_A_MAX, AIS_GNSS_ANT_POS_A_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sExtAntPos.wB), AIS_GNSS_ANT_POS_B_NULL, AIS_GNSS_ANT_POS_B_MAX, AIS_GNSS_ANT_POS_B_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sExtAntPos.wC), AIS_GNSS_ANT_POS_C_NULL, AIS_GNSS_ANT_POS_C_MAX, AIS_GNSS_ANT_POS_C_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sExtAntPos.wD), AIS_GNSS_ANT_POS_D_NULL, AIS_GNSS_ANT_POS_D_MAX, AIS_GNSS_ANT_POS_D_NULL))
        bLoadError |= TRUE;

    if(m_sSysConfigData.sExtAntPos.wA == AIS_GNSS_ANT_POS_A_NULL 
        || m_sSysConfigData.sExtAntPos.wB == AIS_GNSS_ANT_POS_B_NULL 
        || m_sSysConfigData.sExtAntPos.wC == AIS_GNSS_ANT_POS_C_NULL 
        || m_sSysConfigData.sExtAntPos.wD == AIS_GNSS_ANT_POS_D_NULL)
    {
        m_sSysConfigData.sExtAntPos.wA = AIS_GNSS_ANT_POS_A_NULL;
        m_sSysConfigData.sExtAntPos.wB = AIS_GNSS_ANT_POS_B_NULL;
        m_sSysConfigData.sExtAntPos.wC = AIS_GNSS_ANT_POS_C_NULL;
        m_sSysConfigData.sExtAntPos.wD = AIS_GNSS_ANT_POS_D_NULL;
    }

    // Check extended antenna position
    if(RangeCheckBack16(&(m_sSysConfigData.sExtendAntPos.wA), AIS_GNSS_ANT_POS_A_NULL, AIS_GNSS_ANT_POS_A_MAX, AIS_GNSS_ANT_POS_A_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sExtendAntPos.wB), AIS_GNSS_ANT_POS_B_NULL, AIS_GNSS_ANT_POS_B_MAX, AIS_GNSS_ANT_POS_B_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sExtendAntPos.wC), AIS_GNSS_ANT_POS_C_NULL, AIS_GNSS_ANT_POS_C_MAX, AIS_GNSS_ANT_POS_C_NULL))
        bLoadError |= TRUE;
    if(RangeCheckBack16(&(m_sSysConfigData.sExtendAntPos.wD), AIS_GNSS_ANT_POS_D_NULL, AIS_GNSS_ANT_POS_D_MAX, AIS_GNSS_ANT_POS_D_NULL))
        bLoadError |= TRUE;

    if(m_sSysConfigData.sExtendAntPos.wA == AIS_GNSS_ANT_POS_A_NULL 
        || m_sSysConfigData.sExtendAntPos.wB == AIS_GNSS_ANT_POS_B_NULL 
        || m_sSysConfigData.sExtendAntPos.wC == AIS_GNSS_ANT_POS_C_NULL 
        || m_sSysConfigData.sExtendAntPos.wD == AIS_GNSS_ANT_POS_D_NULL)
    {
        m_sSysConfigData.sExtendAntPos.wA = AIS_GNSS_ANT_POS_A_NULL;
        m_sSysConfigData.sExtendAntPos.wB = AIS_GNSS_ANT_POS_B_NULL;
        m_sSysConfigData.sExtendAntPos.wC = AIS_GNSS_ANT_POS_C_NULL;
        m_sSysConfigData.sExtendAntPos.wD = AIS_GNSS_ANT_POS_D_NULL;
    }

    // Check gnss antenna type, SBAS, DGNSS by msg17, show testing SART, long range tx enable msg27, long range auto reply, long range cfg, long range ch1, long range ch2, long range time, silent mode, pilot port restricted, addr msg tx retry cnt, show testing SART, user key SSA, pwd admin, pwd user, reserved
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.nIntGnssType), INT_GNSS_CFG_IDX_MIN,   INT_GNSS_CFG_IDX_MAX,   INT_GNSS_CFG_IDX_DFLT))
        bLoadError |= TRUE;
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.nExtGnssType), EXT_GNSS_CFG_IDX_MIN,   EXT_GNSS_CFG_IDX_MAX,   EXT_GNSS_CFG_IDX_DFLT))
        bLoadError |= TRUE;
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.bEnableSBAS),  MODE_VAL_OFF,           MODE_VAL_ON,            MODE_VAL_OFF))
        bLoadError |= TRUE;

    // Check long range tx enable msg27, long range auto reply, long range cfg, long range ch1, long range ch2
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.bLongRangeTxEnableMsg27),  MODE_VAL_OFF,    MODE_VAL_ON,    DEFAULT_LR_TX))
        bLoadError |= TRUE;
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.bLongRangeAutoReply),      MODE_VAL_OFF,    MODE_VAL_ON,    DEFAULT_LR_AUTOREPLY))
        bLoadError |= TRUE;
    if(RangeCheckBack32((BACK32*)(&m_sSysConfigData.dwLongRangeCfg),           0,               LR_CONFIG_MAX,  LR_CFG_DEFAULT))
        bLoadError |= TRUE;

    if(RangeCheckBack32((BACK32*)(&m_sSysConfigData.dwLongRangeCh1),      AIS_LR_CH_MIN,      AIS_LR_CH_MAX,      AIS_DEFAULT_TXLR_CH_1))
        bLoadError |= TRUE;
    if(RangeCheckBack32((BACK32*)(&m_sSysConfigData.dwLongRangeCh2),      AIS_LR_CH_MIN,      AIS_LR_CH_MAX,      AIS_DEFAULT_TXLR_CH_2))
        bLoadError |= TRUE;
    for(int i = 0 ; i < MAX_MMSI_LR_REQ ; i++)
    {
        if(!CAisLib::IsValidMMSI_BaseSt(m_sSysConfigData.pLongRangeTime[i].nBaseStMMSI) || !CAisLib::IsValidAisSysDateTime(&(m_sSysConfigData.pLongRangeTime[i].sCallDateTime)))
        {
            m_sSysConfigData.pLongRangeTime[i].nBaseStMMSI = AIS_AB_MMSI_NULL;
            CAisLib::SetDefaultSysDateTime(&(m_sSysConfigData.pLongRangeTime[i].sCallDateTime));
        }
    }

    // Check silent mode, pilot port restricted
    if(RangeCheckBack08((BACK08*)(&m_sSysConfigData.bEnableSilentMode),    FALSE,        TRUE,        FALSE))
        bLoadError |= TRUE;
    if(RangeCheckBack08((BACK08*)(&m_sSysConfigData.bPilotPortRestricted), FALSE,        TRUE,        FALSE))
        bLoadError |= TRUE;

    // Check show testing SART
    if(RangeCheckBack16((BACK16*)(&m_sSysConfigData.bShowTestingSART),     MODE_VAL_OFF, MODE_VAL_ON, MODE_VAL_OFF))
        bLoadError |= TRUE;

#ifdef __SAVE_NMEAOUT_SETUP__
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableRMC)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableGSV)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableGLL)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableVTG)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableGBS)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableGGA)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableZDA)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableGNS)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
    RangeCheckBack16((BACK16*)(&(m_sNmeaToMKD.bEnableGSA)),    MODE_VAL_OFF,    MODE_VAL_ON,    MODE_VAL_ON);
#endif

    CBuiltInTestMgr::getInst()->SetROMError(bLoadError);
}

/**
 * @brief Save the ROS configuration data to the backup device.
 * @param None
 * @return The size of the saved data.
 */
int CSetupMgr::SaveRosConfigData(void)
{
    bool bRet = true;
    int  nTotalSize = CROSMgr::getInst()->SerializeRosData(m_pSetupDataBuff);    // Make size of double word
    nTotalSize = MakeSizeOfDoubleWord(nTotalSize);

    m_pDataBackRos->BackDataSegmentWrite(m_pSetupDataBuff, nTotalSize);

#ifdef __ENABLE_SETUP_SAVEVERIFY__
    m_pDataBackRos->BackDataSegmentRead(m_pSetupVerifyBuff, nTotalSize);
    if(!CompareSetupData(m_pSetupVerifyBuff, m_pSetupDataBuff, nTotalSize))
    {
        DEBUG_LOG("SerializeRosData] Verify Error, size: %d\r\n", nTotalSize);
        bRet = false;
    }
#endif

    return bRet;
}

/**
 * @brief Load the ROS configuration data from the backup device.
 * @param None
 * @return True if the data is loaded successfully, false otherwise
 */
bool CSetupMgr::LoadRosConfigData(void)
{
    bool bRet = false;
    int  nDataSize = CROSMgr::getInst()->SerializeRosData(m_pSetupDataBuff);
    int  nTotalSize = MakeSizeOfDoubleWord(nDataSize);

    if (m_pDataBackRos->BackDataSegmentRead(m_pSetupDataBuff, nTotalSize) >= nTotalSize)
    {
        if(VerifySetupData(m_pSetupDataBuff, nDataSize - SETUP_CHECKSUM_LEN))
        {
            CROSMgr::getInst()->LoadRosConfigData(m_pSetupDataBuff);
            bRet = true;
        }
        else
        {
            DEBUG_LOG((char*)"LoadRosConfigData] broken!!! sizeToRead: %d -> head broken: %c%c%c%c\r\n",
                nTotalSize, 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER], 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER+1], 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER+2], 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER+3]);
        }
    }

    if (!bRet)
    {
        CROSMgr::getInst()->ClearRosData();
        ReserveToSaveRosConfigData();
    }

    return bRet;
}

/**
 * @brief Save the event log configuration data to the backup device.
 * @param None
 * @return The size of the saved data.
 */
bool CSetupMgr::SaveLogConfigData(void)
{
    bool bRet = true;
    int  nTotalSize = CEventLogMgr::getInst()->SerializeEventLog(m_pSetupDataBuff);
    nTotalSize = MakeSizeOfDoubleWord(nTotalSize);

    m_pDataBackLog->BackDataSegmentWrite(m_pSetupDataBuff, nTotalSize);

#ifdef __ENABLE_SETUP_SAVEVERIFY__
    m_pDataBackLog->BackDataSegmentRead(m_pSetupVerifyBuff, nTotalSize);
    if(!CompareSetupData(m_pSetupVerifyBuff, m_pSetupDataBuff, nTotalSize))
    {
        DEBUG_LOG("SaveLogConfigData] Verify Error, size: %d\r\n", nTotalSize);
        bRet = FALSE;
    }
#endif

    return bRet;
}

/**
 * @brief Load the event log configuration data from the backup device.
 * @param None
 * @return True if the data is loaded successfully, false otherwise
 */
bool CSetupMgr::LoadLogConfigData(void)
{
    bool bRet = false;
    int  nDataSize = CEventLogMgr::getInst()->SerializeEventLog(m_pSetupDataBuff);
    int  nTotalSize = MakeSizeOfDoubleWord(nDataSize);

    if (m_pDataBackLog->BackDataSegmentRead(m_pSetupDataBuff, nTotalSize) >= nTotalSize)
    {
        if(VerifySetupData(m_pSetupDataBuff, nDataSize - SETUP_CHECKSUM_LEN))
        {
            CEventLogMgr::getInst()->LoadEventLog(m_pSetupDataBuff);
            bRet = true;
        }
        else
        {
            DEBUG_LOG((char*)"LoadLogConfigData] broken!!! sizeToRead: %d -> head broken: %c%c%c%c\r\n",
                nTotalSize, 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER], 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER+1], 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER+2], 
                m_pSetupDataBuff[SETUP_OFFSET_HEADER+3]);
        }
    }

    if (!bRet)
    {
        CEventLogMgr::getInst()->ClearEventLog();
        CEventLogMgr::getInst()->VerifyEventLog();
        ReserveToSaveLogConfigData();
    }

    return bRet;
}

/**
 * @brief Set the all clear mode for the backup device.
 * @param nMode The mode to set (0: normal, 1: clear)
 * @return None
 */
void CSetupMgr::SetBackAllClearMode(int nMode)
{
    m_pDataBackSys->SetAllClearMode(nMode);
    m_pDataBackRos->SetAllClearMode(nMode);
    m_pDataBackLog->SetAllClearMode(nMode);
}

/**
 * @brief Load all configuration data from the backup device.
 * @param None
 * @return None
 */
void CSetupMgr::LoadAllConfigData(void)
{
    bool bLoadOkHw  = LoadHwConfigData();
    bool bLoadOkSys = LoadSysConfigData();

    BOOL bLoadOkRos = LoadRosConfigData();
    BOOL bLoadOkLog = LoadLogConfigData();

    m_bSetupLoadError = (!bLoadOkHw || !bLoadOkSys || !bLoadOkRos || !bLoadOkLog);

    if(m_bSetupLoadError)
    {
        WARNING_LOG("LoadAllConfigData] Error, HwConfig: %d, Sys: %d, ROS: %d, EventLog: %d\r\n",
            bLoadOkHw, bLoadOkSys, bLoadOkRos, bLoadOkLog);
    }
}

/**
 * @brief Save all configuration data to the backup device.
 * @param bForceSave True if the data is saved forcefully, false otherwise
 * @return None
 */
void CSetupMgr::SaveAllConfigData(bool bForceSave)
{
    // Save hardware configuration data
    if (bForceSave || m_bSetupDataChgHwConfig)
    {
        SaveHwConfigData();
        m_bSetupDataChgHwConfig = false;
    }

    // Save system configuration data
    if (bForceSave || m_bSetupDataChgSys)
    {
        SaveSysConfigData();
        m_bSetupDataChgSys = false;
    }

    // Save ROS configuration data
    if (bForceSave || m_bSetupDataChgROS)
    {
        SaveRosConfigData();
        m_bSetupDataChgROS = false;
    }

    // Save event log configuration data
    if (bForceSave || m_bSetupDataChgLog)
    {
        SaveLogConfigData();
        m_bSetupDataChgLog = false;
    }
}

/**
 * @brief Reserve to save the hardware configuration data.
 * @param None
 * @return None
 */
void CSetupMgr::ReserveToSaveHwConfigData()
{
    m_bSetupDataChgHwConfig = true;
}

/**
 * @brief Reserve to save the system configuration data.
 * @param None
 * @return None
 */
void CSetupMgr::ReserveToSaveSysConfigData(void)
{
    m_bSetupDataChgSys = true;
}

/**
 * @brief Reserve to save the ROS configuration data.
 * @param None
 * @return None
 */
void CSetupMgr::ReserveToSaveRosConfigData(void)
{
    m_bSetupDataChgROS = true;
}

/**
 * @brief Reserve to save the event log configuration data.
 * @param None
 * @return None
 */
void CSetupMgr::ReserveToSaveLogConfigData(void)
{
    m_bSetupDataChgLog = true;
}

/**
 * @brief Reserve to save all configuration data.
 * @param None
 * @return None
 */
int CSetupMgr::GetBaudrateFromIdx(int nIdx)
{
    switch(nIdx)
    {
    case BAUD_IDX_4800:
        return 4800;
    case BAUD_IDX_38400:
        return 38400;
    case BAUD_IDX_AUTO:
    default:
        return BAUD_IDX_AUTO;
    }
    return 0;
}

/**
 * @brief Get the baud rate index from the baud rate.
 * @param nBaudrate The baud rate
 * @return The baud rate index
 */
int CSetupMgr::GetIdxFromBaudrate(int nBaudrate)
{
    switch(nBaudrate)
    {
    case 4800:
        return BAUD_IDX_4800;
    case 38400:
        return BAUD_IDX_38400;
    }
    return -1;
}

/**
 * @brief Set the baud rate for the sensor 1.
 * @param nPropertyData The baud rate
 * @return True if the baud rate is set successfully, false otherwise
 */
bool CSetupMgr::SetBaudrateSensor1(int nPropertyData)
{
    //--------------------------------------------
    // m_sSysConfigData.uPortSpeedBitmap 
    //--------------------------------------------
    // bit [1..0] : SENSOR 1
    // bit [3..2] : SENSOR 2
    // bit [5..4] : SENSOR 3
    // bit [7..6] : Long range
    // bit [9..8] : External
    //--------------------------------------------
    int nIdx = GetIdxFromBaudrate(nPropertyData);
    if(nIdx >= 0)
    {
        m_sSysConfigData.uPortSpeedBitmap &= 0x3FC;
        m_sSysConfigData.uPortSpeedBitmap |= nIdx;
        return true;
    }
    return false;
}

/**
 * @brief Get the baud rate for the sensor 1
 * @return The baud rate for the sensor 1
 */
int CSetupMgr::GetBaudrateSensor1(void)
{
    return GetBaudrateFromIdx(GET_BAUD_IDX_SENSOR1(m_sSysConfigData.uPortSpeedBitmap));
}

/**
 * @brief Set the baud rate for the sensor 2
 * @param nPropertyData The baud rate
 * @return True if the baud rate is set successfully, false otherwise
 */
bool CSetupMgr::SetBaudrateSensor2(int nPropertyData)
{
    //-----------------------------------------------------------------------------------------------
    // m_sSysConfigData.uPortSpeedBitmap 
    // 각 비트는 0:4800, 1:38400, 02:auto 을 표시, default : PORT_BAUDMASK_DFLT
    //-----------------------------------------------------------------------------------------------
    // bit [1..0] : SENSOR 1
    // bit [3..2] : SENSOR 2
    // bit [5..4] : SENSOR 3
    // bit [7..6] : Long range
    // bit [9..8] : External
    //----------------------------------------------------------------------------------------
    int nIdx = GetIdxFromBaudrate(nPropertyData);
    if(nIdx >= 0)
    {
        m_sSysConfigData.uPortSpeedBitmap &= 0x3F3;
        m_sSysConfigData.uPortSpeedBitmap |= nIdx << 2;
        return true;
    }
    return false;
}

/**
 * @brief Get the baud rate for the sensor 2
 * @return The baud rate for the sensor 2
 */
int CSetupMgr::GetBaudrateSensor2(void)
{
    return GetBaudrateFromIdx(GET_BAUD_IDX_SENSOR2(m_sSysConfigData.uPortSpeedBitmap));
}

/**
 * @brief Set the baud rate for the sensor 3
 * @param nPropertyData The baud rate
 * @return True if the baud rate is set successfully, false otherwise
 */
bool CSetupMgr::SetBaudrateSensor3(int nPropertyData)
{
    //-----------------------------------------------------------------------------------------------
    // m_sSysConfigData.uPortSpeedBitmap
    // 각 비트는 0:4800, 1:38400, 02:auto 을 표시, default : PORT_BAUDMASK_DFLT
    //-----------------------------------------------------------------------------------------------
    // bit [1..0] : SENSOR 1
    // bit [3..2] : SENSOR 2
    // bit [5..4] : SENSOR 3
    // bit [7..6] : Long range
    // bit [9..8] : External
    //----------------------------------------------------------------------------------------
    int nIdx = GetIdxFromBaudrate(nPropertyData);
    if(nIdx >= 0)
    {
        m_sSysConfigData.uPortSpeedBitmap &= 0x3CF;
        m_sSysConfigData.uPortSpeedBitmap |= nIdx << 4;
        return true;
    }
    return false;
}

/**
 * @brief Get the baud rate for the sensor 3
 * @return The baud rate for the sensor 3
 */
int CSetupMgr::GetBaudrateSensor3(void)
{
    return GetBaudrateFromIdx(GET_BAUD_IDX_SENSOR3(m_sSysConfigData.uPortSpeedBitmap));
}

/**
 * @brief Set the baud rate for the long range
 * @param nPropertyData The baud rate
 * @return True if the baud rate is set successfully, false otherwise
 */
bool CSetupMgr::SetBaudrateLongRange(int nPropertyData)
{
    //-----------------------------------------------------------------------------------------------
    // m_sSysConfigData.uPortSpeedBitmap 
    // 각 비트는 0:4800, 1:38400, 02:auto 을 표시, default : PORT_BAUDMASK_DFLT
    //-----------------------------------------------------------------------------------------------
    // bit [1..0] : SENSOR 1
    // bit [3..2] : SENSOR 2
    // bit [5..4] : SENSOR 3
    // bit [7..6] : Long range
    // bit [9..8] : External
    //----------------------------------------------------------------------------------------
    int nIdx = GetIdxFromBaudrate(nPropertyData);
    if(nIdx >= 0)
    {
        m_sSysConfigData.uPortSpeedBitmap &= 0x33F;
        m_sSysConfigData.uPortSpeedBitmap |= nIdx << 6;
        return true;
    }
    return false;
}

/**
 * @brief Get the baud rate for the long range
 * @return The baud rate for the long range
 */
int CSetupMgr::GetBaudrateLongRange(void)
{
    return GetBaudrateFromIdx(GET_BAUD_IDX_LR(m_sSysConfigData.uPortSpeedBitmap));
}

/**
 * @brief Change the long range CH1
 * @param nNumCH The channel number to be changed
 */
void CSetupMgr::ChangeLongRangeCH1(int nNumCH)
{
    m_sSysConfigData.dwLongRangeCh1 = nNumCH;
    ReserveToSaveSysConfigData();
}

/**
 * @brief Change the long range CH2
 * @param nNumCH The channel number to be changed
 */
void CSetupMgr::ChangeLongRangeCH2(int nNumCH)
{
    m_sSysConfigData.dwLongRangeCh2 = nNumCH;
    ReserveToSaveSysConfigData();
}

/**
 * @brief Factory reset for system configuration data
 */
void CSetupMgr::FactoryResetSys(void)
{
    InitSysConfigData();
    SaveSysConfigData();
}

/**
 * @brief Factory reset for ROS configuration data
 */
void CSetupMgr::FactoryResetROS(void)
{
    CROSMgr::getInst()->ClearRosData();
    SaveRosConfigData();
}

/**
 * @brief Factory reset for event log configuration data
 */
void CSetupMgr::FactoryResetEventLog(void)
{
    CEventLogMgr::getInst()->ClearEventLog();
    SaveLogConfigData();
}

/**
 * @brief Factory reset for all configuration data
 */
void CSetupMgr::FactoryResetAll(void)
{
    // Set all clear mode
    SetBackAllClearMode(1);

    // Factory reset all configuration data
    FactoryResetSys();
    FactoryResetROS();
    FactoryResetEventLog();

    // Clear all clear mode
    SetBackAllClearMode(0);
}

/**
 * @brief Factory reset for mode 3
 */
void CSetupMgr::FactoryResetMode3(void)
{
    SetBackAllClearMode(1);

    FactoryResetSys();
    FactoryResetROS();

    SetBackAllClearMode(0);
}

/**
 * @brief Restore all data
 */
void CSetupMgr::RestoreAllData(void)
{
#ifdef __USE_INTERNAL_FLASH_FOR_BACKUP__
    m_pDataBackHw->SetStartBackAddr((HW_CONF_BACK_UP_ADDR - ADDR_FLASH_BANK_OFFSET) & 0xFFFFFFF);
    m_pDataBackSys->SetStartBackAddr((SYS_DATA_BACK_UP_ADDR - ADDR_FLASH_BANK_OFFSET) & 0xFFFFFFF);
    m_pDataBackRos->SetStartBackAddr((ROS_DATA_BACK_UP_ADDR - ADDR_FLASH_BANK_OFFSET) & 0xFFFFFFF);
    m_pDataBackLog->SetStartBackAddr((LOG_DATA_BACK_UP_ADDR - ADDR_FLASH_BANK_OFFSET) & 0xFFFFFFF);

    m_pDataBackHw->SetAllClearMode(TRUE);
    m_pDataBackSys->SetAllClearMode(TRUE);
    m_pDataBackRos->SetAllClearMode(TRUE);
    m_pDataBackLog->SetAllClearMode(TRUE);

    SaveAllConfigData(true);
#endif
}

/**
 * @brief Verify the setup data
 * @param pBackData The data to be verified
 * @param nSize The size of the data
 * @return True if the data is valid, false otherwise
 */
bool CSetupMgr::VerifySetupData(UCHAR *pBackData, int nSize)
{
    bool bRet = false;
    DWORD dwChecksumCalc = 0;

    if (!memcmp(pBackData, SETUP_HEADER, 4))
    {
        dwChecksumCalc = GetCrc32(pBackData, nSize);
        bRet = (dwChecksumCalc == *((DWORD*)&(pBackData[nSize])));
    }

    return bRet;
}

/**
 * @brief Compare the setup data
 * @param pBackData1 The first data to be compared
 * @param pBackData2 The second data to be compared
 * @param nSize The size of the data
 * @return True if the data is same, false otherwise
 */
bool CSetupMgr::CompareSetupData(UCHAR *pBackData1, UCHAR *pBackData2, int nSize)
{
    bool bRet = false;

    {
        DWORD dwChecksumFlash1 = *((DWORD*)&(pBackData1[SETUP_OFFSET_CHKSUM]));
        DWORD dwChecksumFlash2 = *((DWORD*)&(pBackData2[SETUP_OFFSET_CHKSUM]));

        DWORD dwChecksumCalc1 = GetCrc32(&pBackData1[SETUP_OFFSET_DATA_START], nSize-SETUP_OFFSET_DATA_START);
        DWORD dwChecksumCalc2 = GetCrc32(&pBackData2[SETUP_OFFSET_DATA_START], nSize-SETUP_OFFSET_DATA_START);
        bRet = (dwChecksumCalc1 == dwChecksumFlash1 && dwChecksumCalc2 == dwChecksumFlash2 && dwChecksumFlash1 == dwChecksumFlash2);
    }
    return bRet;
}

/**
 * @brief Check the setup value is on or off
 * @param nValue The value to be checked
 * @return True if the value is valid, false otherwise
 */
bool CSetupMgr::CheckSetupValueOnOff(int nValue)
{
    return (nValue == MODE_VAL_OFF || nValue == MODE_VAL_ON);
}

/**
 * @brief Check the internal gnss configuration value
 * @param nSetupValue The value to be checked
 * @return True if the value is valid, false otherwise
 */
bool CSetupMgr::CheckIntGnssCfgValue(int nSetupValue)
{
    return (INT_GNSS_CFG_IDX_MIN <= nSetupValue && nSetupValue <= INT_GNSS_CFG_IDX_MAX);
}

/**
 * @brief Check the external gnss configuration value
 * @param nSetupValue The value to be checked
 * @return True if the value is valid, false otherwise
 */
bool CSetupMgr::CheckExtGnssCfgValue(int nSetupValue)
{
    return (EXT_GNSS_CFG_IDX_MIN <= nSetupValue && nSetupValue <= EXT_GNSS_CFG_IDX_MAX);
}

/**
 * @brief Reset the addressed message retransmit count
 */
void CSetupMgr::ResetAddrMsgRetransmitCount()
{
    m_nAddrMsgRetransmitCnt = ADDR_TX_RETRYCNT_DFLT;
    m_bAddrMsgRetransmitCntChg = false;
}

/**
 * @brief Set the addressed message retransmit count
 * @param nTxRetryCnt The retransmit count to be set
 */
void CSetupMgr::SetAddrMsgRetransmitCount(int nTxRetryCnt)
{
    m_nAddrMsgRetransmitCnt = nTxRetryCnt;

    m_bAddrMsgRetransmitCntChg = true;
    m_dwAddrMsgRetransmitCntChgSec = cTimerSys::getInst()->GetCurTimerSec();
}

/**
 * @brief Check the addressed message retransmit count
 */
void CSetupMgr::CheckAddrMsgRetransmitCount(void)
{
    //---------------------------------------------------------------------------------
    // ITU-R.1371-5 Annex2 5.3.1
    // Addressed messages should have a destination user ID. 
    // The source station should anticipate an acknowledgement message (Message 7 or Message 13). 
    // If an acknowledgement is not received the station excluding Class B “SO” should retry 
    // the transmission. The station should wait 4s before attempting retries. 
    // When a transmission is retried, the retransmit flag should be set to retransmitted. 
    // The number of retries should be 3, but it could be configurable between 0 and 3 retries 
    // by an external application via the presentation interface. 
    // When set to a different value by an external application, the number of retries should default
    // to 3 retries after 8 min. 
    // The overall result of the data transfer should be forwarded to above layers. 
    // The acknowledgement should be between transport layers in two stations.
    if(m_bAddrMsgRetransmitCntChg)
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwAddrMsgRetransmitCntChgSec) >= 480)    // 8 min
        {
            ResetAddrMsgRetransmitCount();
            CMKD::getInst()->SendAddrMsgRetransmitCount();
        }
    }
}

/**
 * @brief Set the NMEA output to MKD
 * @param dwSetupBitmap The NMEA output to MKD
 */
void CSetupMgr::SetSetupNmeaOutToMKD(DWORD dwSetupBitmap)
{
    //-------------------------------
    // dwBitmap
    //-------------------------------
    // bit 0 : RMC
    // bit 1 : GSV
    // bit 2 : GLL
    // bit 3 : VTG
    // bit 4 : GBS
    // bit 5 : GGA
    // bit 6 : ZDA
    // bit 7 : GSA
    //-------------------------------
    m_sNmeaToMKD.bEnableRMC = (dwSetupBitmap & SETUP_NMEABMP_RMC) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableGSV = (dwSetupBitmap & SETUP_NMEABMP_GSV) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableGLL = (dwSetupBitmap & SETUP_NMEABMP_GLL) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableVTG = (dwSetupBitmap & SETUP_NMEABMP_VTG) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableGBS = (dwSetupBitmap & SETUP_NMEABMP_GBS) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableGGA = (dwSetupBitmap & SETUP_NMEABMP_GGA) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableZDA = (dwSetupBitmap & SETUP_NMEABMP_ZDA) ? TRUE : FALSE;
    m_sNmeaToMKD.bEnableGSA = (dwSetupBitmap & SETUP_NMEABMP_GSA) ? TRUE : FALSE;
}

/**
 * @brief Get the NMEA output to MKD
 * @return The NMEA output to MKD
 */
DWORD CSetupMgr::GetSetupNmeaOutToMKD(void)
{
    DWORD dwBitmap = 0;

    dwBitmap |= m_sNmeaToMKD.bEnableRMC ? SETUP_NMEABMP_RMC : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableGSV ? SETUP_NMEABMP_GSV : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableGLL ? SETUP_NMEABMP_GLL : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableVTG ? SETUP_NMEABMP_VTG : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableGBS ? SETUP_NMEABMP_GBS : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableGGA ? SETUP_NMEABMP_GGA : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableZDA ? SETUP_NMEABMP_ZDA : 0x00;
    dwBitmap |= m_sNmeaToMKD.bEnableGSA ? SETUP_NMEABMP_GSA : 0x00;

    return dwBitmap;
}

/**
 * @brief Set the NMEA output to external device
 * @param dwSetupBitmap The NMEA output to external device
 */
void CSetupMgr::SetSetupNmeaOutToEXT(DWORD dwSetupBitmap)
{
    //-------------------------------
    // dwBitmap
    //-------------------------------
    // bit 0 : RMC
    // bit 1 : GSV
    // bit 2 : GLL
    // bit 3 : VTG
    // bit 4 : GBS
    // bit 5 : GGA
    // bit 6 : ZDA
    // bit 7 : GSA
    //-------------------------------
    m_sNmeaToEXT.bEnableRMC = (dwSetupBitmap & SETUP_NMEABMP_RMC) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableGSV = (dwSetupBitmap & SETUP_NMEABMP_GSV) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableGLL = (dwSetupBitmap & SETUP_NMEABMP_GLL) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableVTG = (dwSetupBitmap & SETUP_NMEABMP_VTG) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableGBS = (dwSetupBitmap & SETUP_NMEABMP_GBS) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableGGA = (dwSetupBitmap & SETUP_NMEABMP_GGA) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableZDA = (dwSetupBitmap & SETUP_NMEABMP_ZDA) ? TRUE : FALSE;
    m_sNmeaToEXT.bEnableGSA = (dwSetupBitmap & SETUP_NMEABMP_GSA) ? TRUE : FALSE;
}

/**
 * @brief Get the NMEA output to external device
 * @return The NMEA output to external device
 */
DWORD CSetupMgr::GetSetupNmeaOutToEXT(void)
{
    DWORD dwBitmap = 0;

    dwBitmap |= m_sNmeaToEXT.bEnableRMC ? SETUP_NMEABMP_RMC : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableGSV ? SETUP_NMEABMP_GSV : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableGLL ? SETUP_NMEABMP_GLL : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableVTG ? SETUP_NMEABMP_VTG : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableGBS ? SETUP_NMEABMP_GBS : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableGGA ? SETUP_NMEABMP_GGA : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableZDA ? SETUP_NMEABMP_ZDA : 0x00;
    dwBitmap |= m_sNmeaToEXT.bEnableGSA ? SETUP_NMEABMP_GSA : 0x00;

    return dwBitmap;
}

/**
 * @brief Check if the AIS is Class A
 * @return True if Class A, false otherwise
 */
bool CSetupMgr::IsAisAClass(void)
{
#ifdef __CLASS_B__
    return false;
#else
    return true;
#endif
}

/**
 * @brief Check if the AIS is Class B
 * @return True if Class B, false otherwise
 */
bool CSetupMgr::IsAisBClass(void)
{
    return !IsAisAClass();
}

/**
 * @brief Check if the AIS is Class B "SO"
 * @return True if Class B "SO", false otherwise
 */
bool CSetupMgr::IsAisBClassSO(void)
{
    return (!IsAisAClass());
}

/**
 * @brief Check if the AIS is Class B "CS"
 * @return True if Class B "CS", false otherwise
 */
bool CSetupMgr::IsAisBClassCS(void)
{
    return false;
}

/**
 * @brief Set the long range time
 * @param nMMSI The MMSI to be set
 * @return True if success, false otherwise
 */
bool CSetupMgr::SetLongRangeTime(int nMMSI)
{
    //------------------------------------------------------------------------
    // - Check 24 hours if there is a record sent within 24 hours
    //   TRUE  : There is a record sent within 24 hours (LRF,LR1~3 can not be sent)
    //   FALSE : There is no record sent within 24 hours (LRF,LR1~3 can be sent)
    // - First, check the delete test for the entire data, then check the insert test.
    //------------------------------------------------------------------------
    int  nElapSec;
    int  nMaxElapIdx = 0;
    int  nMaxElapSec = 0;

    for(int idx = 0 ; idx < MAX_MMSI_LR_REQ ; idx++)
    {
        if(m_sSysConfigData.pLongRangeTime[idx].sCallDateTime.nValid)
        {
            nElapSec = CAisLib::DiffTime(&(cShip::getOwnShipInst()->xSysTime), &(m_sSysConfigData.pLongRangeTime[idx].sCallDateTime));
            if(nElapSec > TIME_ONE_DAY)
            {
                DEBUG_LOG("[LR-NMEA] CheckLRtime] Check 24 Hour, nElapSec:%d, i%d", nElapSec, idx);
                m_sSysConfigData.pLongRangeTime[idx].nBaseStMMSI = AIS_AB_MMSI_NULL;
                CAisLib::ResetDefaultSysTime(&(m_sSysConfigData.pLongRangeTime[idx].sCallDateTime));
            }
            else
            {
                if(nElapSec > nMaxElapSec)
                {
                    nMaxElapSec = nElapSec;
                    nMaxElapIdx = idx;
                }
            }
        }
    }

    //------------------------------------------------------------------------
    // - Save the long range response information. Do not save the duplicate value.
    // - Delete the input data after 24 hours.
    // - If full, delete the oldest data in time and save it.
    //------------------------------------------------------------------------
    int i;
    for(i = 0 ; i < MAX_MMSI_LR_REQ; i++)
    {
        if(m_sSysConfigData.pLongRangeTime[i].nBaseStMMSI == nMMSI)
            return false;
    }

    if(i == MAX_MMSI_LR_REQ)    // check empty element.
    {
        for( i = 0 ; i < MAX_MMSI_LR_REQ; i++)
        {
            if(m_sSysConfigData.pLongRangeTime[i].nBaseStMMSI == AIS_AB_MMSI_NULL)
            {
                m_sSysConfigData.pLongRangeTime[i].nBaseStMMSI     = nMMSI;
                m_sSysConfigData.pLongRangeTime[i].sCallDateTime= cShip::getOwnShipInst()->xSysTime;
                return true;
            }
        }
    }

    if(i == MAX_MMSI_LR_REQ)    // check full element : overwrite the oldest data in time.
    {
        m_sSysConfigData.pLongRangeTime[nMaxElapIdx].nBaseStMMSI  = nMMSI;
        m_sSysConfigData.pLongRangeTime[nMaxElapIdx].sCallDateTime= cShip::getOwnShipInst()->xSysTime;
    }

    return true;
}

/**
 * @brief Set the admin password
 * @param pstrPwd The password to be set
 * @param nLen The length of the password
 */
void CSetupMgr::SetAddminPassword(char *pstrPwd, int nLen)
{
    if(nLen > MAX_PASSWORD_LEN)
        nLen = MAX_PASSWORD_LEN;

    memset(m_sSysConfigData.pstrPwdAdmin, 0, MAX_PASSWORD_LEN);
    memcpy(m_sSysConfigData.pstrPwdAdmin, pstrPwd, nLen);
    ReserveToSaveSysConfigData();
}

/**
 * @brief Check the admin password
 * @param pstrPwd The password to be compared
 * @param nLen The length of the password
 * @return True if matched, false otherwise
 */
bool CSetupMgr::CheckAdminPassword(char *pstrPwd, int nLen)
{
    if(nLen > MAX_PASSWORD_LEN)
        nLen = MAX_PASSWORD_LEN;

    if(memcmp(m_sSysConfigData.pstrPwdAdmin, pstrPwd, nLen))
        return false;

    return true;
}

/**
 * @brief Set the user password
 * @param pstrPwd The password to be set
 * @param nLen The length of the password
 */
void CSetupMgr::SetUserPassword(char *pstrPwd, int nLen) 
{ 
    if(nLen > MAX_PASSWORD_LEN)
        nLen = MAX_PASSWORD_LEN;

    memset(m_sSysConfigData.pstrPwdUser, 0, MAX_PASSWORD_LEN);
    memcpy(m_sSysConfigData.pstrPwdUser, pstrPwd, nLen);
    ReserveToSaveSysConfigData();
}

/**
 * @brief Check the user password
 * @param pstrPwd The password to be compared
 * @param nLen The length of the password
 * @return True if matched, false otherwise
 */
bool CSetupMgr::CheckUserPassword(char *pstrPwd, int nLen)
{
    if(nLen > MAX_PASSWORD_LEN)
        nLen = MAX_PASSWORD_LEN;

    if(memcmp(m_sSysConfigData.pstrPwdUser, pstrPwd, nLen))
        return false;

    return true;
}

/**
 * @brief Set the user key for SSA
 * @param pstrKey The key to be set
 * @param nLen The length of the key
 */
void CSetupMgr::SetUserKeySSA(char *pstrKey, int nLen)
{
    if(nLen > MAX_SSA_KEY_LEN)
        nLen = MAX_SSA_KEY_LEN;

    memset(m_sSysConfigData.pstrUserKeySSA, 0, MAX_SSA_KEY_LEN);
    memcpy(m_sSysConfigData.pstrUserKeySSA, pstrKey, nLen);
    ReserveToSaveSysConfigData();
}

/**
 * @brief Check the user key for SSA
 * @param pstrKey The key to be compared
 * @param nLen The length of the key
 * @return True if matched, false otherwise
 */
bool CSetupMgr::CheckUserKeySSA(char *pstrKey, int nLen)
{
    if(nLen > MAX_SSA_KEY_LEN)
        nLen = MAX_SSA_KEY_LEN;

    if(memcmp(m_sSysConfigData.pstrUserKeySSA, pstrKey, nLen))
        return false;

    return true;
}

/**
 * @brief Run periodically
 */
void CSetupMgr::RunPeriodicallySetup(void)
{
    static DWORD dwLoadOkCheckTick = 0;
    static DWORD dwFlashSaveCheckTick = 0;

    // Check retransmit count
    CheckAddrMsgRetransmitCount();

    // Save flash if reserved save flag is set
    if(SysGetDiffTimeMili(dwFlashSaveCheckTick) > 200)
    {
        SaveAllConfigData(false);
        dwFlashSaveCheckTick = SysGetSystemTimer();
    }

    // Check load ok every 1 sec
    if(SysGetDiffTimeScnd(dwLoadOkCheckTick) >= 1)
    {
        if(m_bSetupLoadError)
        {
            CMKD::getInst()->SendRequestAllSetupData();
        }

        dwLoadOkCheckTick = SysGetSystemTimer();
    }
}
