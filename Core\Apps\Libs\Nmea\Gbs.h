/**
 * @file    Gbs.h
 * @brief   Gbs header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __GBS_H__
#define __GBS_H__

/******************************************************************************
 * 
 * GBS - 
 *
 * $--GBS,hhmmss.ss,x.x,x.x,x.x,xx,x.x,x.x,x.x,h,h*hh
 *            |      |   |   |   |  |   |   |  | |
 *            1      2   3   4   5  6   7   8  9 10
 * 
 * 1. UTC time of the GGA or GNS fixassociated with this sentence.
 * 2. Expected Error in latitude
 * 3. Expected <PERSON>rror in longitude
 * 4. Expected <PERSON>rror in altitude
 * 5. ID number of most likely failed satellite
 * 6. Probalility of missed detection for most likely failed satellite
 * 7. Estimate of bias in meters on most likely failed stellite
 * 8. Standard deviation of bias estimate
 * 9. GNSS System ID
 * 10. GNSS Signal ID
 * 
 ******************************************************************************/
class CGbs : public CSentence
{
public:
    CGbs();

    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool GetGbsValidStatus(void);
    bool IsValidGbsData(void);
    bool IsGbsPosAccFlag(void);

    void RunPeriodically(void);

protected:
    SYS_TIME m_xUtcTime;

    float  m_rErrLat;
    float  m_rErrLon;
    float  m_rErrAlt;
    int    m_nFailedSatID;
    float  m_rProbability;
    float  m_rBiasMeter;
    float  m_rDeviation;
    float  m_rHPL;

    bool   m_bGbsValid;
    DWORD  m_dwRcvTick;
};

#endif /* __GBS_H__ */

