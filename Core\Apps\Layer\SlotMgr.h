#ifndef __SLOTMGR_H__
#define __SLOTMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AisLib.h"
#include "UserDirMgr.h"
#include "ChannelMgr.h"

#define    SLOTREUSE_RULE_0    0        // Rule 0: Free on selection channel and Free on the other channel.
#define    SLOTREUSE_RULE_1    1        // Rule 1: Free on selection channel and Available(1) on the other channel.
#define    SLOTREUSE_RULE_2    2        // Rule 2: Available(1) on selection channel and Free on the other channel.
#define    SLOTREUSE_RULE_3    3        // Rule 3: Available(1) on both channels.
#define    SLOTREUSE_RULE_4    4        // Rule 4: Free on selection channel and Unavailable(2) on the other channel.
#define    SLOTREUSE_RULE_5    5        // Rule 5: Available(1) on selection channel and Unavailable(2) on the other channel.
#define    SLOTREUSE_RULE_NA   6        // not reusable

class CSlotMgr : public CAisLib
{
public:
    CSlotMgr();
    ~CSlotMgr();

    static std::shared_ptr<CSlotMgr> getInst() {
        static std::shared_ptr<CSlotMgr> pInst = std::make_shared<CSlotMgr>();
        return pInst;
    }

public:
    WORD    GetTxTdmaSlotAlloc_SO(CChannelMgr *pCurCh, int nMsgID, int nStartSI, int nSizeSI, const int nNumSlotToReserve, WORD wCurFrMapSlotToAvoidID);
    WORD    GetTxTdmaSlotAlloc_RA(CChannelMgr *pCurCh, int nMsgID, const int nStartSI, const int nSizeSI, const int nNumSlotToReserve);
    WORD    GetProbPersistentAllocSlot(CChannelMgr *pCurCh, WORD *pwCandiSlotList, INT16 nNumCandidates);
    int     GetReusableCandiList(WORD *pwCandiSlotList, int nNumCandiToGet, CChannelMgr *pCurCh, int nMsgID, WORD wStartSI, int nSizeSI, int nNumSlotToReserve);
    int     GetReusableCandiSlotListByPri(WORD *pwCandiListByPriBuff, int nNumCandiToGet, INT8 nSlotReuseStep, CChannelMgr *pCurCh, WORD wStartSI, int nSizeSI, const int nNumSlotToReserve);
    int     GetReusableCandiSlotListByPriSub(WORD *pwCandiListByPriBuff, WORD *pwInputSlogList, int nNumByPri, BOOL bCheckReuseTime, int nNumCandiToGet, INT8 nSlotReuseStep, CChannelMgr *pCh, WORD wStartSI, int nSizeSI, const int nNumSlotToReserve);
    int     GetPriorityAsMsgID(int nMsgID);
    WORD    GetInternalSlotAsPriority(CChannelMgr *pCurCh, int nMsgID, WORD wStartSI, int nSizeSI);

    BOOL    CheckSlotAdjacentSlotRule(CChannelMgr *pOppCh, WORD wFrSlotID);
    BOOL    CheckFrameMapSlotFree(CChannelMgr *pCurCh, WORD wFrSlotID);
    BOOL    CheckReuse1minRule(xDIRDATA *pDirData);
    BOOL    CheckReuse1minRule(int nMMSI);
    BOOL    CheckFrameMapSlotAvail(CChannelMgr *pCurCh, WORD wFrSlotID);
    BOOL    CheckStationNoPosReportFor3Min(DWORD dMMSI);
    BOOL    IsSlotBaseStMsg20FATDMA(CChannelMgr *pCurCh, WORD wFrSlotID);

    void    RunPeriodicallySlotMgr();

    void    GetSlotAllocPriorityList(CChannelMgr *pCurCh, int nMsgID, WORD wStartSI, int nSizeSI);
protected:
    int     GetCandiSlotBuffByPri(WORD *pwCandiListByPriBuff, int nNumCandiToGet, INT8 nSlotReuseStep, CChannelMgr *pCurCh, WORD wStartSI, int nSizeSI, const int nNumSlotToReserve);

};

#endif//__SLOTMGR_H__
