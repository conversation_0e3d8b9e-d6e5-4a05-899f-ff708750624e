/**
 * @file    Acs.cpp
 * @brief   Acs class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "RosMgr.h"
#include "Aca.h"
#include "Acs.h"

/******************************************************************************
*
* ACS - Channel management information Source
*
* $--ACS,x,xxxxxxxxx,hhmmss.ss,xx,xx,xxxx*hh<CR><LF>
*        | |         |         |  |  |
*        1 2         3         4  5  6
*
* 1. Sequence number , 0 to 9
* 2. MMSI of originator
* 3. UTC at receipt of regional operating settings
* 4. UTC day, 01 to 31
* 5. UTC month, 01 to 12
* 6. UTC year
*
******************************************************************************/
CAcs::CAcs() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAcs::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the ACS sentence
 * @param pszSentence The sentence to be made
 * @param psRosData The ROS data
 * @param nRosIndex The ROS index
 * @return The length of the sentence
 */
int32_t CAcs::MakeSentence(char *pszSentence, xROSDATA *psRosData, int nRosIndex)
{
    char temp[RX_MAX_DATA_SIZE];

    memset(temp, 0x00, sizeof(temp));

    if(psRosData->dSrcMMSI != AIS_AB_MMSI_NULL)
        sprintf(pszSentence, "$AIACS,%01d,%09d,", CAca::GetSequentialId(), psRosData->dSrcMMSI);
    else
        sprintf(pszSentence, "$AIACS,%01d,,", CAca::GetSequentialId());

    // Set the time when ROS Data was received. 
    // (SRT: When transmitting, ".0000" is appended after the seconds information)
    if(nRosIndex != ROS_IDX_HIGHSEA)
    {
        if(psRosData->xRcvTime.nValid)
        {
            sprintf(temp, "%02d%02d%02d,%02d,%02d,%04d",
                    psRosData->xRcvTime.xTime.nHour,  psRosData->xRcvTime.xTime.nMin,
                    psRosData->xRcvTime.xTime.nSec,   psRosData->xRcvTime.xDate.nDay,
                    psRosData->xRcvTime.xDate.nMon, psRosData->xRcvTime.xDate.nYear);
            strcat(pszSentence, temp);
        }
        else
        {
            // Default ROS Data
            strcat(pszSentence, ",,,");
        }
    }
    else
    {
        // Default ROS Data
        strcat(pszSentence, ",,,");
    }

    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}