#include <stdlib.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "RtcmLib.h"
#include "GpsBoard.h"
#include "AisLib.h"
#include "Ship.h"
#include "LayerNetwork.h"
#include "SetupMgr.h"
#include "ReportRateMgr.h"
#include "SyncMgr.h"
#include "AlarmThing.h"
#include "Txt.h"
#include "PI.h"
#include "MKD.h"
#include "GpsLib.h"
#include "LayerPhysical.h"
#include "RosMgr.h"
#include "Timer.h"
#include "LongRange.h"
#include "AisModem.h"
#include "GnssInternal.h"
#include "VdlTxMgr.h"
#include "VdlRxMgr.h"
#include "UserDirMgr.h"
#include "AlarmMgr.h"
#include "EventLogMgr.h"
#include "GPIOExt.h"
#include "SensorMgr.h"

//--------------------------
// Global variables
//--------------------------

//--------------------------
// Implementation
//--------------------------
CSensorMgr::CSensorMgr()
{
    m_pSensorGnss = new cUartSYS(UARTID_4, UART4,  UART4_IRQn,  1024, 128, BAUDRATE_INT_GNSS_DFLT);
    m_pSensorExt1 = new cUartSYS(UARTID_3, USART3, USART3_IRQn, 1024, 128, BAUDRATE_INT_GNSS_DFLT);
    m_pSensorExt2 = new cUartSYS(UARTID_5, UART5,  UART5_IRQn,  1024, 128, BAUDRATE_INT_GNSS_DFLT);
    m_pSensorExt3 = new cUartSYS(UARTID_7, UART7,  UART7_IRQn,  1024, 128, BAUDRATE_INT_GNSS_DFLT);

    m_pGnssInt = new CGnssInternal(SENSORID_0, m_pSensorGnss);
    m_pSensor1 = new cGpsBoard(SENSORID_1, m_pSensorExt1);
    m_pSensor2 = new cGpsBoard(SENSORID_2, m_pSensorExt2);
    m_pSensor3 = new cGpsBoard(SENSORID_3, m_pSensorExt3);
 
    m_pHdgBuff = (int*)SysAllocMemory(sizeof(int) * HDG_BUFF_SIZE);

    m_bInitPosSensorOK = FALSE;

    m_pPosSensor    = NULL;
    m_pSogSensor    = NULL;
    m_pCogSensor    = NULL;
    m_pRotSensor    = NULL;
    m_pHdgSensor    = NULL;

    cShip::getOwnShipInst()->ClearOwnShipInfo();
    CAisLib::CalcGrfPosByReal(AIS_REAL_LAT_NULL_VAL, AIS_REAL_LON_NULL_VAL, &(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid));

    m_nPosPriority  = POS_PRIORITY_INVALID;
    m_nHdgPriority  = HDG_PRIORITY_INVALID;

    m_nHdgBuffHead  = 0;
    m_nNumHdgData   = 0;

    m_dwNavStatWrongStartSec= 0;
    m_nHdgCogDiffCnt        = 0;
    m_nGpsMismatchCnt       = 0;

    m_nAntType = EPFD_ANTENNA_TYPE_NONE;

    m_pNmeaSensorList[SENSORID_0] = (cGpsBoard*)m_pGnssInt;
    m_pNmeaSensorList[SENSORID_1] = m_pSensor1;
    m_pNmeaSensorList[SENSORID_2] = m_pSensor2;
    m_pNmeaSensorList[SENSORID_3] = m_pSensor3;
}

CSensorMgr::~CSensorMgr()
{
    delete m_pSensorGnss;
    delete m_pSensorExt1;
    delete m_pSensorExt2;
    delete m_pSensorExt3;

    delete m_pGnssInt;
    delete m_pSensor1;
    delete m_pSensor2;
    delete m_pSensor3;
}

int CSensorMgr::GetSensorID(cGpsBoard *pSensor)
{
    return (pSensor ? pSensor->m_nSensorID : SENSORID_UNDEF);
}

void CSensorMgr::SetAntType(BYTE bAntType)
{
    if(bAntType != m_nAntType)
    {
        m_nAntType = bAntType;
        cShip::getOwnShipInst()->xStaticData.wAntType = m_nAntType;
        UpdateOwnShipAntPos();
    }
}

void CSensorMgr::UpdateOwnShipAntPos()
{
    switch(cShip::getOwnShipInst()->xStaticData.wAntType)
    {
    case EPFD_ANTENNA_TYPE_INT:
        memcpy(&cShip::getOwnShipInst()->xStaticData.sAntPos, CSetupMgr::getInst()->GetIntAntennaPos(), sizeof(xANTPOS));
        break;
    case EPFD_ANTENNA_TYPE_EXT:
        memcpy(&cShip::getOwnShipInst()->xStaticData.sAntPos, CSetupMgr::getInst()->GetExtAntennaPos(), sizeof(xANTPOS));
        break;
    case EPFD_ANTENNA_TYPE_NONE:
    default:
        memset(&cShip::getOwnShipInst()->xStaticData.sAntPos, 0, sizeof(xANTPOS));
        break;
    }
}

BYTE CSensorMgr::GetEpfDeviceType()
{
    if(!m_pPosSensor)
        return AIS_EPFD_UNDEFINED;

    if(m_nAntType == EPFD_ANTENNA_TYPE_INT)
    {
        return AIS_EPFD_INT_GNSS;
    }
    else if(m_nAntType == EPFD_ANTENNA_TYPE_EXT)
    {
        return m_pPosSensor->GetTypeOfEPFS();
    }
    return AIS_EPFD_UNDEFINED;
}

void CSensorMgr::SetIntGnssTimePulseConfig(_tagPpsConfig nConfigPPS)
{
    UBX_CfgTimePulse(m_pGnssInt, nConfigPPS);
}

void CSensorMgr::SetInternalGnssConfig(BYTE bGnssConfig, BOOL bEnableSBAS)
{
    m_pGnssInt->SetGnssConfig(bGnssConfig, bEnableSBAS);
}

BOOL CSensorMgr::IsExtDGNSSMode(void)
{
    return (m_pPosSensor && (m_pPosSensor != m_pGnssInt) && m_pPosSensor->IsDgnssCorrected());
}

BOOL CSensorMgr::IsExtGNSSMode(void)
{
    return (m_pPosSensor && (m_pPosSensor != m_pGnssInt) && !m_pPosSensor->IsDgnssCorrected());
}

BOOL CSensorMgr::IsIntDGNSSMode(void)
{
    return (m_pPosSensor && (m_pPosSensor == m_pGnssInt) && m_pGnssInt->IsDgnssCorrectedByMsg17());
}

BOOL CSensorMgr::IsIntGNSSMode(void)
{
    return (m_pPosSensor && (m_pPosSensor == m_pGnssInt) && !m_pGnssInt->IsDgnssCorrected());
}

BOOL CSensorMgr::IsExtSogCogMode(void)
{
    return ((m_pSogSensor && (m_pSogSensor != m_pGnssInt)) || (m_pCogSensor && (m_pCogSensor != m_pGnssInt)));
}

BOOL CSensorMgr::IsIntSogCogMode(void)
{
    return ((m_pSogSensor && (m_pSogSensor == m_pGnssInt)) || (m_pCogSensor && (m_pCogSensor == m_pGnssInt)));
}

BOOL CSensorMgr::IsIntGNSSSbasMode(void)
{
    return (m_pPosSensor && (m_pPosSensor == m_pGnssInt) && m_pGnssInt->IsDgnssCorrected() && !m_pGnssInt->IsDgnssCorrectedByMsg17());
}

BOOL CSensorMgr::IsUtcTimeValid(int nSensorId)
{
    cGpsBoard *pSensor;

    if      (nSensorId == SENSORID_1)   pSensor = m_pSensor1;
    else if (nSensorId == SENSORID_2)   pSensor = m_pSensor2;
    else if (nSensorId == SENSORID_3)   pSensor = m_pSensor3;
    else                                pSensor = m_pGnssInt;

    return pSensor->IsUtcTimeValid();
}

BOOL CSensorMgr::IsPosUtcFixed(int nSensorId)
{
    cGpsBoard *pSensor;

    if      (nSensorId == SENSORID_1)   pSensor = m_pSensor1;
    else if (nSensorId == SENSORID_2)   pSensor = m_pSensor2;
    else if (nSensorId == SENSORID_3)   pSensor = m_pSensor3;
    else                                pSensor = m_pGnssInt;

    return pSensor->IsPosUtcFixed();
}

BOOL CSensorMgr::IsIntGNSSConnected(void)
{
    return m_pGnssInt->IsConnected();
}

BOOL CSensorMgr::ProcessDgnssDataFromMsg17(UINT uBaseStID, xAISMSG17* pRxMsg17)
{
    UCHAR pDgnssData[160];
    UCHAR pRtcmData[160];
    int   nRtcmLen;

    if(uBaseStID == m_pGnssInt->m_uMsg17BaseStId)
        m_pGnssInt->SetDgnssDataSrcBaseSt(uBaseStID, &(pRxMsg17->xAllPosX), pRxMsg17->wZcount, pRxMsg17->bHealth);

    if(!m_pGnssInt->IsMsg17RefStAvailable(pRxMsg17))
    {
        WARNING_LOG("rcv-MSG17] error-2, IsMsg17RefStAvailable fail BS:%09d\r\n", uBaseStID);
        return FALSE;
    }

    if(pRxMsg17->dRtcmMsgType ==  1 || pRxMsg17->dRtcmMsgType ==  3 ||
        pRxMsg17->dRtcmMsgType ==  6 || pRxMsg17->dRtcmMsgType ==  9 ||   // 1,3,6,9 = DGPS
        pRxMsg17->dRtcmMsgType == 31 || pRxMsg17->dRtcmMsgType == 34 ||
        pRxMsg17->dRtcmMsgType == 36)                                                        // 31,34,36= DGLONASS
    {
        int nSizeDgnssData = pRxMsg17->dDataSize;
        memmove(&pDgnssData[1], pRxMsg17->pDataBuff, nSizeDgnssData);
        pDgnssData[0] = 0x66;                                                                                    // 0x66 = RTCM_PREAMBLE;
        nSizeDgnssData++;

        nRtcmLen = MakeMsgDGNSSToRTCMSC104(pDgnssData, nSizeDgnssData, pRtcmData);

        m_pGnssInt->SendData((UCHAR*)pRtcmData, nRtcmLen);
        m_pGnssInt->SetDgnssDataSrcBaseSt(uBaseStID, &(pRxMsg17->xAllPosX), pRxMsg17->wZcount, pRxMsg17->bHealth);

        DEBUG_LOG("rcv-MSG17] OK, BaseST:%09d, type: %d, rtcmBytes: %d -> %d, health: %d, Z: %d\r\n",
                m_pGnssInt->m_uMsg17BaseStId, pRxMsg17->dRtcmMsgType, nSizeDgnssData, nRtcmLen,
                pRxMsg17->bHealth, pRxMsg17->wZcount);
    }
    else
    {
        WARNING_LOG("rcv-MSG17] NG, msg17Enable:%d, BS:%09d, type:%d, dataBytes:%d, health: %d, Z: %d\r\n",
            CSetupMgr::getInst()->GetEnableDgnssByMsg17(), uBaseStID, pRxMsg17->dRtcmMsgType, pRxMsg17->dDataSize,
            pRxMsg17->bHealth, pRxMsg17->wZcount);
        return FALSE;
    }

    return TRUE;
}

int CSensorMgr::GetIntGNSSUtcSec(void)
{
    return m_pGnssInt->GetUtcSec();
}

BOOL CSensorMgr::GetSensorDate(cGpsBoard *pSensor, int *pYear, int *pMonth, int *pDay)
{
    if(pSensor && pSensor->IsUtcDateValid())
    {
        *pYear = pSensor->GetUtcYear();
        *pMonth= pSensor->GetUtcMon();
        *pDay  = pSensor->GetUtcDay();
        return TRUE;
    }

    *pYear   = DTTM_YEAR_NULL;
    *pMonth  = DTTM_MONTH_NULL;
    *pDay    = DTTM_DAY_NULL;
    return FALSE;
}

BOOL CSensorMgr::GetSensorDate(cGpsBoard *pSensor, SYS_DATE *pDate)
{
    return GetSensorDate(pSensor, &pDate->nYear, &pDate->nMon, &pDate->nDay);
}

BOOL CSensorMgr::GetSensorTime(cGpsBoard *pSensor, int *pHour,int *pMin,int *pSec)
{
    if(pSensor && pSensor->IsUtcTimeValid())
    {
        *pHour = pSensor->GetUtcHour();
        *pMin  = pSensor->GetUtcMin();
        *pSec  = pSensor->GetUtcSec();
        return TRUE;
    }
    *pHour = DTTM_HOUR_NULL;
    *pMin  = DTTM_MIN_NULL;
    *pSec  = DTTM_SEC_NULL;
    return FALSE;
}

BOOL CSensorMgr::GetSensorTime(cGpsBoard *pSensor, SYS_TIME *pxTime)
{
    return GetSensorTime(pSensor, &pxTime->nHour, &pxTime->nMin, &pxTime->nSec);
}

BOOL CSensorMgr::GetSensorDateTime(cGpsBoard *pSensor, SYS_DATE_TIME *psDateTime)
{
    return (psDateTime->nValid = (GetSensorDate(pSensor, &psDateTime->xDate) && GetSensorTime(pSensor, &psDateTime->xTime)));
}

BOOL CSensorMgr::GetSensorDateTime(int nSensorId, SYS_DATE_TIME *psDateTime)
{
    cGpsBoard *pSensor;

    if      (nSensorId == SENSORID_1)   pSensor = m_pSensor1;
    else if (nSensorId == SENSORID_2)   pSensor = m_pSensor2;
    else if (nSensorId == SENSORID_3)   pSensor = m_pSensor3;
    else                                pSensor = m_pGnssInt;

    return (psDateTime->nValid = (GetSensorDate(pSensor, &psDateTime->xDate) && GetSensorTime(pSensor, &psDateTime->xTime)));
}

void CSensorMgr::UpdateUtcDateTime()
{
	CAisLib::GetSysDateTime(&(cShip::getOwnShipInst()->xUtcTime), &(CSyncMgr::getInst()->m_utcSyncDate), &(CSyncMgr::getInst()->m_utcSyncTime));
}

void CSensorMgr::UpdateSysDateTime()
{
    SYS_DATE_TIME sSysTime;
    if(m_pGnssInt->IsRcvrBackupDateTimeValid())
    {
        cShip::getOwnShipInst()->xSysTime = m_pGnssInt->m_sGnssDateTime;
        cShip::getOwnShipInst()->bSysTimeUTC = m_pGnssInt->IsPosUtcFixed();
        cShip::getOwnShipInst()->nSysTimeSrc = SYSTIMESRC_INT_GNSS;
    }
    else if(m_pPosSensor && m_pPosSensor != m_pGnssInt && GetSensorDateTime(m_pPosSensor, &sSysTime))
    {
        cShip::getOwnShipInst()->xSysTime = sSysTime;
        cShip::getOwnShipInst()->bSysTimeUTC = FALSE;
        cShip::getOwnShipInst()->nSysTimeSrc = SYSTIMESRC_EXT_SENSOR;
    }
    else
    {
        cShip::getOwnShipInst()->xSysTime.xDate = CSyncMgr::getInst()->m_utcSyncDate;
        cShip::getOwnShipInst()->xSysTime.xTime = CSyncMgr::getInst()->m_utcSyncTime;
        cShip::getOwnShipInst()->xSysTime.nValid= (CAisLib::IsValidAisSysDate(&(cShip::getOwnShipInst()->xSysTime.xDate))
        										|| CAisLib::IsValidAisSysTime(&(cShip::getOwnShipInst()->xSysTime.xTime)));

        cShip::getOwnShipInst()->bSysTimeUTC    = CAisLib::IsValidAisSysTime(&(cShip::getOwnShipInst()->xSysTime.xTime));
        cShip::getOwnShipInst()->nSysTimeSrc    = SYSTIMESRC_EXT_SYNCSRC;

        DEBUG_LOG("----------------------------------- SysTime] SysTimeFromSyncSrc, sync:%d,%09d, %d,%04d%02d%02d,%02d%02d%02d, s:%d\r\n",
                CSyncMgr::getInst()->m_uSyncSource, CSyncMgr::getInst()->m_uSyncSrcMMSI, cShip::getOwnShipInst()->xSysTime.nValid,
                cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                cTimerSys::getInst()->GetCurTimerSec());
    }
}

int CSensorMgr::GetGpsSensorStatus(void)
{
    if(m_pPosSensor)
    {
        return m_pPosSensor->GetGpsStatus();
    }

    return GPS_FIX_STATUS_LOST;
}

int CSensorMgr::GetPosSensorIdx(void)
{
    // 0=GPS 연결안됨, 1=Sensor1(EXT), 2=Sensor2(EXT), 3=Sensor3(EXT), 4=Internal
    if(!m_pPosSensor || IsGnssLost()) {
        return 0;
    }
	if(m_pPosSensor == m_pSensor1) {
		return 1;
	}
	if(m_pPosSensor == m_pSensor2) {
		return 2;
	}
	if(m_pPosSensor == m_pSensor3) {
		return 3;
	}
	if(m_pPosSensor == (cGpsBoard*)m_pGnssInt) {
		return 4;
	}

	return 0;
}

void CSensorMgr::SetIntGnssRecv(void)
{
    m_pGnssInt->InitializeRcvr();
}

void CSensorMgr::SetSensorUartBaudrate(int nAllPortSpeed)
{
    int nBaudIdx = BAUD_IDX_38400;
    int nBaudrate;

    if (m_pSensorExt1) {
        nBaudIdx = GET_BAUD_IDX_SENSOR1(nBaudIdx);
        if(nBaudIdx != BAUD_IDX_4800 && nBaudIdx != BAUD_IDX_38400)
            nBaudIdx = BAUD_IDX_38400;

        nBaudrate = (nBaudIdx == BAUD_IDX_4800) ? 4800 : 38400;
        if (m_pSensorExt1->GetSpeed() != nBaudrate)
        {
            m_pSensorExt1->SetUartPara(nBaudrate);
        }
    }

    if (m_pSensorExt2) {
        nBaudIdx = GET_BAUD_IDX_SENSOR2(nBaudIdx);
        if(nBaudIdx != BAUD_IDX_4800 && nBaudIdx != BAUD_IDX_38400)
            nBaudIdx = BAUD_IDX_38400;

        nBaudrate = (nBaudIdx == BAUD_IDX_4800) ? 4800 : 38400;
        if (m_pSensorExt2->GetSpeed() != nBaudrate)
        {
            m_pSensorExt2->SetUartPara(nBaudrate);
        }
    }

    if (m_pSensorExt3) {
        nBaudIdx = GET_BAUD_IDX_SENSOR3(nBaudIdx);
        if(nBaudIdx != BAUD_IDX_4800 && nBaudIdx != BAUD_IDX_38400)
            nBaudIdx = BAUD_IDX_38400;

        nBaudrate = (nBaudIdx == BAUD_IDX_4800) ? 4800 : 38400;
        if (m_pSensorExt3->GetSpeed() != nBaudrate)
        {
            m_pSensorExt3->SetUartPara(nBaudrate);
        }
    }
}

BOOL CSensorMgr::IsGnssLost()
{
    return !IsGnssFixed();
}

BOOL CSensorMgr::IsGnssFixed()
{
    return (m_pPosSensor && CAisLib::IsTimeStampValid(m_pPosSensor->GetUtcTimeStamp()));
}

int CSensorMgr::GetHdgData(void)
{
    //--------------
    // scale : * 10
    //--------------

    if(m_pHdgSensor)
        return m_pHdgSensor->GetHdgVal();
    return NMEA_HDG_NULL;
}

cGpsBoard* CSensorMgr::GetPosSensorPriority1(BOOL bCheckTrustable)
{
    //-------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // Find the source using External DGNSS (corrected)
    //-------------------------------------------------------------------------
    INFO_LOG("%d, [1] %d,%d,%d [2] %d,%d,%d\r\n",
            bCheckTrustable, m_pNmeaSensorList[1]->IsPosValid(), m_pNmeaSensorList[1]->IsPosFixModeTrustable(), m_pNmeaSensorList[1]->IsDgnssCorrected(),
            m_pNmeaSensorList[2]->IsPosValid(), m_pNmeaSensorList[2]->IsPosFixModeTrustable(), m_pNmeaSensorList[2]->IsDgnssCorrected());

    if(!CSetupMgr::getInst()->GetEnableExtEPFS())
        return NULL;

    for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i] != m_pGnssInt && m_pNmeaSensorList[i]->IsPosValid() && (!bCheckTrustable || m_pNmeaSensorList[i]->IsPosFixModeTrustable()))
        {
            if(m_pNmeaSensorList[i]->IsDgnssCorrected())
                return m_pNmeaSensorList[i];
        }
    }
    return NULL;
}

cGpsBoard* CSensorMgr::GetPosSensorPriority2(BOOL bCheckTrustable)
{
    //-------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // Find the source using Internal DGNSS (corrected by Msg 17)
    //-------------------------------------------------------------------------
    if(m_pGnssInt->IsPosValid() && m_pGnssInt->IsDgnssCorrectedByMsg17() && (!bCheckTrustable || m_pGnssInt->IsPosFixModeTrustable()))
        return m_pGnssInt;
    return NULL;
}

cGpsBoard* CSensorMgr::GetPosSensorPriority3(BOOL bCheckTrustable)
{
    //-------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // Find the source using Internal DGNSS (corrected by beacon)
    //-------------------------------------------------------------------------
    if(m_pGnssInt->IsPosValid() && m_pGnssInt->IsDgnssCorrectedByBeacon() && (!bCheckTrustable || m_pGnssInt->IsPosFixModeTrustable()))
        return m_pGnssInt;
    return NULL;
}

cGpsBoard* CSensorMgr::GetPosSensorPriority4(BOOL bCheckTrustable)
{
    //-------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // Find the source using External GNSS (uncorrected)
    //-------------------------------------------------------------------------
    if(!CSetupMgr::getInst()->GetEnableExtEPFS())
        return NULL;

    for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i] != m_pGnssInt && m_pNmeaSensorList[i]->IsPosValid() && (!bCheckTrustable || m_pNmeaSensorList[i]->IsPosFixModeTrustable()))
        {
            if(m_pNmeaSensorList[i]->IsPosValid())
                return m_pNmeaSensorList[i];
        }
    }
    return NULL;
}

cGpsBoard* CSensorMgr::GetPosSensorPriority5(BOOL bCheckTrustable)
{
    //-------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // Find the source using Internal GNSS (uncorrected)
    //-------------------------------------------------------------------------

    if(m_pGnssInt->IsPosValid() && (!bCheckTrustable || m_pGnssInt->IsPosFixModeTrustable()))
        return m_pGnssInt;
    return NULL;
}

cGpsBoard* CSensorMgr::GetPosSensorPriority6(BOOL bCheckTrustable)
{
    //--------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // Find the source using External GNSS (dead reckoning position / Manual position input)
    //--------------------------------------------------------------------------------------

    if(!CSetupMgr::getInst()->GetEnableExtEPFS())
        return NULL;

    for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i] != m_pGnssInt && m_pNmeaSensorList[i]->IsPosValid() && (!bCheckTrustable || m_pNmeaSensorList[i]->IsPosFixModeTrustable()))
        {
            return m_pNmeaSensorList[i];
        }
    }
    return NULL;
}

cGpsBoard* CSensorMgr::FindPosSensorByPriority(int nStartPriority, BOOL bCheckTrustable)
{
    switch(nStartPriority)
    {
    case POS_PRIORITY_1:
        return GetPosSensorPriority1(bCheckTrustable);
    case POS_PRIORITY_2:
        return GetPosSensorPriority2(bCheckTrustable);
    case POS_PRIORITY_3:
        return GetPosSensorPriority3(bCheckTrustable);
    case POS_PRIORITY_4:
        return GetPosSensorPriority4(bCheckTrustable);
    case POS_PRIORITY_5:
        return GetPosSensorPriority5(bCheckTrustable);
    case POS_PRIORITY_6:
        return GetPosSensorPriority6(bCheckTrustable);
    }
    return NULL;
}

BOOL CSensorMgr::FindNewPosSensor(int nStartPriority, BOOL bCheckTrustable, cGpsBoard **ppResultSensor, int *pnResultPriority)
{
    cGpsBoard *pSensor = NULL;

    for(int nPriority = nStartPriority ; nPriority <= POS_PRIORITY_6 ; nPriority++)
    {
        if((pSensor = FindPosSensorByPriority(nPriority, bCheckTrustable)))
        {
            *ppResultSensor = pSensor;
            *pnResultPriority = nPriority;
            return TRUE;
        }
    }

    *ppResultSensor = NULL;
    *pnResultPriority = POS_PRIORITY_INVALID;
    return FALSE;
}

void CSensorMgr::UpdatePosSensor()
{
    //--------------------------------------------------------------------------
    // IEC-61993-2 6.10.3.5 Position sensor fallback conditions Table 4
    // IEC-61993-2 19.5.1
    //--------------------------------------------------------------------------
    cGpsBoard *pSensor = NULL;
    int nPosPriority;
    BOOL bChgedEPFS = FALSE;

    static BOOL bInitPosSensorOK= FALSE;
    static int    nOldTypeOfEPFS    = AIS_EPFD_UNDEFINED;

    BOOL bFindOK = FindNewPosSensor(POS_PRIORITY_1, TRUE, &pSensor, &nPosPriority);
    if(!bFindOK)
    {
        FindNewPosSensor(POS_PRIORITY_1, FALSE, &pSensor, &nPosPriority);
    }

    if(pSensor != m_pPosSensor)
    {
        if(pSensor)
        {
            //-----------------------------------------------------------------------------------------------
            // refer to IEC-61993-2 6.10.3.5
            // If data availability changes, the AIS shall automatically switch to the position source
            // with the highest priority available after 5s when switching downwards or 30 s when switching
            // upwards. During this period, the latest valid position shall be used for reporting.
            //-----------------------------------------------------------------------------------------------
            const DWORD nHoldTimeMs = (m_pPosSensor && nPosPriority < m_nPosPriority) ? (POS_FALLBACK_CHECKMS_UP-1) : (POS_FALLBACK_CHECKMS_DN-1);

            static cGpsBoard *pChangingPosSensor = NULL;
            static int nChangingPosPriority = POS_PRIORITY_INVALID;
            static DWORD dwChangePosStartTick = 0;
            if(pSensor != pChangingPosSensor || nPosPriority != nChangingPosPriority)
            {
                pChangingPosSensor = pSensor;
                nChangingPosPriority = nPosPriority;

                dwChangePosStartTick = SysGetSystemTimer();
            }
            else if(nChangingPosPriority != POS_PRIORITY_INVALID)
            {
                if(SysGetDiffTimeMili(dwChangePosStartTick) >= nHoldTimeMs)
                {
                    bChgedEPFS = SetPosSensor(pSensor, nPosPriority);
                    pChangingPosSensor    = NULL;
                    nChangingPosPriority= POS_PRIORITY_INVALID;
                    dwChangePosStartTick = 0;
                }
            }
        }
        else
        {
            if(m_pPosSensor && !m_pPosSensor->IsPosValid())
            {
                // No position sensor!
                bChgedEPFS = SetPosSensor(NULL, POS_PRIORITY_INVALID);
            }
        }
    }
    else if(m_pPosSensor)
    {
        if(m_pPosSensor->GetTypeOfEPFS() != nOldTypeOfEPFS)
            bChgedEPFS = TRUE;
    }

    if(bChgedEPFS)
    {
        CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();                // EPFS 데이터 변경 즉시 MSG 5 송신, 부팅 후 최초 position sensor 가 결정되었을때는 init phase 수행중이라 실제 송신이 안될 가능성도 있다!
    }

    nOldTypeOfEPFS = m_pPosSensor ? m_pPosSensor->GetTypeOfEPFS() : AIS_EPFD_UNDEFINED;

    if(!bInitPosSensorOK)
    {
        if(bChgedEPFS)
            bInitPosSensorOK = TRUE;
    }

    SendOutPosTxtSentence(FALSE);
}

BOOL CSensorMgr::SetPosSensor(cGpsBoard *pSensor, int nPosPriority)
{
    if(m_pPosSensor != pSensor || m_nPosPriority != nPosPriority)
    {
        if(pSensor)
        {
            DEBUG_LOG("-------------------------------------------- PosSensor] sensor : %d -> %d, pri : %d -> %d, int : %d, s:%d\r\n",
                    GetSensorID(m_pPosSensor), GetSensorID(pSensor),
                    m_nPosPriority, nPosPriority, m_pGnssInt->GetGpsStatus(), cTimerSys::getInst()->GetCurTimerSec());
        }
        else
        {
            WARNING_LOG("PosSensor NULL! %x\r\n", m_pPosSensor);
        }

        m_pPosSensor = pSensor;
        m_nPosPriority = nPosPriority;

        BYTE bAntType = EPFD_ANTENNA_TYPE_NONE;
        if(m_pPosSensor == m_pGnssInt)
            bAntType = EPFD_ANTENNA_TYPE_INT;
        else
            bAntType = EPFD_ANTENNA_TYPE_EXT;
        SetAntType(bAntType);

        UpdateOwnShipDynamicInfo();                        // to update MKD immediately

        gpAlarmNoPosSensor->UpdateAlarmStatus(TRUE);
        gpAlarmExtEpfsLost->UpdateAlarmStatus(TRUE);

        if(IsGnssFixed()) {
            CUserDirMgr::getInst()->UpdateStationDistance();
        }

        CMKD::getInst()->SendSysDateTime();
        m_bInitPosSensorOK = TRUE;

        return TRUE;
    }
    return FALSE;
}

float CSensorMgr::GetDistBetweenIntAndExtGnssPos()
{
    //-----------------------------------------------------------
    // the distance between internal and external GNSS antenna
    //-----------------------------------------------------------

    REAL rDistAnts = 0;
    int nLenWidth = CSetupMgr::getInst()->GetIntAntennaPosC() + CSetupMgr::getInst()->GetIntAntennaPosD();            // Beam(width) in meters
    int nLenHeight = CSetupMgr::getInst()->GetIntAntennaPosA() + CSetupMgr::getInst()->GetIntAntennaPosB();            // Length in meters
    if(nLenHeight == CSetupMgr::getInst()->GetExtAntennaPosA() + CSetupMgr::getInst()->GetExtAntennaPosB() &&
        nLenWidth == CSetupMgr::getInst()->GetExtAntennaPosC() + CSetupMgr::getInst()->GetExtAntennaPosD())
    {
        int nIntOrgX = MAX(CSetupMgr::getInst()->GetIntAntennaPosC(), CSetupMgr::getInst()->GetExtAntennaPosC());
        int nIntOrgY = MAX(CSetupMgr::getInst()->GetIntAntennaPosB(), CSetupMgr::getInst()->GetExtAntennaPosB());
        int nIntX = nIntOrgX - CSetupMgr::getInst()->GetIntAntennaPosC();
        int nExtX = nIntOrgX - CSetupMgr::getInst()->GetExtAntennaPosC();
        int nIntY = nIntOrgY - CSetupMgr::getInst()->GetIntAntennaPosB();
        int nExtY = nIntOrgY - CSetupMgr::getInst()->GetExtAntennaPosB();
        rDistAnts = sqrt(pow(nIntX-nExtX, 2) + pow(nIntY-nExtY, 2));

        rDistAnts /= 1000;            // in km
    }
    return rDistAnts;
}

void CSensorMgr::CheckIntExtPosMismatch()
{
    //------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.5
    // 외부 position sensor 가 사용되고 외부 및 내부 위치가 유효할때, 외부와 내부 위치를 1분에 한번 비교한다.
    // 만약 두 위치의 차이가 (100 meter + 두 안테나 사이의 거리) 보다 크고, 15분 동안 이 상태가 유지되면 알람 09 발생해야한다.
    //------------------------------------------------------------------------------------------------------------------------
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 60)
    {
        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();

        if(m_pPosSensor && m_pPosSensor != m_pGnssInt)
        {
            if(m_pPosSensor->IsPosUtcFixed() && m_pGnssInt->IsPosUtcFixed())
            {
                REAL rDistAnts = GetDistBetweenIntAndExtGnssPos();	// the distance between internal and external GNSS antenna
                const REAL rThreshold = rDistAnts + 0.1;            // unit : km, (100 meter + 안테나 거리)

                REAL rDistGps;
                REAL rCOG;
                CGps::GetDistanceAndCourseInMCR(m_pGnssInt->GetLatVal(), m_pGnssInt->GetLonVal(),
                								m_pPosSensor->GetLatVal(), m_pPosSensor->GetLonVal(),
												&rDistGps, &rCOG, DIST_UNIT_KM);

                if(fabs(rDistGps) > rThreshold)
                {
                    if(m_nGpsMismatchCnt < GPS_MISMATCH_MIN_CNT)
                        m_nGpsMismatchCnt++;
                    return;
                }
            }
        }
        m_nGpsMismatchCnt = 0;
    }
}

BOOL CSensorMgr::CheckExtDgnssInUse()
{
    return m_pPosSensor && m_pPosSensor->IsDgnssCorrected();
}

BOOL CSensorMgr::CheckExternalGnss()
{
    return m_pPosSensor && m_pPosSensor->IsExternalGnss();
}

void CSensorMgr::SendOutPosTxtSentence(BOOL bUnconditionally)
{
    //---------------------------------------------------------------------------
    // 현재 GPS 의 상태를 체크하기 위해 주기적으로 호출되어야 한다.
    // (동일한 센서가 유지되더라도 그 상태가 DGNSS -> GNSS 등으로 바뀔수있으므로)
    //---------------------------------------------------------------------------
    static int nOldTxtID = TXT_ID_NO_SENSOR_POS;

    int nTxtID = -1;

    if(IsGnssLost())
        nTxtID = TXT_ID_NO_SENSOR_POS;
    else if(m_pPosSensor)
    {
        if(m_pPosSensor == m_pGnssInt)
            nTxtID = m_pGnssInt->IsDgnssCorrectedByMsg17() ? TXT_ID_INT_DGNSS_MSG17 : TXT_ID_INT_GNSS;
        else
            nTxtID = m_pPosSensor->IsDgnssCorrected() ? TXT_ID_EXT_DGNSS : TXT_ID_EXT_GNSS;
    }

    if(bUnconditionally || nTxtID != nOldTxtID)
    {
        CMKD::getInst()->SendTXTtoPI(nTxtID, true);
    }

    nOldTxtID = nTxtID;
}

BOOL CSensorMgr::IsSogLost()
{
    return (!m_pSogSensor);
}

BOOL CSensorMgr::IsCogLost()
{
    return (!m_pCogSensor);
}

cGpsBoard* CSensorMgr::GetValidExtSogSensor()
{
    for(int i = SENSORID_1 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i]->IsSpdValid())
            return m_pNmeaSensorList[i];
    }
    return NULL;
}

void CSensorMgr::UpdateSogSensor()
{
    //--------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ******** SOG/ COG sensor fallback conditions
    // SOG/COG information from the internal GNSS receiver shall be used, if this internal GNSS    receiver is in use
    // as a position source. This is to avoid transmission of information referenced to different points on the ship.
    //--------------------------------------------------------------------------------------------------------------
    cGpsBoard *pSensor = NULL;
    if(m_pPosSensor)
    {
        /*
        if(m_pPosSensor->IsSpdValid())
        */
        if(m_pPosSensor->GetSpdVal() != NMEA_SOG_NULL)
            pSensor = m_pPosSensor;
        else if(m_pPosSensor->m_nSensorID != SENSORID_INTERNAL)
            pSensor = GetValidExtSogSensor();
    }
    else
        pSensor = GetValidExtSogSensor();

    if(pSensor != m_pSogSensor)
    {
        m_pSogSensor = pSensor;

        DEBUG_LOG("SOG-SRC] %d, pos:%x\r\n", m_pSogSensor ? m_pSogSensor->m_nSensorID : -1, m_pPosSensor);

        gpAlarmNoSOG->UpdateAlarmStatus(TRUE);
    }
}

cGpsBoard* CSensorMgr::GetValidExtCogSensor()
{
    for(int i = SENSORID_1 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i]->IsCrsValid())
            return m_pNmeaSensorList[i];
    }
    return NULL;
}

void CSensorMgr::UpdateCogSensor()
{
    //--------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ******** SOG/ COG sensor fallback conditions
    // SOG/COG information from the internal GNSS receiver shall be used, if this internal GNSS    receiver is in use
    // as a position source. This is to avoid transmission of information referenced to different points on the ship.
    //--------------------------------------------------------------------------------------------------------------
    cGpsBoard *pSensor = NULL;
    if(m_pPosSensor)
    {
        if(m_pPosSensor->GetCrsVal() != NMEA_COG_NULL)
            pSensor = m_pPosSensor;
        else if(m_pPosSensor->m_nSensorID != SENSORID_INTERNAL)
            pSensor = GetValidExtCogSensor();
    }
    else
        pSensor = GetValidExtCogSensor();

    if(pSensor != m_pCogSensor)
    {
        m_pCogSensor = pSensor;

        DEBUG_LOG("COG-SRC] %d, COG: %d, pos:%d, s:%d\r\n",
            m_pCogSensor ? m_pCogSensor->m_nSensorID : -1, m_pCogSensor ? m_pCogSensor->GetCrsVal() : NMEA_COG_NULL, m_pPosSensor ? m_pPosSensor->m_nSensorID : -1,
            cTimerSys::getInst()->GetCurTimerSec());

        gpAlarmNoCOG->UpdateAlarmStatus(TRUE);
    }
}

void CSensorMgr::SendOutSogCogTxtSentence(BOOL bUnconditionally)
{
    //---------------------------------------------------------------------------
    // 현재 GPS 의 상태를 체크하기 위해 주기적으로 호출되어야 한다.
    // (동일한 센서가 유지되더라도 그 상태가 DGNSS -> GNSS 등으로 바뀔수있으므로)
    //---------------------------------------------------------------------------
    static int nOldTxtID = TXT_ID_NO_SENSOR_POS;
    int nTxtID = -1;

    if(m_pSogSensor && m_pCogSensor)
        nTxtID = ((m_pSogSensor == m_pGnssInt || m_pCogSensor == m_pGnssInt) ? TXT_ID_INT_SOG_COG : TXT_ID_EXT_SOG_COG);

    if(nTxtID >= 0)
    {
        if(bUnconditionally || nTxtID != nOldTxtID)
        {
            CMKD::getInst()->SendTXTtoPI(nTxtID, true);
        }

        nOldTxtID = nTxtID;
    }
}


BOOL CSensorMgr::IsRotLost()
{
    return !m_pRotSensor;
}

int CSensorMgr::GetRotData()
{
    int nRot = NMEA_ROT_NULL;
    if(m_pRotSensor)
        nRot = m_pRotSensor->GetRotVal();

    INFO_LOG("RotData] %d, rotSensor: %d, s:%d\r\n", nRot, m_pRotSensor ? m_pRotSensor->m_nSensorID : -1, cTimerSys::getInst()->GetCurTimerSec());
    return nRot;
}

BOOL CSensorMgr::IsRotSensorOtherSrc()
{
    return (m_pRotSensor && m_pRotSensor->IsRotOtherSrc());            // Fault #199
}

cGpsBoard* CSensorMgr::FindRotSensor(int nRotSrcType)
{
    for(int i = SENSORID_0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i]->IsRotValid() && m_pNmeaSensorList[i]->GetRotSrcType() == nRotSrcType)
            return m_pNmeaSensorList[i];
    }
    return NULL;
}

void CSensorMgr::UpdateRotSensor()
{
    //--------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.7 ROT sensor fallback conditions
    // ROT data shall not be derived from COG information
    //--------------------------------------------------------------------------------------------------------------

    if(!CSetupMgr::getInst()->GetEnableExtROT())
        return;

    cGpsBoard *pSensor = FindRotSensor(ROTSCR_TI);
    if(!pSensor)
        pSensor = FindRotSensor(ROTSCR_NOT_TI);
    if(!pSensor)
        pSensor = FindRotSensor(ROTSCR_HDG);

    if(m_pRotSensor != pSensor)
    {
        m_pRotSensor = pSensor;

        if(m_pRotSensor)
        {
            DEBUG_LOG("ROT] SetRotSensor, %d, rotType:%d, s:%d\r\n",
                    m_pRotSensor->m_nSensorID, m_pRotSensor->GetRotSrcType(), cTimerSys::getInst()->GetCurTimerSec());
        }
        else
        {
            DEBUG_LOG("ROT] SetRotSensor, No ROT sensor, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        }

        if(m_pRotSensor)
            CMKD::getInst()->SendTXTtoPI(IsRotSensorOtherSrc() ? TXT_ID_OTHER_ROT_SRC : TXT_ID_ROT_INDICATOR, true);
    }
}

BOOL CSensorMgr::IsHdgLost()
{
    return !m_pHdgSensor;
}

void CSensorMgr::UpdateHdgSensor()
{
    if(!CSetupMgr::getInst()->GetEnableExtHeading())
        return;

    cGpsBoard *pSensor = NULL;
    int nPriority = HDG_PRIORITY_INVALID;
    for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(!m_pNmeaSensorList[i])
        {
            DEBUG_LOG("Error, Sensor-%d is NULL!\r\n", i);
        }

        if(m_pNmeaSensorList[i]->IsHdgValid())
        {
            pSensor = m_pNmeaSensorList[i];
            nPriority = i;
            break;
        }
    }

//    if(!m_pHdgSensor || nPriority < m_nHdgPriority)
    {
        if(pSensor != m_pHdgSensor || nPriority != m_nHdgPriority)
        {
            if(m_pHdgSensor != pSensor)
            {
                m_pHdgSensor = pSensor;
                m_nHdgPriority = nPriority;

                DEBUG_LOG("HDG-SRC] %d\r\n", m_pHdgSensor ? m_pHdgSensor->m_nSensorID : -1);

                if(m_pHdgSensor)
                {
                    CMKD::getInst()->SendTXTtoPI(TXT_ID_HEADING_VALID, true);
                }
            }
        }
    }
}


void CSensorMgr::SaveHdgAvgBuffer(int nHdg)
{
    if(nHdg == NMEA_HDG_NULL)
    {
        m_nHdgBuffHead = 0;
        m_nNumHdgData = 0;
    }
    else
    {
        m_pHdgBuff[m_nHdgBuffHead] = nHdg;
        if(++m_nHdgBuffHead >= HDG_BUFF_SIZE)
            m_nHdgBuffHead = 0;
        if(m_nNumHdgData < HDG_BUFF_SIZE)
            m_nNumHdgData++;
    }
}

float CSensorMgr::GetHdgAvg()
{
    if(!m_pHdgSensor)
        return (float)NMEA_HDG_NULL;

    float fHdgAvg = 0;
    int i;
    if(m_nNumHdgData < HDG_BUFF_SIZE)
        fHdgAvg = NMEA_HDG_NULL;
    else
    {
        for(i = 0 ; i < m_nNumHdgData ; i++)
        {
            if(m_pHdgBuff[i] == NMEA_HDG_NULL)
                break;
            fHdgAvg += m_pHdgBuff[i];
        }
        if(i >= m_nNumHdgData)
            fHdgAvg /= m_nNumHdgData;
        else
            fHdgAvg = (float)NMEA_HDG_NULL;
    }

    return fHdgAvg;
}

void CSensorMgr::SetEnableEPFS(BOOL bEnable)
{
    for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if(m_pNmeaSensorList[i] != m_pGnssInt)
            m_pNmeaSensorList[i]->SetEnableEPFS(bEnable);
    }
}

void CSensorMgr::CheckNavStatusIncorrect()
{
    static DWORD dwCheckSec = 0;
    BOOL bAlarmCondition = FALSE;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > 0)
    {
        if(cShip::getOwnShipInst()->xDynamicData.nSOG != NMEA_SOG_NULL)
        {
            if(cShip::getOwnShipInst()->xNavData.uNavStatus == AIS_NAV_STATUS_UNDER_WAY_ENGINE && cShip::getOwnShipInst()->xDynamicData.nSOG < 10)            // less than 1 kn, SOG 값 : * 10  (NM/hour)
            {
                //----------------------------------------------------------------------------------------------------------
                // Refer to IEC-61993-2 6.5.2
                // When NavStatus is under way and SOG is less than 1 kn for more than 2 h, alarm ID 10 should be generated.
                //----------------------------------------------------------------------------------------------------------
                bAlarmCondition = TRUE;

                INFO_LOG("NavStatus incorrect, navStat:%d, SOG:%d, wrongElap: %d, errStart:%d\r\n",
                        cShip::getOwnShipInst()->xNavData.uNavStatus, cShip::getOwnShipInst()->xDynamicData.nSOG, cTimerSys::getInst()->GetTimeDiffSec(m_dwNavStatWrongStartSec), m_dwNavStatWrongStartSec);

            }
        }

        if(!bAlarmCondition)
        {
            m_dwNavStatWrongStartSec = cTimerSys::getInst()->GetCurTimerSec();

            INFO_LOG("NavStatus incorrect, reset startSec, %d, navStat:%d, SOG:%d\r\n",
                    m_dwNavStatWrongStartSec, cShip::getOwnShipInst()->xNavData.uNavStatus, cShip::getOwnShipInst()->xDynamicData.nSOG);
        }

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}

void CSensorMgr::CheckHeadingSensorOffset()
{
    DWORD dwCheckSec = 0;
    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 1)
    {
        if(cShip::getOwnShipInst()->xDynamicData.nSOG != NMEA_SOG_NULL && cShip::getOwnShipInst()->xDynamicData.nCOG != NMEA_COG_NULL && cShip::getOwnShipInst()->xDynamicData.nHDG != NMEA_HDG_NULL)
        {
            //-----------------------------------------------------------------------------------------------------------------------------------
            // refer to IEC-61993-2 ********
            // Alarm ID 11 shall be activated when SOG is greater than 5 kn and the difference between COG and HDT is greater than 45° for 5 min.
            //-----------------------------------------------------------------------------------------------------------------------------------
            if(cShip::getOwnShipInst()->xDynamicData.nSOG > 50 &&
            		CAisLib::GetDiffAbsYaw((float)cShip::getOwnShipInst()->xDynamicData.nCOG/NMEA_SCALE_COG, (float)cShip::getOwnShipInst()->xDynamicData.nHDG/NMEA_SCALE_HDT) > 45)
            {
                if(m_nHdgCogDiffCnt < HDG_COG_DIFF_SEC_CNT)
                    m_nHdgCogDiffCnt++;

                return;
            }
        }
        m_nHdgCogDiffCnt = 0;
    }
}


void CSensorMgr::UpdatePosData()
{
    if(m_pPosSensor)
    {
    	CAisLib::CalcGrfPosByReal(m_pPosSensor->GetLatVal(), m_pPosSensor->GetLonVal(), &(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid));

        cShip::getOwnShipInst()->xDynamicData.nTimeStamp  = m_pPosSensor->GetUtcTimeStamp();
        cShip::getOwnShipInst()->xDynamicData.bPosAccFlag = m_pPosSensor->GetPosAccFlag();
        cShip::getOwnShipInst()->xDynamicData.bRaimFlag   = m_pPosSensor->GetRaimFlag();

        SYS_DATE_TIME sDateTime;
        if(GetSensorDateTime(m_pPosSensor, &sDateTime))
            cShip::getOwnShipInst()->xPosFixTime = sDateTime;                // It should keep the last time of position fix!!!
        else
        	CAisLib::SetDefaultSysDateTime(&(cShip::getOwnShipInst()->xPosFixTime));
    }
    else
    {
        cShip::getOwnShipInst()->xDynamicData.nTimeStamp  = AIS_TIME_STAMP_INVALID;
        cShip::getOwnShipInst()->xDynamicData.bPosAccFlag = 0;
        cShip::getOwnShipInst()->xDynamicData.bRaimFlag    = 0;

        CAisLib::SetDefaultSysDateTime(&(cShip::getOwnShipInst()->xPosFixTime));
    }

    if(m_pSogSensor)
        cShip::getOwnShipInst()->xDynamicData.nSOG = MIN(m_pSogSensor->GetSpdVal(), AIS_SOG_VDL_MAX);
    else
        cShip::getOwnShipInst()->xDynamicData.nSOG = NMEA_SOG_NULL;

    if(m_pCogSensor)
        cShip::getOwnShipInst()->xDynamicData.nCOG = MIN(m_pCogSensor->GetCrsVal(), AIS_COG_VDL_MAX);
    else
        cShip::getOwnShipInst()->xDynamicData.nCOG = NMEA_COG_NULL;
}

void CSensorMgr::UpdateOwnShipDynamicInfo()
{
    //--------------------------------------------------------------------
    // 반드시 1초에 한번씩 호출되어야한다!
    //--------------------------------------------------------------------
    cShip::getOwnShipInst()->xDynamicData.nHDG    = GetHdgData();
    cShip::getOwnShipInst()->xDynamicData.fHdgAvg = GetHdgAvg();
    cShip::getOwnShipInst()->xDynamicData.nROT    = GetRotData();

    UpdatePosData();
    SaveHdgAvgBuffer(cShip::getOwnShipInst()->xDynamicData.nHDG);

    CheckNavStatusIncorrect();
    CheckHeadingSensorOffset();
    CheckIntExtPosMismatch();

    UpdateUtcDateTime();
    UpdateSysDateTime();

    if(!(cTimerSys::getInst()->GetCurTimerSec() & 0x0000000F))        // per every 16 sec
        CMKD::getInst()->SendSysDateTime();
}

void CSensorMgr::RunUartIsrHandler(int nSensorId)
{
    if (m_pNmeaSensorList[nSensorId] != NULL)
    {
        if (nSensorId == SENSORID_0) {
            m_pSensorGnss->RunUartIsrHandler();
        }
        else if (nSensorId == SENSORID_1) {
            m_pSensorExt1->RunUartIsrHandler();
        }
        else if (nSensorId == SENSORID_2) {
            m_pSensorExt2->RunUartIsrHandler();
        }
        else {
			m_pSensorExt3->RunUartIsrHandler();
        }
    }
}

void CSensorMgr::RunProcessData()
{
    for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
    {
        if (m_pNmeaSensorList[i] != NULL)
        {
            m_pNmeaSensorList[i]->ProcessData(NULL);
        }
    }
}

void CSensorMgr::RunPeriodicallySensorMgr()
{
    static DWORD dwCheckTick = 0;

    if(SysGetDiffTimeMili(dwCheckTick) >= SENSOR_CHECK_TERM_MS)
    {
        //--------------------------------------
        // Keep the order of calling the belows!
        //--------------------------------------
        UpdatePosSensor();
        UpdateSogSensor();
        UpdateCogSensor();

        SendOutSogCogTxtSentence(FALSE);

        UpdateHdgSensor();
        UpdateRotSensor();

        UpdateOwnShipDynamicInfo();

        for(int i = 0 ; i < NUM_NMEA_SENSORS ; i++)
        {
            if (m_pNmeaSensorList[i] != NULL)
            {
                m_pNmeaSensorList[i]->RunPeriodicallyGps();
            }
        }

        dwCheckTick = SysGetSystemTimer();
    }
}
