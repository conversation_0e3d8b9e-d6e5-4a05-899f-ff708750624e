/**
 * @file    Vsd.cpp
 * @brief   Vsd class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <stdio.h>
#include "DataType_AIS.h"
#include "Ship.h"
#include "SetupMgr.h"
#include "MKD.h"
#include "LayerNetwork.h"
#include "Vsd.h"

/******************************************************************************
 * 
 * VSD - Voyage Static Data
 *
 * $--VSD,x.x,x.x,x.x,c--c,hhmmss.ss,xx,xx,x.x,x.x*hh<CR><LF>
 *         |   |   |   |     |       |  |   |   |
 *         1   2   3   4     5       6  7   8   9
 *
 * 1. Type of ship and cargo category, 0 to 255
 * 2. Maximum present static draught, 0 to 25.5
 * 3. Persons on-board, 0 to 8191
 * 4. Destination, 1-20 characters
 * 5. Est. UTC of destination arrival
 * 6. Est. day of arrival at destination, 00 to 31(UTC)
 * 7. Est. month of arrival at destination, 00 to 12(UTC)
 * 8. Navigational status, 0 to 15
 * 9. Regional application flags, 0 to 15
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
CVsd::CVsd() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 */
bool CVsd::Parse(const char *pszSentence)
{
    NAV_DATA xNavData = cShip::getOwnShipInst()->xNavData;
    char    pstrSubData1[64];
    int     nTmpData;
    char    pstrDestination[LEN_MAX_DESTINATION + 1];
    BOOL    bCfgChged            = FALSE;
    BOOL    bChgStaticMsgData    = FALSE;

    CSentence::GetFieldString(pszSentence, 1, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisIntNumStr(pstrSubData1, TRUE))
        {
            return false;
        }

        nTmpData = atoi((char*)pstrSubData1);
        if(nTmpData >= 0)    // null : not changed.(IEC 61993-2, p.100) : 0~255 사이
        {
            if(xNavData.uShipType != nTmpData)
            {
                xNavData.uShipType = nTmpData;
                bCfgChged = TRUE;
                bChgStaticMsgData = TRUE;
            }
        }
    }

    CSentence::GetFieldString(pszSentence, 2, pstrSubData1, sizeof(pstrSubData1));    // Draught: Max 25.5
    if(strlen(pstrSubData1) > 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrSubData1, TRUE))
        {
            return false;
        }

        nTmpData = atof(pstrSubData1) * 10;
        if(nTmpData < AIS_DRAUGHT_MIN || nTmpData > AIS_DRAUGHT_MAX)
        {
            return false;
        }

        if(xNavData.uDraught != nTmpData)
        {
            xNavData.uDraught = nTmpData;
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    CSentence::GetFieldString(pszSentence, 3, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        nTmpData = atoi((char*)pstrSubData1);
        if(nTmpData >= 0)    // null : not changed, : 0 ~ 8191
        {
            if(xNavData.uPerson != nTmpData)
            {
                xNavData.uPerson = nTmpData;
                bCfgChged = TRUE;
            }
        }
    }

    CSentence::GetFieldString(pszSentence, 4, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)        // null : not changed.(IEC 61993-2, p.97)
    {
        memset(pstrDestination, 0x00, sizeof(pstrDestination));
        if(!CAisLib::ConvertNormalString(pstrSubData1, pstrDestination, LEN_MAX_DESTINATION))
        {
            return false;
        }

        if(strlen(pstrDestination) > LEN_MAX_DESTINATION)
        {
            return false;
        }

        CAisLib::CheckValidChar(pstrDestination, LEN_MAX_DESTINATION);
        if(!CAisLib::IsSameAisStr(xNavData.pstrDestination, pstrDestination))
        {
            strcpy(xNavData.pstrDestination, pstrDestination);
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    CSentence::GetFieldString(pszSentence, 5, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        if(strlen(pstrSubData1) < 6)
        {
            return false;
        }

        ETA_DATA sEta = {0};

        pstrSubData1[6] = 0x00;    // NULL
        sEta.nSec = atoi((char*)(pstrSubData1+4));

        pstrSubData1[4] = 0x00;    // NULL
        sEta.nMin = atoi((char*)(pstrSubData1+2));

        pstrSubData1[2] = 0x00;    // NULL
        sEta.nHour = atoi((char*)pstrSubData1);

        if(!CAisLib::IsValidETATime(&sEta))
        {
            return false;
        }

        if(memcmp(&sEta, &(xNavData.xETA), sizeof(SYS_TIME)))
        {
            xNavData.xETA.nHour = sEta.nHour;
            xNavData.xETA.nMin = sEta.nMin;
            xNavData.xETA.nSec = sEta.nSec;
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    CSentence::GetFieldString(pszSentence, 6, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        nTmpData = atoi((char*)pstrSubData1);
        if(nTmpData < 0 || nTmpData > 31)
        {
            return false;
        }

        if(xNavData.xETA.nDay != nTmpData)
        {
            xNavData.xETA.nDay = nTmpData;
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    CSentence::GetFieldString(pszSentence, 7, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        nTmpData = atoi((char*)pstrSubData1);
        if(nTmpData < 0 || nTmpData > 12)
        {
            return false;
        }

        if(xNavData.xETA.nMonth != nTmpData)
        {
            xNavData.xETA.nMonth = nTmpData;
            bCfgChged = TRUE;
            bChgStaticMsgData = TRUE;
        }
    }

    // 0 = under way using engine
    CSentence::GetFieldString(pszSentence, 8, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        nTmpData = atoi((char*)pstrSubData1);
        if(nTmpData >= 0)
        {
            if(nTmpData == AIS_NAV_STATUS_AIS_SART)    // 14=AIS-SART 이면 reject.
            {
                return false;
            }

            if(nTmpData < AIS_NAV_STATUS_MIN || nTmpData > AIS_NAV_STATUS_MAX)
            {
                return false;
            }

            if(xNavData.uNavStatus != nTmpData)
            {
                xNavData.uNavStatus = nTmpData;
                bCfgChged = TRUE;
            }
        }
    }

    CSentence::GetFieldString(pszSentence, 9, pstrSubData1, sizeof(pstrSubData1));
    if(strlen(pstrSubData1) > 0)
    {
        nTmpData = atoi((char*)pstrSubData1);
        nTmpData = GetSpecialManoeuvreIndiFromVSD(nTmpData);

        if(nTmpData >= 0)
        {
            if(xNavData.uManoeuvre != nTmpData)
            {
                xNavData.uManoeuvre = nTmpData;
                bCfgChged = TRUE;
            }
        }
    }

    // Static Data 변경시, 1분내에 Msg 5번을 전송.(비주기적 Msg 5)
    if(bCfgChged)
    {
        cShip::getOwnShipInst()->xNavData = xNavData;
        CSetupMgr::getInst()->ReserveToSaveSysConfigData();

        if(bChgStaticMsgData)
        {
            CMKD::getInst()->SendVSDToPI(STR_TALKER_AI);
            CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();
        }
    }

    return bCfgChged;
}

int CVsd::GetSpecialManoeuvreIndiFromVSD(int nManoeuvre)
{
    switch(nManoeuvre)
    {
    case 8:
        return AIS_MANOEUVRE_ENGAGED;
    case 4:
        return AIS_MANOEUVRE_NOT_ENGAGED;
    case 0:
    case 12:
        return AIS_MANOEUVRE_NOT_AVAIL;
    }
    return -1;
}

int    CVsd::GetSpecialManoeuvreIndiToVSD(int nManoeuvre)
{
    switch(nManoeuvre)
    {
    case AIS_MANOEUVRE_ENGAGED:
        return 8;
    case AIS_MANOEUVRE_NOT_ENGAGED:
        return 4;
    case AIS_MANOEUVRE_NOT_AVAIL:
        return 0;
    }
    return AIS_MANOEUVRE_NOT_AVAIL;
}

/**
 * @brief Make the sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CVsd::MakeSentence(char *pszSentence, char *pszTalker)
{
    int32_t nChecksum;
    char szDest[LEN_MAX_DESTINATION+1];

    memset(szDest, 0, LEN_MAX_DESTINATION+1);
    strcpy(szDest, cShip::getOwnShipInst()->xNavData.pstrDestination);

    sprintf(pszSentence, "$%sVSD,%03d,%02d.%1d,%04d,%s,%02d%02d%02d,%02d,%02d,%02d,%02d",
            (pszTalker == nullptr) ? STR_TALKER_AI : (char*)pszTalker,
            cShip::getOwnShipInst()->xNavData.uShipType, 
            cShip::getOwnShipInst()->xNavData.uDraught/10, 
            cShip::getOwnShipInst()->xNavData.uDraught%10, 
            cShip::getOwnShipInst()->xNavData.uPerson,
            (strlen(szDest) <= 0) ? "@@@@@@@@@@@@@@@@@@@@" : CAisLib::ConvertNMEAString(szDest),
            cShip::getOwnShipInst()->xNavData.xETA.nHour, 
            cShip::getOwnShipInst()->xNavData.xETA.nMin, 
            cShip::getOwnShipInst()->xNavData.xETA.nSec,
            cShip::getOwnShipInst()->xNavData.xETA.nDay,  
            cShip::getOwnShipInst()->xNavData.xETA.nMonth,
            cShip::getOwnShipInst()->xNavData.uNavStatus, 
            GetSpecialManoeuvreIndiToVSD(cShip::getOwnShipInst()->xNavData.uManoeuvre)
            );
    
    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}
