/**
 * @file    Dac.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <memory>
#include "SysConst.h"

#ifndef  __DAC_H__
#define  __DAC_H__

//=============================================================================
class cDac
{
public:
	cDac(DAC_TypeDef *pBaseAddr);
    virtual ~cDac(void);

    static std::shared_ptr<cDac> getInst() {
        static std::shared_ptr<cDac> pInst = std::make_shared<cDac>(DAC1);
        return pInst;
    }

public:
    void  SetDAC0Data(HWORD wData);
    void  SetDAC1Data(HWORD wData);

    void  RunDACIsrHandler(void);

protected:
    DAC_TypeDef           *m_pBaseAddr;
    DWORD                  m_dSysIrqNo;
    DAC_HandleTypeDef      m_xDacHand;
    DAC_ChannelConfTypeDef m_xDacCnfg;
};
//=============================================================================

#endif   /*__DAC_H__*/

