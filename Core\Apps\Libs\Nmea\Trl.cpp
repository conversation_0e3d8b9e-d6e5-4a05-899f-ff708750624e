/**
 * @file    Trl.cpp
 * @brief   Trl class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Trl.h"

/*****************************************************************************
 *
 * TRL - AIS transmitter-non-functioning log
 *
 * $--TRL,x.x,x.x,x,xxxxxxxx,hhmmss.ss,xxxxxxxx,hhmmss.ss,x*hh<CR><LF>
 *        |   |   |   |      |         |        |         |
 *        1   2   3   4      5         6        7         8
 *
 * 1. Total number of log entries 
 * 2. Log entry number 
 * 3. Sequential message identifier 
 * 4. Switch off date 
 * 5. Switch off UTC time 
 * 6. Switch on date 
 * 7. Switch on UTC time 
 * 8. Reason code 
 *
 ******************************************************************************/
// Define static member variables
int8_t CTrl::m_nSequentialId = 0;

 CTrl::CTrl() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CTrl::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the TRL sentence
 * @param pszSentence The sentence to be made
 * @param pstrTalker The talker ID
 * @param pLogData The event log data
 * @param nLogTotalCnt The total number of log entries
 * @param nLogIdx The log entry number
 * @return The length of the sentence
 */
int32_t CTrl::MakeSentence(char *pszSentence, 
                                char *pstrTalker, 
                                EVENTLOG_DATA *pLogData, 
                                int nLogTotalCnt, 
                                int nLogIdx)
{
    int nReasonCode = 0;

    if (pszSentence == NULL)
        return 0;

    if (nLogTotalCnt == 0)
    {
        sprintf(pszSentence, "$%sTRL,0,,,,,,,", pstrTalker);
    }
    else
    {
        // Get the reason code
        switch(pLogData->nEventLogID)
        {
            case ALRID_SECURITYLOG_POWEROFF:
                nReasonCode = SECURITYLOG_POWEROFF;
                break;
            case ALRID_SECURITYLOG_MMSI_INVALID:
                nReasonCode = SECURITYLOG_INVALID_CONFIG;
                break;
            case ALRID_SECURITYLOG_TX_SHUTDOWN:
                nReasonCode = SECURITYLOG_MALFUNCTION;
                break;
            case ALRID_SECURITYLOG_RXONLYMODE:
                nReasonCode = SECURITYLOG_SILENT_MODE;
                break;
            default:
                return 0;
        }

        // Make the sentence with valid date and time. Otherwise, make the sentence with empty date and time. (See the NMEA spec.)
        if(CAisLib::IsValidAisSysDateTime(&(pLogData->sEventTime))
            && CAisLib::IsValidAisSysDateTime(&(pLogData->sEventEndTime))
            && pLogData->bEventTimeUTC)
        {
            sprintf(pszSentence, "$%sTRL,%0d,%0d,%0d,%02d%02d%04d,%02d%02d%02d,%02d%02d%04d,%02d%02d%02d,%d", 
                pstrTalker, nLogTotalCnt, nLogIdx, GetSequentialId(), 
                pLogData->sEventTime.xDate.nDay, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nYear,
                pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec,
                pLogData->sEventEndTime.xDate.nDay, pLogData->sEventEndTime.xDate.nMon, pLogData->sEventEndTime.xDate.nYear,
                pLogData->sEventEndTime.xTime.nHour, pLogData->sEventEndTime.xTime.nMin, pLogData->sEventEndTime.xTime.nSec,
                nReasonCode
            );
        }
        else
        {
            sprintf(pszSentence, "$%sTRL,%0d,%0d,%0d,,,,,%d", 
                pstrTalker, nLogTotalCnt, nLogIdx, GetSequentialId(), nReasonCode);
        }

        IncSequentialId();
    }

    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}
