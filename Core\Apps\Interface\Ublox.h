#ifndef __UBLOX_H__
#define __UBLOX_H__

#include "DataType.h"
#include "AllConst.h"
#include "GpsBoard.h"

//-------------------------------------------------
// Defines
//-------------------------------------------------
#define INT_GNSS_CFG_IDX_GPS_ONLY       0
#define INT_GNSS_CFG_IDX_GLONASS_ONLY   1
#define INT_GNSS_CFG_IDX_BEIDOU_ONLY    2
#define INT_GNSS_CFG_IDX_GALILEO_ONLY   3
#define INT_GNSS_CFG_IDX_GPS_GLONASS    4
#define INT_GNSS_CFG_IDX_GPS_BEIDOU     5
#define INT_GNSS_CFG_IDX_GPS_GALILEO    6

//-------------------------------------------------
// Constants
//-------------------------------------------------
enum _tagPpsConfig
{
	PPS_DISABLE,
	PPS_ENABLE_ALWAYS,
	PPS_ENABLE_FIXED,
};

//---------------------------------
// Export Global functions
//---------------------------------
void UBX_InitRcvr(cGpsBoard *pGnss);
void UBX_CfgRcvrGnss(cGpsBoard *pGnss, BYTE bGnssConfig, BOOL bEnableSBAS);
void UBX_CfgTimePulse(cGpsBoard *pGnss, _tagPpsConfig nConfigPPS);
#endif//__UBLOX_H__
