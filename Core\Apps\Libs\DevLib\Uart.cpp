/**
 * @file    Uart.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "SysConst.h"
#include "SysLib.h"
#include "GPIO.h"
#include "Uart.h"

#include <string.h>
#include <stdlib.h>
#include <stdio.h>

//=============================================================================
#define UART_TX_DATA_HEAD_SIZE       (2)
#define UART_TX_DATA_HEAD_POS        (UART_TX_DMA_MODE_BUFF_SIZE - UART_TX_DATA_HEAD_SIZE)
#define UART_TX_DATA_REAL_POS        (0)
//=============================================================================

//=============================================================================
cUart::cUart(UINT8 uUartID, DWORD dBaseAddr, int nRxBuffSize, int nTxBuffSize, int nSpeed)
{
    m_uUartID     = uUartID;
    m_dBaseAddr   = dBaseAddr;
    m_nRxBuffSize = nRxBuffSize;
    m_nTxBuffSize = nTxBuffSize;

    m_nSpeed      = nSpeed;
    m_dErCount    = 0;
    m_dRxCount    = 0;
    m_dTxCount    = 0;
    m_nRxHead     = 0;
    m_nRxTail     = 0;
    m_nTxHead     = 0;
    m_nTxTail     = 0;
    m_nSending    = 0;

    m_pRxBuffData = (UCHAR*)SysAllocMemory(sizeof(UCHAR) * m_nRxBuffSize);
    m_pTxBuffData = (UCHAR*)SysAllocMemory(sizeof(UCHAR) * m_nTxBuffSize);
}

cUart::~cUart(void)
{
}

int    cUart::GetSpeed()
{
    return m_nSpeed;
}

int cUart::IsSendingData(void)
{
    return(m_nSending);
}

void cUart::SetSendingMode(int nMode)
{
    m_nSending = nMode;
}

void cUart::SetTxBufferClear(void)
{
    m_nTxHead = 0;
    m_nTxTail = 0;
}

int cUart::IsDataInRxBuffer(void)
{
    if (m_nRxHead != m_nRxTail)
        return 1;

    return 0;
}

void cUart::ClearRxBuff()
{
    m_nRxTail = m_nRxHead;
}

int cUart::GetComData(void)
{
    int  nData;

    if (m_nRxHead != m_nRxTail)
    {
        nData = m_pRxBuffData[m_nRxTail];
        ++m_nRxTail;
        if (m_nRxTail >= m_nRxBuffSize)
            m_nRxTail = 0;
    }
    else
        nData = UART_NULL_CHAR;

    return(nData);
}

void cUart::PutComData(UCHAR bData)
{
    m_pTxBuffData[m_nTxHead] = bData;

    ++m_nTxHead;
    if (m_nTxHead >= m_nTxBuffSize)
        m_nTxHead  = 0;
}

int cUart::ReadComData(UCHAR *pData, HWORD wSize)
{
    int  nData;
    int  nCount;

    nCount = 0;
    while (wSize--)
    {
        nData = GetComData();
        if (nData == UART_NULL_CHAR)
            break;

        *pData++ = (UCHAR)nData;
        nCount++;
    }

    return(nCount);
}

void cUart::WriteComData(const UCHAR *pData, HWORD wSize)
{
    while (wSize--)
        PutComData(*pData++);
}

void cUart::WriteComStr(const char *pstrData)
{
    WriteComData((const UCHAR*)pstrData, strlen(pstrData));
}

//=============================================================================
cUartSYS::cUartSYS(UINT8 uUartID, USART_TypeDef *pBaseAddr, DWORD dSysIrqNo, int nRxBuffSize, int nTxBuffSize, int nSpeed)
          : cUart(uUartID, (DWORD)pBaseAddr, nRxBuffSize, nTxBuffSize, nSpeed)
{
    m_pBaseAddr = pBaseAddr;
    m_dSysIrqNo = dSysIrqNo;

    m_nSpeed    = nSpeed;

    if (pBaseAddr == USART1)  __HAL_RCC_USART1_CLK_ENABLE();
    if (pBaseAddr == USART2)  __HAL_RCC_USART2_CLK_ENABLE();
    if (pBaseAddr == USART3)  __HAL_RCC_USART3_CLK_ENABLE();
    if (pBaseAddr == USART6)  __HAL_RCC_USART6_CLK_ENABLE();

    if (pBaseAddr == UART4)   __HAL_RCC_UART4_CLK_ENABLE();
    if (pBaseAddr == UART5)   __HAL_RCC_UART5_CLK_ENABLE();
    if (pBaseAddr == UART7)   __HAL_RCC_UART7_CLK_ENABLE();
    if (pBaseAddr == UART8)   __HAL_RCC_UART8_CLK_ENABLE();

    memset(&m_xUartHand, 0x00, sizeof(m_xUartHand));

    m_xUartHand.Instance          = m_pBaseAddr;
    m_xUartHand.Init.BaudRate     = m_nSpeed;
    m_xUartHand.Init.WordLength   = UART_WORDLENGTH_8B;
    m_xUartHand.Init.StopBits     = UART_STOPBITS_1;
    m_xUartHand.Init.Parity       = UART_PARITY_NONE;
    m_xUartHand.Init.HwFlowCtl    = UART_HWCONTROL_NONE;
    m_xUartHand.Init.Mode         = UART_MODE_TX_RX;
    m_xUartHand.Init.OverSampling = UART_OVERSAMPLING_16;
    m_xUartHand.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;

    if (HAL_UART_Init(&m_xUartHand) != HAL_OK)                                               { while (1); }
    if (HAL_UARTEx_SetTxFifoThreshold(&m_xUartHand, UART_TXFIFO_THRESHOLD_1_8) != HAL_OK)    { while (1); }
    if (HAL_UARTEx_SetRxFifoThreshold(&m_xUartHand, UART_RXFIFO_THRESHOLD_1_8) != HAL_OK)    { while (1); }
    if (HAL_UARTEx_EnableFifoMode(&m_xUartHand) != HAL_OK)                                  { while (1); }


    //__HAL_UART_ENABLE_IT(&m_xUartHand, UART_IT_RXNE);
    m_pBaseAddr->CR1 |= UART_FLAG_RXNE;

    NVIC_ClearPendingIRQ((IRQn_Type)m_dSysIrqNo);
    NVIC_SetPriority((IRQn_Type)m_dSysIrqNo , SYS_UART_INT_PRIORITY);

    NVIC_EnableIRQ((IRQn_Type)m_dSysIrqNo);
}

cUartSYS::~cUartSYS(void)
{
}

int   cUartSYS::ReadComData(UCHAR *pData, HWORD wSize)
{
    return(cUart::ReadComData(pData, wSize));
}

void cUartSYS::WriteComData(const UCHAR *pData, HWORD wSize)
{
    cUart::WriteComData(pData, wSize);

    if (m_nSending == 0)
    {
        m_nSending = 1;

//        __HAL_UART_ENABLE_IT(&m_xUartHand, UART_IT_TXE);      // Enable TX interrupt
        m_pBaseAddr->CR1 |= UART_FLAG_TXE;
    }
}

void cUartSYS::SetUartPara(int nSpeed)
{
    m_nSpeed = nSpeed;
    m_xUartHand.Init.BaudRate = m_nSpeed;

    UART_SetConfig(&m_xUartHand);
}

void cUartSYS::RunUartIsrHandler(void)
{
    DWORD dStatus = m_pBaseAddr->ISR;

    if (dStatus & UART_FLAG_ORE) m_pBaseAddr->ICR = UART_FLAG_ORE;
    if (dStatus & UART_FLAG_NE)  m_pBaseAddr->ICR = UART_FLAG_NE;
    if (dStatus & UART_FLAG_FE)  m_pBaseAddr->ICR = UART_FLAG_FE;
    if (dStatus & UART_FLAG_PE)  m_pBaseAddr->ICR = UART_FLAG_PE;

    if (dStatus & UART_FLAG_RXNE) RunUartIsrRxHandler();

    if (dStatus & UART_FLAG_TXE)  RunUartIsrTxHandler();
}

void cUartSYS::RunUartIsrRxHandler(void)
{
    m_pRxBuffData[m_nRxHead] = m_pBaseAddr->RDR;

    ++m_nRxHead;
    if (m_nRxHead >= m_nRxBuffSize)
        m_nRxHead = 0;
}

void cUartSYS::RunUartIsrTxHandler(void)
{
    if (m_nTxTail == m_nTxHead)
    {
        m_nSending = 0;

        //UART_DisableIt(m_pBaseAddr, UART_IER_TXRDY);          // Disable TX interrupt
        m_pBaseAddr->CR1 &= ~UART_FLAG_TXE;                   // Disable TX interrupt
    }
    else
    {
        m_pBaseAddr->TDR = m_pTxBuffData[m_nTxTail];

        ++m_nTxTail;
        if (m_nTxTail >= m_nTxBuffSize)
            m_nTxTail  = 0;

        if (m_nTxTail == m_nTxHead)
        {
            m_nSending = 0;

            //UART_DisableIt(m_pBaseAddr, UART_IER_TXRDY);      // Disable TX interrupt
            m_pBaseAddr->CR1 &= ~UART_FLAG_TXE;               // Disable TX interrupt
        }
    }
}

//=============================================================================
DWORD cUartSYS::GetStatusRegister(void)
{
    return(m_pBaseAddr->ISR);
}

//=============================================================================
void cUartSYS::SendPacketDataByDMA(int nRingTail)
{
}

//=============================================================================
void cUartSYS::SetDmaStreamNoByBaseAddr(DWORD dBaseAddr)
{
}

void cUartSYS::RunDmaSettingForRX(void)
{
}

void cUartSYS::RunDmaSettingForTX(void)
{
}
