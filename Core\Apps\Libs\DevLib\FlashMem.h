/**
 * @file    FlashMem.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef  __FLASHMEM_H__
#define  __FLASHMEM_H__

#include <memory>
#include "DataType.h"
#include "SysConst.h"
#include "Define_Flash.h"
#include "DevMem.h"

#define FLASH_TIMEOUT_VALUE       50000U /* 50 s */

//=============================================================================
class cFlashMem : public CDevMem
{
public:
    cFlashMem(DWORD dBlockBaseAddr, int nNumberOfSector);
    virtual ~cFlashMem(void);

public:
    void  SetBytesPerSector(int nSectorNo, DWORD dBytesPerSector);
    DWORD GetStartAddrBySectorNo(int nSectorNo);
    int   GetSectorNoByAddr(DWORD dAddr);
    virtual DWORD GetSectorSize(DWORD dAddr);
 
protected:
    DWORD  m_dBlockBaseAddr;
    int    m_nNumberOfSector;
    DWORD *m_pBytesPerSector;
    DWORD *m_pSectorStartAddr;
};

//=============================================================================
class cSTM32IntFlash : public cFlashMem
{
public:
    cSTM32IntFlash(void);
    virtual ~cSTM32IntFlash(void);

    static std::shared_ptr<cSTM32IntFlash> getInst() {
        static std::shared_ptr<cSTM32IntFlash> pInst = std::make_shared<cSTM32IntFlash>();
        return pInst;
    }

private:
    DWORD GetSectorIDByAddr(DWORD dAddr);
    int   IsDualBankMode(void);
    int   LockFlash(void);
    int   UnLockFlash(void);

public:
    virtual int   EraseSector(DWORD dAddr, int nSecCnt=1);
    virtual int   WriteData(DWORD dAddr, void *pData, int nSize);
    virtual int   ReadData(DWORD dAddr, void *pData, int nSize);

protected:
    DWORD  m_vSectorID[FLASH_SECTOR_TOTAL];
};

#endif /*__FLASHMEM_H__*/

