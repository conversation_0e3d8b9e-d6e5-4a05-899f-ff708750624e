/**
 * @file    Zda.cpp
 * @brief   Zda class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Zda.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * ZDA - 
 *
 * $--ZDA, hhmmss.ss,xx,xx,xxxx,xx,xx*hh<CR><LF>
 *           |       |  |   |   |  |
 *           1       2  3   4   5  6
 *
 * 1. UTC
 * 2. Day, 01 to 31
 * 3. Month, 01 to 12 (UTC)
 * 4. Year (UTC)
 * 5. Local zone hours
 * 6. Local zone minutes
 * 
 ******************************************************************************/
CZda::CZda() : CSentence()
{
    ClearData();
}

void CZda::ClearData()
{
	CAisLib::SetDefaultSysDate(&m_xUtcDate);
	CAisLib::SetDefaultSysTime(&m_xUtcTime);

    m_dwUtcValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CZda::Parse(const char *pszSentence)
{
    char *pSource;
    char pstrTmp[128];
    int  nHour,nMin,nSec;
    int  nDay,nMonth,nYear;

    GetFieldString(pszSentence, 1, pstrTmp);     // hhmmss
    if (strlen(pstrTmp) < 6)
        return 1;

    nHour   = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
    nMin    = (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
    nSec    = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';

    GetFieldString(pszSentence, 2, pstrTmp);     // Day, 01 to 31 (UTC)
    nDay    = atoi(pstrTmp);

    GetFieldString(pszSentence, 3, pstrTmp);     // Month, 01 to 12 (UTC)
    nMonth  = atoi(pstrTmp);

    GetFieldString(pszSentence, 4, pstrTmp);     // Year (UTC)
    nYear   = atoi(pstrTmp);

    if (CAisLib::IsValidAisSysTime(nHour, nMin, nSec) && CAisLib::IsValidAisSysDate(nYear, nMonth, nDay))
    {
        m_xUtcTime.nHour = nHour;
        m_xUtcTime.nMin  = nMin;
        m_xUtcTime.nSec  = nSec;

        m_xUtcDate.nYear = nYear;
        m_xUtcDate.nMon  = nMonth;
        m_xUtcDate.nDay  = nDay;

        m_dwUtcValidTick = SysGetSystemTimer();
    }

    return true;
}

/**
 * @brief Check received UTC data is valid or not
 */
bool CZda::IsValidUtcData(void)
{
    return (m_dwUtcValidTick && SysGetDiffTimeMili(m_dwUtcValidTick) < NMEA_UTC_LASTDATA_STAYMS);
}

/**
 * @brief Get UTC date
 */
SYS_DATE CZda::GetUtcDate(void)
{
    return m_xUtcDate;
}

/**
 * @brief Get UTC time
 */
SYS_TIME CZda::GetUtcTime(void)
{
    return m_xUtcTime;
}

/**
 * @brief Call function periodically
 */
void CZda::RunPeriodically(void)
{
    if (m_dwUtcValidTick && SysGetDiffTimeMili(m_dwUtcValidTick) < NMEA_UTC_LASTDATA_STAYMS)
    {
        m_dwUtcValidTick = 0;
    }
}
