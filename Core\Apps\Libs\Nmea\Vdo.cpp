/**
 * @file    Vdo.cpp
 * @brief   Vdo class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Vdo.h"

/******************************************************************************
 * 
 * VDO - VHF Data-link Own-vessel Message
 * 
 * !--VDO,x,x,x,a,s--s,x*hh<CR><LF>
 *        | | | |  |   |
 *        1 2 3 4  5   6
 *
 * 1. Total number of sentences needed to transfer the message, 1 to 9
 * 2. Sentence number, 1 to 9
 * 3. Sequential message identifier, 0 to 9
 * 4. AIS Channel, "A" or "B"
 * 5. Encapsulated ITU-R M.1371 radio message
 * 6. Number of fill-bits, 0 to 5
 *
 ******************************************************************************/
// Define static member variables
int8_t CVdo::m_nSequentialId = 0;
char CVdo::m_pszEncodedBuff[MAX_06ASCII_BUFF];

CVdo::CVdo() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CVdo::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the VDO sentence
 * @param pszSentence Output buffer for the generated sentence
 * @param pEncBuff Encoded source data
 * @param nTxChNo Transmit channel number (A or B)
 * @param nNumSentences Total number of sentences needed to transfer the message
 * @param nSentenceNo Sentence number
 * @param wSeqMsgID Sequential message identifier
 * @param wFillBits Number of fill-bits
 */
void  CVdo::MakeSentenceVDO(char *pszSentence, 
                            const char *pEncBuff, 
                            int nTxChNo, 
                            int nNumSentences, 
                            int nSentenceNo, 
                            int8_t wSeqMsgID, 
                            uint16_t wFillBits)
{
    char  pstrVdoData[RX_MAX_DATA_SIZE];

    // Add the header part
    sprintf(pstrVdoData, "!AIVDO,%d,%d,%s,",
            nNumSentences,
            nSentenceNo,
            (nNumSentences > 1) ? std::to_string(wSeqMsgID).c_str() : "");

    // Add the channel part
    if (nTxChNo == AIS_CHANNEL_AIS1 || nTxChNo == AIS_CHANNEL_AIS2)
    {
        sprintf(pstrVdoData + strlen(pstrVdoData), "%c,", nTxChNo + 'A');
    }
    else
    {
        strcat(pstrVdoData, ",");
    }

    // Add the data part
    if (nSentenceNo == nNumSentences) {
        strcat(pstrVdoData, pEncBuff);
    } else {
        strncat(pstrVdoData, pEncBuff, AIS_VDM_NMEA_MAX_CHARS);
    }
    
    // Add fill bits
    sprintf(pstrVdoData + strlen(pstrVdoData), ",%d", 
            (nSentenceNo == nNumSentences) ? wFillBits : 0);
    
    // Add checksum and append to destination
    CSentence::AddSentenceTail(pstrVdoData);

    // Append the generated sentence to the destination buffer
    strcat(pszSentence, pstrVdoData);
}

/**
 * @brief Make the VDO sentence
 * @param pszSentence Output buffer for the generated sentence
 * @param nTxChNo Transmit channel number (A or B)
 * @param pPacketData Binary packet data to encode
 * @param nPacketSize Size of the packet data in bytes
 * @return The length of the generated sentence
 */
int32_t CVdo::MakeSentence(char *pszSentence, int nTxChNo, uint8_t *pPacketData, int nPacketSize)
{
    char *pSource;
    char  *pDest;
    int    nFullLen;
    int    nNumSentences;
    uint16_t  wFillBits;
    uint16_t  wEncodedLen;

    // Encode the binary packet data to 06 ASCII string
    wFillBits = CAisLib::MakeBin08PacketToAsc06Str(pPacketData, nPacketSize, (unsigned char*)m_pszEncodedBuff, &wEncodedLen);

    // Determine the number of sentences needed to transfer the message
    if (wEncodedLen <= AIS_VDM_NMEA_MAX_CHARS)
        nNumSentences = 1;
    else
    {
        nNumSentences = wEncodedLen / AIS_VDM_NMEA_MAX_CHARS;
        if (wEncodedLen % AIS_VDM_NMEA_MAX_CHARS)
            nNumSentences++;
    }

    pSource = m_pszEncodedBuff;
    pDest = pszSentence;

    // Generate multiple VDO sentences if necessary
    for (int i = 1; i <= nNumSentences-1; i++)
    {
        MakeSentenceVDO(pDest, pSource, nTxChNo, nNumSentences, i, GetSequentialId(), 0);
        pSource += AIS_VDM_NMEA_MAX_CHARS;
    }

    MakeSentenceVDO(pDest, pSource, nTxChNo, nNumSentences, nNumSentences, GetSequentialId(), wFillBits);

    // Increase the sequential message identifier
    if (nNumSentences > 1)
        IncSequentialId();

    return strlen(pszSentence);
}




