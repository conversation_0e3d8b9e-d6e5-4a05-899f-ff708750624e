#include "DataType.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Timer.h"
#include "Ship.h"
#include "LayerNetwork.h"
#include "VdlRxMgr.h"
#include "GpsBoard.h"
#include "AisModem.h"
#include "UserDirMgr.h"
#include "GPIOExt.h"
#include "VdlTxMgr.h"
#include "GnssInternal.h"
#include "SensorMgr.h"
#include "SysOpStatus.h"
#include "SetupMgr.h"
#include "SyncMgr.h"

CSyncMgr::CSyncMgr()
{
    Initialize();
}

CSyncMgr::~CSyncMgr()
{
}

/**
 * @brief Initialize the synchronization manager
 */
void CSyncMgr::Initialize()
{
    m_uSyncSource               = AIS_SYNC_MODE_NONE;
    m_dwSyncToOtherStSec        = 0;

    m_uSyncSrcMMSI              = AIS_AB_MMSI_NULL;
    m_bSyncModeToOtherSt        = FALSE;
    m_dwSyncStartSec            = 0;

    m_bOwnShipSemaphoreMode     = FALSE;
    m_dwOwnShipSemaLostStartSec = 0;
    m_bOwnShipSemaOldLostStat   = FALSE;

    CAisLib::SetDefaultSysDate(&m_utcSyncDate);
    CAisLib::SetDefaultSysTime(&m_utcSyncTime);

    m_dwUtcTimeRcvSecFromVdlMsg = 0;
    m_dwUtcDateRcvSecFromVdlMsg = 0;
    CAisLib::SetDefaultSysDate(&m_utcDateFromVdlMsg);
    CAisLib::SetDefaultSysTime(&m_utcTimeFromVdlMsg);
}

/**
 * @brief Set UTC date from VDL message
 * @param nSenderMMSI Sender MMSI
 * @param nYear Year
 * @param nMonth Month
 * @param nDay Day
 */
void CSyncMgr::SetUtcDateFromVdlMsg(int nSenderMMSI, int nYear, int nMonth, int nDay)
{
    if(nSenderMMSI == m_uSyncSrcMMSI)
    {
        m_utcDateFromVdlMsg.nYear= nYear;
        m_utcDateFromVdlMsg.nMon = nMonth;
        m_utcDateFromVdlMsg.nDay = nDay;
        m_dwUtcDateRcvSecFromVdlMsg = cTimerSys::getInst()->GetCurTimerSec();

        DEBUG_LOG( "SyncUtcTime] Date,FromVdlMsg, sync:%d,%09d, %04d%02d%02d, s:%d\r\n",
                m_uSyncSource, m_uSyncSrcMMSI, m_utcDateFromVdlMsg.nYear, m_utcDateFromVdlMsg.nMon, m_utcDateFromVdlMsg.nDay, cTimerSys::getInst()->GetCurTimerSec());
    }
}

/**
 * @brief Set UTC time from VDL message
 * @param nSenderMMSI Sender MMSI
 * @param nHour Hour
 * @param nMin Minute
 */
void CSyncMgr::SetUtcTimeFromVdlMsg(int nSenderMMSI, int nHour, int nMin)
{
    if(nSenderMMSI == m_uSyncSrcMMSI)
    {
        m_utcTimeFromVdlMsg.nHour= nHour;
        m_utcTimeFromVdlMsg.nMin = nMin;
        m_utcTimeFromVdlMsg.nSec = 0;

        m_dwUtcTimeRcvSecFromVdlMsg = cTimerSys::getInst()->GetCurTimerSec();

        DEBUG_LOG( "SyncUtcTime] Time,FromVdlMsg, sync:%d,%09d, %02d:%02d, s:%d\r\n",
                m_uSyncSource, m_uSyncSrcMMSI, m_utcTimeFromVdlMsg.nHour, m_utcTimeFromVdlMsg.nMin, cTimerSys::getInst()->GetCurTimerSec());
    }
}

bool CSyncMgr::GetOtherSyncStUtcDate(SYS_DATE *psDate)
{
    if(!IsUtcDirectSyncRunning())
    {
        *psDate = m_utcDateFromVdlMsg;
        return true;
    }
    return false;
}

bool CSyncMgr::GetOtherSyncStUtcTime(SYS_TIME *psTime)
{
    if(!IsUtcDirectSyncRunning())
    {
        *psTime = m_utcTimeFromVdlMsg;
        return true;
    }
    return false;
}

/**
 * @brief Update UTC synchronization date and time
 */
void CSyncMgr::UpdateUtcSyncDateTime(void)
{
    SYS_DATE_TIME sUtcSyncDateTime;

    if(IsUtcDirectSyncRunning())
    {
        if(CSensorMgr::getInst()->GetSensorDateTime(SENSORID_0, &sUtcSyncDateTime))
        {
            m_utcSyncDate = sUtcSyncDateTime.xDate;
            m_utcSyncTime = sUtcSyncDateTime.xTime;
        }
        else
        {
        	CAisLib::SetDefaultSysDate(&m_utcSyncDate);
        	CAisLib::SetDefaultSysTime(&m_utcSyncTime);
        }
    }
    else
    {
        if(GetOtherSyncStUtcDate(&(sUtcSyncDateTime.xDate)))
            m_utcSyncDate = sUtcSyncDateTime.xDate;
        else
        	CAisLib::SetDefaultSysDate(&m_utcSyncDate);

        if(GetOtherSyncStUtcTime(&(sUtcSyncDateTime.xTime)))
            m_utcSyncTime = sUtcSyncDateTime.xTime;
        else
        	CAisLib::SetDefaultSysTime(&m_utcSyncTime);
    }
}

/**
 * @brief Set synchronization source
 * @param uSyncSource Synchronization source
 * @param uSyncSrcMMSI Synchronization source MMSI
 */
void CSyncMgr::SetSyncSource(UINT16 uSyncSource, DWORD uSyncSrcMMSI)
{
    m_uSyncSource    = uSyncSource;
    m_uSyncSrcMMSI   = uSyncSrcMMSI;
    m_dwSyncStartSec = cTimerSys::getInst()->GetCurTimerSec();

    // If sync source is none, clear UTC sync date and time
    if(m_uSyncSource == AIS_SYNC_MODE_NONE)
    {
    	CAisLib::SetDefaultSysDate(&m_utcSyncDate);
    	CAisLib::SetDefaultSysTime(&m_utcSyncTime);
    }

    SetGpioLed_Time(IsSyncRunning());
}

/**
 * @brief Update synchronization source
 * @param uSyncSource Synchronization source
 * @param uSyncSrcMMSI Synchronization source MMSI
 */
void CSyncMgr::UpdateSyncSource(UINT16 uSyncSource, DWORD uSyncSrcMMSI)
{
    // If sync source is changed, clear UTC time from VDL message
    if(uSyncSource != m_uSyncSource)
    {
        if(!IsSyncToUTC(uSyncSource))
        {
            SetUtcTimeFromVdlMsg(AIS_AB_MMSI_NULL, DTTM_HOUR_NULL, DTTM_MIN_NULL);
        }
    }
    
    // If sync source is changed, update synchronization source
    if(uSyncSource != m_uSyncSource || uSyncSrcMMSI != m_uSyncSrcMMSI)
    {
        SetSyncSource(uSyncSource, uSyncSrcMMSI);
    }
}

bool CSyncMgr::GetSyncSrcDistDelay(int *pnSyncDistDelayCnter)
{
    *pnSyncDistDelayCnter = 0;

    xDIRDATA* pDirData = CUserDirMgr::getInst()->FindDirDataPtr(m_uSyncSrcMMSI);
    bool bRet = false;

    if(!pDirData)
    {
        INFO_LOG("GetSyncSrcDistDelay] fail, SyncSrc NULL, %09d\r\n", m_uSyncSrcMMSI);
        return false;
    }

    if(pDirData->bPosValid && 0 < pDirData->fDistance && pDirData->fDistance <= AIS_DIST_NM_VALID_MAX)
    {
        //------------------------------------------
        // distance delay 를 sample counter 로 환산
        //------------------------------------------
        #define LIGHT_SPD_NM    161987.1        // light speed in NM
        float fDistDelaySec = (pDirData->fDistance / LIGHT_SPD_NM);
        *pnSyncDistDelayCnter = (int)(fDistDelaySec * (AIS_SLOTS_PER_ONE_FRAME * AIS_MAX_BITS_PER_ONE_RX / 60));
        bRet = true;

        DEBUG_LOG( "GetSyncSrcDistDelay] OK, %09d, dist : %.5f(%.1f,%.1f), ownShip:%.1f,%.1f, delaySec : %.5f, cntr : %d\r\n",
                m_uSyncSrcMMSI, pDirData->fDistance, pDirData->xPosF.fLAT, pDirData->xPosF.fLON,
                cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, fDistDelaySec, *pnSyncDistDelayCnter);
    }
    else
    {
        if(pDirData)
        {
            INFO_LOG( "GetSyncSrcDistDelay] fail, pos invalid, %09d, posValid : %d, dist : %.5f, cntr : %d\r\n",
                m_uSyncSrcMMSI, pDirData->bPosValid, pDirData->fDistance, *pnSyncDistDelayCnter);
        }
        else
        {
            INFO_LOG( "GetSyncSrcDistDelay] fail, src NULL, %09d\r\n", m_uSyncSrcMMSI);
        }
    }

    return bRet;
}

/**
 * @brief Check if synchronization is running
 */
bool CSyncMgr::IsSyncRunning(void)
{
    return (m_uSyncSource != AIS_SYNC_MODE_NONE);
}

/**
 * @brief Check if UTC direct synchronization is running
 */
bool CSyncMgr::IsUtcDirectRunning(void)
{
    return CSensorMgr::getInst()->IsPosUtcFixed(SENSORID_0);
}

/**
 * @brief Check if UTC direct synchronization is running
 */
bool CSyncMgr::IsUtcDirectSyncRunning(void)
{
    return (m_uSyncSource == AIS_SYNC_MODE_UTC_DIRECT);
}

/**
 * @brief Check if UTC indirect synchronization is running
 */
bool CSyncMgr::IsUtcIndirectSyncRunning(void)
{
    return (m_uSyncSource == AIS_SYNC_MODE_UTC_INDIRECT);
}

/**
 * @brief Check if synchronization is to UTC
 */
bool CSyncMgr::IsSyncToUTC(UINT16 uSyncSource)
{
    return (uSyncSource == AIS_SYNC_MODE_UTC_DIRECT || uSyncSource == AIS_SYNC_MODE_UTC_INDIRECT);
}

/**
 * @brief Check if synchronization is to UTC
 */
bool CSyncMgr::IsSyncToUTC(void)
{
    return IsSyncToUTC(m_uSyncSource);
}

/**
 * @brief Set own ship semaphore mode
 */
void CSyncMgr::SetOwnShipSemaphoreMode(bool bSet)
{
    m_bOwnShipSemaphoreMode     = bSet;
    m_dwOwnShipSemaLostStartSec = cTimerSys::getInst()->GetCurTimerSec();
    m_bOwnShipSemaOldLostStat   = FALSE;
}

/**
 * @brief Check if own ship is in semaphore mode
 */
bool CSyncMgr::IsOwnShipSemaphoreMode(void)
{
    return m_bOwnShipSemaphoreMode;
}

/**
 * @brief Check if synchronization is to other station
 */
bool CSyncMgr::IsSyncModeToOtherSt(void)
{
    return m_bSyncModeToOtherSt;
}

/**
 * @brief Set synchronization mode to other station
 */
void CSyncMgr::SetSyncModeToOtherSt(bool bSet)
{
    // If UTC direct is running, do not set synchronization mode to other station
    if(bSet && m_uSyncSource == AIS_SYNC_MODE_UTC_DIRECT)
        return;

    if(bSet != m_bSyncModeToOtherSt)
    {
        m_bSyncModeToOtherSt = bSet;
    }
}

/**
 * @brief Process synchronization to other station
 * @param pRxMgr Receiver manager
 * @param dwSyncState Synchronization state
 * @param wRcvSubMsg Received sub-message
 * @return True if synchronization is processed, false otherwise
 */
bool CSyncMgr::ProcessSyncToOtherSt(CVdlRxMgr *pRxMgr, DWORD dwSyncState, WORD wRcvSubMsg)
{
    // If UTC direct is running, do not process synchronization to other station
    if(m_uSyncSource == AIS_SYNC_MODE_UTC_DIRECT)
        return false;

    if(!pRxMgr)
        return false;

    bool bSyncOK = false;
    bool bRunSyncToExtSt = false;
    bool bDistDelayOK = false;
    bool bUtcIndirectSync = false;
    DWORD dwSrcMMSI = pRxMgr->m_xRxAisMsg.xMsg00.dSrcMMSI;

    // Find directory data for source MMSI
    xDIRDATA *pDirData = CUserDirMgr::getInst()->FindDirDataPtr(dwSrcMMSI);

    // Check if source MMSI is UTC indirect synchronization candidate
    if(pDirData && CUserDirMgr::getInst()->IsUtcIndirectSyncSrcCandi(pDirData))
    {
        bRunSyncToExtSt = true;
        bUtcIndirectSync= true;
    }
    else
    {
         bRunSyncToExtSt = (dwSrcMMSI == m_uSyncSrcMMSI);
    }

    // If source MMSI is synchronization source, run synchronization to other station
    if(bRunSyncToExtSt)
    {
        int nSyncDistDelayCnter = 0;
        bDistDelayOK = GetSyncSrcDistDelay(&nSyncDistDelayCnter);
        {
            if(cAisModem::getInst()->RunSyncToOtherSt(dwSrcMMSI, pRxMgr->m_pRxRawForm, (UINT16)wRcvSubMsg, nSyncDistDelayCnter))
            {
                m_dwSyncToOtherStSec = cTimerSys::getInst()->GetCurTimerSec();
                bSyncOK = true;

                SetSyncModeToOtherSt(true);

                if(bUtcIndirectSync)
                {
                    UpdateSyncSource(AIS_SYNC_MODE_UTC_INDIRECT, dwSrcMMSI);
                }
            }
        }
    }

    return bSyncOK;
}

/**
 * @brief Check if own ship semaphore is qualified
 */
bool CSyncMgr::IsOwnShipSemaQualified()
{
    // If own ship is Class B, it's not able to be semaphore
    if (CSetupMgr::getInst()->IsAisBClass())
        return false;

    return ((cShip::getOwnShipInst()->GetOwnShipMMSI() != AIS_AB_MMSI_NULL) &&
            (CUserDirMgr::getInst()->GetHighPrioritySyncState() == AIS_SYNC_MODE_BASE_INDIRECT) &&
            (m_uSyncSource == AIS_SYNC_MODE_NONE ||
             m_uSyncSource == AIS_SYNC_MODE_UTC_INDIRECT ||
             m_uSyncSource == AIS_SYNC_MODE_BASE_INDIRECT ||
             m_uSyncSource == AIS_SYNC_MODE_MOBILE_SEMAPHORE) &&
            CSensorMgr::getInst()->IsGnssFixed() &&
            (cShip::getOwnShipInst()->xDynamicData.nTimeStamp <= 60) &&
            (cTimerSys::getInst()->GetTimeDiffSec(cShip::getOwnShipInst()->xOwnShipPosReportSec[1]) <= SEMAPHORE_CHECKTIME_SEC));
}

/**
 * @brief Enable semaphore mode if own ship is qualified
 */
bool CSyncMgr::CheckOwnShipSemaphoreStart()
{
    // If own ship is semaphore qualified, check if it's able to be semaphore
    if(IsOwnShipSemaQualified())
    {
        //-----------------------------------------------------------------------------------------------------------------
        // ITU-R-M 1371-5 Annex2 *******.3 Synchronization sources
        // refer to Table 10 (Mobile station's semaphore qualified condition)
        // If more than one station is semaphore qualified, then the station indicating the highest number of
        // received stations should become the active semaphore station. If more than one station indicates the
        // same number of received stations, then the one with the lowest MMSI number becomes the active semaphore station.
        //-----------------------------------------------------------------------------------------------------------------

        int nNumRcvStOwnShip = CUserDirMgr::getInst()->GetOwnShipNumOfRcvStFor9Min();
        if(nNumRcvStOwnShip > 0)
        {
            // Get other station's semaphore MMSI
            DWORD dwSemaMMSI = CUserDirMgr::getInst()->GetSyncSrcForMobileSemaphore();
            xDIRDATA *pDirData = CUserDirMgr::getInst()->FindDirDataPtrMS(dwSemaMMSI);

            // If own ship is semaphore qualified and has more received stations than other station, set own ship as semaphore
            // If own ship is semaphore qualified and has same number of received stations as other station, compare MMSI
            if(!pDirData ||
                (nNumRcvStOwnShip > pDirData->uNumRcvSt9min ||
                (nNumRcvStOwnShip == pDirData->uNumRcvSt9min && cShip::getOwnShipInst()->GetOwnShipMMSI() < pDirData->dMMSI)))
            {
                // Set own ship as semaphore
                SetOwnShipSemaphoreMode(true);

                DEBUG_LOG("CheckOwnSema] Set own sema, otherRcvSt : %09d(%d), ownSt : %09d(%d) s:%d\r\n",
                        pDirData->dMMSI, pDirData->uNumRcvSt9min, cShip::getOwnShipInst()->GetOwnShipMMSI(), 
                        nNumRcvStOwnShip, cTimerSys::getInst()->GetCurTimerSec());

                return true;
            }
        }
    }

    SetOwnShipSemaphoreMode(false);
    return false;
}

/**
 * @brief Check if own ship semaphore is expired
 */
void CSyncMgr::CheckOwnShipSemaphoreExpired()
{
    const int OWNSHIP_SEMAPHORE_CHECK_SEC = 10;
    static DWORD dwSyncCheckSec = 0;

    if(IsOwnShipSemaphoreMode())
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(dwSyncCheckSec) >= OWNSHIP_SEMAPHORE_CHECK_SEC)
        {
            //-------------------------------------------------------------------------------------------------
            // ITU-R-M 1371-5 Annex2 *******.2 Mobile station operation as a semaphore
            // When a mobile station determines that it is the semaphore (see § ******* and § *******.3), 
            // it should decrease its reporting interval to MAC.SyncMobileRate. It should remain in this state 
            // until the semaphore qualifying conditions have been invalid for the last 3 min.
            //-------------------------------------------------------------------------------------------------
            bool bLostTmp = !IsOwnShipSemaQualified();
            if(bLostTmp)
            {
                if(!m_bOwnShipSemaOldLostStat)
                {
                    m_dwOwnShipSemaLostStartSec = cTimerSys::getInst()->GetCurTimerSec();
                }

                if((cTimerSys::getInst()->GetTimeDiffSec(m_dwOwnShipSemaLostStartSec) >= 180))
                {
                    SetOwnShipSemaphoreMode(FALSE);
                }
            }

            m_bOwnShipSemaOldLostStat = bLostTmp;
            dwSyncCheckSec = cTimerSys::getInst()->GetCurTimerSec();
        }
    }
}

/**
 * @brief Update synchronization sources
 */
void CSyncMgr::CheckSyncSources(void)
{
    //--------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 *******.3 Synchronization sources
    // The primary source for synchronization should be the internal UTC source (UTC direct). 
    // If this source should be unavailable the following external synchronization sources, 
    // listed below in the order of priority, should serve as the basis for slot phase and 
    // frame synchronizations:
    // - a station which has UTC time
    // - a base station which is semaphore qualified
    // - other station(S) which are synchronized to a base station
    // - a mobile station, which ic semaphore qualified
    // Table 9 illustrates the different sync mode priorities and the contents of the sync state fields
    // in the communication state.
    //--------------------------------------------------------------------------------------------------
    #define SYNC_STAY_TIMEOUT_SEC        1

    DWORD uMMSI = AIS_AB_MMSI_NULL;
    UINT16 uSyncSource = AIS_SYNC_MODE_NONE;
    static DWORD dwSyncSourceCheckTick = 0;

    // If synchronization is running, check every 10 seconds
    if(IsSyncRunning() && SysGetDiffTimeScnd(dwSyncSourceCheckTick) < 10)
    {
        return;
    }

    dwSyncSourceCheckTick = SysGetSystemTimer();

    // 1. Check if UTC direct is running
    if(IsUtcDirectRunning())
    {
        uSyncSource = AIS_SYNC_MODE_UTC_DIRECT;
        uMMSI = cShip::getOwnShipInst()->GetOwnShipMMSI();
    }
    else if(OPSTATUS::bRunSyncProcess)
    {
        // 2. Check if UTC indirect is running
        if(m_uSyncSource == AIS_SYNC_MODE_UTC_INDIRECT)
        {
            // If UTC indirect is running, stay for 1 second
            if(cTimerSys::getInst()->GetTimeDiffSec(m_dwSyncStartSec) < SYNC_STAY_TIMEOUT_SEC)
            {
                uSyncSource = m_uSyncSource;
                uMMSI       = m_uSyncSrcMMSI;
            }
            else
            {
                // Check if UTC indirect is available
                uMMSI = CUserDirMgr::getInst()->GetSyncSrcForUtcIndirect();
                if(uMMSI != AIS_AB_MMSI_NULL)
                {
                    uSyncSource = AIS_SYNC_MODE_UTC_INDIRECT;
                }
            }
        }
        else
        {
            // Check if UTC indirect is available
            uMMSI = CUserDirMgr::getInst()->GetSyncSrcForUtcIndirect();
            if (uMMSI != AIS_AB_MMSI_NULL)
            {
                uSyncSource = AIS_SYNC_MODE_UTC_INDIRECT;
            }
        }

        // if UTC indirect is not available, check if base direct is running
        if (uSyncSource == AIS_SYNC_MODE_NONE && OPSTATUS::bEnableCalcNumRcvSt)
        {
            // 3. Check if base direct is running
            uMMSI = CUserDirMgr::getInst()->GetSyncSrcForBaseDirect();
            if (uMMSI != AIS_AB_MMSI_NULL)
            {
                uSyncSource = AIS_SYNC_MODE_BASE_DIRECT;
            }
            else
            {
                // 4. Check if base indirect is running
                uMMSI = CUserDirMgr::getInst()->GetSyncSrcForBaseIndirect();
                if (uMMSI != AIS_AB_MMSI_NULL)
                {
                    uSyncSource = AIS_SYNC_MODE_BASE_INDIRECT;
                }
                else
                {
                    // 5. Check if mobile semaphore is running
                    uMMSI = CUserDirMgr::getInst()->GetSyncSrcForMobileSemaphore();
                    if (uMMSI != AIS_AB_MMSI_NULL)
                    {
                        uSyncSource = AIS_SYNC_MODE_MOBILE_SEMAPHORE;
                    }
                }
            }
        }
    }

    // Update synchronization source
    UpdateSyncSource(uSyncSource, uMMSI);
}

/**
 * @brief Run periodically
 */
void CSyncMgr::RunPeriodically(void)
{
    static DWORD dwSyncCheckTick = 0;

    // Monitoring sync source and update UTC sync date/time every 1 second
    if(SysGetDiffTimeScnd(dwSyncCheckTick) > 1)
    {
        CheckSyncSources();
        UpdateUtcSyncDateTime();

        // Check own ship semaphore start and expired 
        // after 2 minutes of system initialization is done
        if(OPSTATUS::bSysInit2minDone)
        {
            CheckOwnShipSemaphoreStart();
            CheckOwnShipSemaphoreExpired();
        }

        dwSyncCheckTick = SysGetSystemTimer();
    }
}