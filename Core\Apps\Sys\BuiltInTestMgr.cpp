/**
 * @file    BuiltInTestMgr.cpp
 * @brief   BuiltInTestMgr class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "ADC.h"
#include "GPIOExt.h"
#include "LayerPhysical.h"
#include "SetupMgr.h"
#include "BuiltInTestMgr.h"

CBuiltInTestMgr::CBuiltInTestMgr()
{
    m_bErrorROM    = false;
    m_bErrorEEPROM = false;
}

CBuiltInTestMgr::~CBuiltInTestMgr()
{

}

/**
 * @brief Set ROM error
 * @param bError true if error, false otherwise
 */
void CBuiltInTestMgr::SetROMError(bool bError)
{
    m_bErrorROM = bError;
}

/**
 * @brief Set EEPROM error
 * @param bError true if error, false otherwise
 */
void CBuiltInTestMgr::SetEEPROMError(bool bError)
{
    m_bErrorEEPROM = bError;
}

/**
 * @brief Check if ROM error occurred
 * @return true if error occurred, false otherwise
 */
bool CBuiltInTestMgr::IsErrorROM()
{
    return m_bErrorROM;
}

/**
 * @brief Check if EEPROM error occurred
 * @return true if error occurred, false otherwise
 */
bool CBuiltInTestMgr::IsErrorEEPROM()
{
    return m_bErrorEEPROM;
}

/**
 * @brief Check if VSWR failure occurred
 * @return true if failure occurred, false otherwise
 */
bool CBuiltInTestMgr::IsVswrFail()
{
    //---------------------------------------------------------------------------------------------
    // 14.6.2.2.1 Method of measurement
    // Prevent the EUT from radiating with full power by mismatching the antenna for a VSWR of 3:1.
    // During the mismatch the output power is not required to be the rated output power.
    //---------------------------------------------------------------------------------------------

    return (CLayerPhysical::getInst()->GetRefPowerAdValue() > CSetupMgr::getInst()->GetVswrLimit());
}

/**
 * @brief Check if Tx malfunction occurred
 * @return true if malfunction occurred, false otherwise
 */
bool CBuiltInTestMgr::IsTxMalFunction()
{
    return !GetGpioLockStatPLL1();
}

/**
 * @brief Check if Rx malfunction occurred on channel 1
 * @return true if malfunction occurred, false otherwise
 */
bool CBuiltInTestMgr::IsRxMalFunctionCH1()
{
    if(CLayerPhysical::getInst()->IsEnableCheckMalfuncRx1())
        return !GetGpioLockStatPLL1();
    return false;
}

/**
 * @brief Check if Rx malfunction occurred on channel 2
 * @return true if malfunction occurred, false otherwise
 */
bool CBuiltInTestMgr::IsRxMalFunctionCH2()
{
    if(CLayerPhysical::getInst()->IsEnableCheckMalfuncRx2())
        return !GetGpioLockStatPLL2();
    return false;
}

/**
 * @brief Check if Rx malfunction occurred on DSC channel
 * @return true if malfunction occurred, false otherwise
 */
bool CBuiltInTestMgr::IsRxMalFunctionChDSC()
{
    if(CLayerPhysical::getInst()->IsEnableCheckMalfuncRx3())
        return !GetGpioLockStatPLL3();
    return false;
}

/**
 * @brief Get DC voltage ADC data
 * @return ADC data
 */
WORD CBuiltInTestMgr::GetDcVoltageAdcData()
{
    return DC_VOLTAGE_MIN;
}

/**
 * @brief Check if DC voltage failure occurred
 * @return true if failure occurred, false otherwise
 */
bool CBuiltInTestMgr::IsDcVoltageFail()
{
    WORD wVoltageAdc = GetDcVoltageAdcData();
    return (SYS_ADC_TO_VOLT(wVoltageAdc) < DC_VOLTAGE_MIN);
}
