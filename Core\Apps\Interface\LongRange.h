#ifndef __LONGRANGE_H__
#define __LONGRANGE_H__

#include "DataType.h"
#include "AllConst.h"

//----------------------------------------
// Constants
//----------------------------------------
#define LRI                 1
#define LRF                 2
#define LR1                 3
#define LR2                 4
#define LR3                 5

#define LR_MODE             0x40000000
#define LR_FR_W             0x00400000
#define LR_FR_U             0x00100000
#define LR_FR_P             0x00008000
#define LR_FR_O             0x00004000
#define LR_FR_I             0x00000100
#define LR_FR_F             0x00000020
#define LR_FR_E             0x00000010
#define LR_FR_C             0x00000004
#define LR_FR_B             0x00000002
#define LR_FR_A             0x00000001
#define LR_CONFIG_MAX       0x41000000
#define LR_CFG_DEFAULT      0x4050C137

#define LR_MODE_MANUAL      0
#define LR_MODE_AUTO        1

#define LR_FRS_NONE         '0'        // none
#define LR_FRS_AP           '2'        // available and provided
#define LR_FRS_NA           '3'        // not available from the AIS unit
#define LR_FRS_NP           '4'        // available but not provided

#define MAX_FR_CHAR         26        // A,B,C,E,F,I,O,P,U,W 10가지만 사용됨(IEC 61993-2, p.93)
#define MAX_FRS_CHAR        MAX_FR_CHAR

//-----------------------------------------------------------------------------------------
// ITU-R 1371-5 Annex4 3.3.1 Transmission interval
// The nominal transmission interval for the long-range AIS broadcast message should be 3 min.
#define LR_REPORT_INTERVAL_SEC     180  // 3 minutes

class cUart;
class CLongRange
{
public:
    CLongRange(void);
    ~CLongRange();

    static std::shared_ptr<CLongRange> getInst() {
        static std::shared_ptr<CLongRange> pInst = std::make_shared<CLongRange>();
        return pInst;
    }

public:
        void    SetUartBaudIdx(int nBaudIdx);
        void    ReloadSetup(void);
        void    SendOutStr(char *pstrMsg);
inline  void    SendOutHighSpdPort(char *pstrMsg, BOOL bSendMKD);

        BYTE    GetLRFunctionReplyStatus(WORD lr_function, BYTE local_data_result);
        BYTE    CheckReplyEnable(BYTE fcn_req);

        void    SetLongRangeConfig(BOOL bEnableAutoReply, DWORD dwLongRangeCfg);

        BOOL    CheckSentenceLRI(char *fields[], int count);
        BOOL    CheckSentenceLRF(char *fields[], int count);

        void    GetLR1sentence(char *psrBuff, WORD wSequenceNum, int requestor_mmsi, BOOL bA);
        void    GetLR2sentence(char *psrBuff, WORD wSequenceNum, BOOL bB, BOOL bC, BOOL bE, BOOL bF);
        void    GetLR3sentence(char *psrBuff, WORD wSequenceNum, BOOL bI, BOOL bO, BOOL bP, BOOL bU, BOOL bW);

        int     GetSequentialLRMsgID(void);

        BOOL    IsLRTxAvail(void);
        void    SetLongRangeRegionCtrl(INT8 nCtrlCode);

        void    EnableSendRcvDataToMKD(BOOL bEnable);
        void    SendSetupUartPortMon();

virtual void    ProcRunComLoopBackResp(char *pstrCmd);

        void    ProcessLRF(WORD sequence_num, int requsetor_mmsi, char *requsetor_name, char *function_request, char *pstrReplyStat=NULL);
        void    ProcessLRILRF(WORD lr_type, char *data, WORD nSrcPortID);
        BOOL    ProcessAckLRF(char *data, WORD nSrcPortID);

        void    ProcessSentence(char *pstrCmd);
        void    ProcessData(cUart *pUartDbgP);
        void    RunUartIsrHandler(void);

        void    RunPeriodicallyLongRangeVDL(BOOL bRunUnconditionally);

public:
    char    m_chPortID;
    int     m_nBaudIdx;
    BOOL    m_bEnableSendRcvDataToMKD;
    int     m_nTxSequenceNum;

    LONGRANGE_INFO  m_sConfigLR;
    INT8            m_nLongRangeTxCtrl;

protected:
    cUart*  m_pUartPort;
    int     m_nRxSize;
    UCHAR*  m_pRxData;
};

#endif//__LONGRANGE_H__
