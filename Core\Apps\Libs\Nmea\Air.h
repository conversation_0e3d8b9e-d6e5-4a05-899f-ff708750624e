/**
 * @file    Air.h
 * @brief   Air header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __AIR_H__
#define __AIR_H__

#define AIR_NONE            0
#define AIR_ID1_MSG1        1
#define AIR_ID1_MSG12       2
#define AIR_ID12_MSG13      3
#define AIR_ID12_MSG123     4

/******************************************************************************
 * 
* AIR - AIS interrogation Request
*
* $--AIR,xxxxxxxxx,x.x,x,x.x,x,xxxxxxxxx,x.x,x*hh<CR><LF>
*        |         |   | |   | |         |   |
*        1         2   3 4   5 6         7   8
*
* 1. MMSI of interrogated station-1
* 2. ITU-R M.1371 message requested from station-1
* 3. message sub-section (Reserved for future use)
* 4. number of second message from station-1
* 5. message sub-section (Reserved for future use)
* 6. MMSI of interrogated station-2
* 7. number of message requested from station-2
* 8. message sub-section (Reserved for future use)
 *
 ******************************************************************************/
class CAir : public CSentence
{
public:
    CAir();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the message type
     * @return The message type
     */
    static uint8_t GetMsgType(void) { return m_nMsgType; }

    /**
     * @brief Get the MMSI of the interrogated station-1
     * @return The MMSI of the interrogated station-1
     */
    static uint32_t GetMMSI1(void) { return m_dwMMSI1; }

    /**
     * @brief Get the ITU-R M.1371 message requested from station-1
     * @return The ITU-R M.1371 message requested from station-1
     */
    static uint32_t GetMsgReq1_1(void) { return m_dwMsgReq1_1; }

    /**
     * @brief Get the message sub-section of the first message from station-1
     * @return The message sub-section of the first message from station-1
     */
    static uint32_t GetMsgSubSec1_1(void) { return m_dwMsgSubSec1_1; }

    /**
     * @brief Get the number of the second message requested from station-1
     * @return The number of the second message requested from station-1
     */
    static uint32_t GetMsgReq1_2(void) { return m_dwMsgReq1_2; }

    /**
     * @brief Get the message sub-section of the second message from station-1
     * @return The message sub-section of the second message from station-1
     */
    static uint32_t GetMsgSubSec1_2(void) { return m_dwMsgSubSec1_2; }

    /**
     * @brief Get the MMSI of the interrogated station-2
     * @return The MMSI of the interrogated station-2
     */
    static uint32_t GetMMSI2(void) { return m_dwMMSI2; }

    /**
     * @brief Get the ITU-R M.1371 message requested from station-2
     * @return The ITU-R M.1371 message requested from station-2
     */
    static uint32_t GetMsgReq2(void) { return m_dwMsgReq2; }

    /**
     * @brief Get the message sub-section of the message from station-2
     * @return The message sub-section of the message from station-2
     */
    static uint32_t GetMsgSubSec2(void) { return m_dwMsgSubSec2; }

    /**
     * @brief Get the channel of interrogation
     * @return The channel of interrogation
     */
    static char GetChannel(void) { return m_cChannel; }

    /**
     * @brief Get the message ID of the first message from station-1
     * @return The message ID of the first message from station-1
     */
    static uint32_t GetMsgID1_1(void) { return m_dwMsgID1_1; }

    /**
     * @brief Get the message ID of the second message from station-1
     * @return The message ID of the second message from station-1
     */
    static uint32_t GetMsgID1_2(void) { return m_dwMsgID1_2; }

    /**
     * @brief Get the message ID of the message from station-2
     * @return The message ID of the message from station-2
     */
    static uint32_t GetMsgID2_1(void) { return m_dwMsgID2_1; }

    /**
     * @brief Make the AIR sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

public:
    static uint8_t  m_nMsgType;
    static uint32_t m_dwMMSI1;
    static uint32_t m_dwMsgReq1_1;
    static uint32_t m_dwMsgSubSec1_1;
    static uint32_t m_dwMsgReq1_2;
    static uint32_t m_dwMsgSubSec1_2;
    static uint32_t m_dwMMSI2;
    static uint32_t m_dwMsgReq2;
    static uint32_t m_dwMsgSubSec2;
    static char     m_cChannel;
    static uint32_t m_dwMsgID1_1;
    static uint32_t m_dwMsgID1_2;
    static uint32_t m_dwMsgID2_1;
};

#endif /* __AIR_H__ */

