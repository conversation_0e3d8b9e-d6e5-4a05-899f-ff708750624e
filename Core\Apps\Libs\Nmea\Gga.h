/**
 * @file    Gga.h
 * @brief   Gga header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __GGA_H__
#define __GGA_H__

/******************************************************************************
 * 
 * GGA - 
 *
 * $--GGA, hhmmss.ss, llll.ll,a,yyyyy.yy,a,x,xx,x.x,x.x,M,x.x,M,x.x,xxxx*hh<CR><LF>
 *          |            |----|     |----| |  |  |   |  |  |  |  |    |
 *          1            2          3      4  5  6   7  8  9  10 11   12
 *
 * 1. UTC of position
 * 2. Latitude N/S
 * 3. Longitude E/W
 * 4. GPS quality indicator
 * 5. Number of satellites in use, 00 12, may be different from the number in view
 * 6. Horizontal dilution of precision
 * 7. Antenna altitude above/below mean sea level (geoid)
 * 8. Units of antenna altitude, m
 * 9. Geoidal separation
 * 10. Units of geoidal separation, m
 * 11. Age of differential GPS data
 * 12. Differential reference station ID, 0000-1023
 * 
 ******************************************************************************/
class CGga : public CSentence
{
public:
    CGga();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidPosData(void);

    UINT8 GetPosModeIndi(void);
    DWORD GetPosModeTick(void);

    double GetLatVal(void);
    double GetLonVal(void);

    SYS_TIME GetUtcTime(void);

    void RunPeriodically(void);

protected:
    SYS_DATE m_xUtcDate;
    SYS_TIME m_xUtcTime;
    double  m_rRcvLatVal;
    double  m_rRcvLonVal;

    UINT8   m_uPosModeIndi;
    DWORD   m_dwPosModeTick;
    DWORD   m_dwPosValidTick;
};

#endif /* __GGA_H__ */

