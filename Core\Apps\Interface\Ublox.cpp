#include <cmsis_os.h>
#include "SysConst.h"
#include "AllConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "RxModem.h"
#include "VdlRxMgr.h"
#include "VdlTxMgr.h"
#include "LayerNetwork.h"
#include "LayerPhysical.h"
#include "TestModeMgr.h"
#include "AisModem.h"
#include "AlarmThing.h"
#include "SetupMgr.h"
#include "GpsBoard.h"
#include "Ublox.h"

//----------------------------------
// Local Prototypes for UBX protocol
//----------------------------------
void UBX_GetCheckSum(BYTE *pData, int nDataLen, BYTE *bChkSumA, BYTE *bChkSumB);
void UBX_MakeFormatSendData(cGpsBoard *pGnss, BYTE *pDataBuff, int nSize);
void UBX_InitPortCfg(cGpsBoard *pGnss, int nBaudRate);
void UBX_InitRcvrNmeaMsgRate(cGpsBoard *pGnss);
void UBX_InitRcvrUbxMsgRate(cGpsBoard *pGnss);
void UBX_CfgSBAS(cGpsBoard *pGnss, BOOL bEnableSBAS);
void UBX_CfgRcvrDatum(cGpsBoard *pGnss, WORD wDatum);
void UBX_CfgRcvrNavMode(cGpsBoard *pGnss);
void UBX_CfgRcvrNmea(cGpsBoard *pGnss);
void UBX_CfgMsgUpdateRate(cGpsBoard *pGnss, BOOL bFast);
void UBX_CfgRcvrSaveCfg(cGpsBoard *pGnss);
void UBX_CfgTimePulse(cGpsBoard *pGnss, BOOL bEnablePpsUnconditionally);
void UBX_SendCmdSetMsgRate(cGpsBoard *pGnss, UINT8 bClassId, UINT8 bMsgId, UINT8 bRate);

//-------------------------------------------------
// Constants for UBX Message Class ID
//-------------------------------------------------
#define MSGCLASS_NMEA       0xF0
#define MSGCLASS_UBX        0xF1
#define MSGCLASS_UBX_RXM    0x02
#define MSGCLASS_UBX_MON    0x0A

//-------------------------------------------------
// Constants for UBX Message ID for NMEA message
//-------------------------------------------------
#define NMEA_MSGID_DTM      0x0A
#define NMEA_MSGID_GBS      0x09
#define NMEA_MSGID_GGA      0x00
#define NMEA_MSGID_GLL      0x01
#define NMEA_MSGID_GNS      0x0D
#define NMEA_MSGID_GPQ      0x40
#define NMEA_MSGID_GRS      0x06
#define NMEA_MSGID_GSA      0x02
#define NMEA_MSGID_GST      0x07
#define NMEA_MSGID_GSV      0x03
#define NMEA_MSGID_RMC      0x04
#define NMEA_MSGID_THS      0x0E
#define NMEA_MSGID_TXT      0x41
#define NMEA_MSGID_VTG      0x05
#define NMEA_MSGID_ZDA      0x08
#define NMEA_MSGID_VLW      0x0F

//-------------------------------------------------
// Constants for UBX Message ID for UBX message
//-------------------------------------------------
#define UBX_MSGID_POS1      0x00
#define UBX_MSGID_SATSTA    0x03
#define UBX_MSGID_TIME      0x04
#define UBX_MSGID_POS2      0x05
#define UBX_MSGID_POS3      0x06
#define UBX_MSGID_MON_HW    0x09
#define UBX_MSGID_MON_GNSS  0x28

//-------------------------------------------------
// Constants for UBX protocol bit field definition
//-------------------------------------------------
#define BFIELD_SYCN1        0x00
#define BFIELD_SYCN2        0x01
#define BFIELD_CLASS        0x02
#define BFIELD_MSG          0x03
#define BFIELD_LENGTH0      0x04
#define BFIELD_LENGTH1      0x05
#define BFIELD_DAT          0x06

//-------------------------------------------------
// Constants for UBX protocol sync character
//-------------------------------------------------
#define UBX_SYNCCH1          0xB5
#define UBX_SYNCCH2          0x62
#define UBX_SIZE_OVHD        8            // overhead size including SYNCCHAR1, SYNCCHAR2, CLASS, ID, LENGTH(2), CH_A, CH_B

//-------------------------------------------------
// Constants for UBX protocol class ID
//-------------------------------------------------
#define UBX_CLSID_NAV        0x01
#define UBX_CLSID_RXM        0x02
#define UBX_CLSID_INF        0x04
#define UBX_CLSID_ACK        0x05
#define UBX_CLSID_CFG        0x06
#define UBX_CLSID_MON        0x0A
#define UBX_CLSID_AID        0x0B
#define UBX_CLSID_TIM        0x0D

//-------------------------------------------------
// Constants for UBX protocol CFG class message ID
//-------------------------------------------------
#define UBX_MSGID_CFG_PRT    0x00
#define UBX_MSGID_CFG_MSG    0x01
#define UBX_MSGID_CFG_RST    0x04
#define UBX_MSGID_CFG_RATE   0x08
#define UBX_MSGID_CFG_TP5    0x31

//-------------------------------------------------
// Constants for UBX protocol NAV class message ID
//-------------------------------------------------
#define UBX_MSGID_NAV_POSLLH  0x02		// NAV-POSLLH
#define UBX_MSGID_NAV_STAT    0x03      // NAV-STATUS
#define UBX_MSGID_NAV_VELNED  0x12      // NAV-VELNED
#define UBX_MSGID_NAV_TIMEUTC 0x21      // NAV-TIMEUTC
#define UBX_MSGID_NAV_SVINFO  0x30      // NAV-SVINFO
#define UBX_MSGID_NAV_SAT     0x35      // NAV-SAT

//-------------------------------------------------
// Constants for UBX protocol MON class message ID
//-------------------------------------------------
#define UBX_MSGID_MON_VER    0x04
#define UBX_MSGID_MON_HW     0x09

//---------------------
// Constants for datum
//---------------------
#define UBX_DATUM_WGS84      0
#define UBX_DATUM_MIN        0
#define UBX_DATUM_MAX        215        // Swiss Datum 1903+ (LV95)

//----------------------------------
// MACROs
//----------------------------------
#define UBX_Delay()          RunSystemDelayMs(100)

//----------------------------------
// Implementation
//----------------------------------
void UBX_InitRcvr(cGpsBoard *pGnss)
{
    UBX_InitPortCfg(pGnss, BAUDRATE_INT_GNSS_DFLT);
    UBX_CfgRcvrNavMode(pGnss);

    UBX_CfgRcvrDatum(pGnss, UBX_DATUM_WGS84);

    UBX_InitRcvrNmeaMsgRate(pGnss);
    UBX_InitRcvrUbxMsgRate(pGnss);

    UBX_CfgRcvrNmea(pGnss);
    UBX_CfgMsgUpdateRate(pGnss, FALSE);                // Set measurement rate 1Hz

    UBX_CfgTimePulse(pGnss, PPS_ENABLE_FIXED);        // output PPS only when GNSS fix

    UBX_CfgRcvrSaveCfg(pGnss);
}

void UBX_InitPortCfg(cGpsBoard *pGnss, int nBaudRate)
{
    //--------------------------------------------------------
    // Ublox, UBX-CFG-PRT, Set Port Configuration for UART
    // Protocol In : UBX+NMEA+RTCM
    // Protocol Out : UBX+NMEA
    // baudrate : 115200 bps
    //--------------------------------------------------------

#define LEN_CMD_PORTCFG        28

    //9600 bps
    const BYTE gpUbxBuffCfgPrt9600[LEN_CMD_PORTCFG] = {
        0xB5, 0x62, 0x06, 0x00, 0x14, 0x00, 0x01, 0x00, 
        0x00, 0x00, 0xD0, 0x08, 0x00, 0x00, 0x80, 0x25, 
        0x00, 0x00, 0x07, 0x00, 0x03, 0x00, 0x00, 0x00, 
        0x00, 0x00, 0xA2, 0xB5};

    // 38400 bps
    const BYTE gpUbxBuffCfgPrt38400[LEN_CMD_PORTCFG] = {
        0xB5, 0x62, 0x06, 0x00, 0x14, 0x00, 0x01, 0x00, 
        0x00, 0x00, 0xD0, 0x08, 0x00, 0x00, 0x00, 0x96, 
        0x00, 0x00, 0x07, 0x00, 0x03, 0x00, 0x00, 0x00, 
        0x00, 0x00, 0x93, 0x90 };

    // 57600 bps
    const BYTE gpUbxBuffCfgPrt57600[LEN_CMD_PORTCFG] = {
        0xB5, 0x62, 0x06, 0x00, 0x14, 0x00, 0x01, 0x00, 
        0x00, 0x00, 0xD0, 0x08, 0x00, 0x00, 0x00, 0xE1, 
        0x00, 0x00, 0x07, 0x00, 0x03, 0x00, 0x00, 0x00, 
        0x00, 0x00, 0xDE, 0xC9};

    // 115200 bps
    const BYTE gpUbxBuffCfgPrt115200[LEN_CMD_PORTCFG] = {
        0xB5, 0x62, 0x06, 0x00, 0x14, 0x00, 0x01, 0x00, 
        0x00, 0x00, 0xD0, 0x08, 0x00, 0x00, 0x00, 0xC2, 
        0x01, 0x00, 0x07, 0x00, 0x03, 0x00, 0x00, 0x00, 
        0x00, 0x00, 0xC0, 0x7E};


    const BYTE *pCmdPortCfg = NULL;
    switch(nBaudRate)
    {
    case 9600:
        pCmdPortCfg = gpUbxBuffCfgPrt9600;
        break;
    case 38400:
        pCmdPortCfg = gpUbxBuffCfgPrt38400;
        break;
    case 57600:
        pCmdPortCfg = gpUbxBuffCfgPrt57600;
        break;
    case 115200:
        pCmdPortCfg = gpUbxBuffCfgPrt115200;
        break;
    }

    if(pCmdPortCfg)
    {
        for(INT8 i = 0 ; i < 5 ; i++)
        {
            pGnss->SendData((BYTE*)pCmdPortCfg, LEN_CMD_PORTCFG);        // Change receiver's baudrate...
            UBX_Delay();
        }

        UBX_Delay();

        pGnss->m_pUartPort->SetUartPara(BAUDRATE_INT_GNSS_DFLT);
    }
}

void UBX_InitRcvrNmeaMsgRate(cGpsBoard *pGnss)
{
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_DTM, 1);

    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GBS, 1);        // RAIM 판단하기위해 필요!
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GGA, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GLL, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GNS, 1);        // GPS,GLONASS 각각의 PA (mode indicator) 를 얻기위해 필요!
    //UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GRS, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GSA, 1);
    //UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GST, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_GSV, 1);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_RMC, 1);

    //UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_THS, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_VLW, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_VTG, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_NMEA, NMEA_MSGID_ZDA, 0);
}

void UBX_InitRcvrUbxMsgRate(cGpsBoard *pGnss)
{
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_UBX, UBX_MSGID_POS1, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_UBX, 0x01, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_UBX, UBX_MSGID_SATSTA, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_UBX, UBX_MSGID_TIME, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_UBX, UBX_MSGID_POS2, 0);
    UBX_SendCmdSetMsgRate(pGnss, MSGCLASS_UBX, UBX_MSGID_POS3, 0);

    //UBX_SendCmdSetMsgRate(pGnss, UBX_CLSID_NAV, UBX_MSGID_NAV_STAT, 1);
    //UBX_GpsRcvrVersionEnable(pGnss);
}

void UBX_GetCheckSum(BYTE *pData, int nDataLen, BYTE *bChkSumA, BYTE *bChkSumB)
{
    //--------------------------------------------------------------------------------------------------------------------
    // The checksum algorithm used is the 8-Bit Fletcher Algorithm, which is used in the TCP standard (RFC 1145).
    //     This algorithm works as follows:
    // Buffer[N] contains the data over which the checksum is to be calculated.
    //     The two CK_ values are 8-Bit unsigned integers, only! If implementing with larger-sized integer values, make
    //     sure to mask both CK_A and CK_B with 0xFF after both operations in the loop.
    //--------------------------------------------------------------------------------------------------------------------

    int  i;
    BYTE bCsA, bCsB;

    bCsA = bCsB = 0;
    for(i = 0 ; i < nDataLen ; i++)
    {
        bCsA += pData[i];
        bCsB += bCsA;
    }
    *bChkSumA = bCsA;
    *bChkSumB = bCsB;
}

void UBX_MakeFormatSendData(cGpsBoard *pGnss, BYTE *pDataBuff, int nSize)
{
    BYTE bCsA, bCsB;

    UBX_GetCheckSum((BYTE*)&pDataBuff[2], nSize-4, &bCsA, &bCsB);        // include CLASS, ID, LENGTH(2bytes)
    pDataBuff[nSize-2]    = bCsA;
    pDataBuff[nSize-1]    = bCsB;
    pGnss->SendData(pDataBuff, nSize);
    UBX_Delay();
}

void UBX_GpsRcvrVersionEnable(cGpsBoard *pGnss)
{
    const BYTE gpUbxBuff[] = {0xB5, 0x62, 0x0A, 0x04, 0x00, 0x00, 0x0E, 0x34};
    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
}

void UBX_SendCmdSetMsgRate(cGpsBoard *pGnss, UINT8 bClassId, UINT8 bMsgId, UINT8 bRate)
{
    //BYTE gpUbxBuff[] = {0xB5, 0x62, 0x06, 0x01, 0x08, 0x00, 0xF0, 0x0A, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x6E};
    BYTE gpUbxBuff[] = {0xB5, 0x62, 0x06, 0x01, 0x08, 0x00, 0xF0, 0x0A, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x0E, 0x7D};

    gpUbxBuff[6] = bClassId;
    gpUbxBuff[7] = bMsgId;
    gpUbxBuff[8] = 0;            // I2C
    gpUbxBuff[9] = bRate;        // UART1
    gpUbxBuff[10]= 0;            // UART2
    gpUbxBuff[11]= 0;            // USB
    gpUbxBuff[12]= 0;            // SPI

    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
}

void UBX_CfgRcvrGnss(cGpsBoard *pGnss, BYTE bGnssConfigIdx, BOOL bEnableSBAS)
{
    //-------------------------------------------------------------------------
    // Input : bGnssConfig : INT_GNSS_CFG_IDX_MIN ~ INT_GNSS_CFG_IDX_MAX
    //-------------------------------------------------------------------------
    //[GPS-max32]
    const BYTE gpUbxBuff_GpsOnly[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x04, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x03, 0x08, 0x20, 0x00, 0x00, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x08, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x7D, 0x1E};

    //[Galileo-min1max8]
    const BYTE gpUbxBuff_GalileoOnly[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x01, 0x08, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x03, 0x01, 0x20, 0x00, 0x00, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x01, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x6C, 0x8F};

    //[Beidou-min1max32]
    const BYTE gpUbxBuff_BeidouOnly[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x01, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x03, 0x01, 0x20, 0x00, 0x01, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x01, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x6C, 0x87};

    //[Glonass-min1max32]
    const BYTE gpUbxBuff_GlonassOnly[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x01, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x03, 0x01, 0x20, 0x00, 0x00, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x01, 0x20, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x6C, 0x6F};

    //[Gps+Beidou]
    const BYTE gpUbxBuff_GpsBeidou[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x04, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x03, 0x08, 0x20, 0x00, 0x01, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x08, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x7E, 0x3A};

    //[Gps+Galileo]
    const BYTE gpUbxBuff_GpsGalileo[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x04, 0x08, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x03, 0x08, 0x20, 0x00, 0x00, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x08, 0x20, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x7E, 0x42};

    //[Gps+Glonass]
    const BYTE gpUbxBuff_GpsGlonass[] = {
        0xB5, 0x62, 0x06, 0x3E, 0x3C, 0x00, 0x00, 0x20, 0x20, 0x07, 0x00, 0x01, 0x20, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x01, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x04, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x01, 0x03, 0x08, 0x20, 0x00, 0x00, 0x00, 0x01, 0x01, 0x04, 0x00, 0x08, 0x00, 0x00, 0x00,
        0x01, 0x03, 0x05, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x05, 0x06, 0x08, 0x20, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x7E, 0x22};

    const int SIZE_GNSS_CONFIGDAT = sizeof(gpUbxBuff_GpsOnly);


    BYTE *pUbxBuff = NULL;

    switch(bGnssConfigIdx)
    {
    case INT_GNSS_CFG_IDX_GPS_ONLY:
        pUbxBuff = (BYTE*)gpUbxBuff_GpsOnly;
        break;
    case INT_GNSS_CFG_IDX_GLONASS_ONLY:
        pUbxBuff = (BYTE*)gpUbxBuff_GlonassOnly;
        break;
    case INT_GNSS_CFG_IDX_BEIDOU_ONLY:
        pUbxBuff = (BYTE*)gpUbxBuff_BeidouOnly;
        break;
    case INT_GNSS_CFG_IDX_GALILEO_ONLY:
        pUbxBuff = (BYTE*)gpUbxBuff_GalileoOnly;
        break;
    case INT_GNSS_CFG_IDX_GPS_GLONASS:
        pUbxBuff = (BYTE*)gpUbxBuff_GpsGlonass;
        break;
    case INT_GNSS_CFG_IDX_GPS_BEIDOU:
        pUbxBuff = (BYTE*)gpUbxBuff_GpsBeidou;
        break;
    case INT_GNSS_CFG_IDX_GPS_GALILEO:
        pUbxBuff = (BYTE*)gpUbxBuff_GpsGalileo;
        break;
    default:
        DEBUG_LOG("UBX_CfgRcvrGnss] Invalid param : %d\r\n", bGnssConfigIdx);
        return;
    }

    UBX_MakeFormatSendData(pGnss, (BYTE*)pUbxBuff, SIZE_GNSS_CONFIGDAT);

    UBX_CfgSBAS(pGnss, bEnableSBAS);
}

void UBX_CfgSBAS(cGpsBoard *pGnss, BOOL bEnableSBAS)
{
    BYTE gpUbxBuff[] = {0xB5, 0x62, 0x06, 0x16, 0x08, 0x00, 0x01, 0x07, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2F, 0xD5};
    gpUbxBuff[6] = bEnableSBAS;
    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
}

void UBX_CfgRcvrMsgAck(cGpsBoard *pGnss)
{
    // Ublox, UBX-CFG-MSG, Disable all NMEA sentences
    /*
    const BYTE gpUbxBuffDisableAllNmea[] = {
        0xB5, 0x62, 0x05, 0x01, 0x02, 0x00, 0x06, 0x01, 0x0F, 0x38,
        0xB5, 0x62, 0x05, 0x00, 0x02, 0x00, 0x06, 0x01, 0x0E, 0x33,
    };
    */
    BYTE gpUbxBuff[] = {0xB5, 0x62, 0x05, 0x01, 0x02, 0x00, 0x06, 0x01, 0x0F, 0x38,};

    gpUbxBuff[3] = 1;
    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuff, sizeof(gpUbxBuff));

    gpUbxBuff[3] = 0;
    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
}

void UBX_CfgRcvrDatum(cGpsBoard *pGnss, WORD wDatum)
{
    // Ublox, UBX-CFG-DAT command
    BYTE gpUbxBuffDatum[] = {0xB5, 0x62, 0x06, 0x06, 0x02, 0x00, 0x00, 0x00, 0x0E, 0x4A};

    gpUbxBuffDatum[6] = wDatum;                                                            // lower byte little-endian WORD
    gpUbxBuffDatum[7] = wDatum << 8;                                                    // high byte little-endian WORD
    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuffDatum, sizeof(gpUbxBuffDatum));
}

void UBX_CfgRcvrNavMode(cGpsBoard *pGnss)
{
    //-----------------------------------------------------------------------------------------------------------------
    // Ublox, UBX-CFG-NAV5 command
    //-----------------------------------------------------------------------------------------------------------------
    // gpUbxBuffNav5[8] dynModel (Dynamic platform model) = 0x05 (Sea)
    // gpUbxBuffNav5[9] fixMode (Position fixing mode) = 0x03 (Audo 2D/3D)
    // gpUbxBuffNav5[10..13] fixedAlt (Fixed altitude (mean sea level) for 2D fix mode) = 0x00000000
    // gpUbxBuffNav5[14..17] fixedAltVar (Fixed altitude variance for 2D mode) = 0x00002710
    // gpUbxBuffNav5[18] minElev (Minimum Elevation for a GNSS satellite to be used in NAV)
    // gpUbxBuffNav5[19] drLimit (Maximum time to perform dead reckoning (linear extrapolation) in case of GPS signal loss) = 0x00
    // gpUbxBuffNav5[20..21] pDop (Position DOP Mask to use) = 0x00FA
    // gpUbxBuffNav5[22..23] tDop (Time DOP Mask to use) = 0x00FA
    // gpUbxBuffNav5[24..25] pAcc (Position Accuracy Mask) = 0x001E
    // gpUbxBuffNav5[26..27] tAcc (Time Accuracy Mask) = 0x012C
    // gpUbxBuffNav5[28] staticHoldThresh (Static hold threshold)
    // gpUbxBuffNav5[34] staticHoldMaxDist, Static hold distance threshold (before quitting static hold)
    //-----------------------------------------------------------------------------------------------------------------

    BYTE gpUbxBuffNav5[] = {
        0xB5, 0x62, 0x06, 0x24, 0x24, 0x00, 0xFF, 0xFF, 0x05, 0x03, 0x00, 0x00, 0x00, 0x00, 0x10, 0x27,
        0x00, 0x00, 0x0A, 0x00, 0xFA, 0x00, 0xFA, 0x00, 0x64, 0x00, 0x5E, 0x01, 0x00, 0x3C, 0x00, 0x00,
        0x00, 0x00, 0xC8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4B, 0x26};

    gpUbxBuffNav5[18] = 10;        // [12] minElev (Minimum Elevation for a GNSS satellite to be used in NAV)

//    gpUbxBuffNav5[28] = 10;        // [28] staticHoldThresh (Static hold threshold) = 2 cm/s, use static hold mode to avoid position wandering! 별로 효과없음!
//    gpUbxBuffNav5[34] =            // staticHoldMaxDist

    UBX_MakeFormatSendData(pGnss, gpUbxBuffNav5, sizeof(gpUbxBuffNav5));
}

void UBX_CfgRcvrNmea(cGpsBoard *pGnss)
{
    /*
    //---------------------------------------------------------------
    // Ublox, UBX-CFG-NMEA, Extended NMEA protocol configuration V1
    // Enable COG output even if COG is frozen
    //---------------------------------------------------------------
    const BYTE gpUbxBuff[] = {0xB5, 0x62, 0x06, 0x17, 0x14, 0x00, 0x20, 0x40, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xC4};
    */
    //------------------------------------------------------------------------------------------------
    // Ublox, UBX-CFG-NMEA, Extended NMEA protocol configuration V1
    // trackFilt    : Enable COG output even if COG is frozen
    // NMEA version : 4.0
    // mainTalkerId : 'GP'
    // svNumbering = 1: Extended - Use proprietary numbering, to enable it output Beidou SV sentences
    //------------------------------------------------------------------------------------------------
    const BYTE gpUbxBuff[] = {0xB5, 0x62, 0x06, 0x17, 0x14, 0x00, 0x20, 0x40, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00,
                              0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0xD0};

    pGnss->SendData((BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
    UBX_Delay();
}

void UBX_CfgMsgUpdateRate(cGpsBoard *pGnss, BOOL bFast)
{
    //------------------------------------------------------------------------------------------------
    // bFast : TRUE = measurement time 200ms (5 Hz rate)
    //           FALSE= measurement time 1000ms (1 Hz rate)
    //------------------------------------------------------------------------------------------------
    //------------------------------------------------------------------------------------------------
    // Ublox, UBX-CFG-RATE command
    // measRate : measurement
    //
    // bFast : TRUE = measurement time 200ms (5 Hz rate)
    //           FALSE= measurement time 1000ms (1 Hz rate)
    //------------------------------------------------------------------------------------------------
    BYTE gpUbxBuff[] = {0xB5, 0x62, 0x06, 0x08, 0x06, 0x00, 0xC8, 0x00, 0x01, 0x00, 0x01, 0x00, 0xDE, 0x6A};

    pGnss->SendData((BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
    gpUbxBuff[6] = bFast ? 0xC8 : 0xE8;
    gpUbxBuff[7] = bFast ? 0x00 : 0x03;
    UBX_MakeFormatSendData(pGnss, (BYTE*)gpUbxBuff, sizeof(gpUbxBuff));
}

void UBX_CfgRcvrSaveCfg(cGpsBoard *pGnss)
{
    //---------------------------------------------------------------------------------------------------------------------------------
    // Ublox, UBX-CFG-CFG, Clear, Save and Load configurations
    // deviceMask(Mask which selects the devices for this command) = 0x03 devBBR(device battery backed RAM) and devFlash (device Flash)
    //---------------------------------------------------------------------------------------------------------------------------------
    const BYTE gpUbxBuffSaveCfg[] = {0xB5, 0x62, 0x06, 0x09, 0x0D, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
                                    0x00, 0x00, 0x03, 0x1D, 0xAB};

    pGnss->SendData((BYTE*)gpUbxBuffSaveCfg, sizeof(gpUbxBuffSaveCfg));
    UBX_Delay();
}

void UBX_CfgTimePulse(cGpsBoard *pGnss, _tagPpsConfig nConfigPPS)
{
    //--------------
    // UBX-CFG-TP5
    //--------------

    const BYTE gpSetPulseUnconditionally[]={0xB5, 0x62, 0x06, 0x31, 0x20, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x42,
                                            0x0F, 0x00, 0x40, 0x42, 0x0F, 0x00, 0xA0, 0x86, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                            0x00, 0x00, 0x73, 0x00, 0x00, 0x00, 0x14, 0xCA};

    const BYTE gpSetPulseOnlyWhenFixed[] = {0xB5, 0x62, 0x06, 0x31, 0x20, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x42,
                                            0x0F, 0x00, 0x40, 0x42, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA0, 0x86, 0x01, 0x00, 0x00, 0x00,
                                            0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x18, 0x3E};

    const BYTE gpSetPulseDisable[]={0xB5, 0x62, 0x06, 0x31, 0x20, 0x00, 0x00, 0x01, 0x00, 0x00, 0x32, 0x00, 0x00, 0x00, 0x40, 0x42,
                                    0x0F, 0x00, 0x40, 0x42, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA0, 0x86, 0x01, 0x00, 0x00, 0x00,
                                    0x00, 0x00, 0xF6, 0x00, 0x00, 0x00, 0xC9, 0xB2};

    switch(nConfigPPS)
    {
    case PPS_ENABLE_ALWAYS:
        pGnss->SendData((BYTE*)gpSetPulseUnconditionally, sizeof(gpSetPulseUnconditionally));
        break;
    case PPS_ENABLE_FIXED:
        pGnss->SendData((BYTE*)gpSetPulseOnlyWhenFixed, sizeof(gpSetPulseOnlyWhenFixed));
        break;
    case PPS_DISABLE:
        pGnss->SendData((BYTE*)gpSetPulseDisable, sizeof(gpSetPulseDisable));
        break;
    }
}
