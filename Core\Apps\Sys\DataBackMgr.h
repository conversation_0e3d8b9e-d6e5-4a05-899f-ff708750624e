/**
 * @file    DataBackMgr.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <memory>
#include "SysConst.h"
#include "DevMem.h"
#include "Define_Flash.h"

#ifndef  __DATABACKMGR_HPP
#define  __DATABACKMGR_HPP

//=============================================================================
#define BACK_DEVICE_FLASH       0
#define BACK_DEVICE_EEPROM      1
//=============================================================================
#define DATA_SIGNATURE_STR      "INLA"
#define DATA_SIGNATURE_HEX      (0x494E4C41)
//=============================================================================
#define BACK_MODE_SAVE          0
#define BACK_MODE_LOAD          1
//=============================================================================

class CDataBackMgr
{
public:
    CDataBackMgr(int nDeviceId, DWORD dStartBackAddr);
    virtual ~CDataBackMgr(void);

public:
    void  SetAllClearMode(int nMode);
    int   GetAllClearMode(void);

    void  SetStartBackAddr(DWORD dStartBackAddr);
    DWORD GetStartBackAddr(void);

    int   BackDataSegmentWrite(void *pData, int nWriteSize);
    int   BackDataSegmentRead(void *pData, int nReadSize);

    int   FindDataSegmentStart(int nSegSize);
    int   FindDataSegmentSize(int nDataSize);

private:
    int         m_nDeviceId;
    int         m_nStartPos;
    int         m_nAllClearMode;
    DWORD       m_dStartBackAddr;
    std::shared_ptr<CDevMem> m_pBackDeviceCtrl;
};

#endif /*__DATABACKMGR_HPP*/

