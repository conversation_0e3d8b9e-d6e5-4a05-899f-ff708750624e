#include <memory>
#include "DataType.h"
#include "AllConst.h"

#ifndef __SARTMGR_H__
#define __SARTMGR_H__

typedef struct  
{
	UINT	uMMSI;
	INT8	nType;					// SART_TYPE_ACTIVE ~ SART_TYPE_TYPEAPP
	INT8	nAlarmStatus;			// ALARM_STAT_RECTIFIED/ALARM_STAT_NEW_ALR/ALARM_STAT_ACKED
	DWORD	dwPosRcvSec;
	DWORD	dwTextRcvSec;
} SART_DATA;


class CSartMgr
{
public:
	CSartMgr();
	~CSartMgr();

    static std::shared_ptr<CSartMgr> getInst() {
        static std::shared_ptr<CSartMgr> pInst = std::make_shared<CSartMgr>();
        return pInst;
    }

public:
	enum _tagConsts
	{
		NUM_SARTS = 10,
	};

	SART_DATA	*m_pSartList;

protected:
	void	ClearSartData(INT8 nIndex);
	void	SetDataSART(INT8 nIndex, UINT uMMSI, INT8 nType);
	void	AddSART(UINT uMMSI, INT8 nType);
	void	RemoveSARTwithMMSI(UINT uMMSI);

public:
	int		FindSART(UINT uMMSI);
	int		FindEmptySlot();
	int		FindOldestSART();
	INT8	GetTypeOfSART(UINT uMMSI);
	BOOL	IsTestingSART(UINT uMMSI);
	void	CheckAndAddSART(UINT uMMSI, int nNavStatus);
	void	CheckTypeOfSART(UINT uMMSI, char *pstr);
	BOOL	CheckAlarmNewActiveSART();
	void	SetActiveSartAlarmAcked();

	void	RunPeriodicallySART();
};

#endif//__SARTMGR_H__
