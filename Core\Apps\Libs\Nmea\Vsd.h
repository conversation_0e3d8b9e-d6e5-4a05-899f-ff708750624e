/**
 * @file    Vsd.h
 * @brief   Vsd header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Sentence.h"

#ifndef __VSD_H__
#define __VSD_H__

/******************************************************************************
 * 
 * VSD - Voyage Static Data
 *
 * $--VSD,x.x,x.x,x.x,c--c,hhmmss.ss,xx,xx,x.x,x.x*hh<CR><LF>
 *         |   |   |   |     |       |  |   |   |
 *         1   2   3   4     5       6  7   8   9
 *
 * 1. Type of ship and cargo category, 0 to 255
 * 2. Maximum present static draught, 0 to 25.5
 * 3. Persons on-board, 0 to 8191
 * 4. Destination, 1-20 characters
 * 5. Est. UTC of destination arrival
 * 6. Est. day of arrival at destination, 00 to 31(UTC)
 * 7. Est. month of arrival at destination, 00 to 12(UTC)
 * 8. Navigational status, 0 to 15
 * 9. Regional application flags, 0 to 15
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
class CVsd : public CSentence 
{
public:
    CVsd();

    static bool Parse(const char *pszSentence);
    static int32_t MakeSentence(char *pszSentence, char *pszTalker=nullptr);
    
    static int  GetSpecialManoeuvreIndiFromVSD(int nManoeuvre);
    static int  GetSpecialManoeuvreIndiToVSD(int nManoeuvre);
};

#endif /* __VSD_H__ */

