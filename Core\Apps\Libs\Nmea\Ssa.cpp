/**
 * @file    Ssa.cpp
 * @brief   Ssa class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Ssa.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Predef.h"
#include "Ship.h"

/******************************************************************************
 * IEC 61993-2:2018 Annex F
 * 
 * SSA - Sender signature authentication
 *
 * $--SSA,ccc,c,h--h*hh<CR><LF>
 *         |  |  |
 *         1  2  3
 *
 * 1. Signature protected sentence
 * 2. Signature calculation method
 * 3. Signature authentication
 *
 ******************************************************************************/
CSsa::CSsa() : CSentence()
{
    ClearData();
}

void CSsa::ClearData(void)
{
    m_nCode = SSA_NA;
    m_cSignMethod = '\0';
    m_dwRcvTick = 0;
    memset(m_szAuthentication, 0x00, sizeof(m_szAuthentication));
}
    
/**
 * @brief Parse the sentence
 */
bool CSsa::Parse(const char *pszSentence)
{
    char pstrTmp[8];

    memset(pstrTmp, 0x00, sizeof(pstrTmp));
    GetFieldString(pszSentence, 1, pstrTmp);

    if(!strncmp(pstrTmp, "EPV", 3))
        m_nCode = SSA_EPV;
    else if(!strncmp(pstrTmp, "SSD", 3))
        m_nCode = SSA_SSD;
    else if(!strncmp(pstrTmp, "VSD", 3))
        m_nCode = SSA_VSD;
    else
        return false;

    if (!GetFieldString(pszSentence, 2, &m_cSignMethod, sizeof(m_cSignMethod)))
    {
        WARNING_LOG("SSA] m_cSignMethod failed\r\n");
        ClearData();
        return false;
    }

    if (m_cSignMethod == '1')
    {
        if (!GetFieldString(pszSentence, 3, m_szAuthentication, sizeof(m_szAuthentication)))
        {
            WARNING_LOG("SSA] m_szAuthentication failed\r\n");
            ClearData();
            return false;
        }
        else
        {
            m_dwRcvTick = SysGetSystemTimer();
        }
    }
    else if (m_cSignMethod == 'P')
    {
        if (!GetFieldString(pszSentence, 3, m_szAuthentication, sizeof(m_szAuthentication)))
        {
            WARNING_LOG("SSA] m_szAuthentication failed\r\n");
            ClearData();
            return false;
        }
        else
        {
            m_dwRcvTick = SysGetSystemTimer();
        }   
    }
    else
    {
        ClearData();
        return false;
    }

    return true;
}

/**
 * @brief Call function periodically
 */
void CSsa::RunPeriodically(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_HDG_LASTDATA_STAYMS)
    {
        ClearData();
    }
}