/**
 * @file    DevMem.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef  __DEVMEM_H__
#define  __DEVMEM_H__

#include <memory>
#include "DataType.h"

//=============================================================================
class CDevMem
{
public:
    CDevMem(void){};
    virtual ~CDevMem(void){};

public:
    virtual DWORD GetSectorSize(DWORD dAddr) = 0;
    virtual int   EraseSector(DWORD dAddr, int nSecCnt=1) = 0;
    virtual int   WriteData(DWORD dAddr, void *pData, int nSize) = 0;
    virtual int   ReadData(DWORD dAddr, void *pData, int nSize) = 0;
};

#endif /*__DEVMEM_H__*/

