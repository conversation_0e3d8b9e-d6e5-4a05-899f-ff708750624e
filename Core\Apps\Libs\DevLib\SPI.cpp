/**
 * @file    SPI.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include "main.h"
#include "SysConst.h"
#include "GPIOExt.h"
#include "SPI.h"

#define TIMEOUT_SPI_TRX     100     // 100ms timeout

cSpiSYS::cSpiSYS(SPI_TypeDef *pBaseAddr)
{
    m_pBaseAddr = pBaseAddr;
    InitSPI();
}
cSpiSYS::~cSpiSYS(void)
{
}

void cSpiSYS::DeinitSPI()
{
    HAL_SPI_DeInit(&m_xSpiHand);
}

void cSpiSYS::InitSPI()
{
    memset(&m_xSpiHand, 0x00, sizeof(m_xSpiHand));

    m_xSpiHand.Instance                         = m_pBaseAddr;
    m_xSpiHand.Init.Mode                        = SPI_MODE_MASTER;

    if (m_pBaseAddr == SPI1)
    {
        m_xSpiHand.Init.Direction               = SPI_DIRECTION_2LINES_TXONLY;
    }
    else
    {
        m_xSpiHand.Init.Direction               = SPI_DIRECTION_2LINES;
    }

    m_xSpiHand.Init.DataSize                    = SPI_DATASIZE_8BIT;
    m_xSpiHand.Init.CLKPolarity                 = SPI_POLARITY_LOW;               // CPOL= 0
    m_xSpiHand.Init.CLKPhase                    = SPI_PHASE_1EDGE;                // CPA = 1
    m_xSpiHand.Init.NSS                         = SPI_NSS_SOFT;
    m_xSpiHand.Init.BaudRatePrescaler           = SPI_BAUDRATEPRESCALER_32;
    m_xSpiHand.Init.FirstBit                    = SPI_FIRSTBIT_MSB;
    m_xSpiHand.Init.TIMode                      = SPI_TIMODE_DISABLE;
    m_xSpiHand.Init.CRCCalculation              = SPI_CRCCALCULATION_DISABLE;
    m_xSpiHand.Init.CRCPolynomial               = 0;
    m_xSpiHand.Init.NSSPMode                    = SPI_NSS_PULSE_ENABLE;
    m_xSpiHand.Init.FifoThreshold               = SPI_FIFO_THRESHOLD_01DATA;
    m_xSpiHand.Init.TxCRCInitializationPattern  = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
    m_xSpiHand.Init.RxCRCInitializationPattern  = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
    m_xSpiHand.Init.MasterSSIdleness            = SPI_MASTER_SS_IDLENESS_00CYCLE;
    m_xSpiHand.Init.MasterInterDataIdleness     = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
    m_xSpiHand.Init.MasterReceiverAutoSusp      = SPI_MASTER_RX_AUTOSUSP_DISABLE;
    m_xSpiHand.Init.MasterKeepIOState           = SPI_MASTER_KEEP_IO_STATE_DISABLE;
    m_xSpiHand.Init.IOSwap                      = SPI_IO_SWAP_DISABLE;

    if (HAL_SPI_Init(&m_xSpiHand) != HAL_OK)
    {
        Error_Handler();
    }
}

HAL_StatusTypeDef cSpiSYS::WriteSpiData(UCHAR cbus_address, UINT16 *data_ptr, UCHAR bytes_per_access, UCHAR accesses)
{
    UCHAR number_of_bytes;
    UCHAR access_count = 0;
    UCHAR sendData[9];        //extra one for address + max. data 8byte
    HAL_StatusTypeDef errorcode = HAL_OK;
    
    number_of_bytes = (accesses * bytes_per_access) + 1;    //extra one for address
    
    if (number_of_bytes-1 > 8)
        return HAL_ERROR;
    
    sendData[0] = cbus_address;
    
    for (access_count = 1; access_count < number_of_bytes;)
    {
        if (bytes_per_access == 2)
        {
            sendData[access_count++] = (*data_ptr & 0xFF00)>>8;
            sendData[access_count++] = (*data_ptr & 0x00FF);
        }
        else
        {
            sendData[access_count++] = (*data_ptr & 0x00FF);         //mask the data off
        }
    
        data_ptr++;
    }
    
    
    errorcode = HAL_SPI_Transmit(&m_xSpiHand, sendData, number_of_bytes, TIMEOUT_SPI_TRX);
    
    return errorcode;
}

HAL_StatusTypeDef cSpiSYS::ReadSpiData(UCHAR cbus_address, UINT16 *data_ptr, UCHAR bytes_per_access, UCHAR accesses)
{
    UCHAR number_of_bytes;
    UCHAR access_count;
    UCHAR sndData[8];
    UCHAR rcvData[8];
    HAL_StatusTypeDef errorcode = HAL_OK;
    
    number_of_bytes = accesses * bytes_per_access;
    
    if (number_of_bytes > 8)
        return HAL_ERROR;
    
    memset(sndData, 0x00, 8);
    sndData[0] = cbus_address;
    errorcode = HAL_SPI_TransmitReceive(&m_xSpiHand, sndData, rcvData, number_of_bytes+1, TIMEOUT_SPI_TRX);
    
    for (access_count = 1; access_count < number_of_bytes+1;)
    {
        if (bytes_per_access == 2)
        {
            *data_ptr = rcvData[access_count++]<<8;
            *data_ptr |= rcvData[access_count++];
        }
        else if (bytes_per_access == 1)
        {
            *data_ptr |= rcvData[access_count++];
        }
    
        data_ptr++;
    }

    return errorcode;
}

HAL_StatusTypeDef cSpiSYS::WriteSpiData(UINT8 *pBuf, int nSize)
{
    return HAL_SPI_Transmit(&m_xSpiHand, pBuf, nSize, TIMEOUT_SPI_TRX);
}

HAL_StatusTypeDef cSpiSYS::ReadSpiData(UINT8 *pBuf, int nSize)
{
    return HAL_SPI_Receive(&m_xSpiHand, pBuf, nSize, TIMEOUT_SPI_TRX);
}

HAL_StatusTypeDef cSpiSYS::WriteAndReadSpiData(UINT8 *pTxBuf, UINT8 *pRxBuf, int nSize)
{
    return HAL_SPI_TransmitReceive(&m_xSpiHand, pTxBuf, pRxBuf, nSize, TIMEOUT_SPI_TRX);
}
