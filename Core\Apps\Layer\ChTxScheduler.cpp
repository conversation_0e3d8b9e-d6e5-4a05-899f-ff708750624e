#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "AisLib.h"
#include "Ship.h"
#include "LayerNetwork.h"
#include "ChannelMgr.h"
#include "ReportRateMgr.h"
#include "LayerPhysical.h"
#include "AisMsg.h"
#include "SlotMgr.h"
#include "VdlTxMgr.h"
#include "Uart.h"
#include "GPIOExt.h"
#include "TestModeMgr.h"
#include "Timer.h"
#include "AisModem.h"
#include "SetupMgr.h"
#include "LongRange.h"
#include "SysOpStatus.h"
#include "ChTxScheduler.h"

int   CChTxScheduler::m_nRRChgTimeOutMin = 0;
DWORD CChTxScheduler::m_dwRRTimeOutStartTick = 0;
DWORD CChTxScheduler::m_dwRRTimeOutMS = 0;
int   CChTxScheduler::m_nRRChgSlaveNewSI = SLOTID_NONE;

CChTxScheduler::CChTxScheduler(CChannelMgr *pChannel)
{
    m_pChannel = pChannel;
    m_dwChLastPosTxSec    = 0;
    m_nRoutineNextTxSlot = SLOTID_NONE;

    Initialize();
}

CChTxScheduler::~CChTxScheduler(void)
{
}

/**
 * @brief Initialize the channel transmission scheduler
 */
void CChTxScheduler::Initialize(void)
{
    m_fChReportRate         = 0;
    m_fChReportRateHalf     = 0;

    m_nSizeSI               = 0;
    m_nSizeHalfSI           = 0;

    m_nNSS                  = SLOTID_NONE;
    m_nNS                   = SLOTID_NONE;
    m_nNextStartSI          = SLOTID_NONE;

    m_nFirstFrameTxSlotId   = SLOTID_NONE;
    m_nChangeRRphaseTxSlotId= SLOTID_NONE;

    m_nFirstFrameTxCnt      = SLOTID_NONE;
    m_dwSendPosReportTick   = 0;

    m_nAddCntITDMA          = 0;
    m_nNextItdmaTxCnt       = 0;
    m_nNextItdmaTxSlotId    = SLOTID_NONE;
    m_nItdmaStartSI         = 0;
    m_nItdmaNI              = 0;
    m_nItdmaSizeSI          = 0;

    m_dwStaticReportIntervalSec     = 0;
    m_dwStaticReportNextSec         = 0;

    m_dwRoutineAllocLastCheckSec    = 0;
    m_wRoutineAllocLastCheckSlot    = 0;
    m_nRoutinePosMsgID              = AIS_MSG_NO_ID_UNDEFINED;

    m_dwLongRangeReportIntervalSec  = 0;
    m_dwLongRangeTxNextSec          = 0;

    m_nLastScheduledTxSlotID        = 0;
}

/**
 * @brief Shift the frame index
 * @param nShiftSlot Number of slots to shift
 */
void CChTxScheduler::ShiftFrameIndex(INT16 nShiftSlot)
{
    DEBUG_LOG("ShiftFrameIndex] before, shift : %d, NSS : %d, NS : %d, startSI : %d, firstFrameSlot : %d, ChgRRTxSlot : %d, LastTxSlot : %d\r\n",
            nShiftSlot, m_nNSS, m_nNS, m_nNextStartSI, m_nFirstFrameTxSlotId, m_nChangeRRphaseTxSlotId, m_nLastScheduledTxSlotID);

    if(m_nNSS != SLOTID_NONE)                   m_nNSS = CAisLib::FrameMapSlotIdAdd(m_nNSS, nShiftSlot);
    if(m_nNS != SLOTID_NONE)                    m_nNS = CAisLib::FrameMapSlotIdAdd(m_nNS, nShiftSlot);
    if(m_nNextStartSI != SLOTID_NONE)           m_nNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, nShiftSlot);
    if(m_nFirstFrameTxSlotId != SLOTID_NONE)    m_nFirstFrameTxSlotId = CAisLib::FrameMapSlotIdAdd(m_nFirstFrameTxSlotId, nShiftSlot);
    if(m_nChangeRRphaseTxSlotId != SLOTID_NONE) m_nChangeRRphaseTxSlotId = CAisLib::FrameMapSlotIdAdd(m_nChangeRRphaseTxSlotId, nShiftSlot);
    if(m_nItdmaStartSI != SLOTID_NONE)          m_nItdmaStartSI = CAisLib::FrameMapSlotIdAdd(m_nItdmaStartSI, nShiftSlot);
    if(m_nNextItdmaTxSlotId != SLOTID_NONE)     m_nNextItdmaTxSlotId = CAisLib::FrameMapSlotIdAdd(m_nNextItdmaTxSlotId, nShiftSlot);
    if(m_nRoutineNextTxSlot != SLOTID_NONE)     m_nRoutineNextTxSlot = CAisLib::FrameMapSlotIdAdd(m_nRoutineNextTxSlot, nShiftSlot);

    ResetLastScheduledTxSlotID();
}

/**
 * @brief Set the channel report interval
 * @param nTotalNI Total number of slots in a frame
 * @param fChReportIntervalSec Channel report interval in seconds
 */
void CChTxScheduler::SetChReportInterval(int nTotalNI, float fChReportIntervalSec)
{
    m_fChReportIntervalSec = fChReportIntervalSec;
    if(m_fChReportIntervalSec > 0)
    {
        m_fChReportRate = 60.0f / fChReportIntervalSec;
        m_fChReportRateHalf = m_fChReportRate / 2;
        m_nNI = CAisLib::GetNIfromRR(m_fChReportRate);

        m_nSizeHalfSI = Round(nTotalNI * 0.1f);
        m_nSizeSI = (m_nSizeHalfSI << 1);
    }
    else
    {
        m_fChReportRate = 0;
        m_fChReportRateHalf = 0;
        m_nSizeSI = 0;
        m_nSizeHalfSI = 0;
        m_nNI = 0;
    }

    m_nNSS = m_nNS = m_nNextStartSI = SLOTID_NONE;

    m_dwRoutineAllocLastCheckSec= cTimerSys::getInst()->GetCurTimerSec()+m_fChReportIntervalSec;
    m_wRoutineAllocLastCheckSlot= OPSTATUS::nCurFrameMapSlotID;

    DEBUG_LOG("SetChReportInterval] totalNI:%d, chRI:%.1f, chRR : %.1f, chNI : %d, chSizeSI : %d, chHalfSI: %d, %d, %d\r\n",
            nTotalNI, m_fChReportIntervalSec, m_fChReportRate, m_nNI, m_nSizeSI, m_nSizeHalfSI, Round(nTotalNI * 0.1f), (m_nSizeHalfSI << 1));
}

/**
 * @brief Set the new report interval for SO
 * @param fReportIntervalSec New report interval in seconds
 */
void CChTxScheduler::SetChNewReportIntervalSec(float fReportIntervalSec)
{
    m_fNewReportIntervalSecSO = fReportIntervalSec;
}

/**
 * @brief Set the static report interval in seconds
 * @param dwStaticIntervalSec Static report interval in seconds
 */
void CChTxScheduler::SetStaticReportIntervalSec(DWORD dwStaticIntervalSec)
{
    m_dwStaticReportIntervalSec = dwStaticIntervalSec;
}

/**
 * @brief Initialize the static report next second
 * @param dwSec Next second to transmit static report
 */
void CChTxScheduler::InitStaticReportSecCh(DWORD dwSec)
{
    m_dwStaticReportNextSec = dwSec;
}

/**
 * @brief Set the long range report interval in seconds
 * @param uLongRangeIntervalSec Long range report interval in seconds
 */
void CChTxScheduler::SetLongRangeReportInterval(UINT16 uLongRangeIntervalSec)
{
    m_dwLongRangeReportIntervalSec = uLongRangeIntervalSec;
}

/**
 * @brief Initialize the long range report next second
 * @param dwSec Next second to transmit long range report
 */
void CChTxScheduler::InitLongRangeReportSecCh(DWORD dwSec)
{
    m_dwLongRangeTxNextSec = dwSec;
}

/**
 * @brief Set the nominal start slot
 * @param uSlotID Nominal start slot
 */
void CChTxScheduler::SetNSS(INT16 uSlotID)
{
    m_nNSS = uSlotID;
    m_nNS  = uSlotID;
}

/**
 * @brief Get the nominal start slot
 * @return Nominal start slot
 */
INT16 CChTxScheduler::GetNSS(void)
{
    return m_nNSS;
}

/**
 * @brief Increment the nominal slot
 */
void CChTxScheduler::IncNS(void)
{
    m_nNS = CAisLib::FrameMapSlotIdAdd(m_nNS, m_nNI);
}

/**
 * @brief Reset the next start slot
 */
void CChTxScheduler::ResetNextStartSI(void)
{
    m_nNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNSS, m_nNI);
    m_nNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, -m_nSizeHalfSI);
}

/**
 * @brief Increment the next start slot
 */
void CChTxScheduler::IncNextStartSI(void)
{
    m_nNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, m_nNI);
}

/**
 * @brief Decrement the next start slot
 */
void CChTxScheduler::DecNextStartSI(void)
{
    m_nNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, -m_nNI);
}

/**
 * @brief Check if the report rate is valid
 * @return True if the report rate is valid, false otherwise
 */
bool CChTxScheduler::CheckRRvalid(void)
{
    return (m_nNI > 0 && m_nSizeSI > 0 && m_nNextStartSI != SLOTID_NONE);
}

/**
 * @brief Set the position message slot column for routine allocation
 * @param nSlotID Slot ID
 * @param nSlotOffset Slot offset
 * @param nPosMsgID Position message ID
 * @param nSlotTimeOut Slot time-out
 */
void CChTxScheduler::SetPosMsgSlotColumnRA(int nSlotID, int nSlotOffset, int nPosMsgID, int nSlotTimeOut)
{
    const int nNumSlot = 1;

    int nRandTmo = nSlotTimeOut;
    if(nRandTmo < TMO_MIN)
        nRandTmo = CAisLib::GetRandomSlotTimeOut();

    m_pChannel->SetFrameMapOneMsg(true, nSlotID, nNumSlot, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nRandTmo,                    // time-out 은 SOTDMA 에서 시작하기 위해 이 프레임에서는 time-out 에 1을 더해준다
                                        POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, nSlotOffset, true, false, 0, false);

    m_pChannel->SetFrameMapOneMsgColumn(true, CAisLib::FrameMapSlotIdAdd(nSlotID, NUM_SLOT_PER_FRAME), nNumSlot,
    										cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, nRandTmo-1,
                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, nPosMsgID, 0, false, false, 0, false);
}

/**
 * @brief Set the position slot column for SO
 * @param nTxSlotID Slot ID
 * @param nPosMsgID Position message ID
 * @param nCurStartSI Current start slot
 * @param nSizeSI Size of the slot
 * @param bCheckTmoZero Check if the time-out is zero
 * @return Next slot ID
 */
int CChTxScheduler::SetPosSlotColumnCheckTmoSO(int nTxSlotID, int nPosMsgID, int nCurStartSI, int nSizeSI, bool bCheckTmoZero)
{
    //-------------------------------------------------------------------------------------------------------------------------
    // if time-out of wTxSlotID is zero, allocate a new slot of wTxSlotID on the next frame and set slot offset of wTxSlotID
    // otherwise, just set slot column and return, slot time-out remains unchanged.
    //
    // nTxSlotID : slot id of which time-out is zero
    //-------------------------------------------------------------------------------------------------------------------------
    const int nNumSlot = 1;
    FRAMEMAP_SLOTDATA *pSlotInfo = NULL;

    if(nTxSlotID == SLOTID_NONE || !(pSlotInfo = m_pChannel->GetSlotDataPtr(nTxSlotID)))
    {
        WARNING_LOG("SetPosSlotColumnCheckTmoSO] return-1, curSlot: %d(%d), SI : %d, sizeSI : %d, posMsg: %d, checkTmoZero: %d\r\n",
            nTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(nTxSlotID), nCurStartSI, nSizeSI, nPosMsgID, bCheckTmoZero);
        return SLOTID_NONE;
    }

    DEBUG_LOG("SetPosSlotColumnCheckTmoSO] begin, curSlot: %d(%d)(tmo:%d), SI : %d, sizeSI : %d, posMsg: %d, checkTmoZero: %d\r\n",
            nTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(nTxSlotID), pSlotInfo->nSlotTimeOut, nCurStartSI, nSizeSI, nPosMsgID, bCheckTmoZero);

    if(!bCheckTmoZero || pSlotInfo->nSlotTimeOut != 0)
    {
        m_pChannel->SetFrameMapOneMsgColumn(true, nTxSlotID, nNumSlot, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, pSlotInfo->nSlotTimeOut,
                                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, nPosMsgID, 0, false, false, 0, false);

        WARNING_LOG("SetPosSlotColumnCheckTmoSO] return-2, curSlot: %d(%d)(tmo:%d), SI : %d, sizeSI : %d, posMsg: %d, checkTmoZero: %d\r\n",
            nTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(nTxSlotID), pSlotInfo->nSlotTimeOut, nCurStartSI, nSizeSI, nPosMsgID, bCheckTmoZero);
        return SLOTID_NONE;
    }

    int nRandTmo = CAisLib::GetRandomSlotTimeOut();
    UINT uSlotOffset = 0;

    int nNextStartSI = nCurStartSI;
    nNextStartSI = CAisLib::FrameMapSlotIdAdd(nCurStartSI, NUM_SLOT_PER_FRAME);
    int nNextFrameTxSlot = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_SO(m_pChannel, nPosMsgID, nNextStartSI, nSizeSI, nNumSlot, SLOTID_NONE);


    if(nNextFrameTxSlot != SLOTID_NONE)
    {
        uSlotOffset = CAisLib::FrameMapGetDiffSlotID(nTxSlotID, nNextFrameTxSlot);
        if(uSlotOffset == NUM_SLOT_PER_FRAME)
        {
            int nSizeTmp = CAisLib::FrameMapGetDiffSlotID(nNextStartSI, nNextFrameTxSlot);
            int nSizeTmp1 = CAisLib::FrameMapGetDiffSlotID(nNextFrameTxSlot, nNextStartSI);
            if(nSizeTmp > nSizeTmp1)
                nSizeTmp = nSizeTmp1;
            ++nSizeTmp;

            int nNewSizeSI = nSizeSI - nSizeTmp;
            int nNewNextStartSI = CAisLib::FrameMapSlotIdAdd(nNextFrameTxSlot, 1);

            int nNewNextFrameTxSlot = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_SO(m_pChannel, nPosMsgID, nNewNextStartSI, nNewSizeSI, 1, nTxSlotID);

            DEBUG_LOG("SetPosSlotColumnCheckTmoSO] next offset is 2250, retry! alloc : %d(%d) -> %d(%d), SI : %d -> %d, size : %d -> %d\r\n",
                    nNextFrameTxSlot, m_pChannel->GetSlotIdFromFrameSlotID(nNextFrameTxSlot),
                    nNewNextFrameTxSlot, m_pChannel->GetSlotIdFromFrameSlotID(nNewNextFrameTxSlot), nNextStartSI, nNewNextStartSI, nSizeSI, nNewSizeSI);

            if (nNewNextFrameTxSlot != SLOTID_NONE)
                nNextFrameTxSlot = nNewNextFrameTxSlot;
        }
    }

    if(nNextFrameTxSlot == SLOTID_NONE)
    {
        //------------------------------------------------------------------------------
        // refer to ITU-R M.1371-5 ed.2014 Appndix2 3.3.1.2 Candidate slots
        // When no candidate slot is available, the use of the current slot is allowed.
        // refer to Figure 9
        //------------------------------------------------------------------------------
        nNextFrameTxSlot = CAisLib::FrameMapSlotIdAdd(nTxSlotID, NUM_SLOT_PER_FRAME);
        if(CSlotMgr::getInst()->CheckFrameMapSlotAvail(m_pChannel, nNextFrameTxSlot))
        {
            DEBUG_LOG("ChRoutine] Continue to use current slot! C : %d, TxSlot : %d, nextFrameTxSlot : %d\r\n",
                cAisModem::getInst()->GetSlotNoCounter(), nTxSlotID, nNextFrameTxSlot);
        }
        else
        {
            nNextFrameTxSlot = SLOTID_NONE;
            DEBUG_LOG("ChRoutine] Can't find SO candi! C : %d, TxSlot : %d, nextFrameTxSlot : %d\r\n",
                cAisModem::getInst()->GetSlotNoCounter(), nTxSlotID, nNextFrameTxSlot);
        }
    }

    if(nNextFrameTxSlot == SLOTID_NONE)
    {
        uSlotOffset = 0;

        DEBUG_LOG("SetPosSlotColumnCheckTmoSO] Alloc error %d, SI : %d(%d), size : %d\r\n",
                nNextFrameTxSlot, nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(nNextStartSI), nSizeSI);
    }
    else
    {
        m_pChannel->SetFrameMapOneMsgColumn(true, nNextFrameTxSlot, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,    // Allocate a new slot in the next frame
                                                nRandTmo, POS_REPORT_SCHEDULED, TDMA_SOTDMA, nPosMsgID, 0, false, false, 0, false);

        uSlotOffset = CAisLib::FrameMapGetDiffSlotID(nTxSlotID, nNextFrameTxSlot);
    }

    pSlotInfo->uSlotOffset = uSlotOffset;                                                                                // update offset value of the next slot in the current frame
    return nNextFrameTxSlot;
}

/**
 * @brief Allocate a slot for urgency message
 * @param nMsgID Message ID
 * @param nStartSI Start slot
 * @param nSizeSI Size of the slot
 * @return Allocated slot ID
 */
int CChTxScheduler::UrgencyAllocSO(int nMsgID, const int nStartSI, const int nSizeSI)
{
    int nAllocSlotSO = SLOTID_NONE;
    int nSoStartSI= SLOTID_NONE;
    int nSoSizeSI = 0;
    UINT uSlotOffset = 0;

    int nAllocSlotRA = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nStartSI, nSizeSI, 1);
    if(nAllocSlotRA != SLOTID_NONE)
    {
        nSoStartSI= CAisLib::FrameMapSlotIdAdd(nAllocSlotRA, 1);
        nSoSizeSI = nSizeSI - CAisLib::FrameMapGetDiffSlotID(nStartSI, nSoStartSI);
        if(nSoSizeSI > 0)
        {
            nAllocSlotSO = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nSoStartSI, nSoSizeSI, 1);
            if(nAllocSlotSO != SLOTID_NONE)
            {
                uSlotOffset = CAisLib::FrameMapGetDiffSlotID(nAllocSlotRA, nAllocSlotSO);
                m_pChannel->SetFrameMapOneMsg(true, nAllocSlotRA, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, 0,
                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, uSlotOffset, false, false, 0, false);

                SetPosMsgSlotColumnRA(nAllocSlotSO, 0, nMsgID, (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? TMO_UNKNOWN : CLayerNetwork::getInst()->m_nNewFrameTimeOut));
            }
        }
    }

    DEBUG_LOG("UrgencySO] op:%d,%d, SI : %d(%d), sizeSI: %d, SoSI:%d, SoSizeSI: %d, SlotRA : %d(%d),off:%d, SlotSO : %d(%d),m:%d, s:%d\r\n",
            m_pChannel->GetChOpMode(), m_pChannel->GetChOpPhase(), nStartSI, m_pChannel->GetSlotIdFromFrameSlotID(nStartSI), nSizeSI, nSoStartSI, nSoSizeSI,
            nAllocSlotRA, m_pChannel->GetSlotIdFromFrameSlotID(nAllocSlotRA), uSlotOffset,
            nAllocSlotSO, m_pChannel->GetSlotIdFromFrameSlotID(nAllocSlotSO), nMsgID, cTimerSys::getInst()->GetCurTimerSec());

    return nAllocSlotSO;
}

/**
 * @brief Process the network entry phase
 * @param nEntrySlot Entry slot
 * @param nNSS NSS
 * @return True if the network entry phase is complete, false otherwise
 */
bool CChTxScheduler::ProcessPhaseNetworkEntry(int nEntrySlot, int nNSS)
{
    if(!m_pChannel->IsTxAvailableCh())
    {
        WARNING_LOG("ChNetworkEntry] CH not available!");
        return true;
    }

    const BYTE NUM_SLOT = 1;
    const INT8 nNewPosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    const bool bRunChgOpMode = CLayerNetwork::getInst()->m_nNetworkEntryPrevPosMsg != nNewPosMsgID;

    SetNSS(nNSS);
    ResetNextStartSI();

    int nCurSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, -m_nNI);

    m_nFirstFrameTxCnt    = 0;

    if(!CReportRateMgr::getInst()->IsReportRateForSOTDMA())
    {
        DEBUG_LOG("ChNetworkEntry] ITDMA, NSS : %d, NS : %d, NI : %d, sizeSI : %d, SI : %d\r\n",
                m_nNSS, m_nNS, m_nNI, m_nSizeSI, nCurSI);
        return true;
    }

    if(!CheckRRvalid())
        return true;

    DEBUG_LOG("ChNetworkEntry] before, entryTX : %d, NSS : %d(%d), SI : %d(%d), sizeSI:%d, nextSI: %d(%d)\r\n",
            nEntrySlot, m_nNSS, m_pChannel->GetSlotIdFromFrameSlotID(m_nNSS),
            nCurSI, m_pChannel->GetSlotIdFromFrameSlotID(nCurSI), m_nSizeSI, m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI));

    WORD wNextTxSlotID = SLOTID_NONE;
    UINT uSlotOffset = 0;

    if(!bRunChgOpMode)
        wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nCurSI, m_nSizeSI);

    if(wNextTxSlotID == SLOTID_NONE)
    {
        wNextTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_SO(m_pChannel, AIS_MSG_NO_ID_03, nCurSI, m_nSizeSI, NUM_SLOT, SLOTID_NONE);
        if(wNextTxSlotID == SLOTID_NONE)
        {
            WARNING_LOG("ChNetworkEntry] Alloc Error! Tx : %d, M:%d, NS : %d, StartSI : %d\r\n",
                    m_nFirstFrameTxSlotId, m_nRoutinePosMsgID, m_nNS, nCurSI);
            return false;
        }

        DEBUG_LOG("ChNetworkEntry] GetNextTX new, entryTX : %d(%d), nextTX : %d(%d), TMO : %d\r\n",
                nEntrySlot, m_pChannel->GetSlotIdFromFrameSlotID(nEntrySlot),
                wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
                CLayerNetwork::getInst()->m_nNewFrameTimeOut);

    #ifdef __RANDOM_SLOT_TMO__
        m_pChannel->SetFrameMapOneMsg(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                    (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? ITDMA_ALLOCSLOT_TMO : CLayerNetwork::getInst()->m_nNewFrameTimeOut),
                                    POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, 0, false, false, 0, false);
    #else
        m_pChannel->SetFrameMapOneMsgColumn(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, CLayerNetwork::getInst()->m_nNewFrameTimeOut,
                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, 0, false, false, 0, false);
    #endif
    }
    else
    {
        DEBUG_LOG("ChNetworkEntry] GetNextTX existing, entryTX : %d(%d), nextTX : %d(%d), MSG : %d, TMO : %d\r\n",
                nEntrySlot, m_pChannel->GetSlotIdFromFrameSlotID(nEntrySlot),
                wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
                CLayerNetwork::getInst()->m_nNetworkEntryPrevPosMsg, CLayerNetwork::getInst()->m_nNewFrameTimeOut);

        FRAMEMAP_SLOTDATA *pNextSlot = m_pChannel->GetSlotDataPtr(wNextTxSlotID);
        m_pChannel->SetFrameMapOneMsg(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                    (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? pNextSlot->nSlotTimeOut : CLayerNetwork::getInst()->m_nNewFrameTimeOut),
                                    POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
    }

    uSlotOffset = CAisLib::FrameMapGetDiffSlotID(nEntrySlot, wNextTxSlotID);

    m_pChannel->SetFrameMapOneMsg(true, nEntrySlot, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? ITDMA_ALLOCSLOT_TMO : CLayerNetwork::getInst()->m_nNewFrameTimeOut),
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, uSlotOffset, false, false, 0, false);

    if(CLayerNetwork::getInst()->m_bNetEntryFromExisting)
        m_pChannel->SetLastTxSlotWithRange(nEntrySlot, wNextTxSlotID, true);

    DEBUG_LOG("ChNetworkEntry] Set entry slot, existing : %d, entryTX : %d(%d), nextTX : %d(%d), off : %d, TMO : %d\r\n",
            CLayerNetwork::getInst()->m_bNetEntryFromExisting, nEntrySlot, m_pChannel->GetSlotIdFromFrameSlotID(nEntrySlot),
            wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
            uSlotOffset, CLayerNetwork::getInst()->m_nNewFrameTimeOut);

    m_nFirstFrameTxSlotId = wNextTxSlotID;

    IncNS();

    bool bRet = (m_nFirstFrameTxSlotId != SLOTID_NONE);

    FRAMEMAP_SLOTDATA *pSlotCur = m_pChannel->GetSlotDataPtr(nEntrySlot);
    if(bRet)
    {
        DEBUG_LOG("ChNetworkEntry] end, OK, entryTX : %d(%d) m:%d, numSlot: %d, nextTX : %d(%d), m:%d, NSS : %d, NS : %d, SI : %d, nextSI: %d\r\n",
                nEntrySlot, m_pChannel->GetSlotIdFromFrameSlotID(nEntrySlot), pSlotCur ? pSlotCur->uMsgID : -1, pSlotCur->uNumSlots,
                m_nFirstFrameTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nFirstFrameTxSlotId),
                m_nRoutinePosMsgID, m_nNSS, m_nNS, nCurSI, m_nNextStartSI);
    }
    else
    {
        WARNING_LOG("ChNetworkEntry] end, FAIL! entryTX : %d(%d) m:%d, nextTX : %d(%d), NSS : %d, NS : %d, SI : %d, nextSI: %d\r\n",
                nEntrySlot, m_pChannel->GetSlotIdFromFrameSlotID(nEntrySlot), pSlotCur ? pSlotCur->uMsgID : -1,
                m_nFirstFrameTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nFirstFrameTxSlotId),
                m_nRoutinePosMsgID, m_nNSS, m_nNS, nCurSI, m_nNextStartSI);
    }

    return bRet;
}

/**
 * @brief Process the first frame phase
 */
void CChTxScheduler::ProcessPhaseFirstFrame(void)
{
    const BYTE NUM_SLOT = 1;
    const INT8 nNewPosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    const bool bRunChgOpMode = CLayerNetwork::getInst()->m_nNetworkEntryPrevPosMsg != nNewPosMsgID;

    WORD wNextTxSlotID = SLOTID_NONE;
    WORD uSlotOffset = 0;
    bool bLastTxOfFirstFramePhase = false;
    bool bTxItdma = false;
    bool bIncNext = false;

    if(!m_pChannel->IsTxAvailableCh())
        return;

    if(CReportRateMgr::getInst()->IsReportRateForSOTDMA())
    {
        if(!CheckRRvalid())
            return;

        if(m_nFirstFrameTxCnt < m_fChReportRate)
        {
            const int DIFF_SLOT_LIMIT_MAX = TX_CHECK_SLOT_SPACE+m_nSizeSI;
            int nCurSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, -m_nNI);
            int nCheckEnd = CAisLib::FrameMapSlotIdAdd(nCurSI, m_nSizeSI-1);
            int nDiffSlot = CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, nCheckEnd);

            if(nDiffSlot <= DIFF_SLOT_LIMIT_MAX)
            {
                bLastTxOfFirstFramePhase = (m_nFirstFrameTxCnt >= m_fChReportRate-1);

                if(m_nFirstFrameTxSlotId == SLOTID_NONE)
                {
                    m_nFirstFrameTxSlotId = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, m_nNextStartSI, (m_nFirstFrameTxCnt == 0 ? RATDMA_SI : m_nSizeSI), 1);
                }

                if(m_nFirstFrameTxSlotId != SLOTID_NONE)
                {
                    FRAMEMAP_SLOTDATA *pSlotCur = m_pChannel->GetSlotDataPtr(m_nFirstFrameTxSlotId);
                    if(bLastTxOfFirstFramePhase)
                    {
                        //-------------------------------------
                        // first frame 의 마지막 송신 처리
                        //-------------------------------------
                        if(pSlotCur->uMsgID == AIS_MSG_NO_ID_03)
                        {
                            uSlotOffset = 0;
                            bTxItdma = true;
                        }
                        else
                        {
                            SetPosSlotColumnCheckTmoSO(m_nFirstFrameTxSlotId, nNewPosMsgID, m_nNextStartSI, m_nSizeSI, true);
                        }
                    }
                    else
                    {
                        //-------------------------------------
                        // first frame 의 마지막 송신이 아닐때
                        //-------------------------------------

                        if(!bRunChgOpMode)
                        {
                            wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(m_nNextStartSI, m_nSizeSI);
                        }

                        if(wNextTxSlotID == SLOTID_NONE)
                        {
                            wNextTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_SO(m_pChannel, AIS_MSG_NO_ID_03, m_nNextStartSI, m_nSizeSI, NUM_SLOT, SLOTID_NONE);    // 그 다음 송신할 슬롯을 SOTDMA 로 할당받는다.
                            if(wNextTxSlotID == SLOTID_NONE)
                            {
                                WARNING_LOG("ChFirstFrame] GetNext, Alloc Error! SI:%d \r\n", m_nNextStartSI);
                            }
                            else
                            {
                                m_pChannel->SetFrameMapOneMsg(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                                    (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? ITDMA_ALLOCSLOT_TMO : CLayerNetwork::getInst()->m_nNewFrameTimeOut),
                                                                    POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, uSlotOffset, false, false, 0, false);

                                DEBUG_LOG("ChFirstFrame] GetNext, new, SetCurTx-IT, curTX : %d(%d),m:%d,tmo:%d nextTX : %d(%d),m:%d,  NextStartSI : %d\r\n",
                                        m_nFirstFrameTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nFirstFrameTxSlotId), pSlotCur->uMsgID, pSlotCur->nSlotTimeOut,
                                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), AIS_MSG_NO_ID_03, m_nNextStartSI);

                                uSlotOffset = CAisLib::FrameMapGetDiffSlotID(m_nFirstFrameTxSlotId, wNextTxSlotID);                                // 슬롯 오프셋은 다음 송신슬롯까지의 오프셋
                                bTxItdma = true;
                            }
                        }
                        else
                        {
                            FRAMEMAP_SLOTDATA *pSlotNextTx = m_pChannel->GetSlotDataPtr(wNextTxSlotID);

                            DEBUG_LOG("ChFirstFrame] GetNext, Existing, SetCurTx-SO, curTX : %d(%d),m:%d, nextTX : %d(%d),m:%d, posMsg:%d, NextStartSI : %d, entryExisting:%d\r\n",
                                    m_nFirstFrameTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nFirstFrameTxSlotId), pSlotCur ? pSlotCur->uMsgID : -1,
                                    wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), pSlotNextTx ? pSlotNextTx->uMsgID : -1, nNewPosMsgID, m_nNextStartSI,
                                    CLayerNetwork::getInst()->m_bNetEntryFromExisting);

                            m_pChannel->SetFrameMapOneMsg(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                                (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? pSlotNextTx->nSlotTimeOut : CLayerNetwork::getInst()->m_nNewFrameTimeOut),
                                                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, 0, false, false, 0, false);

                            int nCurSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, -m_nNI);
                            SetPosSlotColumnCheckTmoSO(m_nFirstFrameTxSlotId, nNewPosMsgID, nCurSI, m_nSizeSI, true);
                        }

                        if(CLayerNetwork::getInst()->m_bNetEntryFromExisting)
                            m_pChannel->SetLastTxSlotWithRange(m_nFirstFrameTxSlotId, wNextTxSlotID, true);
                    }

                    if(bTxItdma)
                    {
                        SetPosMsgSlotColumnRA(m_nFirstFrameTxSlotId, uSlotOffset, nNewPosMsgID, pSlotCur->nSlotTimeOut);

                        DEBUG_LOG("ChFirstFrame] SetCurTx-IT, TxCnt : %d, Tx : %d(%d),m:%d,tmo:%d, Next : %d, Off : %d, TMO : %d\r\n",
                                m_nFirstFrameTxCnt, 
                                m_nFirstFrameTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nFirstFrameTxSlotId), pSlotCur->uMsgID, pSlotCur->nSlotTimeOut,
                                wNextTxSlotID, uSlotOffset, CLayerNetwork::getInst()->m_nNewFrameTimeOut);
                    }

                    if(m_nFirstFrameTxSlotId != SLOTID_NONE)
                    {
                        CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
                    }

                    m_nFirstFrameTxCnt++;
                    m_nFirstFrameTxSlotId = wNextTxSlotID;

                    if(!bLastTxOfFirstFramePhase)
                    {
                        IncNS();
                        IncNextStartSI();
                    }

                    bIncNext = true;

                    FRAMEMAP_SLOTDATA *pSlotNextTx = m_pChannel->GetSlotDataPtr(m_nFirstFrameTxSlotId);
                    DEBUG_LOG("ChFirstFrame] end, last:%d, txCnt : %d, nextTX:%d(%d),m:%d, nextNS:%d, nextSI:%d, sizeSI:%d, s:%d\r\n",
                            bLastTxOfFirstFramePhase, m_nFirstFrameTxCnt, 
                            m_nFirstFrameTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nFirstFrameTxSlotId), pSlotNextTx ? pSlotNextTx->uMsgID : -1,
                            m_nNS, m_nNextStartSI, m_nSizeSI, cTimerSys::getInst()->GetCurTimerSec());

                    if(bLastTxOfFirstFramePhase)
                    {
                        m_pChannel->SetChOpPhase(OPPHASE_ROUTINE);

                        DEBUG_LOG("ChFirstFrame] LastSlot, S:%d, m:%d, cnt:%d\r\n", m_nFirstFrameTxSlotId, pSlotCur->uMsgID, m_nFirstFrameTxCnt);
                    }
                }

                if(!bIncNext)
                {
                    m_nFirstFrameTxCnt++;
                    m_nFirstFrameTxSlotId = wNextTxSlotID;

                    if(bLastTxOfFirstFramePhase)
                    {
                        m_pChannel->SetChOpPhase(OPPHASE_ROUTINE);

                        DEBUG_LOG("ChFirstFrame] exception, goto Routine, SI:%d, cnt:%d\r\n", m_nFirstFrameTxSlotId, m_nNextStartSI, m_nFirstFrameTxCnt);
                    }
                    else
                    {
                        IncNS();
                        IncNextStartSI();
                    }
                    bIncNext = true;
                }
            }
            else if(nDiffSlot >= (NUM_SLOT_PER_FRAME + TX_CHECK_SLOT_SPACE + m_nSizeSI) && m_nFirstFrameTxCnt > 0)
            {
                DEBUG_LOG("ChFirstFrame] DelayedTiming, cnt: %d, diff:%d, chkEnd:%d(%d), nextSI:%d(%d), sizeSI:%d, NS:%d, NI:%d, lastTxSlot : %d, %d\r\n",
                        m_nFirstFrameTxCnt, nDiffSlot, 
                        nCheckEnd, m_pChannel->GetSlotIdFromFrameSlotID(nCheckEnd),
                        m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
                        m_nSizeSI, m_nNS, m_nNI, m_nRoutineNextTxSlot, cTimerSys::getInst()->GetCurTimerSec());

                // Next Start SI를 계속 증가시키면 MSG2번이 반복적으로 생성되는 문제로 인해 막음.
                //IncNS();
                //IncNextStartSI();
            }
        }
    }
}

/**
 * @brief Process the routine phase
 */
void CChTxScheduler::ProcessPhaseRoutine(void)
{
    if(m_pChannel->IsTxAvailableCh() && CheckRRvalid())
    {
        if(CReportRateMgr::getInst()->IsReportRateForSOTDMA())
        {
            ProcessPhaseRoutine_SO();
        }
        else
        {
            ProcessPhaseRoutine_IT();
        }
    }
}

/**
 * @brief Process the routine phase for SO
 */
void CChTxScheduler::ProcessPhaseRoutine_SO(void)
{
    FRAMEMAP_SLOTDATA *pSlotCur = 0;
    int    nNextFrameTxSlot = 0;
    UINT16 uSlotOffset = 0;
    WORD wTxSlotID = 0;
    bool bIncNext = false;
    WORD wTempItdmaSlot = SLOTID_NONE;

    const int DIFF_SLOT_LIMIT_MAX = TX_CHECK_SLOT_SPACE+m_nSizeSI;
    int nCheckEnd = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, m_nSizeSI-1);
    int nDiffSlot = CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, nCheckEnd);

    if(nDiffSlot <= DIFF_SLOT_LIMIT_MAX)
    {
        bool bFinishRRmode = false;
        int nRRElapMs = 0;

        // Check if assigned RR mode is finished
        if(CLayerNetwork::getInst()->m_nOpMode == OPMODE_ASSIGNED_RR)
        {
            nRRElapMs = SysGetDiffTimeMili(m_dwRRTimeOutStartTick);
            bFinishRRmode = nRRElapMs >= m_dwRRTimeOutMS;
        }

        // Get preallocated SO slot with same message id
        wTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(m_nNextStartSI, m_nSizeSI, m_nRoutinePosMsgID, false, true);

        DEBUG_LOG("ChRoutine] diff:%d, chkEnd:%d, C:%d, Tx:%d(%d), urgS:%d(%d), NS:%d(%d), NI:%d, startSI:%d(%d), sizeSI:%d, elap:%d,%d, TT-%d:%d:%d, RRtmo: %d, FinishRR:%d, %d,%d(%d), %d\r\n",
                nDiffSlot, nCheckEnd, cAisModem::getInst()->GetSlotNoCounter(),
                wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                m_nUrgencyTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(m_nUrgencyTxSlotID),
                m_nNS, m_pChannel->GetSlotIdFromFrameSlotID(m_nNS), m_nNI, m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
                m_nSizeSI, m_dwRoutineAllocLastCheckSec, cTimerSys::getInst()->GetTimeDiffSec(m_dwRoutineAllocLastCheckSec),
                cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                m_nRRChgTimeOutMin, bFinishRRmode, m_dwRRTimeOutMS, nRRElapMs, m_nNextStartSI,
                cTimerSys::getInst()->GetCurTimerSec());

        if(wTxSlotID == SLOTID_NONE)
            wTxSlotID = m_nUrgencyTxSlotID;
        else
            m_nUrgencyTxSlotID = SLOTID_NONE;

        if(wTxSlotID == SLOTID_NONE)
        {
            FRAMEMAP_SLOTDATA *pSlotTmp = NULL;

            // Check if SOTDMA slot if temporarily used as ITDMA slot
            wTempItdmaSlot = m_pChannel->GetInternalAllocatedSlot_SO(m_nNextStartSI, m_nSizeSI, AIS_MSG_NO_ID_03);
            if (    (wTempItdmaSlot == SLOTID_NONE)
                || !(pSlotTmp = m_pChannel->GetSlotDataPtr(wTempItdmaSlot)) 
                || !pSlotTmp->bItdmaKeepFlag)
            {
                //---------------------------
                // Start Urgency allocation
                //---------------------------
                int nUrgencySI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, NUM_TXSLOT_AHEAD);
                int nSizeSI = m_nSizeSI - NUM_TXSLOT_AHEAD;
                if(nSizeSI > 0)
                {
                    wTxSlotID = UrgencyAllocSO(AIS_MSG_NO_ID_03, nUrgencySI, nSizeSI);       // similar to first frame phase
                    m_nUrgencyTxSlotID = wTxSlotID;                                          // Start of urgency alloc
                }
            }
            else
            {
                wTxSlotID = wTempItdmaSlot;
            }
        }

        m_nRoutineNextTxSlot = (wTxSlotID == SLOTID_NONE ? m_nNextStartSI : wTxSlotID);

        if(wTxSlotID != SLOTID_NONE && (pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID)))
        {
            m_dwRoutineAllocLastCheckSec = cTimerSys::getInst()->GetCurTimerSec();
            m_wRoutineAllocLastCheckSlot = OPSTATUS::nCurFrameMapSlotID;

            DEBUG_LOG("ChRoutine] check %s, msg:%d, C : %d, Tx : %d(%d),%d, NI:%d, NS : %d(%d), startSI : %d(%d), sizeSI : %d, TMO : %d, NS : %d, %d\r\n",
                    m_nUrgencyTxSlotID == SLOTID_NONE ? "Normal" : "Urgency",
                    m_nRoutinePosMsgID, cAisModem::getInst()->GetSlotNoCounter(),
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), m_pChannel->IsIntTxReservedSlot(pSlotCur),
                    m_nNI, m_nNS, m_pChannel->GetSlotIdFromFrameSlotID(m_nNS), m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
                    m_nSizeSI, pSlotCur->nSlotTimeOut, m_nNS, m_dwRoutineAllocLastCheckSec);

            if(m_nUrgencyTxSlotID != SLOTID_NONE)
            {
                //--------------------------------------------
                // Urgency allocation, exception handling
                //--------------------------------------------
                const int NUM_SLOT = 1;
                bool bTxItdma = false;
                WORD wNextTxSlotID = SLOTID_NONE;
                int nNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, m_nNI);

                uSlotOffset = 0;

                // 그 다음 SI 내에서 기존 SOTDMA 예약된 슬롯을 찾는다
                wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nNextStartSI, m_nSizeSI);
                if(wNextTxSlotID == SLOTID_NONE)
                {
                    wNextTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_SO(m_pChannel, m_nRoutinePosMsgID, nNextStartSI, m_nSizeSI, NUM_SLOT, SLOTID_NONE);    // 그 다음 송신할 슬롯을 SOTDMA 로 할당받는다.
                    if(wNextTxSlotID == SLOTID_NONE)
                    {
                        WARNING_LOG("ChRoutine] Urgency,GetNext, Alloc Error! SI:%d \r\n", nNextStartSI);
                    }
                    else
                    {
                        SetPosMsgSlotColumnRA(wNextTxSlotID, 0, m_nRoutinePosMsgID, (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? TMO_UNKNOWN : CLayerNetwork::getInst()->m_nNewFrameTimeOut));

                        DEBUG_LOG("ChRoutine] Urgency,GetNext, new, SetCurTx-IT, curTX : %d(%d),m:%d,tmo:%d nextTX : %d(%d),m:%d,  NextStartSI : %d\r\n",
                                wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), pSlotCur->uMsgID, pSlotCur->nSlotTimeOut,
                                wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), AIS_MSG_NO_ID_03, nNextStartSI);

                        uSlotOffset = CAisLib::FrameMapGetDiffSlotID(wTxSlotID, wNextTxSlotID);
                        bTxItdma = true;
                    }
                    m_nUrgencyTxSlotID = wNextTxSlotID;     // Continue or End of urgency alloc
                }
                else
                {
                    m_nUrgencyTxSlotID = SLOTID_NONE;       // End of urgency alloc

                    FRAMEMAP_SLOTDATA *pSlotNextTx = m_pChannel->GetSlotDataPtr(wNextTxSlotID);
                    DEBUG_LOG("ChRoutine] Urgency,GetNext, Existing, SetCurTx-SO, curTX : %d(%d),m:%d, nextTX : %d(%d),m:%d, posMsg:%d, NextStartSI : %d, entryExisting:%d\r\n",
                            wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), pSlotCur ? pSlotCur->uMsgID : -1,
                            wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), pSlotNextTx ? pSlotNextTx->uMsgID : -1, m_nRoutinePosMsgID, nNextStartSI,
                            CLayerNetwork::getInst()->m_bNetEntryFromExisting);
                }

                SetPosMsgSlotColumnRA(wTxSlotID, uSlotOffset, m_nRoutinePosMsgID, pSlotCur->nSlotTimeOut);

                DEBUG_LOG("ChRoutine] Urgency,SetCurTx-IT, Tx : %d(%d),m:%d,tmo:%d, Next : %d(%d), Off : %d, %d\r\n",
                        wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                        pSlotCur->uMsgID, pSlotCur->nSlotTimeOut,
                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), uSlotOffset, CLayerNetwork::getInst()->m_nNewFrameTimeOut);
            }
            else if(wTxSlotID != wTempItdmaSlot)
            {
                if(CLayerNetwork::getInst()->m_nOpMode == OPMODE_ASSIGNED_RR)
                {
                    if(bFinishRRmode)
                    {
                        DEBUG_LOG("ChRoutine] AssignedRR] Terminate, S:%d(%d), RRtmo: %d, FinishRR:%d, %d,%d, s: %d\r\n",
                                wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), cTimerSys::getInst()->GetCurTimerSec());
                                m_nRRChgTimeOutMin, bFinishRRmode, m_dwRRTimeOutMS, nRRElapMs,
                        CLayerNetwork::getInst()->RunReturnToAutoModeFromAssignedMode_RR();            // 할당모드 종료
                        return;
                    }
                }

                if(pSlotCur->nSlotTimeOut == 0)
                {
                    DEBUG_LOG("ChRoutine] TMO zero, S:%d(%d), opMode:%d, s:%d\r\n",
                            wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), 
                            m_pChannel->GetChOpMode(), cTimerSys::getInst()->GetCurTimerSec());

                    if(m_pChannel->GetChOpMode() == OPMODE_ASSIGNED_SLOT)
                    {
                        DEBUG_LOG("ChRoutine] AssignedSlotMode Terminate, S:%d(%d)\r\n",
                                wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID));

                        CLayerNetwork::getInst()->RunReturnFromAssignedMode_Slot(m_pChannel, OPMODE_CONTINUOUS, ASSIGNED_MODE_NONE);    // 할당모드 종료
                        return;
                    }

                    nNextFrameTxSlot = SetPosSlotColumnCheckTmoSO(wTxSlotID, m_nRoutinePosMsgID, m_nNextStartSI, m_nSizeSI, true);
                }
            }

            if(wTxSlotID != SLOTID_NONE)
            {
                CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
            }
        }
        else
        {
            WARNING_LOG("ChRoutine] XXXXXXXXXXXXXXXXXXXXX Error! No allocSlot, msg:%d, limit:%d, diff:%d, chkEnd:%d, C:%d, NS:%d, NI:%d, startSI:%d(%d), sizeSI:%d, elap:%d,%d, TT-%d:%d:%d, %d\r\n",
                    m_nRoutinePosMsgID, DIFF_SLOT_LIMIT_MAX, nDiffSlot, nCheckEnd, cAisModem::getInst()->GetSlotNoCounter(),
                    m_nNS, m_nNI, m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
                    m_nSizeSI, m_dwRoutineAllocLastCheckSec, cTimerSys::getInst()->GetTimeDiffSec(m_dwRoutineAllocLastCheckSec),
                    cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                    cTimerSys::getInst()->GetCurTimerSec());
        }

        SetNextTempItdmaSI();
        IncNS();
        IncNextStartSI();

        CLayerNetwork::getInst()->m_pLastRoutineCH = m_pChannel;

        DEBUG_LOG("ChRoutine] SO-after, C:%d, NS:%d, NI:%d, nextSI:%d(%d), sizeSI:%d, lastTxSlot : %d, %d\r\n",
            cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
            m_nSizeSI, m_nRoutineNextTxSlot, cTimerSys::getInst()->GetCurTimerSec());
    }
    else if(nDiffSlot > (NUM_SLOT_PER_FRAME + TX_CHECK_SLOT_SPACE + m_nSizeSI))
    {
        DEBUG_LOG("ChRoutine] DelayedTiming, diff:%d, chkEnd:%d(%d), nextSI:%d(%d), sizeSI:%d, NS:%d, NI:%d, lastTxSlot : %d, %d\r\n",
                nDiffSlot, 
                nCheckEnd, m_pChannel->GetSlotIdFromFrameSlotID(nCheckEnd),
                m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
                m_nSizeSI, m_nNS, m_nNI, m_nRoutineNextTxSlot, cTimerSys::getInst()->GetCurTimerSec());

        SetNextTempItdmaSI();
        IncNS();
        IncNextStartSI();
    }

    ProcessTempUseReportByITDMA_Ch();
}

/**
 * @brief Process the routine phase for IT
 */
void CChTxScheduler::ProcessPhaseRoutine_IT(void)
{
    //------------------------------------------------------------------------------
    // Using ITDMA
    // refer to Ivan Test Method 16.6.3.1
    // ITDMA transmission with offset 0, keep flag 0, number of slots 0(== 1 slot)
    //------------------------------------------------------------------------------
    int nCheckEnd = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, m_nSizeSI-1);
    int nDiffSlot = CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, nCheckEnd);

    if(nDiffSlot <= TX_CHECK_SLOT_SPACE+m_nSizeSI)                                                    // 다음 송신에 임박했을때 처리
    {
        m_dwRoutineAllocLastCheckSec = cTimerSys::getInst()->GetCurTimerSec();
        m_wRoutineAllocLastCheckSlot = OPSTATUS::nCurFrameMapSlotID;

        WORD wTxSlotID = SLOTID_NONE;
        FRAMEMAP_SLOTDATA *pSlotCur = NULL;

        wTxSlotID = m_pChannel->GetInternalAllocatedSlot_IT(m_nNextStartSI, m_nSizeSI, m_nRoutinePosMsgID);

        if(wTxSlotID == SLOTID_NONE || !(pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID)))
        {
            wTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, m_nRoutinePosMsgID, m_nNextStartSI, m_nSizeSI, 1);
            pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID);

            DEBUG_LOG("ChRoutine-IT] First IT alloc! msg:%d, S : %d, M : %d, NI : %d, StartSI : %d\r\n",
                    m_nRoutinePosMsgID, wTxSlotID, m_nRoutinePosMsgID, m_nNI, m_nNextStartSI);
        }

        if(wTxSlotID != SLOTID_NONE && (pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID)))
        {
            int nNextNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, m_nNI);

            WORD wNextTxSlotId = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, m_nRoutinePosMsgID, nNextNextStartSI, m_nSizeSI, 1);
            if(wNextTxSlotId == SLOTID_NONE)
            {
                DEBUG_LOG("ChRoutine-IT] Alloc Error! msg:%d, StartSI : %d\r\n",
                        m_nRoutinePosMsgID, nNextNextStartSI);
            }
            else
            {
                m_pChannel->SetFrameMapOneMsg(true, wNextTxSlotId, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, 0,
                                                    POS_REPORT_SCHEDULED, TDMA_ITDMA, m_nRoutinePosMsgID, 0, false, false, 0, false);

                UINT uSlotOffset= CAisLib::FrameMapGetDiffSlotID(wTxSlotID, wNextTxSlotId);
                m_pChannel->SetFrameMapOneMsg(true, wTxSlotID, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, 0,
                                                    POS_REPORT_SCHEDULED, TDMA_ITDMA, m_nRoutinePosMsgID, uSlotOffset, false, false, 0, false);

                CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);

                DEBUG_LOG("ChRoutine-IT] Alloc OK! msg:%d, NI : %d, startSI : %d,%d sizeSI : %d, nextTxSlot : %d, off:%d->%d\r\n",
                        m_nRoutinePosMsgID, m_nNI, m_nNextStartSI, nNextNextStartSI, m_nSizeSI, wNextTxSlotId, uSlotOffset, pSlotCur->uSlotOffset, pSlotCur->uNumSlots);
            }
        }

        m_nRoutineNextTxSlot = (wTxSlotID == SLOTID_NONE ? m_nNextStartSI : wTxSlotID);
        IncNS();
        IncNextStartSI();

        DEBUG_LOG("ChRoutine-IT] msg:NI : %d, NS : %d, startSI : %d, sizeSI : %d, lastTxSlot : %d, %d\r\n",
                m_nRoutinePosMsgID, m_nNI, m_nNS, m_nNextStartSI, m_nSizeSI, m_nRoutineNextTxSlot, cTimerSys::getInst()->GetCurTimerSec());
    }
}

/** 
 * @brief Set report rate by ITDMA
 * @param bSet true to enable, false to disable
 * @return true if successful, false otherwise
 */
bool CChTxScheduler::SetReportRateByITDMA(bool bSet)
{
    if(!m_pChannel->IsTxAvailableCh())
        return false;

    //--------------------------------------------------------------------------------------------------------
    // ITU-R 1371-5 Ann.2 ******* Changing course (applicable to Class A shipborne mobile equipment, only)
    // A change of course should be determined by calculating the mean value of the heading information 
    // (HDG) for the last 30 s and comparing the result with the present heading. When HDG is unavailable, 
    // the Rr should not be affected.
    // If the difference exceeds 5°, a higher Rr should be applied in accordance with Table 1, Annex 1. 
    // The higher Rr should be maintained by using ITDMA to complement SOTDMA scheduled transmissions 
    // in order to derive the desired Rr. When 5° is exceeded, the reporting interval should be decreased
    // beginning with a broadcast within the next 150 slots (see § *******.1) using either a scheduled 
    // SOTDMA slot, or a RATDMA access slot (see § *******).

    if (bSet)
    {
        // Insert 2 ITDMA slots between SOTDMA slots
        m_nAddCntITDMA = 3;
        m_nItdmaNI = m_nNI / 3;
        m_nItdmaSizeSI = m_nItdmaNI * 0.2f;
        m_nItdmaStartSI = SLOTID_NONE;
        m_nNextItdmaTxCnt = 0;
        m_nNextItdmaTxSlotId = SLOTID_NONE;

        DEBUG_LOG("ProcTmpITDMA] enable, soSI : %d, soNI : %d, cnt : %d, itdmaNI : %d, itSI : %d, sizeSI : %d, s:%d\r\n",
                m_nNextStartSI, m_nNI, m_nNextItdmaTxCnt, m_nItdmaNI, m_nItdmaStartSI, m_nItdmaSizeSI, cTimerSys::getInst()->GetCurTimerSec());
    }
    else
    {
        if (m_nNextItdmaTxSlotId != SLOTID_NONE)
        {
            m_pChannel->SetFrameMapOneMsg(true, m_nNextItdmaTxSlotId, 1, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, 0,
                                                POS_REPORT_UNSCHEDULED, TDMA_ITDMA, AIS_MSG_NO_ID_03, 0, false, false, 0, false);
            CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
        }

        m_nItdmaNI = 0;
        m_nItdmaSizeSI = 0;
        m_nNextItdmaTxCnt = 0;
        m_nNextItdmaTxSlotId = SLOTID_NONE;

        DEBUG_LOG("ProcTmpITDMA] disable, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
    }

    return true;
}

/**
 * @brief Set the next temporary ITDMA start slot
 */
void CChTxScheduler::SetNextTempItdmaSI(void)
{
    //--------------------------------------------------------------------------------
    // 코스 변경에 따른 일시적인 ITDMA 송신을 현재 SOTDMA 의 NS 에서 수행되도록 초기화
    //--------------------------------------------------------------------------------
    m_nItdmaStartSI   = m_nNextStartSI;
    m_nNextItdmaTxCnt = 1;
}

/**
 * @brief Process the temporary use of ITDMA for reporting
 * @return True if successful, false otherwise
 */
bool CChTxScheduler::ProcessTempUseReportByITDMA_Ch(void)
{
    const BYTE NUM_SLOT = 1;

    WORD wNextTxSlotID = SLOTID_NONE;
    bool bLastTx = false;
    int  nItdmaTxSlotId = SLOTID_NONE;
    int  nNextItdmaTxSlotId = SLOTID_NONE;
    bool bKeepFlag = false;
    UINT uSlotOffset = 0;

    if(!CReportRateMgr::getInst()->IsITDMAEnabled())
        return false;

    if(!m_pChannel->IsTxAvailableCh())
        return false;

    if(!m_nAddCntITDMA)
        return false;

    if(m_nItdmaStartSI == SLOTID_NONE)
        return false;

    if(!m_nNextItdmaTxCnt)
        return false;

    int nItdmaCurStartSI = CAisLib::FrameMapSlotIdAdd(m_nItdmaStartSI, m_nItdmaNI * m_nNextItdmaTxCnt);

    if(CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, nItdmaCurStartSI) <= TX_CHECK_SLOT_SPACE)
    {
        nItdmaTxSlotId = m_nNextItdmaTxSlotId;
        bool bRunItdmaTx = (nItdmaTxSlotId != SLOTID_NONE);

        if(nItdmaTxSlotId == SLOTID_NONE)
        {
            nItdmaTxSlotId = m_pChannel->GetSoTdmaSlotForItdmaTx(nItdmaCurStartSI, m_nItdmaSizeSI);
            bRunItdmaTx = (nItdmaTxSlotId == SLOTID_NONE);
        }

        DEBUG_LOG("ProcTmpITDMA] before, Run : %d, curTX : %d(%d), soSI : %d, itdmaSI : %d, SoNI : %d, itdmaNI : %d, itdmaSizeSI : %d, txCnt : %d\r\n",
                bRunItdmaTx, nItdmaTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(nItdmaTxSlotId),
                m_nItdmaStartSI, nItdmaCurStartSI, m_nNI, m_nItdmaNI, m_nItdmaSizeSI, m_nNextItdmaTxCnt);

        if(bRunItdmaTx)
        {
            if(nItdmaTxSlotId == SLOTID_NONE)
                nItdmaTxSlotId = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nItdmaCurStartSI, m_nItdmaSizeSI, 1);

            if(nItdmaTxSlotId == SLOTID_NONE)
            {
                DEBUG_LOG("ProcTmpITDMA] Alloc ERROR! StartSI : %d, sizeSI:%d\r\n",
                    nItdmaCurStartSI, m_nItdmaSizeSI);

                int nItdmaNextEndSI = CAisLib::FrameMapSlotIdAdd(nItdmaCurStartSI, (int)(m_nItdmaNI*0.2f));
                if(CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, nItdmaNextEndSI) < 2)
                {
                    if(++m_nNextItdmaTxCnt >= m_nAddCntITDMA)
                        m_nNextItdmaTxCnt = 0;
                }
            }
            else
            {
                DEBUG_LOG("ProcTmpITDMA] TX : %d(%d), soSI : %d, itdmaSI : %d, itdmaNI : %d, SIsize : %d, txCnt : %d\r\n",
                        nItdmaTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(nItdmaTxSlotId),
                        m_nItdmaStartSI, nItdmaCurStartSI, m_nItdmaNI, m_nItdmaSizeSI, m_nNextItdmaTxCnt);

                int nItdmaNextStartSI;
                if(m_nNextItdmaTxCnt == 1)
                    nItdmaNextStartSI = CAisLib::FrameMapSlotIdAdd(nItdmaCurStartSI, m_nItdmaNI);
                else
                    nItdmaNextStartSI = CAisLib::FrameMapSlotIdAdd(m_nItdmaStartSI, m_nNI+m_nItdmaNI);

                nNextItdmaTxSlotId = m_pChannel->GetSoTdmaSlotForItdmaTx(nItdmaNextStartSI, m_nItdmaSizeSI);
                if(nNextItdmaTxSlotId == SLOTID_NONE)
                {
                    nNextItdmaTxSlotId = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nItdmaNextStartSI, m_nItdmaSizeSI, 1);

                    if(nNextItdmaTxSlotId != SLOTID_NONE)
                    {
                        uSlotOffset = CAisLib::FrameMapGetDiffSlotID(nItdmaTxSlotId, nNextItdmaTxSlotId);
                        m_pChannel->SetFrameMapOneMsg(true, nNextItdmaTxSlotId, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, 0,
                                                            POS_REPORT_UNSCHEDULED, TDMA_ITDMA, AIS_MSG_NO_ID_03, 0, 0, false, 0, false);
                    }
                }

                m_pChannel->SetFrameMapOneMsg(true, nItdmaTxSlotId, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC, 0,
                                                    POS_REPORT_UNSCHEDULED, TDMA_ITDMA, AIS_MSG_NO_ID_03, uSlotOffset, bKeepFlag, false, 0, false);

                m_nNextItdmaTxSlotId = nNextItdmaTxSlotId;
                CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
            }
        }

        if(++m_nNextItdmaTxCnt >= m_nAddCntITDMA)
            m_nNextItdmaTxCnt = 0;

        DEBUG_LOG("ProcTmpITDMA] end, curTX : %d(%d), nextTX : %d(%d), off:%d, soSI : %d, itdmaSI : %d, itdmaNI : %d, SIsize : %d, txCnt : %d\r\n",
                nItdmaTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(nItdmaTxSlotId),
                m_nNextItdmaTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextItdmaTxSlotId), uSlotOffset,
                m_nItdmaStartSI, nItdmaCurStartSI, m_nItdmaNI, m_nItdmaSizeSI, m_nNextItdmaTxCnt);
    }

    return true;
}

/**
 * @brief Process the periodic static voyage message transmission
 * @param bTxUnconditionally True to transmit unconditionally, false otherwise
 * @return True if successful, false otherwise
 */
bool CChTxScheduler::ProcessPeriodicStaticVoyageMsgTx(bool bTxUnconditionally)
{
    //-------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 14.2.2.4 Static data reporting intervals
    // Confirm that the EUT transmits Message 5 with a reporting interval of 6 min alternating Channel A and Channel B.
    //-------------------------------------------------------------------------------------------------------------------

    if(!CVdlTxMgr::IsTxAvailable())
        return false;

    if(!m_dwStaticReportIntervalSec)
        return false;

    bool bRet = false;

    if(bTxUnconditionally || (m_dwStaticReportNextSec > 0 && cTimerSys::getInst()->GetTimeDiffSecToFuture(m_dwStaticReportNextSec) < 2))
    {
        int nTimeOutSlot = CAisLib::GetNumSlotFromSec(m_dwStaticReportIntervalSec) * 0.1;

        DEBUG_LOG("PeriodicStaticMsg-Tx] %d,RI : %d, nextSec : %d, curSec : %d, remain : %u, sizeSI : %d, s:%d\r\n",
                bTxUnconditionally, m_dwStaticReportIntervalSec, m_dwStaticReportNextSec,
                cTimerSys::getInst()->GetCurTimerSec(), cTimerSys::getInst()->GetTimeDiffSecToFuture(m_dwStaticReportNextSec), 
                nTimeOutSlot, cTimerSys::getInst()->GetCurTimerSec());

        if(CLayerNetwork::getInst()->ReserveTxStaticVoyageMsg(m_pChannel, true, nTimeOutSlot))
        {
            m_dwStaticReportNextSec = cTimerSys::getInst()->GetCurTimerSec() + m_dwStaticReportIntervalSec;
            bRet = true;
        }
    }
    return bRet;
}

/**
 * @brief Process the long range message transmission
 * @return True if successful, false otherwise
 */
bool CChTxScheduler::ProcessLongRangeMsgTx(void)
{
    //-------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 14.2.2.4 Static data reporting intervals
    // Confirm that the EUT transmits Message 5 with a reporting interval of 6 min alternating Channel A and Channel B.
    //-------------------------------------------------------------------------------------------------------------------

    if(!CVdlTxMgr::IsTxAvailable())
        return false;

    if(!m_dwLongRangeReportIntervalSec)
        return false;

    if(m_dwLongRangeTxNextSec > 0 && cTimerSys::getInst()->GetTimeDiffSecToFuture(m_dwLongRangeTxNextSec) < 2)
    {
        CLongRange::getInst()->RunPeriodicallyLongRangeVDL(true);
        if(CLongRange::getInst()->IsLRTxAvail())
        {
            const int NUM_SLOTS = 1;
            WORD wNextTxSlotId = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_27, CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, TX_CHECK_SLOT_SPACE_MSG5), RATDMA_SI, NUM_SLOTS);

            if(wNextTxSlotId == SLOTID_NONE)
            {
                DEBUG_LOG("[LR-CheckTx] Alloc Error! wait next chance, %d, s:%d\r\n", m_dwLongRangeTxNextSec, cTimerSys::getInst()->GetCurTimerSec());
            }
            else
            {
                m_pChannel->SetFrameMapOneMsgColumn(true, wNextTxSlotId, NUM_SLOTS, 
                                                    cShip::getOwnShipInst()->GetOwnShipMMSI(), 
                                                    SLOTSTAT_INT_ALLOC, 0, POS_REPORT_UNSCHEDULED,
                                                    TDMA_RATDMA, AIS_MSG_NO_ID_27, 0, false, false, 0, false);

                m_dwLongRangeTxNextSec = cTimerSys::getInst()->GetCurTimerSec() + m_dwLongRangeReportIntervalSec;
                CLayerNetwork::getInst()->m_pLastTxChMsg27 = m_pChannel;
                CLayerNetwork::getInst()->m_dwLastTxSecMsg27 = cTimerSys::getInst()->GetCurTimerSec();
                CLayerNetwork::getInst()->InitLongRangeReportSec();

                return true;
            }
        }
        else
        {
            m_dwLongRangeTxNextSec = cTimerSys::getInst()->GetCurTimerSec() + m_dwLongRangeReportIntervalSec;
        }
    }
    return false;
}

bool CChTxScheduler::AllocUnscheduledMultiSlotMsg(UINT16 uMsgID, WORD wStartSI, BYTE bNumSlot, bool bCheckSoTdmaSlot, BYTE nBuffIdx, INT16 nSizeSI, int *pnTxSlot, bool bFailNoExistingSO)
{
    //-------------------------------------------------------------------------------------------------
    // Slot allocation for transmission of Message 3, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 24, 25, 26,
    //-------------------------------------------------------------------------------------------------

    if(pnTxSlot)
        *pnTxSlot = SLOTID_NONE;

    if(bCheckSoTdmaSlot && !CReportRateMgr::getInst()->IsReportRateForSOTDMA())
        bCheckSoTdmaSlot = false;

    if(!bCheckSoTdmaSlot)
        bFailNoExistingSO = false;

    if(wStartSI == SLOTID_NONE)
    {
        wStartSI = CAisLib::GetFrameMapSlotID(cAisModem::getInst()->GetCurrentFrameNo(), cAisModem::getInst()->GetSlotNoCounter());
        wStartSI = CAisLib::FrameMapSlotIdAdd(wStartSI, TX_RESERVE_SLOT_SPACE);
    }

    WORD wEndSI = CAisLib::FrameMapSlotIdAdd(wStartSI, nSizeSI);
    WORD wAllocTxSlotID = SLOTID_NONE;
    WORD wExistingTxSlotID = SLOTID_NONE;
    FRAMEMAP_SLOTDATA *pSlotExisting = NULL;
    bool bTryAllocTx = false;
    bool bAllocRA = false;

    if(bCheckSoTdmaSlot)
    {
        wExistingTxSlotID = m_pChannel->GetSoTdmaSlotForItdmaTx(wStartSI, nSizeSI);
        if(wExistingTxSlotID == SLOTID_NONE)
        {
            WARNING_LOG("AllocMultiSlotMsg] SO-IT, SO not found, m:%d, numSlot:%d, lastTx: %d(%d), SI : %d(%d), endSI: %d(%d), sizeSI : %d, s:%d\r\n",
                    uMsgID, bNumSlot,
                    m_nLastScheduledTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(m_nLastScheduledTxSlotID),
                    wStartSI, m_pChannel->GetSlotIdFromFrameSlotID(wStartSI), wEndSI, m_pChannel->GetSlotIdFromFrameSlotID(wEndSI), nSizeSI, cTimerSys::getInst()->GetCurTimerSec());
        }

        if(wExistingTxSlotID != SLOTID_NONE)
        {
            WORD wNewSI = CAisLib::FrameMapSlotIdAdd(wExistingTxSlotID, 1);
            int nNewSizeSI = CAisLib::FrameMapGetDiffSlotID(wNewSI, wEndSI);

            wAllocTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, uMsgID, wNewSI, nNewSizeSI, bNumSlot);
            bTryAllocTx = true;

            if(wAllocTxSlotID == SLOTID_NONE)
            {
                wExistingTxSlotID = SLOTID_NONE;
                bTryAllocTx = false;

                WARNING_LOG("AllocMultiSlotMsg] alloc-1, cancel SO, m:%d, Exist:%d(%d), New:%d(%d), SI:%d, sizeSI:%d, s:%d\r\n",
                    uMsgID, wExistingTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wExistingTxSlotID),
                    wAllocTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wAllocTxSlotID), wNewSI, nNewSizeSI, cTimerSys::getInst()->GetCurTimerSec());
            }
        }
    }

    if(bCheckSoTdmaSlot && bFailNoExistingSO && wExistingTxSlotID == SLOTID_NONE)
    {
        WARNING_LOG("AllocMultiSlotMsg] Error! No Existing SO, m:%d, Exist:%d, New:%d, SI:%d, sizeSI:%d, s:%d\r\n",
                uMsgID, wExistingTxSlotID, wAllocTxSlotID, wStartSI, nSizeSI, cTimerSys::getInst()->GetCurTimerSec());
        return false;
    }

    if(!bTryAllocTx)
    {
        wAllocTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, uMsgID, wStartSI, nSizeSI, bNumSlot);
        DEBUG_LOG("AllocMultiSlotMsg] alloc-2, m:%d, Exist:%d, New:%d, SI:%d, sizeSI:%d, s:%d\r\n",
                uMsgID, wExistingTxSlotID, wAllocTxSlotID, wStartSI, nSizeSI, cTimerSys::getInst()->GetCurTimerSec());
    }

    if(wAllocTxSlotID == SLOTID_NONE)
    {
        WARNING_LOG("AllocMultiSlotMsg] Alloc Error! msg:%d, numSlot:%d, SI : %d, sizeSI : %d, s:%d\r\n",
                uMsgID, bNumSlot, wStartSI, nSizeSI, cTimerSys::getInst()->GetCurTimerSec());
        return false;
    }

    int nExistingMsgID = -1;
    int nExistingTmo = -1;
    int nExistingOff = 0;
    if(wExistingTxSlotID == SLOTID_NONE)
    {
        m_pChannel->SetFrameMapOneMsgColumn(true, wAllocTxSlotID, bNumSlot, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                0, POS_REPORT_UNSCHEDULED, TDMA_RATDMA, uMsgID, 0, false, false, nBuffIdx, false);
    }
    else
    {
        pSlotExisting = m_pChannel->GetSlotDataPtr(wExistingTxSlotID);

        nExistingMsgID  = pSlotExisting->uMsgID;
        nExistingTmo    = pSlotExisting->nSlotTimeOut;
        nExistingOff    = pSlotExisting->uSlotOffset;

        m_pChannel->SetFrameMapOneMsgColumn(true, wAllocTxSlotID, bNumSlot, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                0, POS_REPORT_UNSCHEDULED, TDMA_ITDMA, uMsgID, 0, false, false, nBuffIdx, false);

        UINT uSlotOffset= CAisLib::FrameMapGetDiffSlotID(wExistingTxSlotID, wAllocTxSlotID);

        if(uSlotOffset <= 0)
        {
            WARNING_LOG("AllocMultiSlotMsg] Offset Zero, So:%d(%d), RA:%d(%d), off:%d, s:%d\r\n",
                        wExistingTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wExistingTxSlotID),
                        wAllocTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wAllocTxSlotID), 
                        uSlotOffset, cTimerSys::getInst()->GetCurTimerSec());
        }

        pSlotExisting->uMsgID = AIS_MSG_NO_ID_03;
        pSlotExisting->uSlotOffset = uSlotOffset;
    }

    if(pnTxSlot)
        *pnTxSlot = wAllocTxSlotID;

    DEBUG_LOG("AllocMultiSlotMsg] after, OK! msg:%d, slot:%d(%d), RA:%d, numSlot:%d, SI:%d(%d), sizeSI:%d, %d(%d),m:%d,tmo:%d -> IT:m:%d,tmo:%d, checkSO:%d,%d,%d, s:%d\r\n",
                uMsgID, wAllocTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wAllocTxSlotID), bAllocRA,
                bNumSlot, wStartSI, m_pChannel->GetSlotIdFromFrameSlotID(wStartSI), nSizeSI,
                wExistingTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wExistingTxSlotID),
                nExistingMsgID, nExistingTmo, pSlotExisting ? pSlotExisting->uMsgID : -1, pSlotExisting ? pSlotExisting->nSlotTimeOut : -1,
                bCheckSoTdmaSlot, CReportRateMgr::getInst()->IsReportRateForSOTDMA(), bFailNoExistingSO, cTimerSys::getInst()->GetCurTimerSec());

    return true;
}

/**
 * @brief Terminate the position report in SO mode
 */
void CChTxScheduler::TerminatePosReportSO(void)
{
    m_pChannel->SetLastTxFrameAndFree(CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, TX_CHECK_SLOT_SPACE));

    DEBUG_LOG("PrepToChgSOtoITDMA] NI : %d, nextStartSI : %d, lastTxSlot : %d\r\n",
            m_nNI, m_nNextStartSI, m_nRoutineNextTxSlot);
}

/**
 * @brief Set the assigned report rate time-out
 */
void CChTxScheduler::SetAssignedRRTimeOut(void)
{
    m_nRRChgTimeOutMin = CAisLib::GetRandomSlotTimeOut();
    m_dwRRTimeOutMS    = (m_nRRChgTimeOutMin + 1) * 60000;
    m_dwRRTimeOutStartTick = SysGetSystemTimer();
}

/**
 * @brief Process the first phase of changing the report rate
 * @return True if the phase is completed, false otherwise
 */
bool CChTxScheduler::ProcessPhaseChgReportRateFirst(void)
{
    if(CLayerNetwork::getInst()->IsMasterChChgRR(m_pChannel))
        return ProcessPhaseChgReportRateFirst_MasterCH();
    return ProcessPhaseChgReportRateFirst_SlaveCH();
}

/**
 * @brief Process the first phase of changing the report rate for the master channel
 * @return True if the phase is completed, false otherwise
 */
bool CChTxScheduler::ProcessPhaseChgReportRateFirst_MasterCH(void)
{
    const INT8 nNewPosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    const bool bRunAssignedChgRR = (CLayerNetwork::getInst()->m_nOpMode == OPMODE_ASSIGNED_RR);
    const bool bRunAutoToAssignedChgRR = bRunAssignedChgRR && CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg == AIS_MSG_NO_ID_01;
    const bool bRunChgOpMode = CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg != nNewPosMsgID;
    const BYTE NUM_SLOT = 1;

    int nTotalNI = CAisLib::GetNIfromRR(CReportRateMgr::getInst()->GetReportRate());
    FRAMEMAP_SLOTDATA *pSlotCur = NULL;
    FRAMEMAP_SLOTDATA *pSlotNext = NULL;
    WORD wTxSlotID = SLOTID_NONE;
    WORD wNextTxSlotID = SLOTID_NONE;
    int  nCurSI = SLOTID_NONE;
    int  nNextStartSI = SLOTID_NONE;
    bool bCurTxNewAlloc = false;
    bool bRet = false;

    if(!m_pChannel->IsTxAvailableCh())
    {
        WARNING_LOG("ChChgRRphase-First-Master] masterCH tx not enabled! s : %d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        return false;
    }
    if(!CLayerNetwork::getInst()->IsMasterChChgRR(m_pChannel))
        return false;

    if(!CReportRateMgr::getInst()->IsReportRateForSOTDMA())
        return false;

    if(CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_nNextStartSI) <= TX_CHECK_SLOT_SPACE)
    {
        nCurSI = m_nNextStartSI;
        nNextStartSI = CAisLib::FrameMapSlotIdAdd(nCurSI, m_nNI);

        DEBUG_LOG("ChChgRRphase-First-Master] before, RunAssignedRR : %d, autoToAS: %d, NS : %d, NI : %d, SI : %d(%d), sizeSI : %d, newPosMsg: %d\r\n",
                bRunAssignedChgRR, bRunChgOpMode, m_nNS, m_nNI, nCurSI, m_pChannel->GetSlotIdFromFrameSlotID(nCurSI), m_nSizeSI, nNewPosMsgID);

        wTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nCurSI, m_nSizeSI, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, false, true);
        if(wTxSlotID == SLOTID_NONE)
        {
            bCurTxNewAlloc = true;
            wTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nCurSI, m_nSizeSI, NUM_SLOT);

            m_pChannel->SetFrameMapOneMsgColumn(true, wTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                    (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? CAisLib::GetRandomSlotTimeOut() : m_nRRChgTimeOutMin),
                                                    POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);

            DEBUG_LOG("ChChgRRphase-First-Master] checkCur, No last slot and alloc RA, Tx : %d, prevMsg:%d, C :%d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                    wTxSlotID, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg,
                    cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI);
        }
        else
        {
            DEBUG_LOG("ChChgRRphase-First-Master] checkCur, existingSO, Tx : %d(%d) msg:%d, C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                    CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI);
        }

        if(wTxSlotID == SLOTID_NONE || !(pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID)))
        {
            DEBUG_LOG("ChChgRRphase-First-Master] checkCur, No alloc, Tx : %d, msg:%d, C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d, time-out : %d\r\n",
                wTxSlotID, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI, pSlotCur->nSlotTimeOut);
        }
        else
        {
            IncNS();

            DEBUG_LOG("ChChgRRphase-First-Master] checkCur, OK, Tx : %d(%d),tmo:%d, msg:%d, C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d, time-out : %d\r\n",
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), pSlotCur->nSlotTimeOut,
                    CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI, pSlotCur->nSlotTimeOut);

            if(bRunAssignedChgRR)
            {
                SetAssignedRRTimeOut();
            }

            int nNewNSS = wTxSlotID;

            nTotalNI = CAisLib::GetNIfromRR(CReportRateMgr::getInst()->GetReportRate());
            SetChReportInterval(nTotalNI, m_fNewReportIntervalSecSO);

            SetNSS(nNewNSS);
            ResetNextStartSI();
            IncNS();

            nCurSI = CAisLib::FrameMapSlotIdAdd(m_nNextStartSI, -m_nNI);
            nNextStartSI = m_nNextStartSI;

            bool bNextExisting = false;

            wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nNextStartSI, m_nSizeSI, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, false, true);
            if(wNextTxSlotID == SLOTID_NONE)
            {
                DEBUG_LOG("ChChgRRphase-First-Master] checkNext, not found existingSO, msg : %d, NSS : %d, NS : %d, NI : %d, SI : %d, newSizeSI : %d, newNI : %d\r\n",
                        CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, m_nNSS, m_nNS, m_nNI, nNextStartSI, m_nSizeSI, m_nNI);

                wNextTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, nNewPosMsgID, nNextStartSI, m_nSizeSI, 1);
                if(wNextTxSlotID == SLOTID_NONE)
                {
                    DEBUG_LOG("ChChgRRphase-First-Master] checkNext, Alloc Error! nextTx : %d(%d), msg : %d, NSS : %d, NS : %d, NI : %d, SI : %d, newSizeSI : %d, newNI : %d\r\n",
                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
                        CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, m_nNSS, m_nNS, m_nNI, nNextStartSI, m_nSizeSI, m_nNI);
                }
            }
            else
            {
                bNextExisting = true;

                pSlotNext = m_pChannel->GetSlotDataPtr(wNextTxSlotID);
                DEBUG_LOG("ChChgRRphase-First-Master] CheckNext, found existingSO, NextTx : %d(%d) msg:%d,tmo:%d, C :%d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), pSlotNext->uMsgID, pSlotNext->nSlotTimeOut,
                        cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nNextStartSI, m_nSizeSI);
            }

            if(wNextTxSlotID != SLOTID_NONE && (pSlotNext = m_pChannel->GetSlotDataPtr(wNextTxSlotID)))
            {
                bRet = true;

                if(!bNextExisting) {
                    m_pChannel->SetFrameMapOneMsgColumn(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                    (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? CAisLib::GetRandomSlotTimeOut() : m_nRRChgTimeOutMin),
                                                    POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                }
                else {
                    SetPosSlotColumnCheckTmoSO(wNextTxSlotID, nNewPosMsgID, nNextStartSI, m_nSizeSI, true);
                }

                m_pChannel->SetFrameMapOneMsgColumn(true, wTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                        pSlotCur->nSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                if(pSlotCur->nSlotTimeOut <= 0)
                    pSlotCur->nSlotTimeOut = 1;

                DEBUG_LOG("ChChgRRphase-First-Master] checkNext, OK, nextTx : %d(%d),tmo:%d, msg : %d, NSS : %d, NS : %d, NI : %d, SI : %d, newSizeSI : %d, newNI : %d, m:%d, tmo:%d\r\n",
                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), pSlotNext->nSlotTimeOut,
                        CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, m_nNSS, m_nNS, m_nNI, nNextStartSI, m_nSizeSI, m_nNI, m_nRRChgTimeOutMin, pSlotCur->uMsgID);
            }
        }

        m_pChannel->SetLastTxSlotWithRange(wTxSlotID, wNextTxSlotID, true);

        if(bRet)
        {
            CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
        }
        else
        {
            wTxSlotID = nCurSI;
            wNextTxSlotID = m_nNextStartSI;

            IncNS();

            int nNewNSS = wTxSlotID;

            nTotalNI = CAisLib::GetNIfromRR(CReportRateMgr::getInst()->GetReportRate());
            SetChReportInterval(nTotalNI, m_fNewReportIntervalSecSO);

            SetNSS(nNewNSS);
            ResetNextStartSI();
            IncNS();
        }

        m_nChangeRRphaseTxSlotId = wNextTxSlotID;
        m_nChangeRRphaseTxCnt = 0;

        CChannelMgr *pOtherCH = CLayerNetwork::getInst()->GetOppositeChPtr(m_pChannel);
        int nOppNSS = SLOTID_NONE;
        if(pOtherCH->IsTxAvailableCh())
        {
            // slave CH 의 NS 보고주기 및 NSS 등은 여기서 변경해준다!
            nOppNSS = CAisLib::FrameMapSlotIdAdd(m_nNSS, nTotalNI);

            pOtherCH->SetChReportInterval(nTotalNI, m_fNewReportIntervalSecSO);
            pOtherCH->SetNSS(nOppNSS);
            pOtherCH->ResetNextStartSI();
            pOtherCH->SetLastTxSlotWithRange(wTxSlotID, pOtherCH->m_nNextStartSI, true);
            m_nRRChgSlaveNewSI = CAisLib::FrameMapSlotIdAdd(nOppNSS, -m_nSizeHalfSI);
        }
        else
        {
            pOtherCH->SetChOpPhase(OPPHASE_CHG_RR_PREP_SECOND);
        }

        DEBUG_LOG("ChChgRRphase-First-Master] after, RunAssignedRR: %d, opMode:%d, mdm:%d, SI:%d(%d), curTx:%d(%d), m:%d, tmo:%d, keep:%d, nextTx:%d(%d), m:%d, tmo:%d, NSS:%d NS:%d, NI:%d, nextSI:%d, sizeSI:%d, newTMO:%d, totalNI:%d, slaveNSS:%d(%d),SI:%d(%d), s:%d\r\n",
                bRunAssignedChgRR, CLayerNetwork::getInst()->m_nOpMode, cAisModem::getInst()->GetSlotNoCounter(),
                nCurSI, m_pChannel->GetSlotIdFromFrameSlotID(nCurSI),
                wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                pSlotCur ? pSlotCur->uMsgID : -1, pSlotCur ? pSlotCur->nSlotTimeOut : -1, pSlotCur->bItdmaKeepFlag,
                wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
                pSlotNext ? pSlotNext->uMsgID : -1, pSlotNext ? pSlotNext->nSlotTimeOut : -1,
                m_nNSS, m_nNS, m_nNI, m_nNextStartSI, m_nSizeSI, m_nRRChgTimeOutMin,
                nTotalNI, pOtherCH->m_nNSS, pOtherCH->GetSlotIdFromFrameSlotID(pOtherCH->m_nNSS),
                pOtherCH->m_nNextStartSI, pOtherCH->GetSlotIdFromFrameSlotID(pOtherCH->m_nNextStartSI), cTimerSys::getInst()->GetCurTimerSec());

        m_pChannel->SetChOpPhase(OPPHASE_CHG_RR_PREP_SECOND);
    }
    return bRet;
}

/**
 * @brief Process the first phase of changing the report rate for the slave channel
 * @return True if the phase is completed, false otherwise
 */
bool CChTxScheduler::ProcessPhaseChgReportRateFirst_SlaveCH(void)
{
    //------------------------------------------------------------------------------    ----------------------
    // Refer to 1371-5 Ann.2 *******
    // *******.1 Wait for next transmit slot
    // *******.2 Scan next selection interval
    // *******.3 Wait for next selection interval
    // refer to FIGURE 15
    //----------------------------------------------------------------------------------------------------
    //-----------------------------------------
    // slave 송신채널일때 보고주기의 변경
    //-----------------------------------------

    const BYTE NUM_SLOT = 1;
    const INT8 nNewPosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    const bool bRunAssignedChgRR = (CLayerNetwork::getInst()->m_nOpMode == OPMODE_ASSIGNED_RR);
    const bool bRunAutoToAssignedChgRR = bRunAssignedChgRR && CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg == AIS_MSG_NO_ID_01;
    const bool bRunChgOpMode = CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg != nNewPosMsgID;

    FRAMEMAP_SLOTDATA *pSlotCur = NULL;
    FRAMEMAP_SLOTDATA *pSlotNext = NULL;
    WORD wTxSlotID = SLOTID_NONE;
    WORD wNextTxSlotID = SLOTID_NONE;
    bool bCurTxNewAlloc = false;
    bool bRet = false;

    if(!m_pChannel->IsTxAvailableCh())
    {
        DEBUG_LOG("ChChgRRphase-First-Slave] goto Send phase, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        m_pChannel->SetChOpPhase(OPPHASE_CHG_RR_PREP_SECOND);
        return false;
    }

    if(CLayerNetwork::getInst()->IsMasterChChgRR(m_pChannel))
        return false;

    if(!CReportRateMgr::getInst()->IsReportRateForSOTDMA())
        return false;

    if(m_nRRChgSlaveNewSI == SLOTID_NONE)
    {
        return false;
    }

    if(CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_nRRChgSlaveNewSI) <= TX_CHECK_SLOT_SPACE)
    {
        int nCurSI = m_nRRChgSlaveNewSI;
        int nNextStartSI = CAisLib::FrameMapSlotIdAdd(nCurSI, m_nNI);

        wTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nCurSI, m_nSizeSI, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, false, true);
        if(wTxSlotID == SLOTID_NONE)
        {
            bCurTxNewAlloc = true;
            wTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nCurSI, m_nSizeSI, NUM_SLOT);

            DEBUG_LOG("ChChgRRphase-First-Slave] checkCur, No last slot and alloc RA-MSG3, Tx : %d(%d), C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI);
        }
        else
        {
            DEBUG_LOG("ChChgRRphase-First-Slave] checkCur, existingSO, Tx : %d(%d), msg:%d, C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, cAisModem::getInst()->GetSlotNoCounter(),
                    m_nNS, m_nNI, nCurSI, m_nSizeSI);
        }

        if(wTxSlotID == SLOTID_NONE || !(pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID)))
        {
            DEBUG_LOG("ChChgRRphase-First-Slave] xxxxxxxxxxxxxxxxxxxx CheckCur, No alloc, Tx : %d(%d), msg:%d, C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, 
                    cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI);
        }
        else
        {
            DEBUG_LOG("ChChgRRphase-First-Slave] CheckCur, OK, Tx : %d(%d), msg:%d,tmo:%d, C : %d, NS : %d, NI : %d, nextSI : %d, sizeSI : %d\r\n",
                    wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID), pSlotCur->uMsgID, pSlotCur->nSlotTimeOut,
                    cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nCurSI, m_nSizeSI);

            IncNS();

            bool bNextExisting = false;

            wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nNextStartSI, m_nSizeSI, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, false, true);
            if(wNextTxSlotID == SLOTID_NONE)
            {
                DEBUG_LOG("ChChgRRphase-First-Slave] CheckNext, not found existingSO, C : %d, NS : %d, NI : %d, nextSI : %d(%d), sizeSI : %d\r\n",
                        cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(nNextStartSI), m_nSizeSI);

                wNextTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, nNewPosMsgID, nNextStartSI, m_nSizeSI, 1);
                if(wNextTxSlotID == SLOTID_NONE)
                {
                    DEBUG_LOG("ChChgRRphase-First-Slave] CheckNext, Alloc Error! C : %d, NS : %d, NI : %d, nextSI : %d(%d), sizeSI : %d, time-out : %d\r\n",
                        cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(nNextStartSI), m_nSizeSI, pSlotCur->nSlotTimeOut);
                }
            }
            else
            {
                bNextExisting = true;

                pSlotNext = m_pChannel->GetSlotDataPtr(wNextTxSlotID);
                DEBUG_LOG("ChChgRRphase-First-Slave] CheckNext, found existingSO, NextTx : %d(%d), msg:%d,tmo:%d, C : %d, NS : %d, NI : %d, nextSI : %d(%d), sizeSI : %d\r\n",
                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), pSlotNext->uMsgID, pSlotNext->nSlotTimeOut,
                        cAisModem::getInst()->GetSlotNoCounter(), m_nNS, m_nNI, nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(nNextStartSI), m_nSizeSI);
            }

            if(wNextTxSlotID != SLOTID_NONE && (pSlotNext = m_pChannel->GetSlotDataPtr(wNextTxSlotID)))
            {
                bRet = true;

                if(!bNextExisting)
                    m_pChannel->SetFrameMapOneMsgColumn(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                    (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? CAisLib::GetRandomSlotTimeOut() : m_nRRChgTimeOutMin),    // Unconditionally get a new slot time-out
                                                    POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                else
                    SetPosSlotColumnCheckTmoSO(wNextTxSlotID, nNewPosMsgID, nNextStartSI, m_nSizeSI, true);

                if(bCurTxNewAlloc) {
                    m_pChannel->SetFrameMapOneMsgColumn(true, wTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                            (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? CAisLib::GetRandomSlotTimeOut() : m_nRRChgTimeOutMin),
                                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                }

                m_pChannel->SetFrameMapOneMsgColumn(true, wTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                        pSlotCur->nSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                if(pSlotCur->nSlotTimeOut <= 0)
                    pSlotCur->nSlotTimeOut = 1;
            }
        }

        if(bRet) {
            CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
        }
        else {
            wTxSlotID = nCurSI;
            wNextTxSlotID = m_nNextStartSI;
        }

        m_pChannel->SetLastTxSlotWithRange(wTxSlotID, wNextTxSlotID, true);

        m_nRRChgSlaveNewSI = SLOTID_NONE;
        m_nChangeRRphaseTxSlotId = wNextTxSlotID;
        m_nChangeRRphaseTxCnt = 0;

        DEBUG_LOG("ChChgRRphase-First-Slave] after, OK: %d, opMode:%d, C:%d, curTx:%d(%d), m:%d, tmo:%d, keep:%d, nextTx:%d(%d), m:%d, tmo:%d, NSS:%d, NS:%d, NI:%d, nextSI:%d(%d), sizeSI:%d, newTMO:%d, s:%d\r\n",
            bRet, CLayerNetwork::getInst()->m_nOpMode, cAisModem::getInst()->GetSlotNoCounter(),
            wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
            pSlotCur ? pSlotCur->uMsgID : -1, pSlotCur ? pSlotCur->nSlotTimeOut : -1, pSlotCur->bItdmaKeepFlag,
            wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
            pSlotNext ? pSlotNext->uMsgID : -1, pSlotNext ? pSlotNext->nSlotTimeOut : -1,
            m_nNSS, m_nNS, m_nNI, m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI), m_nSizeSI, m_nRRChgTimeOutMin, cTimerSys::getInst()->GetCurTimerSec());

        m_pChannel->SetChOpPhase(OPPHASE_CHG_RR_PREP_SECOND);
    }

    return bRet;
}

/**
 * @brief Process the second phase of changing the report rate
 */
void CChTxScheduler::ProcessPhaseChgReportRateSecond(void)
{
    //----------------------------------------------------------------------------------------------------
    // Refer to 1371-5 Ann.2 *******
    // refer to FIGURE 15
    //----------------------------------------------------------------------------------------------------

    const BYTE NUM_SLOT = 1;
    const INT8 nNewPosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    const bool bRunAssignedChgRR = (CLayerNetwork::getInst()->m_nOpMode == OPMODE_ASSIGNED_RR);
    const bool bRunAutoToAssignedChgRR = bRunAssignedChgRR && CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg == AIS_MSG_NO_ID_01;
    const bool bRunChgOpMode = CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg != nNewPosMsgID;
    FRAMEMAP_SLOTDATA *pSlotCur = NULL;
    FRAMEMAP_SLOTDATA *pSlotNext = NULL;
    WORD wTxSlotID = SLOTID_NONE;
    WORD wNextTxSlotID = SLOTID_NONE;
    WORD uSlotOffset = 0;
    bool bLastTxOfChgPhase = false;
    bool bIncNext = false;
    bool bRet = false;
    bool bCurTxNewAlloc = false;

    if(!m_pChannel->IsTxAvailableCh())
    {
        m_nChangeRRphaseTxSlotId = SLOTID_NONE;
        m_pChannel->SetChOpPhase(OPPHASE_ROUTINE);
        m_pChannel->SetChOpMode(CLayerNetwork::getInst()->CheckAssignedModeRunning() ? OPMODE_ASSIGNED_RR : OPMODE_CONTINUOUS);
        return;
    }

    if(m_nChangeRRphaseTxCnt < m_fChReportRate)
    {
        const int DIFF_SLOT_LIMIT_MAX = TX_CHECK_SLOT_SPACE+m_nSizeSI;
        int nCurSI = m_nNextStartSI;
        int nCheckEnd = CAisLib::FrameMapSlotIdAdd(nCurSI, m_nSizeSI-1);
        int nDiffSlot = CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, nCheckEnd);
        if(nDiffSlot <= DIFF_SLOT_LIMIT_MAX)  // 다음 송신에 임박했을때 처리
        {
            int nNextStartSI = CAisLib::FrameMapSlotIdAdd(nCurSI, m_nNI);

            DEBUG_LOG("ChChgRRphase-Scnd] before, cnt : %d, curTxSlot : %d(%d), NSS : %d, NS : %d, NI : %d, SI : %d(%d), sizeSI : %d, nextSI: %d, tmo : %d, prevMsg: %d\r\n",
                    m_nChangeRRphaseTxCnt, m_nChangeRRphaseTxSlotId, m_pChannel->GetSlotIdFromFrameSlotID(m_nChangeRRphaseTxSlotId),
                    m_nNSS, m_nNS, m_nNI, nCurSI, m_pChannel->GetSlotIdFromFrameSlotID(nCurSI),
                    m_nSizeSI, nNextStartSI, m_nRRChgTimeOutMin, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg);

            bLastTxOfChgPhase = (m_nChangeRRphaseTxCnt >= m_fChReportRate-1);   // Change RR phase 의 마지막 송신인가

            SetNextTempItdmaSI();

            wTxSlotID = m_nChangeRRphaseTxSlotId;

            if(wTxSlotID == SLOTID_NONE)
            {
                //--------------------------
                // Exception handling
                //--------------------------
                bCurTxNewAlloc = true;
                wTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, AIS_MSG_NO_ID_03, nCurSI, m_nSizeSI, 1);    // 슬롯이 할당되지 않았으면 재시도한다!
                if(wTxSlotID != SLOTID_NONE)
                    SetPosMsgSlotColumnRA(wTxSlotID, uSlotOffset, nNewPosMsgID, (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? TMO_UNKNOWN : m_nRRChgTimeOutMin));
            }
            if(wTxSlotID != SLOTID_NONE)
            {
                pSlotCur = m_pChannel->GetSlotDataPtr(wTxSlotID);

                bool bNextExisting = false;

                wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nNextStartSI, m_nSizeSI, CLayerNetwork::getInst()->m_nChgRRphasePrevPosMsg, false, true);    // 그 다음 SI 내에서 SOTDMA 예약된 슬롯을 찾는다

                if(bLastTxOfChgPhase)
                {
                    bRet = true;

                    // Change RR phase 의 마지막 송신 처리
                    uSlotOffset = 0;    // Change RR phase 의 마지막 송신이면 슬롯 오프셋은 제로(슬롯할당 종료)

                    m_pChannel->SetFrameMapOneMsg(true, wTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                    pSlotCur->nSlotTimeOut,    POS_REPORT_SCHEDULED, TDMA_SOTDMA, bCurTxNewAlloc ? AIS_MSG_NO_ID_03 : nNewPosMsgID,
                                                    0, true, false, 0, false);
                    SetPosSlotColumnCheckTmoSO(wTxSlotID, nNewPosMsgID, nCurSI, m_nSizeSI, true);
                }
                else
                {
                    bRet = true;

                    if(wNextTxSlotID == SLOTID_NONE)
                        wNextTxSlotID = m_pChannel->GetInternalAllocatedSlot_SO(nNextStartSI, m_nSizeSI, nNewPosMsgID, false, true);

                     if(wNextTxSlotID == SLOTID_NONE)
                    {
                        wNextTxSlotID = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(m_pChannel, nNewPosMsgID, nNextStartSI, m_nSizeSI, NUM_SLOT);    // SI 내에 송신할 SOTDMA 슬롯이 없으면 RATDMA 슬롯을 할당받는다.

                        if(wNextTxSlotID == SLOTID_NONE)
                        {
                            m_pChannel->SetFrameMapOneMsgColumn(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                            (CLayerNetwork::getInst()->IsRandomTimeoutRequired() ? CAisLib::GetRandomSlotTimeOut() : m_nRRChgTimeOutMin),    // Unconditionally get a new slot time-out
                                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                        }
                    }
                    else {
                        bNextExisting = true;
                    }

                    if(wNextTxSlotID != SLOTID_NONE && (pSlotNext = m_pChannel->GetSlotDataPtr(wNextTxSlotID)))
                    {
                        if(bNextExisting)
                        {
                            m_pChannel->SetFrameMapOneMsgColumn(true, wNextTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                                    pSlotNext->nSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                            //SetPosSlotColumnCheckTmoSO(wNextTxSlotID, nNewPosMsgID, nNextStartSI, m_nSizeSI, true);
                        }

                        if(bNextExisting)
                        {
                            m_pChannel->SetFrameMapOneMsgColumn(true, wTxSlotID, NUM_SLOT, cShip::getOwnShipInst()->GetOwnShipMMSI(), SLOTSTAT_INT_ALLOC,
                                                                    pSlotCur->nSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_SOTDMA, nNewPosMsgID, 0, false, false, 0, false);
                            SetPosSlotColumnCheckTmoSO(wTxSlotID, nNewPosMsgID, nCurSI, m_nSizeSI, true);
                        }
                        else
                        {
                            UINT uSlotOffset = CAisLib::FrameMapGetDiffSlotID(wTxSlotID, wNextTxSlotID);
                            SetPosMsgSlotColumnRA(wTxSlotID, uSlotOffset, nNewPosMsgID, pSlotCur->nSlotTimeOut);
                        }

                        m_pChannel->SetLastTxSlotWithRange(wTxSlotID, wNextTxSlotID, true);
                    }
                }

                if(bRet)
                {
                    m_nChangeRRphaseTxCnt++;
                   CLayerNetwork::getInst()->SetLastScheduledPosReportTime(m_pChannel);
                }

                if(bLastTxOfChgPhase)
                {
                    IncNS();
                    IncNextStartSI();

                    DEBUG_LOG("ChChgRRphase-Scnd] after, last, cnt:%d, opMode:%d, C:%d, curTx:%d(%d), m:%d, tmo:%d, nextEx: %d, nextTx:%d(%d), m:%d, tmo:%d, NSS:%d, NS:%d, NI:%d, nextSI:%d, sizeSI:%d, newTMO:%d, s:%d\r\n",
                            m_nChangeRRphaseTxCnt, CLayerNetwork::getInst()->m_nOpMode, cAisModem::getInst()->GetSlotNoCounter(),
                            wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                            pSlotCur ? pSlotCur->uMsgID : -1, pSlotCur ? pSlotCur->nSlotTimeOut : -1,
                            bNextExisting, wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
                            pSlotNext ? pSlotNext->uMsgID : -1, pSlotNext ? pSlotNext->nSlotTimeOut : -1,
                            m_nNSS, m_nNS, m_nNI, nNextStartSI, m_nSizeSI, m_nRRChgTimeOutMin, cTimerSys::getInst()->GetCurTimerSec());

                    m_nChangeRRphaseTxSlotId = SLOTID_NONE;
                    m_pChannel->SetChOpPhase(OPPHASE_ROUTINE);

                    if(CLayerNetwork::getInst()->CheckAssignedModeRunning())
                        m_pChannel->SetChOpMode(OPMODE_ASSIGNED_RR);            // 보고율 할당모드로 인한 보고율 변경 시 메시지 16 수신처리시 network layer 는 먼저 할당모드로 변경되고 지금 여기서 채널이 할당모드로 변경된다
                }
                else
                {
                    m_nChangeRRphaseTxSlotId = wNextTxSlotID;

                    IncNS();
                    IncNextStartSI();

                    DEBUG_LOG("ChChgRRphase-Scnd] after, cnt:%d, opMode:%d, C:%d, curTx:%d(%d), m:%d, tmo:%d, nextTx:%d(%d), m:%d, tmo:%d, NSS:%d, NS:%d, NI:%d, nextExist:%d, nextSI:%d(%d), sizeSI:%d, newTMO:%d, s:%d\r\n",
                            m_nChangeRRphaseTxCnt, CLayerNetwork::getInst()->m_nOpMode, cAisModem::getInst()->GetSlotNoCounter(),
                            wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                            pSlotCur ? pSlotCur->uMsgID : -1, pSlotCur ? pSlotCur->nSlotTimeOut : -1,
                            wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID), pSlotNext ? pSlotNext->uMsgID : -1, pSlotNext ? pSlotNext->nSlotTimeOut : -1,
                            m_nNSS, m_nNS, m_nNI, bNextExisting, nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(nNextStartSI), m_nSizeSI, m_nRRChgTimeOutMin, cTimerSys::getInst()->GetCurTimerSec());
                }
                bIncNext = true;
            }

            if(!bIncNext)
            {
                m_nChangeRRphaseTxCnt++;

                m_nChangeRRphaseTxSlotId = wNextTxSlotID;

                if(bLastTxOfChgPhase)
                {
                    IncNS();
                    IncNextStartSI();

                    FRAMEMAP_SLOTDATA *pSlotCurTx = m_pChannel->GetSlotDataPtr(wTxSlotID);
                    DEBUG_LOG("ChChgRRphase-Scnd] exception, last, cnt:%d, opMode:%d, C:%d, curTx:%d(%d), m:%d, tmo:%d, nextTx:%d(%d), m:%d, tmo:%d, NSS:%d, NS:%d, NI:%d, nextSI:%d, sizeSI:%d, newTMO:%d, s:%d\r\n",
                        m_nChangeRRphaseTxCnt, CLayerNetwork::getInst()->m_nOpMode, cAisModem::getInst()->GetSlotNoCounter(),
                        wTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wTxSlotID),
                        pSlotCurTx ? pSlotCurTx->uMsgID : -1, pSlotCurTx ? pSlotCurTx->nSlotTimeOut : -1,
                        wNextTxSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wNextTxSlotID),
                        pSlotNext ? pSlotNext->uMsgID : -1, pSlotNext ? pSlotNext->nSlotTimeOut : -1,
                        m_nNSS, m_nNS, m_nNI, nNextStartSI, m_nSizeSI, m_nRRChgTimeOutMin, cTimerSys::getInst()->GetCurTimerSec());

                    m_nChangeRRphaseTxSlotId = SLOTID_NONE;
                    m_pChannel->SetChOpPhase(OPPHASE_ROUTINE);

                    if(CLayerNetwork::getInst()->CheckAssignedModeRunning())
                        m_pChannel->SetChOpMode(OPMODE_ASSIGNED_RR);            // 보고율 할당모드로 인한 보고율 변경 시 메시지 16 수신처리시 network layer 는 먼저 할당모드로 변경되고 지금 여기서 채널이 할당모드로 변경된다
                }
                else
                {
                    m_nChangeRRphaseTxSlotId = wNextTxSlotID;

                    IncNS();
                    IncNextStartSI();
                }
            }
        }
        else if(nDiffSlot > (NUM_SLOT_PER_FRAME + TX_CHECK_SLOT_SPACE + m_nSizeSI))
        {
            DEBUG_LOG("ChChgRRphase-Scnd] DelayedTiming, diff:%d, chkEnd:%d(%d), nextSI:%d(%d), sizeSI:%d, NS:%d, NI:%d, lastTxSlot : %d, %d\r\n",
                    nDiffSlot, 
                    nCheckEnd, m_pChannel->GetSlotIdFromFrameSlotID(nCheckEnd),
                    m_nNextStartSI, m_pChannel->GetSlotIdFromFrameSlotID(m_nNextStartSI),
                    m_nSizeSI, m_nNS, m_nNI, m_nRoutineNextTxSlot, cTimerSys::getInst()->GetCurTimerSec());

            m_nChangeRRphaseTxSlotId = SLOTID_NONE;

            // Next Start SI를 계속 증가시키면 MSG2번이 반복적으로 생성되는 문제로 인해 막음.
        #if 0
            IncNS();
            IncNextStartSI();
        #endif
        }
    }

    ProcessTempUseReportByITDMA_Ch();
}

/**
 * @brief Reissue the assigned slot mode
 * @param wFrSlotIDStart The starting frame slot ID
 * @param nTimeOut The time-out value
 */
void CChTxScheduler::ReissueSlotAssignedMode(WORD wFrSlotIDStart, int nTimeOut)
{
    if(m_pChannel->IsTxAvailableCh())
    {
        m_pChannel->FrChangePosMsgModeTimeOut(wFrSlotIDStart, NUM_SLOT_PER_FRAME, nTimeOut);
    }
}

/**
 * @brief Run the assigned slot mode
 * @param uBaseStMMSI The base station MMSI
 * @param wRcvFrameID The received frame ID
 * @param wRcvSlotID The received slot ID
 * @param wOffset The offset value
 * @param wIncrementTotal The total increment value
 * @param wIncrementCh The channel increment value
 * @param nTimeOut The time-out value
 * @return True if the assigned slot mode is running, false otherwise
 */
bool CChTxScheduler::RunPhaseAssignedSlot(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD wIncrementTotal, WORD wIncrementCh, int nTimeOut)
{
    bool bRunAssignedSlotPhase = false;
    float fReportIntervalSec = INT_SLOT_TO_SEC(wIncrementCh);

    WORD wFrRcvSlotID = CAisLib::GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wFrStartSlotID = CAisLib::FrameMapSlotIdAdd(wFrRcvSlotID, wOffset);

    DEBUG_LOG("RunSlotAssignP] input, BaseSt : %09d, startSlot:%d(%d), rcv:%d,%d, off:%d, inc:%d,%d tmo:%d, opModeCh : %d\r\n",
            uBaseStMMSI, wFrStartSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wFrStartSlotID),
            wRcvFrameID, wRcvSlotID, wOffset, wIncrementTotal, wIncrementCh, nTimeOut, m_pChannel->GetChOpMode());

    switch(m_pChannel->GetChOpMode())
    {
    case OPMODE_CONTINUOUS:
    case OPMODE_ASSIGNED_RR:
        // 다음 SI 로 부터 한 프레임동안의 SOTDMA 예약슬롯은 time-out을 zero 로 설정하고, 이후 모든 프레임에서 해당 슬롯을 해제한다.
        m_pChannel->SetLastTxFrameAndFree(wFrStartSlotID);//m_nNextStartSI);
        bRunAssignedSlotPhase = true;
        break;

    case OPMODE_ASSIGNED_SLOT:
        {
            //----------------------------
            // assignment re-issued
            //----------------------------
            if(CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI == uBaseStMMSI)
            {
                DEBUG_LOG("[ASSS-StartSlotAssignP] re-issue OK, tmo:%d, startSlot:%d(%d), RIsec:%.1f, NSS:%d, NS:%d, NI:%d, SI:%d, sizeSI:%d\r\n",
                        nTimeOut, wFrStartSlotID, m_pChannel->GetSlotIdFromFrameSlotID(wFrStartSlotID),
                        m_fChReportIntervalSec, m_nNSS, m_nNS, m_nNI, m_nNextStartSI, m_nSizeSI);
                ReissueSlotAssignedMode(wFrStartSlotID, nTimeOut);
            }
            else
            {
                DEBUG_LOG("[ASSS-StartSlotAssignP] ignore re-issue, baseSt different, %09d, %09d\r\n",
                        CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI, uBaseStMMSI);
            }
        }
        break;
    }

    if(bRunAssignedSlotPhase)
    {
        if(uBaseStMMSI != AIS_AB_MMSI_NULL)
        {
            bRunAssignedSlotPhase = m_pChannel->UpdateAssignedSlotsFATDMAtoSOTDMA(uBaseStMMSI, wRcvFrameID, wRcvSlotID, wOffset, wIncrementCh, nTimeOut);
        }

        if(bRunAssignedSlotPhase)
        {
            CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI = uBaseStMMSI;
            SetChReportInterval(wIncrementTotal, fReportIntervalSec);
            SetNSS(wFrStartSlotID);
            ResetNextStartSI();

            DEBUG_LOG("[ASSS-StartSlotAssignP] RIsec:%.1f, NSS:%d, NS:%d, NI:%d, SI:%d, sizeSI:%d\r\n",
                    m_fChReportIntervalSec, m_nNSS, m_nNS, m_nNI, m_nNextStartSI, m_nSizeSI);
        }
        else
        {
            WARNING_LOG("[ASSS-RunSlotAssignedMode FAIL!");
        }
    }
    return bRunAssignedSlotPhase;
}

/**
 * @brief Terminate the assigned slot mode
 * @param wLastAssignedTxSlot The last assigned transmit slot
 */
void CChTxScheduler::TerminateSlotAssignedMode(WORD wLastAssignedTxSlot)
{
    //--------------------------------------------------------------------------------------------------------------------------------
    // 입력  wLastAssignedTxSlot : 슬롯할당모드를 종료하고 자동모드로 전환시 호출될때 슬롯할당모드에서 송신할 마지막 슬롯번호를 지정
    //--------------------------------------------------------------------------------------------------------------------------------

    if(m_pChannel->GetChOpMode() == OPMODE_ASSIGNED_SLOT)
    {
        DEBUG_LOG("[ASSS-TerminateSlotAssignMode] before, lastAssignedSlot : %d,%d \r\n",
                wLastAssignedTxSlot, m_pChannel->GetSlotIdFromFrameSlotID(wLastAssignedTxSlot));

        if(wLastAssignedTxSlot != SLOTID_NONE)
        {
            //---------------------------------------------------------------------
            // 타임아웃에 의한 종료가 아닐 경우 실행중이던 슬롯할당 모드 중단
            // 할당되었던 슬롯들은 모두 기지국에 의한 FATDMA 슬롯으로 재설정해야함
            // 마지막 할당모드 송신슬롯의 다음 슬롯부터 FATDMA 슬롯으로 복귀
            //---------------------------------------------------------------------
            m_pChannel->RestoreAssginedSlotsSOTDMAtoFATDMA( CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI,
            													CAisLib::FrameMapSlotIdAdd(wLastAssignedTxSlot, 1) );
        }

        CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI = AIS_AB_MMSI_NULL;
    }
}

/**
 * @brief Prepare to transmit frame map slot
 * @param wMapSlotID Map slot ID
 * @return true if success, false otherwise
 */
bool CChTxScheduler::PrepareTransmitFrameMapSlot(WORD wMapSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotInfo = NULL;
    bool bTxOK = false;

    // If TX is not available, print out warning log every 3 seconds.
    if(!CVdlTxMgr::IsTxAvailable())
    {
        static DWORD dwOldTick = 0;
        if(SysGetDiffTimeScnd(dwOldTick) > 3)
        {
            WARNING_LOG(" Critical Error! TX not available, rcvOnly:%d(%d), validMMSI:%d(%09d), Hw-shutdown:%d\r\n",
                    CLayerNetwork::getInst()->IsSilentMode(), CSetupMgr::getInst()->GetEnableSilentMode(),
					CAisLib::IsValidMMSI_MobileSt(cShip::getOwnShipInst()->GetOwnShipMMSI()),
					cShip::getOwnShipInst()->GetOwnShipMMSI(), CLayerPhysical::getInst()->IsTxHwShutdownOccurred());

            dwOldTick = SysGetSystemTimer();
        }

        return false;
    }

    // Get slot data if slot is reserved for internal tx.
    if((pSlotInfo = m_pChannel->GetSlotDataPtr(wMapSlotID)))
    {
        if(m_pChannel->IsIntTxReservedSlot(pSlotInfo))
        {
            // Make VDL TX message
            bTxOK = CVdlTxMgr::MakeVdlTxMsg(m_pChannel, wMapSlotID);
            CLayerNetwork::m_pLastTxCH = m_pChannel;
        }
    }
    return bTxOK;
}

/**
 * @brief Reset the last scheduled transmit slot ID
 */
void CChTxScheduler::ResetLastScheduledTxSlotID(void)
{
    m_nLastScheduledTxSlotID = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, 1);
}

/**
 * @brief Process the transmit
 */
void CChTxScheduler::ProcessTransmit(void)
{
    //-------------------------------------------
    // Frame map 에 예약된 대로 송신을 실행한다.
    //-------------------------------------------
    if(!CLayerNetwork::getInst()->IsPhaseTxAvailable())
        return;

    // 한번 호출되면 예약된 NUM_TXSLOT_AHEAD 개의 슬롯을 송신한다! 송신메시지 생성에 필요한 여분의 시간을 줘야한다!
    WORD wEndSlotID = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, NUM_TXSLOT_AHEAD);
    int nDiff = CAisLib::FrameMapGetDiffSlotID(m_nLastScheduledTxSlotID, wEndSlotID);
    if(nDiff > NUM_TXSLOT_AHEAD)
    {
        ResetLastScheduledTxSlotID();
    }

    int nStx = 0;
    int nEnd = 0;
    if (m_nLastScheduledTxSlotID < wEndSlotID)
        nEnd = wEndSlotID - m_nLastScheduledTxSlotID;
    else
        nEnd = (wEndSlotID + NUM_SLOT_FRAMEMAP) - m_nLastScheduledTxSlotID;

    while(nStx < nEnd)
    {
        int nNumTxSlots = 0;

        PrepareTransmitFrameMapSlot(m_nLastScheduledTxSlotID);

        nNumTxSlots = m_pChannel->FreeFrameMapOneMsg(m_nLastScheduledTxSlotID);
        m_nLastScheduledTxSlotID = CAisLib::FrameMapSlotIdAdd(m_nLastScheduledTxSlotID, nNumTxSlots);
        nStx += nNumTxSlots;
    }
}

/**
 * @brief Process the operation phase
 */
void CChTxScheduler::ProcOpPhase(void)
{
    switch(m_pChannel->GetChOpPhase())
    {
    case OPPHASE_ONBOOT_WAITSYNC:
    case OPPHASE_MONITOR_VDL:
    case OPPHASE_NETWORKENTRY:
        break;

    case OPPHASE_FIRST_FRAME:
        ProcessPhaseFirstFrame();
        break;
    case OPPHASE_ROUTINE:
        ProcessPhaseRoutine();
        break;
    case OPPHASE_CHG_RR_PREP_FIRST:
        ProcessPhaseChgReportRateFirst();
        break;
    case OPPHASE_CHG_RR_PREP_SECOND:
        ProcessPhaseChgReportRateSecond();
        break;

    case OPPHASE_CH_CHG_PHASE:
    case OPPHASE_DIAGNOSTIC:
        break;
    }
}

/**
 * @brief Process the slot change time
 */
void CChTxScheduler::ProcessSlotChangeTime_Scheduler(void)
{
    ProcOpPhase();
}

/**
 * @brief Run the process transmit scheduler
 */
void CChTxScheduler::RunProcessTxScheduler(void)
{
    ProcessTransmit();
}

/**
 * @brief Run periodically transmit scheduler
 */
void CChTxScheduler::RunPeriodicallyTxScheduler(void)
{
    //-------------------------
    // it's called every 1 sec
    //-------------------------

    if( m_pChannel->GetChOpPhase() != OPPHASE_MONITOR_VDL &&
        m_pChannel->GetChOpPhase() != OPPHASE_NETWORKENTRY &&
        m_pChannel->GetChOpPhase() != OPPHASE_DIAGNOSTIC)
    {
        ProcessPeriodicStaticVoyageMsgTx(false);
        ProcessLongRangeMsgTx();
    }
}
