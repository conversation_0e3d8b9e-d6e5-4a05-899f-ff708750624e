/**
 * @file    Vdm.h
 * @brief   Vdm header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Sentence.h"

#ifndef __VDM_H__
#define __VDM_H__

/******************************************************************************
 * 
 * VDM - VHF Data-link Message
 * 
 * !--VDM,x,x,x,a,s--s,x*hh<CR><LF>
 *        | | | |  |   |
 *        1 2 3 4  5   6
 *
 * 1. Total number of sentences needed to transfer the message, 1 to 9
 * 2. Sentence number, 1 to 9
 * 3. Sequential message identifier, 0 to 9
 * 4. AIS Channel, "A" or "B"
 * 5. Encapsulated ITU-R M.1371 radio message
 * 6. Number of fill-bits, 0 to 5
 *
 ******************************************************************************/
class CVdm : public CSentence
{
public:
    CVdm();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool   Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the VDM sentence
     * @param pszSentence Output buffer for the generated sentence
     * @param pEncBuff Encoded source data
     * @param nRxChNo Receive channel number (A or B)
     * @param nNumSentences Total number of sentences needed to transfer the message
     * @param nSentenceNo Sentence number
     * @param wSeqMsgID Sequential message identifier
     * @param wFillBits Number of fill-bits
     */
    static void MakeSentenceVDM(char *pszSentence, 
                                const char *pEncBuff, 
                                int nRxChNo, 
                                int nNumSentences, 
                                int nSentenceNo, 
                                int8_t wSeqMsgID, 
                                uint16_t wFillBits);

    /**
     * @brief Make the VDM sentence
     * @param pszSentence Output buffer for the generated sentence
     * @param nRxChNo Receive channel number (A or B)
     * @param pPacketData Binary packet data to encode
     * @param nPacketSize Size of the packet data in bytes
     * @return The length of the generated sentence
     */
    static int32_t MakeSentence(char *pszSentence, int nRxChNo, uint8_t *pPacketData, int nPacketSize);

public:
    static int8_t m_nSequentialId;
    static char   m_pszEncodedBuff[MAX_06ASCII_BUFF];
};

#endif /* __VDM_H__ */

