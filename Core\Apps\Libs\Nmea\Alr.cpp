/**
 * @file    Alr.cpp
 * @brief   Alr class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "AlarmThing.h"
#include "Alr.h"

/******************************************************************************
 * 
 * ALR - Set alarm state
 *
 * $--ALR,hhmmss.ss,xxx,A,A,c--c*hh<CR><LF>
 *         |         |  | |   |
 *         1         2  3 4   5
 *
 * 1. Time of alarm condition change, UTC
 * 2. Local alarm number(identifier)[identification number of alarm source]
 * 3. Alarm condition (A=threshold exceeded, V=not exceeded)
 * 4. <PERSON><PERSON>'s acknowledge state, A=acknowledged V=unacknowledge
 * 5. <PERSON><PERSON>'s description text
 *
 ******************************************************************************/
CAlr::CAlr() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAlr::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the ALR sentence
 * @param pszSentence The sentence to be made
 * @param nAlrID The alarm ID
 * @param pAlrTime The alarm time
 * @param nAlrStat The alarm status
 * @param pstrAlarmTxt The alarm text
 * @return The length of the sentence
 */
int32_t CAlr::MakeSentence(char *pszSentence, int nAlrID, SYS_DATE_TIME *pAlrTime, _tagBAMAlertStat nAlrStat, char *pstrAlarmTxt)
{
    char pstrTmp[RX_MAX_DATA_SIZE];

    if(pAlrTime->nValid)
        sprintf(pszSentence, "$AIALR,%02d%02d%02d,", pAlrTime->xTime.nHour, pAlrTime->xTime.nMin, pAlrTime->xTime.nSec);
    else
        strcpy(pszSentence, "$AIALR,,");

    sprintf(pstrTmp, "%03d,%c,%c,", nAlrID,
        CAlarmThing::IsAlarmStatOccurred(nAlrStat) ? 'A' : 'V', CAlarmThing::IsAlarmStatOccurred(nAlrStat) ? 'A' : 'V');
    strcat(pszSentence, pstrTmp);
    strcat(pszSentence, pstrAlarmTxt);

    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}

/**
 * @brief Make the ALR sentence
 * @param pszSentence The sentence to be made
 * @param pAlarmThing The alarm thing structure
 * @return The length of the sentence
 */
int32_t CAlr::MakeSentence(char *pszSentence, CAlarmThing *pAlarmThing)
{
    return MakeSentence(pszSentence, 
                        pAlarmThing->GetAlarmID(), 
                        pAlarmThing->GetAlarmTime(), 
                        pAlarmThing->GetAlertStat(), 
                        pAlarmThing->GetAlarmMsg());
}