#ifndef __ROSMGR_H__
#define __ROSMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AisLib.h"

//=============================================================================
// Defines
//----------------------------------------------------------------------------
// ITU-R M.1371-5(4.1.5) and IALA Volume I Part II : Page- 92
// Transition zone size:
// Default: 5 nautical miles
// Possible range: 1 to 8 nautical miles in steps of 1 nautical mile
#define TRZONE_SIZE_MIN                         1
#define TRZONE_SIZE_MAX                         8
#define TRZONE_SIZE_DFLT                        5

//----------------------------------------------------------------------------
// IALA Volume I Part II : Page- 92
// Any Class A shipborne mobile AIS station can internally store information for eight
// different regions. This gives the possibility to “download” to Class A shipborne mobile
// AIS stations information for several regions (for example covering inland waterways)
// from one shore station.
#define MAX_ROS_DATA_SIZE                       8

//----------------------------------------------------------------------------
// IALA Volume I Part II : 18.4.2 Channel management by automatic means
// It is recommended that Basic AIS Service “Switch AIS VDL channels via AIS VDL”
// should be used by a competent authority for all regions at least every 10 minutes.
#define ROS_TIMEOUT_BY_MSG22                    600  // 10 min

#define ROS_IDX_HIGHSEA                         (MAX_ROS_DATA_SIZE + 0)
#define ROS_IDX_NULL                            (-1)

// Define Zone
#define ROS_MODE_HIGH_SEA_ZONE                  0    // Default Zone
#define ROS_MODE_OUTER_TR_ZONE                  1    // Outer TR_Zone
#define ROS_MODE_INNER_TR_ZONE                  2    // Inner TR_Zone
#define ROS_MODE_INNER_ZONE                     3    // Inner Region

// Define ROS sources
#define ROS_SRC_MSG_BROAD                       1    // via MSG 22
#define ROS_SRC_MSG_ADDRD                       2    // via MSG 22
#define ROS_SRC_DSC                             3    // via DSC
#define ROS_SRC_MKD                             4    // via MKD (Manual Input)
#define ROS_SRC_PI                              5    // via PI  (ACA sentence)
#define ROS_SRC_MIN                             ROS_SRC_DFLT
#define ROS_SRC_MAX                             ROS_SRC_PI
#define ROS_SRC_DFLT                            0    // default

//----------------------------------------------------------------------------
#define ROA_NULL                                (0)  // High Sea Region
#define ROA_UP                                  (1)
#define ROA_DOWN                                (2)
#define ROA_RIGHT                               (3)
#define ROA_LEFT                                (4)

//----------------------------------------------------------------------------
// ROS Error Code
#define ROS_ERR_NO_ERR                          (0)
#define ROS_ERR_TOO_SMALL_ROA                   (1)
#define ROS_ERR_TOO_LARGE_ROA                   (2)

#define ROS_ERR_ROA_CORNER_INVALID              (5)
#define ROS_ERR_ROA_OVERLAP_2HOURS              (6)
#define ROS_ERR_ROS_DATA                        (7)

//----------------------------------------------------------------------------
// refer to ACA sentence in IEC-61162
#define TRXMODE_TXARXA_TXBRXB                   0        // ACA, Msg 22, Mag23, DSC // Value of 0, transmit on channels A and B, receive on channels A and B,
#define TRXMODE_TXARXA____RXB                   1        // ACA, Msg 22, Mag23, DSC // Value of 1, transmit on channel A, receive on channels A and B,
#define TRXMODE____RXA_TXBRXB                   2        // ACA, Msg 22, Mag23      // Value of 2, transmit on channel B, receive on channels A and B,
#define TRXMODE____RXA____RXB                   3        // ACA,                    // Value of 3, do not transmit, receive on channels A and B,
#define TRXMODE____RXA_______                   4        // ACA,                    // Value of 4, do not transmit, receive on channel A,
#define TRXMODE___________RXB                   5        // ACA,                    // Value of 5, do not transmit, receive on channel B.
// The belows are defined by me, not defined in IEC-61162
#define TRXMODE_TXARXA_______                   6        //                     DSC only, Single Channel mode, not defined in ACA sentence, definition only for internal use!
#define TRXMODE_NONE                            7

#define TRXMODE_DFLT                            TRXMODE_TXARXA_TXBRXB
#define TRXMODE_MIN                             TRXMODE_TXARXA_TXBRXB
#define TRXMODE_MAX                             TRXMODE_TXARXA_______
#define VDL_TRXMODE_MAX                         TRXMODE____RXA_TXBRXB
#define ACA_TRXMODE_MAX                         TRXMODE___________RXB

//----------------------------------------------------------------------------
// the lower number, the higher priority
#define TRXMODE_BY_TRZONE                       0
#define TRXMODE_BY_MSG22_ADDRSD                 1
#define TRXMODE_BY_MSG23                        2
#define TRXMODE_BY_MSG22_BRDCST                 3
#define TRXMODE_BY_DEFAULT                      4
#define TRXMODE_BY_UNKNOWN                      5

//----------------------------------------------------------------------------
// Low power mode operation based on Ship type, NavStatus and SOG
#define POWER_MODE_DEFAULT                      (0)
#define POWER_MODE_ACTIVE_LOW                   (1)
#define POWER_MODE_INACTIVE_LOW                 (2)

//---------------------------------------------------------------------------
typedef  struct
{
    INT8        nRosIdx;
    INT8        nRosMode;
    INT8        nRosAdjIdx;
    CH_SETUP    sChSetup;
} ROS_DATA;

typedef  struct
{
    UCHAR    bValidMode;            // ROS data valid flag
    UCHAR    bTxRxMode;             // TRX mode
    UCHAR    bTxPower;              // TX power
    UCHAR    bTrZoneSize;           // TR Zone size
    UCHAR    bRosSource;            // ROS source

    UCHAR    bBandwidthA;           // Channel A Bandwidth
    UCHAR    bBandwidthB;           // Channel B Bandwidth
    HWORD    wChannelNoA;           // Channel A Number
    HWORD    wChannelNoB;           // Channel B Number

    DWORD    dSrcMMSI;              // Source MMSI

    POS_ALLH xPointNE;              // Region NE position
    POS_ALLH xPointSW;              // Region SW position

    SYS_DATE_TIME xRcvTime;         // Received ROS time
    DWORD         dwRcvSysSec;      // Received ROS second
} xROSDATA;

//---------------------------------------------------------------------------
typedef  struct
{
    POS_ALLH xRectNE;               // +-------------+
    POS_ALLH xRectNW;               // |             |
    POS_ALLH xRectSE;               // |             |
    POS_ALLH xRectSW;               // +-------------+

    // inner Zone
    POS_ALLH xInnerNE;              //  +------------------+
    POS_ALLH xInnerNW;              //  | +--------------+ |
    POS_ALLH xInnerSE;              //  | +--------------+ |
    POS_ALLH xInnerSW;              //  +------------------+

    // outer Zone
    POS_ALLH xOuterNE;
    POS_ALLH xOuterNW;
    POS_ALLH xOuterSE;
    POS_ALLH xOuterSW;
}xROADATA;
//===========================================================================

// Regional operation setting class
class CROSMgr
{
public:
    CROSMgr();
    virtual ~CROSMgr(void);

    static std::shared_ptr<CROSMgr> getInst() {
        static std::shared_ptr<CROSMgr> pInst = std::make_shared<CROSMgr>();
        return pInst;
    }

public:
    /**
     * @brief Initialize ROS setup
     */
    void    InitRosSetup();
    /**
     * @brief Clear ROS and ROA data
     */
    void    ClearAllRosData(void);
    void    ClearOneRosData(int nPos);
    void    ClearOneRosData(xROSDATA *pRosData);
    void    ClearOneRoaData(xROADATA *pRoaData);
    void    ClearOneRosData(xROSDATA *pRosData, xROADATA *pRoaData);

    bool    IsRosIndexValid_Ext(int nRosIndex);
    bool    IsRosIndexValid_Int(int nRosIndex);
    bool    IsRosInUse(int nRosIndex);
    void    SetRosData(ROS_DATA *psRosData);
    void    SetTrxModeChData(int nTxRxMode, int nTxRxModeBy, int nChA, int nChB);
    void    SetChSetupData(CH_SETUP *psRosSetup, bool bSetTrxMode);
    int     SerializeRosData(UCHAR *pBackData);
    bool    LoadRosConfigData(UCHAR *pBackData);
    void    VerifyRosData(void);
    void    ClearRosData(void);

    void    SetDefaultRoaData(xROADATA *pRoaData);
    void    SetDefaultRosData(xROSDATA *pRosData);
    void    SetDefaultRosData(xROSDATA *pRosData, xROADATA *pRoaData);

    REAL    GetDistanceROAtoROA(xROADATA *pROA1, xROADATA *pROA2);
    int     GetMostDistantROSfromOwnShip();
    int     GetMostDistantROSfromROS(xROADATA *pRoa);
    void    SaveNewROS(xROSDATA *pRosData, xROADATA *pRoaData, int nRosIndex);
    bool    CheckRosRectValid(xROSDATA *pRosData);
    bool    CheckRosDataValid(xROSDATA *pRosData);

    int     UpdateRosData(xROSDATA *pRosData, int nRosIndex=-1);
    void    UpdateAddressedRosData(xROSDATA *psRosData, bool bCheckCurRosMode);

    int     CheckRoaSizeIsValid(xROADATA *pRoaData);
    bool    CheckSameRoaExist(xROSDATA *pRosData, xROADATA *pRoaData);
    void    RemoveOverlappedROA(xROSDATA *pRosData, xROADATA *pRoaData);
    int     GetNumValidROS(void);

    bool    ROAOverlapRule1(xROADATA *pROA, POS_ALLH *pNE, POS_ALLH *pSW, POS_ALLH *pNW, POS_ALLH *pSE);
    bool    ROAOverlapRule2(xROADATA *pROA, POS_ALLH *pNE, POS_ALLH *pSW, POS_ALLH *pNW, POS_ALLH *pSE);
    bool    ROAOverlapRule3(xROADATA *pROA, POS_ALLH *pNE, POS_ALLH *pSW, POS_ALLH *pNW, POS_ALLH *pSE);
    bool    ROAOverlapRule4(xROADATA *pOldROA, xROADATA *pNewROA);

    bool    CheckInsideRectangleXX(POS_GRID *pTestPnt, POS_GRID *pNE, POS_GRID *pSW);
    bool    CheckInsideRectangleXY(POS_GRID *pTestPnt, int nLonNE, int nLatNE, int nLonSW, int nLatSW);
    bool    CheckInsideRectangleXZ(POS_GRID *pTestPnt, POS_GRID *pRectNE, POS_GRID *pRectSW);

    bool    CheckROAOverlapMatch(xROADATA *pOldROA, xROADATA *pNewROA);
    bool    IsROAInside3Corner(POS_ALLH *pHalfPosP);
    bool    CheckROA8nmRule(xROADATA *pROA);
    int     CheckIgnoreRosByPI(xROSDATA *pROS, xROADATA *pROA);

    void    CalcAllRoaDataByROS(void);
    void    CalcOneRoaDataByROS(xROSDATA *pROS, xROADATA *pROA);

    REAL    GetDistanceBoundary(FLOAT fShipLat, FLOAT fShipLon, POS_ALLH *pROA1, POS_ALLH *pROA2);
    REAL    GetDistanceNearestROABoundary(FLOAT fShipLat, FLOAT fShipLon, xROADATA *pROA);
    int     GetNearestROA(POS_GRFP *pShipPos, int nIndexX, int nIndexY);
    int     GetNearestROA2(POS_GRFP *pShipPos, int nTrRoaType, int nIndexX, int nIndexY);

    int     CheckInsideOverlapOuterRegion(POS_GRFP *pShipPos, int nExceptRosIndex);

    void    SetInnerOuterZoneByROA(xROADATA *pROA, int nTrZoneSizeNM);

    int     CheckFromInnerZoneToTrZone(POS_GRFP *pShipPos, xROADATA *pROA);

    bool    CheckROADataOver500NM(void);
    bool    CheckROSData24Hours(SYS_DATE_TIME *pCurrUTC);
    bool    CheckRemoveObsoleteROSData(void);

    int     IsInsidePolygon(POS_GRID *pShipPos, POS_GRID *pPolygon, int nPoints);

    int     CheckCurRegionToAdjacentRegion(POS_GRFP *pShipPos, int nCurrROS, int nTrRoaType);
    int     CheckInsideTrZoneType(POS_GRFP *pShipPos, xROADATA *pROA);
    int     GetCurrRosNo(void);
    xROSDATA *GetCurrRosDataPtr(void);
    xROADATA *GetCurrRoaDataPtr(void);
    int     GetRoaRunMode(void);
    int     IsRoaRunModeTRZone(void);

    bool    IsTrMode(int nRosMode);
    bool    IsTrModeRunning(void);

    int     GetOuterRosOwnShipLocated(int nExceptRos);
    int     GetAdjacentRos(int nCurRos);
    void    GetMatchedRosToTransit(INT8 *pnCurRosMode, INT8 *pnCurRosIdx, INT8 *pnAdjRosIdx);

    bool    GetRosDataPosBelongsTo(ROS_DATA *psRosSetup);
    void    ProcessRosModeTransit(void);

    bool    IsTrModeAvailable(int nRosMode, CH_SETUP *psChSetup);

    bool    IsLowPowerModeRequired(void);
    bool    SetTxPowerModeROS(UINT8 uTxPower);

    bool    IsSameNewTrxModeSetup(int nNewRosMode, CH_SETUP *psNewChSetup, INT8 *pnTxRxModeToSet, INT8 *pnTRxModeMethodToSet);
    bool    GetNewTrxModeByPriority(int nNewRosMode, CH_SETUP *psNewChSetup, INT8 *pnTxRxModeToSet, INT8 *pnTRxModeMethodToSet);
    void    SetTxRxMode(INT8 nNewTxRxMode, INT8 nNewTRxModeMethod);
    void    SetTxRxModeChEnable(void);

    void    RunPeriodically(void);

    bool    CheckOwnShipInsideOfOuterTrZone(int niRos);
    bool    CheckOwnShipInsideOfInnerTrZone(int niRos);
    bool    CheckOwnShipInsideOfInnerZone(int niRos);

    int     GetTrxModeByFromRosSetup(int nRosMode, int nRosSource);

    bool    IsRosDataDefaultSetting(xROSDATA *psRosData);
    bool    IsSameChSetupData(CH_SETUP *psRosSetup1, CH_SETUP *psRosSetup2, bool bCheckTrxMode, bool bCheckTrxModeBy, bool bCheckPowerMode);

public:
    xROSDATA        *m_vAllRosDATA;
    xROADATA        *m_vAllRoaDATA;

    CH_SETUP        m_sChSetup;
    ROS_DATA        m_sRosData;

    int             m_nRosInUse;        // Current used ROS index
    SYS_DATE_TIME   m_xRosInUseTime;    // Current used ROS in-use time

    DWORD           m_dwTRxModeStartSec;

    int             m_nLowPowerModeByNavStatus;

    bool            m_bCheckTrxModeByAddrMsg22;
    bool            m_bTrxModeByAddrMsg22Done;
    DWORD           m_dwTRxModeStartSecByAddrMsg22;
};

#endif//__ROSMGR_H__
