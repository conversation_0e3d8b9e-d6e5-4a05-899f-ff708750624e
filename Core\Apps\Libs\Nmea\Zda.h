/**
 * @file    Zda.h
 * @brief   Zda header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __ZDA_H__
#define __ZDA_H__

/******************************************************************************
 * 
 * ZDA - 
 *
 * $--ZDA, hhmmss.ss,xx,xx,xxxx,xx,xx*hh<CR><LF>
 *           |       |  |   |   |  |
 *           1       2  3   4   5  6
 *
 * 1. UTC
 * 2. Day, 01 to 31
 * 3. Month, 01 to 12 (UTC)
 * 4. Year (UTC)
 * 5. Local zone hours
 * 6. Local zone minutes
 * 
 ******************************************************************************/
class CZda : public CSentence
{
public:
    CZda();
    void ClearData();

    bool Parse(const char *pszSentence);
    bool IsValidUtcData(void);

    SYS_DATE GetUtcDate(void);
    SYS_TIME GetUtcTime(void);
    void RunPeriodically(void);

protected:
    SYS_DATE m_xUtcDate;
    SYS_TIME m_xUtcTime;

    DWORD   m_dwUtcValidTick;
};

#endif /* __ZDA_H__ */

