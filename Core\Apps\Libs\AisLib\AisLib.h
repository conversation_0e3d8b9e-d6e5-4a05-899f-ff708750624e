/**
 * @file    AisLib.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"

#ifndef  __AISLIB_H__
#define __AISLIB_H__

//=================================================================
#define DTTM_BASE_YEAR              (2000)
#define DTTM_ONE_HOUR_SECONDS       (3600)  // 60 * 60
#define DTTM_ONE_DAY_SECONDS        (24 * DTTM_ONE_HOUR_SECONDS)
//-----------------------------------------------------------------
#define DTTM_YEAR_NULL              (0)
#define DTTM_YEAR_MIN               (2000)
#define DTTM_YEAR_MAX               (2099)
#define DTTM_MONTH_NULL             (0)
#define DTTM_MONTH_MIN              (1)
#define DTTM_MONTH_MAX              (12)
#define DTTM_DAY_NULL               (0)
#define DTTM_DAY_MIN                (1)
#define DTTM_DAY_MAX                (31)
#define DTTM_HOUR_NULL              (24)
#define DTTM_HOUR_MIN               (0)
#define DTTM_HOUR_MAX               (23)
#define DTTM_MIN_NULL               (60)
#define DTTM_MIN_MIN                (0)
#define DTTM_MIN_MAX                (59)
#define DTTM_SEC_NULL               (60)
#define DTTM_SEC_MIN                (0)
#define DTTM_SEC_MAX                (59)

#define ETA_MONTH_NULL              (0)
#define ETA_MONTH_MIN               (0)
#define ETA_MONTH_MAX               (12)
#define ETA_DAY_NULL                (0)
#define ETA_DAY_MIN                 (0)
#define ETA_DAY_MAX                 (31)
#define ETA_HOUR_NULL               (24)
#define ETA_HOUR_MIN                (0)
#define ETA_HOUR_MAX                (24)
#define ETA_MIN_NULL                (60)
#define ETA_MIN_MIN                 (0)
#define ETA_MIN_MAX                 (60)
#define ETA_SEC_NULL                (60)
#define ETA_SEC_MIN                 (0)
#define ETA_SEC_MAX                 (60)

class CAisLib
{
public:
    //============================================================================
    static int      GetDaysInMonth(int nYear, int nMonth);
    static int      GetDaysByDate(int nYear, int nMonth, int nDay);
    static int      GetDiffTimeSeconds(SYS_DATE_TIME *pCurrTime, SYS_DATE_TIME *pPastTime);
    //============================================================================
    static UCHAR    ConvertAsc06ToAscii(UCHAR bAsc06);
    static UCHAR    ConvertBin06ToAscii(UCHAR bBin06);
    static UCHAR    ConvertAsciiToBin06(UCHAR bAscii);
    static DWORD    GetAisMsgFieldINT(UCHAR *pAisMsg, int nStartBit, int nNumOfBits);
    static int      GetAisMsgFieldSTR(UCHAR *pAisMsg, char *pString, int nStartBit, int nNumOfBits);
    //----------------------------------------------------------------------------
    static int      MakeBin08PacketToAsc06Str(UCHAR *pBin08Str, int nBin08Len, UCHAR *pAsc06Str, HWORD *pAsc06Len);
    //----------------------------------------------------------------------------
    static int      MakeValidAisAsciiString(char *pAsciiStr, int nAsciiLen);
    static BOOL     ConvertNormalString(const char *pszText, char *pszBuf, int nBuffSize);
    static BOOL     IsNMEAReservedCharater(char ch);
    static char*    ConvertNMEAString(const char *pszText);
    //------------------------------------------------------------------------
    static void     SetAisTxMsgFieldPack(UCHAR *pAisMsg, int nStartBit, int nBitWidth, DWORD dData);
    static void     SetAisTxMsgFieldPackY(UCHAR *pAisMsg, int nStartBit, int nBitWidth, UCHAR *pData);
    static void     SetAisTxMsgFieldPackStr(UCHAR *pAisMsg, int nStartBit, int nBitWidth, char *pData, int nLenStr);
    //------------------------------------------------------------------------
    static void     SetAisTxMsgFieldDataX(UCHAR *pAisMsg, int nStartBit, int nBitWidth, DWORD dData);
    static void     SetAisTxMsgFieldDataY(UCHAR *pAisMsg, int nStartBit, int nBitWidth, UCHAR *pData);
    //------------------------------------------------------------------------
    static void     ConvertAisTxDataToPack(UCHAR *pTxPack, UCHAR *pTxData, int nBitWidth);
    //============================================================================
    static int      IsValidAisFloatPOS(POS_FLOAT *pPosF);
    static int      IsValidAisGridPOS(POS_GRID *pPosG);
    static int      IsValidAisHighPOS(POS_HIGH *pPosF);
    static int      IsValidAisLowPOS(POS_LOW *pPosH);
    //----------------------------------------------------------------------------
    static AISFF    AisGridLatToHigh(LGRID nData);
    static AISFF    AisGridLonToHigh(LGRID nData);
    static LGRID    AisHighPosLatToGrid(AISFF nData);
    static LGRID    AisHighPosLonToGrid(AISFF nData);
    //------------------------------------------------------------------------
    static AISFF    AisGridLatToLowLat(LGRID nData);
    static AISFF    AisGridLonToLowLon(LGRID nData);
    static LGRID    AisLowLatToGridLat(AISFF nData);
    static LGRID    AisLowPosLonToGrid(AISFF nData);
    //------------------------------------------------------------------------
    static void     AisFullPosToFLOAT(POS_HIGH *pFullPos, POS_FLOAT *pFloatPos);
    static void     AisFullPosToGRID(POS_HIGH *pFullPos, POS_GRID *pGridPos);
    //------------------------------------------------------------------------
    static void     AisLowPosToFLOAT(POS_LOW *pLowPos, POS_FLOAT *pFloatPos);
    static void     AisLowPosToGRID(POS_LOW *pLowPos, POS_GRID *pGridPos);
    //----------------------------------------------------------------------------
    static void     AisFullPosToLow(POS_HIGH *pHighPos, POS_LOW *pLowPos);
    //----------------------------------------------------------------------------
    static int      GetAisHighPosLatByMsgField(int nAisFullLat);
    static int      GetAisHighPosLonByMsgField(int nAisFullLon);
    static int      GetAisHalfLatDataByAisLAT(int nAisHalfLat);
    static int      GetAisHalfLonDataByAisLON(int nAisHalfLon);
    //----------------------------------------------------------------------------
    static void     GetAisHighPosDataByMsg(POS_HIGH *pFullPos);
    static void     GetAisLowPosDataByMsg(POS_LOW *pLowPos);
    //============================================================================
    static LREAL    LatToLat(LREAL rLat);
    static LREAL    LonToLon(LREAL rLon);
    //============================================================================
    static void     CalcFullPosByFULL(POS_ALLF *pFullPos);
    static void     CalcFullPosByFLOAT(POS_ALLF *pFullPos);
    //------------------------------------------------------------------------
    static void     CalcFullPosByLow(POS_ALLH *pAllPosH);
    static void     CalcLowPosByGRID(POS_ALLH *pAllPosH);
    static void     CalcGridLowPosByFLOAT(POS_ALLH *pAllPosH);
    //------------------------------------------------------------------------
    static void     ClearFullPosToNULL(POS_ALLF *pFullPos);
    static void     ClearHalfPosToNULL(POS_ALLH *pAllPosH);
    static void     CalcGrfPosByReal(REAL rLat, REAL rLon, POS_GRFP *psShipPos);
    //========================================================================

    //============================================================================
    static DWORD    GetAisFreqByChannelNo(HWORD wChNo);
    static HWORD    GetAisChannelNoByFreq(DWORD dFreq);
    static int      IsAisChanneFreqValidByCH(HWORD wChNo);
    static int      IsAisChanneFreqValidByFR(DWORD dFreq);
    static int      IsAisChanneBW25ValidByCH(HWORD wChNo);
    static int      IsAisChanneBW25ValidByFR(DWORD dFreq);
    //============================================================================
    static int      IsValidMMSI_BaseSt(DWORD dMMSI);
    static int      IsValidMMSI_MobileSt(DWORD dMMSI);
    static int      IsValidMMSI_SAR(DWORD dMMSI);
    static int      IsValidMMSI_AtoN(DWORD dMMSI);
    static int      IsValidMMSI(DWORD dMMSI);
    static int      IsValidMMSI_SART(DWORD dMMSI);
    static BOOL     IsValidImoNum(UINT uImoNum);
    static BOOL     IsValidImo(UINT uImoNum);
    static BOOL     IsShipTypeTanker(int nShipType);
    static DWORD    GetDimensionOfShip(xANTPOS *pAntPos, xANTPOS *pExtPos);
    static int      SetETAData(ETA_DATA *eta);
    //============================================================================
    static UINT16   GetScheduledPosReportMsgID(BYTE bOpPhase);
    static int      GetNIfromRR(float fReportRate);
    static int      GetNIfromRI(float fReportIntSec);
    static BOOL     IsValidTxPower(BYTE tx_power);
    static BOOL     IsValidBandwidth(WORD wBandwidth);
    static BOOL     IsOwnShipTypeBelongsToAssigned(int nShipType);
    static int      GetAisRxMsgMaxBitSizeByMsgID(int nMsgID);
    //============================================================================
    static int      GetRandomValueOneBySEED(int nStartVal, int nLastVal);
    static int      GetRandomSlotTimeOut();
    //============================================================================
    static int      GetAssignedRRby10minRR(int nOffset, int nMinutes);
    static float    GetAssignedRRbySlotInc(int nInc);
    //============================================================================
    static int      IsValidAisSysDateTime(SYS_DATE_TIME *pSysTime);
    static int      IsValidAisSysDate(int nYear, int nMon, int nDay);
    static int      IsValidAisSysDate(SYS_DATE *pSysTime);
    static int      IsValidAisSysTime(int nHour, int nMin, int nSec);
    static int      IsValidAisSysTime(SYS_TIME *pSysTime);
    static int      IsValidETATime(ETA_DATA *pEta);

    static void     ResetDefaultSysTime(SYS_DATE_TIME *sys_time);
    static void     SetDefaultSysDate(SYS_DATE *pSysDate);
    static void     SetDefaultSysTime(SYS_TIME *pSysTime);
    static void     SetDefaultSysDateTime(SYS_DATE_TIME *pSysTime);
    static BOOL     GetSysDateTime(SYS_DATE_TIME *pDateTime, SYS_DATE *pSrcDate, SYS_TIME *pSrcTime);
    static BOOL     IsTimeStampValid(int nTimeStamp);
    //============================================================================
    static WORD     FrameMapSlotIdAdd(INT16 nSlotID, INT16 nInc);
    static INT16    GetFrameMapSlotID(INT16 nFrameID, INT16 nSlotID);
    static DWORD    GetDiffDword(DWORD dwOldSlotID, DWORD dwNewSlotID, DWORD dwMaxValue);
    static DWORD    GetDiffDwordMax(DWORD dwOldSlotID, DWORD dwNewSlotID);
    static WORD     GetOneFrameDiffSlotID(WORD wOldSlotID, WORD wNewSlotID);
    static WORD     FrameMapGetDiffSlotID(WORD wOldSlotID, WORD wNewSlotID);
    static int      GetFrMapElapTimeSlot(int nOldSlotID, int nNewSlotID);
    static int      GetSlotShift(int nOldFrameID, int nOldSlotId, int nNewFrameID, int nNewSlotId);
    static int      GetNumSlotFromSec(int nElapSec);
    static BOOL     CheckValidSlotID(WORD wSlotID);
    static BOOL     CheckValidFrameMapSlotID(WORD wSlotID);
    static int      GetHdgAisFieldData();
    static int      GetRotAisFieldData();
    static int      GetRotDataFromAisFieldData(int nRotFieldData);
    static int      MemCmp(BYTE *pSrc, BYTE *pTrt, int nSize);
    static void     FTOA(double x, int f, char *str);
    static float    GetDiffYaw(float rDegNew, float rDegOld);
    static double   GetDiffAbsYaw(double rDegNew, double rDegOld);
    static BOOL     CheckWordDataExisting(WORD *pwList, int nSizeList, WORD wData);
    static int      HexaStrToData(char *pstrHexa, BYTE *pData, BOOL bCapital);
    static BOOL     IsValidAisFloatNumStr(char* pstrFloatNum, BOOL bCheckPlus);
    static BOOL     IsValidAisIntNumStr(char* pstrFloatNum, BOOL bCheckPlus);
    //============================================================================
    static void     GetDataSentence(char *data, int order, char seperator, char *sub_data, int nSubSize);

    static BYTE     GetROSSourceCharToInfo(char src_char);
    static char     GetROSSourceInfoToChar(BYTE ros_src);
    static void     ConvertPosDataToDMM(int pos_data, UINT *pos_deg, UINT *pos_min, UINT *pos_sub_min, int unit);
    static int      GetDaysFromYearFirst(int day, int month, int year);
    static int      DiffTime(SYS_DATE_TIME *c_time, SYS_DATE_TIME *p_time);
    static BOOL     IsNMEAChar(BYTE c);
    static BOOL     CheckFieldSeqNum( char *nTxSeqNum );
    static BOOL     CheckFieldStatus( char *status, char a, char b );
    static BOOL     ToIEC61162Chars( char *src, char *target, size_t length );
    static double   GetLongitude(char *data, char dir_flag);
    static double   GetLatitude(char *data,	char dir_flag);
    static BOOL     GetMonthNumToStr(int mon_num, char *mon_str);
    static BOOL     IsDefaultTime(SYS_DATE_TIME *sys_time);
    static BOOL     IsValidDimension(xANTPOS *ant_pos);
    static int	    CheckValidChar(char *pstrData, int nDataLen);
    static char*    FillDataStrFixedSize(char *pstrData, int nDataLen);
    static INT16    GetSlotAppDataLength(BYTE app_type, int nTxMsg, BOOL bDestIndicator, BOOL bBinaryFlag, UINT16 nNumDataBits);
    static int      HexaStrToDecimal(char *hexa_str, int digit_num);
    static BYTE     ASCII8ToBin6bit(BYTE ascii_8);
    static BOOL     ConvertASCIICodeTo6BitBinary(char *buff_8bit, char *buff_6bit);
    static UINT8    SplitNMEASentence(char *data, char *fields[], size_t length);
    static BOOL     IsSameAisStr(const char *pstr1, const char *pstr2);
};

#endif  /*__AISLIB_H__*/
