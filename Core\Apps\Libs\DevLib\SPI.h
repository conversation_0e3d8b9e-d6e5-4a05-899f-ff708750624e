/**
 * @file    SPI.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef  __SPI_H__
#define  __SPI_H__

#include "SysConst.h"


class cSpiSYS
{
protected:
    SPI_TypeDef        *m_pBaseAddr;
    SPI_HandleTypeDef   m_xSpiHand;
    UCHAR              *m_vTxBuffData;

public:
    cSpiSYS(SPI_TypeDef *pBaseAddr);
    virtual ~cSpiSYS(void);

public:
    void  InitSPI();
    void  DeinitSPI();

    HAL_StatusTypeDef WriteSpiData(UCHAR cbus_address, UINT16 *data_ptr, UCHAR bytes_per_access, UCHAR accesses);
    HAL_StatusTypeDef ReadSpiData(UCHAR cbus_address, UINT16 *data_ptr, UCHAR bytes_per_access, UCHAR accesses);

    HAL_StatusTypeDef WriteSpiData(UINT8 *pBuf, int nSize);
    HAL_StatusTypeDef ReadSpiData(UINT8 *pBuf, int nSize);
    HAL_StatusTypeDef WriteAndReadSpiData(UINT8 *pTxBuf, UINT8 *pRxBuf, int nSize);
};

#endif
