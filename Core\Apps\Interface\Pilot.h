#ifndef __PILOT_H__
#define __PILOT_H__

#include "DataType.h"
#include "AllConst.h"
#include "Uart.h"
#include "PI.h"

class CPILOT : public CPI
{
public:
    CPILOT(void)
    : CPI(HIGHSPD_PORTID_1, MON_PORTID_CH_PILOT, new cUartSYS(UARTID_2, USART2, USART2_IRQn, 1024, 2048, 38400)) {
    };
    ~CPILOT() {};

    static std::shared_ptr<CPILOT> getInst() {
        static std::shared_ptr<CPILOT> pInst = std::make_shared<CPILOT>();
        return pInst;
    }
};

#endif /*__PILOT_H__*/
