/**
 * @file    Timer.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include "main.h"
#include "SysConst.h"
#include "SysLib.h"
#include "GPIO.h"
#include "DAC.h"
#include "ADC.h"
#include "AisLib.h"
#include "AisModem.h"
#include "RxModem.h"
#include "Timer.h"
#include "GPIOExt.h"
#include "SysOpStatus.h"
#include "FskModem.h"

volatile DWORD gdwCurSysTimerSec = 0;
volatile static DWORD dwSysTickScaleCnt = 0;
volatile static DWORD dwOneSecCalcCnt = 0;

//=============================================================================
cTimerSys::cTimerSys(TIM_TypeDef *pBaseAddr, DWORD dSysIrqNo, DWORD dRunFreq, DWORD dIrqEnable)
{
    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    DWORD dTempX = (HAL_RCC_GetPCLK1Freq() * 2) / dRunFreq;

    m_pBaseAddr  = pBaseAddr;
    m_dSysIrqNo  = dSysIrqNo;
    m_dRunFreq   = dRunFreq;

    memset(&m_xTimerHand, 0x00, sizeof(m_xTimerHand));

    m_xTimerHand.Instance               = m_pBaseAddr;
    m_xTimerHand.Init.Prescaler         = 0;
    m_xTimerHand.Init.CounterMode       = TIM_COUNTERMODE_UP;
    m_xTimerHand.Init.Period            = dTempX - 1;
    m_xTimerHand.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&m_xTimerHand) != HAL_OK)
    {
        Error_Handler();
    }

    if (dIrqEnable)
    {
        if (HAL_TIM_Base_Start_IT(&m_xTimerHand) != HAL_OK)
        {
            Error_Handler();
        }
    }

    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&m_xTimerHand, &sMasterConfig) != HAL_OK)
    {
        Error_Handler();
    }

    NVIC_ClearPendingIRQ((IRQn_Type)m_dSysIrqNo);
    NVIC_SetPriority((IRQn_Type)m_dSysIrqNo , SYS_SYS_TICK_INT_PRIORITY);
    NVIC_EnableIRQ((IRQn_Type)m_dSysIrqNo);

    HAL_TIM_Base_Start(&m_xTimerHand);
}

cTimerSys::~cTimerSys(void)
{
}

DWORD cTimerSys::GetCounterValue(void)
{
    return(m_pBaseAddr->CNT);
}

void  cTimerSys::RunTimerIsrHandler()
{
    HWORD wRxAfA, wRxAfB;
    wRxAfA = CAdc::getInstAdc1()->GetADCData(ADC_1_GMSK_RXA_CH_NO);
    wRxAfB = CAdc::getInstAdc3()->GetADCData(ADC_3_GMSK_RXB_CH_NO);

    if(OPSTATUS::bEnableToRunTasks)
    {
    #ifdef __ENABLE_DSC__
        cFskModem::getInst()->RunTimerIsrHandler(wRxAfA);
    #endif

        cAisModem::getInst()->ProcessTxRxAllInINTR(wRxAfA, wRxAfB);
    }

    if(++dwSysTickScaleCnt >= SYSTICK_SCALE)
    {
        SysIncSystemTimer();
        dwSysTickScaleCnt = 0;

        if(++dwOneSecCalcCnt >= ONE_SEC_SYSTICK)
        {
            dwOneSecCalcCnt = 0;
            ++gdwCurSysTimerSec;
        }
    }
}

DWORD cTimerSys::GetCurTimerSec()
{
    return gdwCurSysTimerSec;
}

int cTimerSys::GetTimeDiffSec(DWORD dwOldTmrSec)
{
    volatile DWORD dwSysSec = 0;
    dwSysSec = gdwCurSysTimerSec;
    return (int)CAisLib::GetDiffDwordMax(dwOldTmrSec, dwSysSec);
}

int cTimerSys::GetTimeDiffSecToFuture(DWORD dwNewTmrSec)
{
    volatile DWORD dwSysSec = 0;
    dwSysSec = gdwCurSysTimerSec;
    return (int)CAisLib::GetDiffDwordMax(dwSysSec, dwNewTmrSec);
}
//=============================================================================
