/**
 * @file    Dtm.h
 * @brief   Dtm header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __DTM_H__
#define __DTM_H__

/******************************************************************************
 * 
 * DTM - 
 *
 * $--DTM, x.x,T*hh<CR><LF>
 *          |__|
 *          1
 * 
 * 1. Heading, degrees true
 * 
 ******************************************************************************/
class CDtm : public CSentence
{
public:
    CDtm();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidDatumLoc(void);
    bool IsValidDatumRef(void);

protected:
    int     m_nDatumLoc;
    int     m_nDatumRef;
    DWORD   m_dwRcvTick;
};

#endif /* __DTM_H__ */

