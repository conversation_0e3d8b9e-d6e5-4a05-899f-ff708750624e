/**
 * @file    Aca.h
 * @brief   Aca header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ACA_H__
#define __ACA_H__

/******************************************************************************
 *
 * ACA - AIS Regional Channel Assignment Message
 *                                                                      16  18
 *                                                                      |   |
 * $--ACA,x,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a,x,xxxx,x,xxxx,x,x,x,a,x,hhmmss.ss*hh<CR><LF>
 *        |    |    |    |     |    |    |    |     | |  |   |  |   | |   |      |
 *        1    2    3    4     5    6    7    8     9 10 11  12 13 14 15  17     19
 *
 * 1.  Sequence Number, 0 to 9
 * 2.  Region Northeast corner latitude
 * 3.  N/S - 'N' or 'S'
 * 4.  Region Northeast corner longitude
 * 5.  E/W - 'E' or 'W'
 * 6.  Region Southwest corner latitude
 * 7.  N/S - 'N' or 'S'
 * 8.  Region Southwest corner longitude
 * 9.  E/W - 'E' or 'W'
 * 10. Transition Zone Size
 * 11. Channel A
 * 12. Channel A bandwidth
 * 13. Channel B
 * 14. Channel B bandwidth
 * 15. Tx/Rx mode control
 * 16. Power level control
 * 17. Information source
 * 18. In-Use Flag
 * 19. Time of "in-use" change
 *
 ******************************************************************************/
class CAca : public CSentence
{
public:
    CAca();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the sentence
     * @param pszSentence The sentence to be made
     * @param psRosData The ROS data
     * @param nRosIndex The ROS index
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, xROSDATA *psRosData, int nRosIndex);

public:
    static int8_t m_nSequentialId;
};

#endif /* __ACA_H__ */

