/**
 * @file    Epv.cpp
 * @brief   Epv class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Ship.h"
#include "SetupMgr.h"
#include "Epv.h"

/*****************************************************************************
 *
 * EPV - Command or report equipment property value
 *
 * $--EPV,a,c--c.,c--c,x.x,c--c*hh<CR><LF>
 *        |  |     |    |   |
 *        1  2     3    4   5
 *
 * 1. Sentence status flag
 * 2. Equipment type
 * 3. Unique identifier
 * 4. Property identifier for the property to be set
 * 5. Value of property to be set
 *
 ******************************************************************************/
// Define static member variables
uint8_t  CEpv::m_nStatusFlag = 0;
char     CEpv::m_chEquipType[8];
uint32_t CEpv::m_dwUniqueID = 0;
uint8_t  CEpv::m_nPropertyID = 0;
char     CEpv::m_pPropertyValue[64];

 CEpv::CEpv() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CEpv::Parse(const char *pszSentence)
{
    char chStatusFlag = GetFieldChar(pszSentence, 1);
    if (chStatusFlag == 'C')
        m_nStatusFlag = EPV_STAT_FLAG_CMD;
    else if (chStatusFlag == 'R')
        m_nStatusFlag = EPV_STAT_FLAG_REQ;
    else
        return false;

    memset(m_chEquipType, 0x00, sizeof(m_chEquipType));
    GetFieldString(pszSentence, 2, m_chEquipType, sizeof(m_chEquipType));

    m_dwUniqueID = GetFieldInteger(pszSentence, 3);
    if (m_dwUniqueID == NMEA_NULL_INTEGER || m_dwUniqueID != cShip::getOwnShipInst()->GetOwnShipMMSI())
        return false;

    m_nPropertyID = GetFieldInteger(pszSentence, 4);
    if (m_nPropertyID == NMEA_NULL_INTEGER)
        return false;

    memset(m_pPropertyValue, 0x00, sizeof(m_pPropertyValue));
    GetFieldString(pszSentence, 5, m_pPropertyValue, sizeof(m_pPropertyValue));
    if (strlen(m_pPropertyValue) == 0)
        return false;

    return true;
}

/**
 * @brief Make the EPV sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CEpv::MakeSentence(char *pszSentence, int nPropertyID)
{
    uint8_t bData = 0;

    switch(nPropertyID)
    {
    case EPV_PROP_SENSOR1_BAUDRATE:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetBaudrateSensor1());
        break;
    case EPV_PROP_SENSOR2_BAUDRATE:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetBaudrateSensor2());
        break;
    case EPV_PROP_SENSOR3_BAUDRATE:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetBaudrateSensor3());
        break;
    case EPV_PROP_LR_BAUDRATE:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetBaudrateLongRange());
        break;
    case EPV_PROP_MMSI:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, cShip::getOwnShipInst()->GetOwnShipMMSI());
        break;
    case EPV_PROP_IMO:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, cShip::getOwnShipInst()->GetOwnShipIMO());
        break;
    case EPV_PROP_LR_CONFIG:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%C", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetLongRangeAutoReply() ? 'A' : 'M');
        break;
    case EPV_PROP_LR_CH1:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetLongRangeCh1());
        break;
    case EPV_PROP_LR_CH2:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetLongRangeCh2());
        break;
    case EPV_PROP_CHANGE_ADMIN_PWD:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%s", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetAdminPassword());
        break;
    case EPV_PROP_CHANGE_USER_PWD:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%s", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetUserPassword());
        break;
    case EPV_PROP_SHOW_TESTING_SART:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetShowTestingSART());
        break;
    case EPV_PROP_SILENT_MODE:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetEnableSilentMode());
        break;
    case EPV_PROP_LOCATING_DEVICE_ALERT:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetEnableAlert14());
        break;
    case EPV_PROP_SENSOR_ALERT:
        bData = (CSetupMgr::getInst()->GetEnableExtEPFS() << 2) | (CSetupMgr::getInst()->GetEnableExtHeading() << 1) | CSetupMgr::getInst()->GetEnableExtROT();
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, bData);
        break;
    case EPV_PROP_PILOT_PORT_RESTRICTED:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetPilotPortRestricted());
        break;

    case EPV_PROP_ANTENNA_EA:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetExtendAntennaPosA());
        break;
    case EPV_PROP_ANTENNA_EB:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetExtendAntennaPosB());
        break;
    case EPV_PROP_ANTENNA_EC:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetExtendAntennaPosC());
        break;
    case EPV_PROP_ANTENNA_ED:
        sprintf(pszSentence, "$AIEPV,R,AI,%d,%d,%d", cShip::getOwnShipInst()->GetOwnShipMMSI(), nPropertyID, CSetupMgr::getInst()->GetExtendAntennaPosD());
        break;

    case EPV_PROP_DGNSS_BAUDRATE:
    // Identifiers for IEC 61162-450 interface configuration
    case EPV_PROP_IP_AND_NETMASK:
    case EPV_PROP_SFI:
    case EPV_PROP_MULTICAST_GROUP:
    case EPV_PROP_MULTICAST_GROUP2:
    case EPV_PROP_TRANSMISSION_GROUP:
    // Identifiers for SFI configuration
    case EPV_PROP_PRI_POSITION_SENSOR:
    case EPV_PROP_SEC_POSITION_SENSOR:
    case EPV_PROP_PRI_SOG_COG_SENSOR:
    case EPV_PROP_SEC_SOG_COG_SENSOR:
    case EPV_PROP_PRI_HDG_SENSOR:
    case EPV_PROP_SEC_HDG_SENSOR:
    case EPV_PROP_PRI_ROT_SENSOR:
    case EPV_PROP_SEC_ROT_SENSOR:
    case EPV_PROP_PRI_AIS_CONTROL:
    case EPV_PROP_SEC_AIS_CONTROL:
    case EPV_PROP_PRI_ALERT_SOURCE:
    case EPV_PROP_SEC_ALERT_SOURCE:
    default:
        return 0;
    }

    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}
