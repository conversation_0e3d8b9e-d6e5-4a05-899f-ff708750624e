/**
 * @file    DataType.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __DATATYPE_H__
#define  __DATATYPE_H__

#include <stdio.h>

//=============================================================================
#if !defined(_ASSEMBLER_)
//-----------------------------------------------------------------------------
typedef    signed   char         SCHAR;
typedef    signed   char          INT8;
typedef  unsigned   char         UCHAR;
typedef  unsigned   char         UINT8;
typedef  unsigned   char          BYTE;
typedef  short      int          SHORT;
typedef  unsigned   short        HWORD;
typedef  unsigned   short         WORD;
typedef  unsigned   int          DWORD;
typedef  unsigned   int           UINT;
typedef  unsigned   int         UINT32;
typedef  unsigned   short       UINT16;
typedef             long          LONG;
typedef  unsigned   long         ULONG;
typedef  short      int          INT16;
typedef             int          INT32;
typedef  float                  REAL32;
typedef  float                   FLOAT;
typedef  double                 REAL64;
typedef  double                   REAL;
//-----------------------------------------------------------------------------
typedef             int          LGRID;
typedef             int          LMERC;
typedef  double                  LREAL;
typedef             int          AISFF;
typedef             int          AISHH;
//-----------------------------------------------------------------------------
typedef  unsigned   char          BOOL;
//-----------------------------------------------------------------------------
typedef  DWORD                    TIME;
typedef  DWORD                  TIME_T;
typedef  DWORD                    TICK;
typedef  DWORD                 SECTIME;
typedef  unsigned   char        BACK08;
typedef  short      int         BACK16;
typedef             int         BACK32;
typedef  unsigned   short       BACKWW;
typedef  unsigned   int         BACKDD;
//-----------------------------------------------------------------------------
typedef  long long  int          INT64;
typedef  unsigned long long int  QWORD;
//-----------------------------------------------------------------------------
typedef  double                 NOTCHX;
//-----------------------------------------------------------------------------
typedef struct
{
    int    nYear;          // 0 -- 9999
    int    nMon;           // 1 -- 12
    int    nDay;           // 1 -- 31
} SYS_DATE;
//-----------------------------------------------------------------------------
typedef struct
{
    int    nHour;          // 0 -- 23
    int    nMin;           // 0 -- 59
    int    nSec;           // 0 -- 59
} SYS_TIME;
//-----------------------------------------------------------------------------
typedef struct
{
    SYS_DATE xDate;
    SYS_TIME xTime;
} DATE_TIME;
//-----------------------------------------------------------------------------
typedef struct
{
    SYS_DATE xDate;
    SYS_TIME xTime;
    int      nValid;       // MODE_VAL_OFF,...,MODE_VAL_ON
} SYS_DATE_TIME;
//-----------------------------------------------------------------------------
typedef struct
{
    int    nLAT;           // * 3600000
    int    nLON;           // * 3600000
} POS_GRID;
//-----------------------------------------------------------------------------
typedef struct
{
    FLOAT  fLAT;
    FLOAT  fLON;
} POS_FLOAT;
//-----------------------------------------------------------------------------
typedef struct
{
    REAL   rLAT;
    REAL   rLON;
} POS_REAL;
//-----------------------------------------------------------------------------
typedef struct
{
    int    nLAT;           // * 600000 (AIS_HIGH_SCALE)
    int    nLON;           // * 600000 (AIS_HIGH_SCALE)
} POS_HIGH;
//-----------------------------------------------------------------------------
typedef struct
{
    int    nLAT;           // * 600    (AIS_POS_LOW_SCALE)
    int    nLON;           // * 600    (AIS_POS_LOW_SCALE)
} POS_LOW;
//-----------------------------------------------------------------------------
typedef struct
{
    POS_GRID  xPosG;
    POS_HIGH  xPosH;
    POS_REAL  xPosR;
    POS_FLOAT xPosF;
} POS_GRFP;
//-----------------------------------------------------------------------------
typedef struct
{
    POS_HIGH  xPosH;        // in 1/10000 min -> 1 / 600000 (==AIS_HIGH_SCALE) 도 단위
    POS_GRID  xPosG;        // 1 / 3600000 도 단위
    POS_FLOAT xPosF;        // 1 도 단위
} POS_ALLF;
//-----------------------------------------------------------------------------
typedef struct
{
    POS_LOW   xPosL;        // in 1/10 min -> 1 / 600 (==AIS_POS_LOW_SCALE) 도 단위
    POS_GRID  xPosG;        // 1 / 3600000 도 단위
    POS_FLOAT xPosF;        // 1 도 단위
} POS_ALLH;
//-----------------------------------------------------------------------------
#endif
//=============================================================================

#ifndef TRUE
#define TRUE        1
#endif

#ifndef FALSE
#define FALSE       0
#endif

#ifndef MAX
#define MAX(a,b)    ((a) > (b) ? (a) : (b))
#endif

#ifndef MIN
#define MIN(a,b)    ((a) < (b) ? (a) : (b))
#endif

#include "DataType_AIS.h"

#endif /*__DATATYPE_H__*/
