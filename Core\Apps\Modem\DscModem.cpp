/**
 * @file    RxModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include "SysLib.h"
#include "SysLog.h"
#include "DscModem.h"
#include "SetupMgr.h"
#include "DscMgr.h"

//--------------------
// Definitions
//--------------------
#define IsDscEosCh(bCh)    (bCh == DSC_EOS_CHR_117 || bCh == DSC_EOS_CHR_127)

//--------------------
// static variables
//--------------------
static UCHAR G_vBitMaskX[] = {0x80, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x01};


//----------------
// Implementation
//----------------
cDscModem::cDscModem(std::shared_ptr<cFskModem> pFskModemP)
{
    m_pFskModemP = pFskModemP;

    m_vPhaseData = (UCHAR*)SysAllocMemory(DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE * sizeof(UCHAR));
    memset(m_vPhaseData, 0x00, DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE * sizeof(UCHAR));
    m_nPhaseCntr = 0;

    m_vDxRxData = (HWORD*)SysAllocMemory(DSC_DX_RX_SIZE * sizeof(HWORD));
    memset(m_vDxRxData, 0x00, DSC_DX_RX_SIZE * sizeof(HWORD));
    m_nDxRxSize = 0;

    m_vDxData = (HWORD*)SysAllocMemory(DSC_DX_RX_SIZE * sizeof(HWORD));
    memset(m_vDxData, 0x00, DSC_DX_RX_SIZE * sizeof(HWORD));
    m_nDxSize = 0;

    m_vRxData = (HWORD*)SysAllocMemory(DSC_DX_RX_SIZE * sizeof(HWORD));
    memset(m_vRxData, 0x00, DSC_DX_RX_SIZE * sizeof(HWORD));
    m_nRxSize = 0;

    m_vRealData = (UCHAR*)SysAllocMemory(DSC_DX_RX_SIZE * sizeof(UCHAR));
    memset(m_vRealData, 0x00, DSC_DX_RX_SIZE * sizeof(UCHAR));
    m_nRealSize = 0;

    m_vRecvPacketD = (UCHAR*)SysAllocMemory(DSC_CHAR_BIT_SIZE * sizeof(UCHAR));
    m_nRecvPacketP = 0;
    memset(m_vRecvPacketD, 0x00, DSC_CHAR_BIT_SIZE * sizeof(UCHAR));


    m_nDscRxStatus   = DSC_RX_STATUS_PHASING;
    m_dTotalRxBitCnt = 0;

    m_nDscRxBitCnt   = 0;
    m_wDscRxLastWord = 0;
    m_wDscRxLastChar = 0;

    m_nEosFoundModeX = DSC_RX_EOS_FOUND_NONE;
    m_nCharsAfterEOS = 0;
    m_nEosFoundDxPos = 0;

    m_nPhaseIndx     = 0;

    m_bRecvFormatSpec= 0;
}

cDscModem::~cDscModem(void)
{
}

int cDscModem::CombineDscCharX(UCHAR *pRecv, HWORD *pDscChar)
{
    int   i;
    HWORD wTemp;
    HWORD wCalc;

    wTemp = 0;
    for (i = 0; i < DSC_CHAR_BIT_SIZE; i++)
    {
        wTemp = wTemp >> 1;
        if (pRecv[i])
            wTemp = wTemp | 0x0200;
    }

    *pDscChar = wTemp;
    wCalc = CalcDscEccValue(wTemp);

    if (wCalc == wTemp)
        return 1;

    return 0;
}

int cDscModem::CombineDscCharY(UCHAR *pRecv, HWORD *pDscChar, int nStart, int nMaxSize)
{
    int   i;
    HWORD wTemp;
    HWORD wCalc;

    wTemp = 0;
    for (i = 0; i < DSC_CHAR_BIT_SIZE; i++)
    {
        if (nStart >= nMaxSize)
            nStart -= nMaxSize;

        wTemp = wTemp >> 1;
        if (pRecv[nStart])
            wTemp = wTemp | 0x0200;

        ++nStart;
    }

    *pDscChar = wTemp;
    wCalc = CalcDscEccValue(wTemp);

    if (wCalc == wTemp)
        return 1;

    return 0;
}

HWORD cDscModem::CalcDscEccValue(HWORD wData)
{
    static  int  vCode[] = {0, 4, 2, 6, 1, 5, 3, 7};
    int   i;
    int   nCount;

    wData &= 0x007f;
    nCount = 0;

    for (i = 0; i < 7; i++)
    {
        if (!(wData & G_vBitMaskX[7 - i]))
            ++nCount;
    }

    wData = (vCode[nCount] << 7) | wData;

    return(wData);
}

int cDscModem::IsDscDxCharacter(HWORD wChar)
{
    wChar = wChar & 0x7f;
    if (wChar == 125)
        return 1;

    return 0;
}

int cDscModem::IsDscRxCharacter(HWORD wChar)
{
    wChar = wChar & 0x7f;
    if (wChar >= 104 && wChar <= 111)
        return 1;

    return 0;
}

int cDscModem::CountDscFormat(HWORD wFormat, HWORD *pData, int nSize)
{
    int  nCount;

    nCount = 0;
    while (nSize)
    {
        if (wFormat == *pData)
            ++nCount;

        ++pData;
        --nSize;
    }

    return(nCount);
}

void cDscModem::CheckEndReceiving(HWORD wDscChar)
{
    static HWORD vFindX[] = {DSC_FRMT_INDIVIDUAL, DSC_FRMT_VTS_GEOGRAPHY};
    static int   vSizeX[] = {1                 , 1                    };
    int    nDxStart, i, nOffsetZ;
    HWORD  vFormatX[4];

    m_wDscRxLastWord = wDscChar;
    m_wDscRxLastChar = wDscChar & 0x007f;

    m_vDxRxData[m_nDxRxSize++] = wDscChar;
    if (m_nDxRxSize >= DSC_DX_RX_SIZE) {
        RunMessageReceivingEnd();
        return;
    }

    nDxStart = 11 - (111 - (m_vDxRxData[0] & 0x007f)) * 2;
    if (m_nDxRxSize == (nDxStart + 8)) {
        vFormatX[0] = m_vDxRxData[nDxStart + 0];
        vFormatX[1] = m_vDxRxData[nDxStart + 5];
        vFormatX[2] = m_vDxRxData[nDxStart + 2];
        vFormatX[3] = m_vDxRxData[nDxStart + 7];

        m_bRecvFormatSpec = 0x00;

        for (i = 0; i < (sizeof(vFindX) / sizeof(vFindX[0])); i++) {
            if (CountDscFormat(CalcDscEccValue(vFindX[i]), vFormatX, 4) >= vSizeX[i]) {
                m_bRecvFormatSpec = vFindX[i];
                break;
            }
        }

        if (m_bRecvFormatSpec == 0x00) {
            RunMessageReceivingEnd();
            return;
        }
    }

    if (m_bRecvFormatSpec == DSC_FRMT_INDIVIDUAL || m_bRecvFormatSpec == DSC_FRMT_VTS_GEOGRAPHY) {
        if (m_bRecvFormatSpec == DSC_FRMT_INDIVIDUAL)
            nOffsetZ = 32;
        else
            nOffsetZ = 44;

        if (RunCommonEndChk(nDxStart, nOffsetZ))
            CheckRcvDscMessage(nDxStart);
    }
}

int cDscModem::RunCommonEndChk(int nDxStart, int nOffsetZ)
{
    int  nTempX;
    int  nTempY;
    int  nOffsetX = nDxStart + nOffsetZ - 6;
    int  nOffsetY = nDxStart + nOffsetZ - 1;

    if (m_nEosFoundModeX != DSC_RX_EOS_FOUND_NONE) {
        --m_nCharsAfterEOS;
        if (m_nCharsAfterEOS <= 0) {
            DEBUG_LOG("RcvDSC] EndChk, done! %d\r\n", m_nCharsAfterEOS);
            return 1;
        }
        return 0;
    }

    if (m_nDxRxSize >= (nDxStart + nOffsetZ)) {
        if (m_vDxRxData[nOffsetX] == CalcDscEccValue(m_vDxRxData[nOffsetX]) ||
            m_vDxRxData[nOffsetY] == CalcDscEccValue(m_vDxRxData[nOffsetY]))
        {
            nTempX = m_vDxRxData[nOffsetX] & 0x7f;
            nTempY = m_vDxRxData[nOffsetY] & 0x7f;

            if(1)            // 여기서는 비교안하고 이후에 비교하도록 수정함
            {
                if (IsDscEosCh(m_wDscRxLastChar))
                {
                    if(m_nEosFoundModeX == DSC_RX_EOS_FOUND_NONE)
                    {
                        if (m_nDxRxSize & 0x0001)           // RX position
                        {
                            m_nEosFoundModeX = DSC_RX_EOS_FOUND_RX;
                            m_nCharsAfterEOS = 2;
                        }
                        else                                // DX position
                        {
                            m_nEosFoundModeX = DSC_RX_EOS_FOUND_DX;
                            m_nCharsAfterEOS = 7;
                            m_nEosFoundDxPos = m_nDxRxSize;
                        }
                    }

                    DEBUG_LOG("RcvDSC] EndChk, rcvEOS : %d, dxrxCnt:%d, EosFoundMode:%d, EosDxPos: %d, chAfterEOS: %d, tmpX: %d, tmpY : %d, dxStart: %d\r\n",
                            m_wDscRxLastChar, m_nDxRxSize, m_nEosFoundModeX, m_nEosFoundDxPos, m_nCharsAfterEOS, nTempX, nTempY, nDxStart);
                    return 0;
                }
            }
            else
            {
                RunMessageReceivingEnd();
                return 0;
            }
        }
        else
        {
            RunMessageReceivingEnd();
            return 0;
        }
    }

    return 0;
}

void cDscModem::CheckRcvDscMessage(int nDxStart)
{
    int   i, nStatus;
    HWORD wEccValue;
    HWORD wRealData;
    UCHAR bEccCheck;
    UCHAR bTestDx;
    UCHAR bTestRx;

    m_nDxSize = 0;
    m_nRxSize = 0;

    for (i = (nDxStart + 4); i < (m_nDxRxSize - 4); i += 2)            // format-sp 무시
    {
        m_vDxData[m_nDxSize++] = m_vDxRxData[i + 0];
        m_vRxData[m_nRxSize++] = m_vDxRxData[i + 5];
    }

    m_nRealSize = 0;
    wEccValue   = 0;

    const int nEccCheckCnt = m_nRxSize - 1;

    for (i = 0; i < nEccCheckCnt ; i++)                               // ECC 무시
    {
        bTestDx = m_vDxData[i] == CalcDscEccValue(m_vDxData[i]);
        bTestRx = m_vRxData[i] == CalcDscEccValue(m_vRxData[i]);

        nStatus = 0;
        if (bTestDx)  nStatus |= 0x0001;
        if (bTestRx)  nStatus |= 0x0002;
        if (nStatus == 0x0003 && m_vDxData[i] != m_vRxData[i])
            nStatus = 0x0000;

        if (nStatus)
        {
            if (bTestDx)
                wRealData = m_vDxData[i];
            else
                wRealData = m_vRxData[i];

            wEccValue = wEccValue ^ wRealData;
            m_vRealData[m_nRealSize] = wRealData & 0x7f;
            ++m_nRealSize;
        }
        else
        {
            if (i < (m_nRxSize - 2))
            {
                RunMessageReceivingEnd();
                return;
            }
        }
    }

    wEccValue = wEccValue ^ CalcDscEccValue(m_bRecvFormatSpec);
    wEccValue = CalcDscEccValue(wEccValue);
    if (wEccValue == m_vDxData[m_nDxSize - 1] || wEccValue == m_vRxData[m_nRxSize - 1])
        bEccCheck = 0x00;                                  // ecc-good
    else
        bEccCheck = 0x80;                                  // ecc-error

    //---------------------------------------------------------------------------------------------------------------
    // Refer to ITU-R 825-3
    // Refer to ITU-R 1371-5 Annex 3
    // - AIS 는 DSC 호출 중 Format specifier 가 103(call to a group of ships in a specified VTS area),
    //   120(call to a particular individual station)일때만 처리하면 됨.
    // - Format specifier 다음에 오는 Address 옵션에 따라, 수신되는 Category의 자리수가 변동될 수 있다.
    //   그러므로, 먼저 "Address" 심볼을 올바르게 파싱할 수 있어야 한다.
    // - 개별호출 : Format specifier가 120(individual station)일 때, Address로 MMSI가 수신된다.(M.825-3,p.7)
    // - 그룹호출 : Format specifier가 103(specified VTS area)일 때, Address로 좌표값이 수신된다.(22 digits == 11 characters)
    //   Format specifier 의 다음 digit 이 4일 경우 그 다음 3 digit은 course 값을 지정한 group 호출을 의미한다.
    //   Format specifier 의 다음 digit 이 5일 경우 그 다음 2 digit은 ship type 값을 지정한 group 호출을 의미한다.
    //       -> 0 ~ 3: azimuth sector(in geographic coordinate)
    //       ->     4: Ships on a certain course
    //       -> 5 ~ 9: Ships of a certain type
    //---------------------------------------------------------------------------------------------------------------
    // ex) format specifier 103 일 경우 m_vRealData
    //     00,03,00,00,00,00,00,59,99,59,99,103,00,27,30,00,00,104,09,20,81,00,104,10,20,82,00,104,12,00,03,00,
    //        00,14,20,104,13,20,03,00,00,12,10,117(EOS),
    //---------------------------------------------------------------------------------------------------------------

    if (bEccCheck == 0x00)
    {
        DEBUG_LOG("DSCdata] size:%d,%d rcvData: ", m_nDxRxSize, m_nRealSize);

        int nDscDataSize = m_nRealSize;
        UCHAR pDscDataBuff[DSC_DX_RX_SIZE];
        memcpy(pDscDataBuff, m_vRealData, m_nRealSize);

        RunMessageReceivingEnd();

        if (m_bRecvFormatSpec == DSC_FRMT_INDIVIDUAL)    RcvIndividualMsg(pDscDataBuff, nDscDataSize);
        if (m_bRecvFormatSpec == DSC_FRMT_VTS_GEOGRAPHY) RcvVtsGeogrphMsg(pDscDataBuff, nDscDataSize);
    }
}

void cDscModem::ClearDscMsg(xDscAllMsg *psDscMsg)
{
    psDscMsg->bFormat        = DSC_FRMT_NONE;
    psDscMsg->bCategory        = DSC_CATEGORY_NONE;
    psDscMsg->dTgtMMSI        = AIS_AB_MMSI_NULL;
    psDscMsg->dSrcMMSI        = AIS_AB_MMSI_NULL;
    psDscMsg->uTxPower        = AIS_TX_POWER_HIGH;
    psDscMsg->uPrimaryChID    = AIS1_DEFAULT_CH_NUM;
    psDscMsg->uPrimaryBandwidth        = AIS_CH_BW_25_0_KHZ;
    psDscMsg->uSecondaryChID        = AIS2_DEFAULT_CH_NUM;
    psDscMsg->uSecondaryBandwidth    = AIS_CH_BW_25_0_KHZ;
    psDscMsg->uGuardRegionChID        = AIS_CH_NUM_NONE;
    psDscMsg->uGuardRegionBandwidth = AIS_CH_BW_25_0_KHZ;

    psDscMsg->uGroupCourse        = NMEA_HDG_NULL;
    psDscMsg->uGroupShipType    = AIS_SHIPTYPE_NULL;

    psDscMsg->xGeoData.xNE_Pos.nGridLat = AIS_GRID_LAT_NULL_VAL;
    psDscMsg->xGeoData.xNE_Pos.nGridLon = AIS_GRID_LON_NULL_VAL;

    psDscMsg->xGeoData.xSW_Pos.nGridLat = AIS_GRID_LAT_NULL_VAL;
    psDscMsg->xGeoData.xSW_Pos.nGridLon = AIS_GRID_LON_NULL_VAL;

    psDscMsg->sRosNE.nGridLat = AIS_GRID_LAT_NULL_VAL;
    psDscMsg->sRosNE.nGridLon = AIS_GRID_LON_NULL_VAL;

    psDscMsg->sRosSW.nGridLat = AIS_GRID_LAT_NULL_VAL;
    psDscMsg->sRosSW.nGridLon = AIS_GRID_LON_NULL_VAL;
}

void cDscModem::RcvIndividualMsg(UCHAR *pDscData, int nDscLen)
{
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    // | Target-ID(5) | Category(1) | Source-ID(5) | VTS-EXP-MSG-ID(1)---1| Symbol-NO(1)---1 | Data(1,...,n)---1 |
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    // |              |        103  |              |              104     |       00 --- 99  |                   |
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    // |                                           +       <<<<<----- max 4 times ----->>>>>                     +
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    xDscAllMsg xDscMsg;

    ClearDscMsg(&xDscMsg);
    xDscMsg.bFormat   = DSC_FRMT_INDIVIDUAL;
    xDscMsg.dTgtMMSI  = GetDscMMSI(&pDscData[0]);
    xDscMsg.bCategory = pDscData[5];

    if(xDscMsg.bCategory == DSC_CATEGORY_VTS) {
        if(RcvCommonDscMsg(&xDscMsg, &pDscData[6], nDscLen-6)) {
        	CDSCMgr::getInst()->PutDscMsg(&xDscMsg);
        }
    }
    else
    {
        INFO_LOG("DscCall-Indi] Error, wrong category : %d\r\n", xDscMsg.bCategory);
    }
}

void cDscModem::RcvVtsGeogrphMsg(UCHAR *pDscData, int nDscLen)
{
    //---------------------------------------------------------------------------------------------------------------
    // Refer to ITU-R 825-3
    // Refer to ITU-R 1371-5 Annex 3
    // - AIS 는 DSC 호출 중 Format specifier 가 103(call to a group of ships in a specified VTS area),
    //   120(call to a particular individual station)일때만 처리하면 됨.
    // - Format specifier 다음에 오는 Address 옵션에 따라, 수신되는 Category의 자리수가 변동될 수 있다.
    //   그러므로, 먼저 "Address" 심볼을 올바르게 파싱할 수 있어야 한다.
    // - Format specifier가 120(individual station)일 때, Address로 MMSI가 수신된다.(M.825-3,p.7)
    // - Format specifier가 103(specified VTS area)일 때, Address로 좌표값이 수신된다.(22 digits == 11 characters)
    //   Format specifier 의 다음 digit 이 4일 경우 그 다음 3 digit은 course 값을 지정한 group 호출을 의미한다.
    //   Format specifier 의 다음 digit 이 5일 경우 그 다음 2 digit은 ship type 값을 지정한 group 호출을 의미한다.
    //       -> 0 ~ 3: azimuth sector(in geographic coordinate)
    //       ->     4: Ships on a certain course
    //       -> 5 ~ 9: Ships of a certain type
    //---------------------------------------------------------------------------------------------------------------
    // ex) format specifier 103 일 경우 m_vRealData
    //     00,03,00,00,00,00,00,59,99,59,99,103,00,27,30,00,00,104,09,20,81,00,104,10,20,82,00,104,12,00,03,00,
    //     00,14,20,104,13,20,03,00,00,12,
    //---------------------------------------------------------------------------------------------------------------

    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    // | Geo-Data(11) | Category(1) | Source-ID(5) | VTS-EXP-MSG-ID(1)---1| Symbol-NO(1)---1 | Data(1,...,n)---1 |
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    // |              |        103  |              |              104     |       00 --- 99  |                   |
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    // |                                           +       <<<<<----- max 4 times ----->>>>>                     +
    // |--------------+-------------+--------------+----------------------+------------------+-------------------+
    xDscAllMsg xDscMsg;

    ClearDscMsg(&xDscMsg);
    xDscMsg.bFormat = DSC_FRMT_VTS_GEOGRAPHY;
    int nNumBytes = GetDscGroupSpecifier(&xDscMsg, &pDscData[0]);
    if(GetDscGeoData(&xDscMsg.xGeoData, &pDscData[nNumBytes]))
    {
        xDscMsg.bCategory = pDscData[nNumBytes+11];

        if(xDscMsg.bCategory == DSC_CATEGORY_VTS)
        {
            if(RcvCommonDscMsg(&xDscMsg, &pDscData[nNumBytes+12], nDscLen - (nNumBytes+12))) {
            	CDSCMgr::getInst()->PutDscMsg(&xDscMsg);
            }
        }
        else
        {
            INFO_LOG("DscCall-Area] Error, wrong category : %d\r\n", xDscMsg.bCategory);
        }
    }
}

BOOL cDscModem::RcvCommonDscMsg(xDscAllMsg *pDscMsg, UCHAR *pDscData, int nDscLen)
{
    int    i, nSkip;
    HWORD  wTempX;

    pDscMsg->dSrcMMSI = GetDscMMSI(pDscData +  0);
    pDscData += 5;
    nDscLen  -= 5;

    for (i = 0; i < DSC_MAX_DSC_EXP_MSG; i++)
    {
        nSkip = 0;

        if (*pDscData == DSC_MSGID_VTS_EXP)
        {
            ++pDscData;

            if (*pDscData == DSC_EXP_TX_POWER)
            {
                wTempX = pDscData[1];

                if ((CSetupMgr::getInst()->IsAisAClass() && wTempX != 1 && wTempX != 12)
                    || (CSetupMgr::getInst()->IsAisBClass() && wTempX != 1 && wTempX != 5))
                {
                    INFO_LOG("RcvDSCdata] ignore, invalid PowerLevel : %d\r\n", wTempX);
                    return FALSE;
                }
                pDscMsg->uTxPower = (wTempX == 1 ? AIS_TX_POWER_LOW : AIS_TX_POWER_HIGH);       // TX-power
                nSkip = 1;
            }

            if (*pDscData == DSC_EXP_PRI_REGION_CH)               // followed by 3 symbol
            {
                wTempX = GetDscWord(pDscData + 1);
                pDscMsg->uPrimaryChID = wTempX;
                pDscMsg->uPrimaryBandwidth = (pDscData[3] / 10) == 0 ? AIS_CH_BW_25_0_KHZ : AIS_CH_BW_12_5_KHZ;  // Band-Width (0=25kHz, 1=12.5kHz)
                nSkip = 4;
            }

            if (*pDscData == DSC_EXP_SEC_REGION_CH)               // followed by 3 symbol
            {
                wTempX = GetDscWord(pDscData + 1);
                pDscMsg->uSecondaryChID = wTempX;
                pDscMsg->uSecondaryBandwidth = (pDscData[3] / 10) == 0 ? AIS_CH_BW_25_0_KHZ : AIS_CH_BW_12_5_KHZ;  // Band-Width (0=25kHz, 1=12.5kHz)
                nSkip = 4;
            }

            if (*pDscData == DSC_EXP_GUARD_REGION_CH)             // followed by 3 symbol
            {
                wTempX = GetDscWord(pDscData + 1);
                pDscMsg->uGuardRegionChID = wTempX;
                pDscMsg->uGuardRegionBandwidth = (pDscData[3] / 10) == 0 ? AIS_CH_BW_25_0_KHZ : AIS_CH_BW_12_5_KHZ;  // Band-Width (0=25kHz, 1=12.5kHz)
                nSkip = 4;
            }

            if (*pDscData == DSC_EXP_NE_CORNER_REGION)            // followed by 6 symbol
            {
                GetDscCoordinateX(&pDscData[1], &(pDscMsg->sRosNE));
                nSkip = 7;
            }

            if (*pDscData == DSC_EXP_SW_CORNER_REGION)            // followed by 6 symbol
            {
                GetDscCoordinateX(&pDscData[1], &(pDscMsg->sRosSW));
                nSkip = 7;
            }
        }

        if (IsDscEosCh(*pDscData) || nSkip == 0)
            break;

        pDscData += nSkip;
        nDscLen  -= nSkip;
        if (nDscLen <= 0)
            break;
    }

    return TRUE;
}

DWORD cDscModem::GetDscMMSI(UCHAR *pDscData)
{
    char  vTempX[32];

    sprintf(vTempX, "%2d%02d%02d%02d%d", pDscData[0], pDscData[1], pDscData[2], pDscData[3], pDscData[4] / 10);

    return(atoi(vTempX));
}

int  cDscModem::GetDscGroupSpecifier(xDscAllMsg *psDscMsg, UCHAR *pDscData)
{
    char  pstrTmp[32];

    int nGroupSpecifier  = pDscData[0] / 10;

    if(nGroupSpecifier == 4)        // Course
    {
    	sprintf(pstrTmp, "%d%02d", pDscData[0] % 10, pDscData[1]);
        psDscMsg->uGroupCourse = atoi(pstrTmp);

        DEBUG_LOG("GrCourse : %d\r\n", psDscMsg->uGroupCourse);
        return 2;
    }
    if(nGroupSpecifier >= 5)        // Ship type
    {
    	sprintf(pstrTmp, "%2d", pDscData[0]);
        psDscMsg->uGroupShipType = atoi(pstrTmp);

        DEBUG_LOG("GrShipType : %d\r\n", psDscMsg->uGroupShipType);
        return 1;
    }
    return 0;
}

BOOL cDscModem::GetDscGeoData(xDscGeo *pGeoData, UCHAR *pDscData)
{
    int nNEWS    = pDscData[0] / 10;
    int nGridNwLat = GetDscGeoGridLat(pDscData + 0, nNEWS, 100);
    int nGridNwLon = GetDscGeoGridLon(pDscData + 3, nNEWS, 100);
    int nGridVert= GetDscGeoGridVert(pDscData + 7);
    int nGridHori= GetDscGeoGridHori(pDscData + 9);

    pGeoData->xNE_Pos.nGridLat = CheckGridLatRange(nGridNwLat);
    pGeoData->xNE_Pos.nGridLon = CheckGridLonRange(nGridNwLon + nGridHori);

    pGeoData->xSW_Pos.nGridLat = CheckGridLatRange(pGeoData->xNE_Pos.nGridLat - nGridVert);
    pGeoData->xSW_Pos.nGridLon = CheckGridLonRange(pGeoData->xNE_Pos.nGridLon - nGridHori);

    return TRUE;
}

int cDscModem::GetDscGeoGridLat(UCHAR *pDscData, int nNEWS, int nUnit)
{
    int   nDeg, nMin, nTmp = 0;
    int   nGridLat;

    nDeg = (pDscData[0] % 10) * 10 + pDscData[1] / 10;
    nMin = (pDscData[1] % 10) * 10 + pDscData[2] / 10;

    if(nUnit == 100)
        nTmp = (pDscData[2] % 10) * 10 + pDscData[3] / 10;
    else if (nUnit == 10)
        nTmp = (pDscData[2] % 10);

    nGridLat = nDeg * AIS_GRID_MUL_FACTOR + nMin * (AIS_GRID_MUL_FACTOR / 60) + nTmp * (AIS_GRID_MUL_FACTOR / (60 * 100));

    if (nNEWS == DSC_NEWS_SE || nNEWS == DSC_NEWS_SW)
        nGridLat = -nGridLat;

    return(nGridLat);
}

int cDscModem::GetDscGeoGridLon(UCHAR *pDscData, int nNEWS, int nUnit)
{
    int   nDeg = 0, nMin = 0, nTmp = 0;
    int   nGridLon;

    if(nUnit == 100)
    {
        nDeg = (pDscData[0] % 10) * 100 + pDscData[1];
        nMin = pDscData[2];
        nTmp = pDscData[3];
    }
    else if(nUnit == 10)
    {
        nDeg = (pDscData[0] * 10) + pDscData[1] / 10;
        nMin = (pDscData[1] % 10) * 10 + pDscData[2] / 10;
        nTmp = pDscData[2] % 10;
    }

    nGridLon = nDeg * AIS_GRID_MUL_FACTOR + nMin * (AIS_GRID_MUL_FACTOR / 60) + nTmp * (AIS_GRID_MUL_FACTOR / (60 * 100));

    if (nNEWS == DSC_NEWS_NW || nNEWS == DSC_NEWS_SW)
        nGridLon = -nGridLon;

    return(nGridLon);
}

int cDscModem::GetDscGeoGridHori(UCHAR *pDscData)
{
    int   nMin, nTmp;
    int   nGridVal;

    nMin = pDscData[0];
    nTmp = pDscData[1];

    nGridVal = nMin * (AIS_GRID_MUL_FACTOR / 60) + nTmp * (AIS_GRID_MUL_FACTOR / (60 * 100));

    return(nGridVal);
}

int cDscModem::GetDscGeoGridVert(UCHAR *pDscData)
{
    int   nMin, nTmp;
    int   nGridVal;

    nMin = pDscData[0];
    nTmp = pDscData[1];

    nGridVal = nMin * (AIS_GRID_MUL_FACTOR / 60) + nTmp * (AIS_GRID_MUL_FACTOR / (60 * 100));

    return(nGridVal);
}

HWORD cDscModem::GetDscWord(UCHAR *pDscData)
{
    char  vTempX[32];
    sprintf(vTempX, "%02d%02d", pDscData[0], pDscData[1]);

    DEBUG_LOG("DscWord] %02X, %02X, %s, %d\r\n", pDscData[0], pDscData[1], vTempX, atoi(vTempX));
    return(atoi(vTempX));
}

void cDscModem::GetDscCoordinateX(UCHAR *pDscData, xDscPOS *pDscPos)
{
    int   nNEWS;
    int   nGridLat;
    int   nGridLon;

    nNEWS    = pDscData[0] / 10;
    nGridLat = GetDscGeoGridLat(pDscData + 0, nNEWS, 10);
    nGridLon = GetDscGeoGridLon(pDscData + 3, nNEWS, 10);

    pDscPos->nGridLat = CheckGridLatRange(nGridLat);
    pDscPos->nGridLon = CheckGridLonRange(nGridLon);
}

int cDscModem::CheckGridLatRange(int nGridLat)
{
    nGridLat = MIN(nGridLat, AIS_GRID_LAT_MAX_VAL);
    nGridLat = MAX(nGridLat, AIS_GRID_LAT_MIN_VAL);
    return(nGridLat);
}

int cDscModem::CheckGridLonRange(int nGridLon)
{
    nGridLon = MIN(nGridLon, AIS_GRID_LON_MAX_VAL);
    nGridLon = MAX(nGridLon, AIS_GRID_LON_MIN_VAL);

    return(nGridLon);
}

void cDscModem::RunMessageReceivingEnd(void)
{
    m_nDscRxStatus   = DSC_RX_STATUS_PHASING;
    m_dTotalRxBitCnt = 0;
    m_nRecvPacketP   = 0;
    m_nPhaseCntr     = 0;
    m_nPhaseIndx     = 0;
    m_nDxRxSize      = 0;

    m_nEosFoundModeX = DSC_RX_EOS_FOUND_NONE;
    m_nCharsAfterEOS = 0;
    m_nEosFoundDxPos = 0;

    m_pFskModemP->ResetFskModem();
}

void cDscModem::RunPhaseProcess(UCHAR nBitData)
{
    HWORD vDxRx[DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE];
    int   vEccX[DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE];
    int   nDxCnt;
    int   nRxCnt;
    int   i, k, nTemp;

    if (m_nPhaseCntr < DSC_PHASE_BIT_SIZE)
    {
        m_vPhaseData[m_nPhaseCntr++] = nBitData;
        m_nPhaseIndx = 0;
        return;
    }

    if (m_nPhaseCntr >= (DSC_PHASE_BIT_SIZE + DSC_PHASE_BIT_SIZE / 4))
    {
        RunMessageReceivingEnd();
        return;
    }

    nTemp = m_nPhaseCntr;
    if (nTemp >= DSC_PHASE_BIT_SIZE)
        nTemp -= DSC_PHASE_BIT_SIZE;

    m_vPhaseData[nTemp] = nBitData;
    ++m_nPhaseCntr;

    ++m_nPhaseIndx;
    if (m_nPhaseIndx >= DSC_PHASE_BIT_SIZE)
        m_nPhaseIndx  = 0;

    for (i = 0; i < (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE); i++)
        vEccX[i] = CombineDscCharY(&m_vPhaseData[0], &vDxRx[i], i * DSC_CHAR_BIT_SIZE + m_nPhaseIndx, DSC_PHASE_BIT_SIZE);

    nDxCnt = 0;
    for (i = 0; i < (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE - 4); i += 2)
        if (IsDscDxCharacter(vDxRx[i]) && vEccX[i])
            ++nDxCnt;

    nRxCnt = 0;
    for (i = 1; i < (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE); i += 2)
        if (IsDscRxCharacter(vDxRx[i]) && vEccX[i])
            ++nRxCnt;

    if ((nDxCnt >= 2 && nRxCnt >= 1) ||      // 2DX, 1RX
        (nDxCnt >= 1 && nRxCnt >= 2) ||      // 1DX, 2RX
        (nDxCnt >= 0 && nRxCnt >= 3))        // 3RX
    {
        for (i = 1; i < (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE); i += 2)
        {
            if (IsDscRxCharacter(vDxRx[i]) && vEccX[i])
            {
                nTemp = (111 - (vDxRx[i] & 0x7f)) * 2 + 1;
                if (nTemp != i)
                    return;

                break;
            }
        }

        m_nDscRxStatus = DSC_RX_STATUS_RUNNING;
        m_nPhaseCntr   = 0;
        m_nPhaseIndx   = 0;
        m_nRecvPacketP = 0;

        for (i = 1; i < (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE); i += 2)
        {
            if (IsDscRxCharacter(vDxRx[i]) && vEccX[i])
            {
                nTemp = vDxRx[i] & 0x7f;
                if (nTemp < 106)
                {
                    i = i - (106 - nTemp) * 2;
                    if (i < 1)
                        i = 1;

                    vDxRx[i] = CalcDscEccValue(106);
                }

                for (k = i; k < (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE); k++)
                    m_vDxRxData[k - i] = vDxRx[k];

                m_nDxRxSize = (DSC_PHASE_BIT_SIZE / DSC_CHAR_BIT_SIZE) - i;
                break;
            }
        }
    }
}

void cDscModem::RunDataProcess(UCHAR nBitData)
{
    HWORD  wDscChar;

    m_vRecvPacketD[m_nRecvPacketP++] = nBitData;
    if (m_nRecvPacketP >= DSC_CHAR_BIT_SIZE)
    {
        CombineDscCharX(m_vRecvPacketD, &wDscChar);
        CheckEndReceiving(wDscChar);
        m_nRecvPacketP = 0;
    }
}

void cDscModem::ProcessRcvData()
{
    int   nBitData;

    while (1)
    {
        nBitData = m_pFskModemP->GetFskBitData();
        if(nBitData == FSK_MDM_RX_BIT_DATA_NULL)
            break;

        ++m_dTotalRxBitCnt;

        if (m_nDscRxStatus == DSC_RX_STATUS_PHASING)
            RunPhaseProcess((UCHAR)nBitData);
        else
            RunDataProcess((UCHAR)nBitData);
    }
}
