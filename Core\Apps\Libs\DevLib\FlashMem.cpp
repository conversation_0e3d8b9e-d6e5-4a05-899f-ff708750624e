/**
 * @file    FlashMem.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include "stm32h743xx.h"
#include "SysConst.h"
#include "SysLib.h"
#include "GPIO.h"
#include "FlashMem.h"


cFlashMem::cFlashMem(DWORD dBlockBaseAddr, int nNumberOfSector)
{
    m_dBlockBaseAddr   = dBlockBaseAddr;
    m_nNumberOfSector  = nNumberOfSector;
    m_pBytesPerSector  = new DWORD[nNumberOfSector];
    m_pSectorStartAddr = new DWORD[nNumberOfSector];
}

cFlashMem::~cFlashMem(void)
{
    delete [] m_pBytesPerSector;
    delete [] m_pSectorStartAddr;
}

void  cFlashMem::SetBytesPerSector(int nSectorNo, DWORD dBytesPerSector)
{
    m_pBytesPerSector[nSectorNo] = dBytesPerSector;
    m_pSectorStartAddr[nSectorNo] = m_dBlockBaseAddr + (nSectorNo * dBytesPerSector);
}

DWORD cFlashMem::GetStartAddrBySectorNo(int nSectorNo)
{
    return(m_pSectorStartAddr[nSectorNo]);
}

int   cFlashMem::GetSectorNoByAddr(DWORD dAddr)
{
    int   i;

    if (dAddr < m_dBlockBaseAddr || dAddr >= ADDR_FLASH_END_ADDR)
        return 0;

    dAddr &= ~(0x100000);

    for (i = 0; i < m_nNumberOfSector; i++)
        if (dAddr <= m_pSectorStartAddr[i])
            return(i);

    return 0;
}

DWORD cFlashMem::GetSectorSize(DWORD dAddr)
{
    return(m_pBytesPerSector[GetSectorNoByAddr(dAddr)]);
}
//=============================================================================

//=============================================================================
cSTM32IntFlash::cSTM32IntFlash(void) 
: cFlashMem(ADDR_FLASH_BANK0_SECTOR_00, FLASH_SECTOR_TOTAL)
{
    static DWORD vSectorID[FLASH_SECTOR_TOTAL]  = {FLASH_SECTOR_0 , FLASH_SECTOR_1 , FLASH_SECTOR_2 , FLASH_SECTOR_3 , 
                                                   FLASH_SECTOR_4 , FLASH_SECTOR_5 , FLASH_SECTOR_6 , FLASH_SECTOR_7 };
    static DWORD vSectorSize[FLASH_SECTOR_TOTAL]= {128, 128, 128, 128, 128, 128, 128, 128};

    for (int i = 0; i < m_nNumberOfSector; i++)
    {
        m_vSectorID[i] = vSectorID[i];
        SetBytesPerSector(i, vSectorSize[i] * 1024);
    }
}

cSTM32IntFlash::~cSTM32IntFlash(void)
{
}

DWORD cSTM32IntFlash::GetSectorIDByAddr(DWORD dAddr)
{
    DWORD   nSectorNo = GetSectorNoByAddr(dAddr);

    if (nSectorNo > FLASH_SECTOR_TOTAL)
        return 0;

    return(m_vSectorID[nSectorNo]);
}

int   cSTM32IntFlash::EraseSector(DWORD dAddr, int nSecCnt)
{
    uint32_t dSectError;
    HAL_StatusTypeDef status = HAL_OK;
    FLASH_EraseInitTypeDef EraseInit;

    if(!IsDualBankMode())
    {
        return 0;
    }

    for(int i = 0 ; i < 10 ; i++)
    {
        UnLockFlash();

        EraseInit.TypeErase     = FLASH_TYPEERASE_SECTORS;
        EraseInit.VoltageRange  = FLASH_VOLTAGE_RANGE_3;
        EraseInit.Sector        = GetSectorIDByAddr(dAddr);
        EraseInit.NbSectors     = nSecCnt;
        EraseInit.Banks         = (dAddr < FLASH_BANK2_BASE) ? FLASH_BANK_1 : FLASH_BANK_2;

        status = HAL_FLASHEx_Erase(&EraseInit, &dSectError);

        LockFlash();

        if (status == HAL_OK)
        {
            break;
        }
    }

    return 0;
}

int   cSTM32IntFlash::WriteData(DWORD dAddr, void *pData, int nSize)
{
    DWORD dStartAddr;
    DWORD *pFullData;
    UCHAR ByteBuff[32];
    int   i;
    int   nFullSize;
    int   nByteSize;
    DWORD nErrorCode;

    if(!IsDualBankMode())
    {
        return 0;
    }

    UnLockFlash();

    dStartAddr = dAddr - (dAddr % _FLASH_WORD_);
    pFullData  = (DWORD *)pData;

    nFullSize  = nSize / _FLASH_WORD_;
    nByteSize  = nSize % _FLASH_WORD_;

    for (i = 0; i < nFullSize; i++)
    {
        HAL_FLASH_Program(FLASH_TYPEPROGRAM_FLASHWORD, dStartAddr, (DWORD)pFullData);

        nErrorCode = HAL_FLASH_GetError();
        if (nErrorCode > 0)
        {
            LockFlash();
            return nErrorCode;
        }

        dStartAddr += _FLASH_WORD_;
        pFullData += FLASH_NB_32BITWORD_IN_FLASHWORD;
    }

    if (nByteSize > 0)
    {
        memset(ByteBuff, 0x00, sizeof(UCHAR));
        memcpy(ByteBuff, pFullData, nByteSize);
        HAL_FLASH_Program(FLASH_TYPEPROGRAM_FLASHWORD, dStartAddr, (DWORD)ByteBuff);

        nErrorCode = HAL_FLASH_GetError();
        if (nErrorCode > 0)
        {
            LockFlash();
            return nErrorCode;
        }
    }

    LockFlash();

    return 0;
}

int   cSTM32IntFlash::ReadData(DWORD dAddr, void *pData, int nSize)
{
    UCHAR *pTempData;
    UCHAR *pReadAddr;
    int   i;

    if(!IsDualBankMode())
    {
        return 0;
    }

    pTempData = (UCHAR *)pData;
    pReadAddr = (UCHAR *)dAddr;

    for (i = 0; i < nSize; i++)
    {
        *pTempData = *pReadAddr;
        ++pTempData;
        ++pReadAddr;

        if (FLASH_WaitForLastOperation(FLASH_TIMEOUT_VALUE, (dAddr < FLASH_BANK2_BASE) ? FLASH_BANK_1 : FLASH_BANK_2) != HAL_OK)
        {
            return HAL_FLASH_GetError();
        }
    }

    return 0;
}

int   cSTM32IntFlash::LockFlash(void)
{
    HAL_FLASH_Lock();
    return 0;
}

int   cSTM32IntFlash::UnLockFlash(void)
{
    HAL_FLASH_Unlock();
    return 0;
}

int   cSTM32IntFlash::IsDualBankMode(void)
{
    return 1;
}
//=============================================================================

