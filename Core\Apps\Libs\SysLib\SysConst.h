/**
 * @file    SysConst.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include "stm32h7xx.h"
#include "stm32h7xx_hal_conf.h"
#include "system_stm32h7xx.h"
#include "stm32h743xx.h"

#include "AllConst.h"
#include "DataType.h"

#ifndef __SYSCONST_H__
#define __SYSCONST_H__

#define  SYS_ADC_VALUE_MAX                  0x0FFF              // 12bit ADC data from CPU ADC H/W module
#define  SYS_ADC_TO_VOLT(x)                 (3.3f * (float)(x) / (float)SYS_ADC_VALUE_MAX)

//=============================================================================
#define  BAUDRATE_INT_GNSS_DFLT             9600
#define  TASK_SETFREQ_SLEEP_MS              2

//=============================================================================
#define  SYS_SYS_TICK_INT_PRIORITY          (0)                 // G=0,S=0 the highest
#define  SYS_UART_INT_PRIORITY              (3)                 // G=1,S=1
#define  SYS_ADC_INT_PRIORITY               (3)                 // G=1,S=1
#define  SYS_DAC_INT_PRIORITY               (3)                 // G=1,S=1
#define  SYS_TIMER_INT_PRIORITY             (3)                 // G=1,S=1
#define  SYS_SPI_INT_PRIORITY               (3)                 // G=3,S=1 the lowest
//=============================================================================

//=============================================================================
#define  ONE_SEC_TICK                                 48000
#define  ONE_SEC_SYSTICK                               1000
#define  SYSTICK_SCALE                                   48
#define  CALC_SEC_TO_TICK(X)        (ONE_SEC_SYSTICK * (X))
#define  CALC_TICK_TO_SEC(X)        ((X) / ONE_SEC_SYSTICK)
#define  CALC_MILI_TO_TICK(X)       (ONE_SEC_SYSTICK * (X) / 1000)
#define  CALC_TICK_TO_MILI(X)       ((X) * (1000 / ONE_SEC_SYSTICK))
//=============================================================================

#endif
