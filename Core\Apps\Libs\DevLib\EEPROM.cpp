/**
 * @file    Eeprom.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>

#include "SysConst.h"
#include "SysLib.h"
#include "GPIOExt.h"
#include "EEPROM.h"

#define EE_TOTALBYTES       16384
#define EE_ADDRBITS         14
#define EE_PAGESIZE         64
#define EE_LASTPAGE         (EE_TOTALBYTES/EE_PAGESIZE)-1

#define EEWRSR              0x01    // Write status register
#define EEWRITE             0x02    // Write to memory array
#define EEREAD              0x03    // Read from memory array
#define EEWRDI              0x04    // Write disable
#define EERDSR              0x05    // Read status register
#define EEWREN              0x06    // Write enable

#define EERDID              0x83    // Read identification page
#define EEWRID              0x82    // Write identification page

#define EEPROM_STATUS_WIP   0x01

#define DELAY_SPI           SysDelayLoop(10)
#define DELAY_HOLD          SysDelayLoop(5)

CEEPROM::CEEPROM()
{
    m_pSPI4 = new cSpiSYS(SPI4);
    SetGpioEEPROM_WP(FALSE);
    SetGpioEEPROM_CS(TRUE);
}

CEEPROM::~CEEPROM()
{
}

BOOL CEEPROM::EEPROM_IsBusy(void)
{
	UCHAR vTxBuffer[2];
	UCHAR vRxBuffer[2];
	vTxBuffer[0] = EERDSR;
    
    SetGpioEEPROM_CS(FALSE);                            // CS  : Low
    m_pSPI4->WriteAndReadSpiData(vTxBuffer, vRxBuffer, 2);
    SetGpioEEPROM_CS(TRUE);                             // CS  : High

    if( (vRxBuffer[1] & EEPROM_STATUS_WIP) == EEPROM_STATUS_WIP) return true;
    return false;
}


void CEEPROM::EEPROM_WriteEnable(void)
{
	UCHAR Buffer = EEWREN;
    
    SetGpioEEPROM_CS(FALSE);                            // CS  : Low
    m_pSPI4->WriteSpiData(&Buffer, 1);
    SetGpioEEPROM_CS(TRUE);                             // CS  : High
}

void CEEPROM::EEPROM_WriteDisable(void)
{
	UCHAR Buffer = EEWRDI;
    
    SetGpioEEPROM_CS(FALSE);                            // CS  : Low
    m_pSPI4->WriteSpiData(&Buffer, 1);
    SetGpioEEPROM_CS(TRUE);                             // CS  : High
}

int CEEPROM::WriteData(DWORD dwAddr, void *pData, int nSize)
{
	UCHAR vTxBuffData[EE_PAGESIZE + 3];
    HAL_StatusTypeDef errorcode = HAL_OK;

    int pages = (nSize / EE_PAGESIZE);
    int remain = nSize % EE_PAGESIZE;

    for (int nCnt = 0; nCnt < pages; nCnt++)
    {
        memcpy(vTxBuffData, (BYTE *)pData + (nCnt*EE_PAGESIZE), EE_PAGESIZE);
        EEPROM_WriteEnable();

        vTxBuffData[0] = EEWRITE;
        vTxBuffData[1] = ((dwAddr + (nCnt*EE_PAGESIZE)) >> 8) & 0xFF;             // MSB of Address
        vTxBuffData[2] = (dwAddr + (nCnt*EE_PAGESIZE)) & 0xFF;                    // LSB of Address
        memcpy(&vTxBuffData[3], (BYTE *)pData + (nCnt*EE_PAGESIZE), EE_PAGESIZE); // Page Data
        
        SetGpioEEPROM_CS(FALSE);
        errorcode = m_pSPI4->WriteSpiData(vTxBuffData, EE_PAGESIZE + 3);
        SetGpioEEPROM_CS(TRUE);

        if (errorcode != HAL_OK)
        {
            return errorcode;
        }

        DWORD dwTimeout = SysGetSystemTimer();
        while(EEPROM_IsBusy()) {
            if (SysGetDiffTimeMili(dwTimeout) < 3000)
                DELAY_HOLD;
            else
                return -2;
        }
    }

    if (remain)
    {
        memset(vTxBuffData, 0x00, EE_PAGESIZE + 3);
        EEPROM_WriteEnable();
    
        vTxBuffData[0] = EEWRITE;
        vTxBuffData[1] = ((dwAddr + (pages*EE_PAGESIZE)) >> 8) & 0xFF;        // MSB of Address
        vTxBuffData[2] = (dwAddr + (pages*EE_PAGESIZE)) & 0xFF;               // LSB of Address
        memcpy(&vTxBuffData[3], (BYTE *)pData + (pages*EE_PAGESIZE), remain); // Page Data
        
        SetGpioEEPROM_CS(FALSE);
        m_pSPI4->WriteSpiData(vTxBuffData, remain + 3);
        SetGpioEEPROM_CS(TRUE);

        DWORD dwTimeout = SysGetSystemTimer();
        while(EEPROM_IsBusy()) {
            if (SysGetDiffTimeMili(dwTimeout) < 3000)
                DELAY_HOLD;
            else
                return -2;
        }
    }

    return 0;
}

int CEEPROM::ReadData(DWORD dwAddr, void *pData, int nSize)
{
	UCHAR vTxBuffData[4];
	UCHAR vRxBuffData[4];
    HAL_StatusTypeDef errorcode = HAL_OK;

    for (int nCnt = 0; nCnt < nSize; nCnt++)
    {
        memset(vTxBuffData, 0x00, 4);
        vTxBuffData[0] = EEREAD;
        vTxBuffData[1] = ((dwAddr + nCnt) >> 8) & 0xFF;    // MSB of Address
        vTxBuffData[2] = (dwAddr + nCnt) & 0xFF;           // LSB of Address

        SetGpioEEPROM_CS(FALSE);
        errorcode = m_pSPI4->WriteAndReadSpiData(vTxBuffData, vRxBuffData, 4);
        if (errorcode != HAL_OK)
        {
            return errorcode;
        }

        ((BYTE *)pData)[nCnt] = vRxBuffData[3];
        SetGpioEEPROM_CS(TRUE);
    }
    
    return 0;
}



