/**
 * @file    GmskLib.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"

#ifndef  __GMSKLIB_H__
#define  __GMSKLIB_H__

//=============================================================================
#define  RX_GMSK_FILTER_USE_MODE
//#define  RX_CORR_PREAMBLE_DETECT
//----------------------------------------------------------------------------
#define  RX_GMSK_SAMPLE_RATE                    (48000)
#define  RX_GMSK_BT_0_5_FIR_N                   (13)
#define  RX_GMSK_TO_INT_FACTOR                  (1 << 4)
//=============================================================================
#define  TX_GMSK_BT_0_4_FIR_N                   (17)
//=============================================================================
#define  RX_GMSK_MAX_DATA_VALUE                 (RX_GMSK_SAMPLE_RATE * RX_GMSK_TO_INT_FACTOR)
#define  TX_GMSK_MAX_DATA_VALUE                 (RX_GMSK_SAMPLE_RATE * RX_GMSK_TO_INT_FACTOR)
//=============================================================================
#define  AIS_GMSK_TX_DATA_MODE_EMPTY           -35          // 송신data없음
#define  AIS_GMSK_TX_DATA_MODE_WAITING         -34          // 송신대기중
#define  TX_START_INTERNAL_SHIFT                0           // wait time (in sample count) from slot edge before transmission

#define  RAMPUP_TIME_SAMPLECNT                  40          // Ramp up time (in sample count) for 833us (8bit = 40)

#define  NUM_TX_END_WAIT_DELAY_BIT              10          // 송신종료 후 1ms 이후에 TX RF off 시키기 위한 delay bit 수
#define  NUM_TX_END_BUFFER_BIT                  3

#define  NRZI_START_LEVEL                       1

#define  AIS_GMSK_TX_DATA_MODE_SENDING          (+1)        //  1--nGmskAdSize:송신중
//=============================================================================
#define  AIS_GMSK_TX_BUFF_SIZE                  (NUM_TXSLOT_AHEAD)    // NUM_TXSLOT_AHEAD 와 연동되어야한다!
//=============================================================================

//=============================================================================
typedef  struct _xTxDATA {
         int    nTxChnlNo;
         int    nTxFreqVal;
         int    nTxSlotNo;
         int    nTxMsgID;
         int    nGmskAdSize;
volatile int    nTxSendSize;
         HWORD *pGmskDaData;
} xTxDATA;
//-----------------------------------------------------------------------------
typedef  struct _xAisTxGmskDATA {
        int     vGmskTxHead;
        int     vGmskTxTail;
        int     nRawBitSize;
        int     nTxPrevFreq;

        int     nLowerDAC[2];
        int     nUpperDAC[2];
        int     nCnterDAC[2];

        UCHAR   nNrziRunMode;       // gbEnableNRZI
        UCHAR   nNrziRunLevel;      // gbNrziLevel

        UCHAR   nBitStuffMode;      // gbEnableBitStuffing
        UCHAR   nBitStuffCntr;      // gbBitStuffCnt
        int     nNumStuffBits;

        UCHAR   vRawBitData[AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX * 8];
        xTxDATA vGmskTxData[AIS_GMSK_TX_BUFF_SIZE];

        SHORT   vGmskBfData[TX_GMSK_BT_0_4_FIR_N];
} xAisTxGmskDATA;
//============================================================================

//============================================================================
xAisTxGmskDATA *GetAisTxGmskDATA(void);
void    ClearAisTxGmskBuffData(void);
void    SetAisTxGmskDacRefOffset(int nDacCenter, int nDacOffset);
HWORD   GetAisTxGmskLowerDAC(int nChNo);
HWORD   GetAisTxGmskUpperDAC(int nChNo);
HWORD   GetAisTxGmskCnterDAC(int nChNo);
//============================================================================
void    ClearAisTxGmskDATA(void);
void    SetAisTxNrziRunMode(int nMode);
void    SetAisTxBitStuffMode(int nMode);
int     MakeRealAisTxRawData(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits, int nAdditionalBitStuffBits);
int     MakeTestMsgFrameAisTxRawDataRandomPRBS(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits);
int     MakeTestMsgFrameAisTxRawDataFixedPRBS(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits, BYTE bPacketID, BOOL bInvertedNRZI);
int     MakeTestInfiniteSeqAisTxRawData(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits);
int     MakeAisTestSignal2GmskTxSendData(int nTxChNo, int nSlotNo);
int     MakeAisTestSignal3GmskTxSendData(int nTxChNo, int nSlotNo);
//============================================================================
void    AppendPhyBitmapData(UCHAR bBitMapData);
void    AppendVdlBitmapData(UCHAR bBitMapData);
void    AppendVdlBitStream(UCHAR *pBitStream, int nNumBits);
void    AppendMsgBitStream(BYTE *pBitStream, int nNumBits);
//============================================================================
HWORD   GetAisCrc16FCS(UCHAR *pData, int nLen);
//============================================================================
void    MakeAisGmskTxSendData(int nTxChNo, int nSlotNo, int nTxMsgID);
void    MakeAisUnmodCarrierGmskTxSendData(int nTxChNo, int nSlotNo);
//============================================================================

#endif

