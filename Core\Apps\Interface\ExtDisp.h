#ifndef __EXTDISP_H__
#define __EXTDISP_H__

#include "DataType.h"
#include "AllConst.h"
#include "Uart.h"
#include "PI.h"

class CEXTDISP : public CPI
{
public:
    CEXTDISP(void)
    : CPI(HIGHSPD_PORTID_2, MON_PORTID_CH_EXTDISP, new cUartSYS(UARTID_1, USART1, USART1_IRQn, 1024, 2048, 38400)) {
    };
    ~CEXTDISP() {};

    static std::shared_ptr<CEXTDISP> getInst() {
        static std::shared_ptr<CEXTDISP> pInst = std::make_shared<CEXTDISP>();
        return pInst;
    }
};

#endif /*__EXTDISP_H__*/
