/**
 * @file    Txt.cpp
 * @brief   Txt class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "LayerNetwork.h"
#include "SensorMgr.h"
#include "SetupMgr.h"
#include "ROSMgr.h"
#include "Txt.h"

//-----------------------------------------------------------------------------
// refer to IEC-61993-2 6.10.3.4 Monitoring sensor data status
// Table 3 - Sensor status indications signalled using TXT sentence formatter
//-----------------------------------------------------------------------------
#define STR_TXT_MSG_EXT_DGNSS           "AIS: external DGNSS in use"                        // TXT_ID_EXT_DGNSS
#define STR_TXT_MSG_EXT_GNSS            "AIS: external GNSS in use"                         // TXT_ID_EXT_GNSS
#define STR_TXT_MSG_INT_DGNSS_BEACON    "AIS: internal DGNSS in use (beacon)"               // TXT_ID_INT_DGNSS_BEACON
#define STR_TXT_MSG_INT_DGNSS_MSG17     "AIS: internal DGNSS in use (Message 17)"           // TXT_ID_INT_DGNSS_MSG17
#define STR_TXT_MSG_INT_GNSS            "AIS: internal GNSS in use"                         // TXT_ID_INT_GNSS
#define STR_TXT_MSG_NO_POS_SENSOR       "AIS: No position sensor"                           // TXT_ID_NO_SENSOR_POS
#define STR_TXT_MSG_EXT_COGSOG          "AIS: external SOG/COG in use"                      // TXT_ID_EXT_SOG_COG
#define STR_TXT_MSG_INT_COGSOG          "AIS: internal SOG/COG in use"                      // TXT_ID_INT_SOG_COG
#define STR_TXT_MSG_HDG_INVALID         "AIS: Heading valid"                                // TXT_ID_HEADING_VALID
#define STR_TXT_MSG_ROT_INDICATOR       "AIS: Rate of Turn Indicator in use"                // TXT_ID_ROT_INDICATOR
#define STR_TXT_MSG_ROT_OTHER           "AIS: Other ROT source in use"                      // TXT_ID_OTHER_ROT_SRC
#define STR_TXT_MSG_CHMNG_CHGED         "AIS: Channel management parameters changed"        // TXT_ID_CH_MNG_CHANGE

// refer to IEC-61993-2:2018 ED3.0 6.10.2.4
#define STR_TXT_MSG_LOW_PWR_MODE_ACT    "AIS: Low power tanker mode active"
#define STR_TXT_MSG_LOW_PWR_MODE_INACT  "AIS: Low power tanker mode inactive"
#define STR_TXT_MSG_ASSIGNED_MODE       "AIS: Operating in assigned mode by Message 16 "
#define STR_TXT_MSG_DATALINK_MNG_MODE   "AIS: Operating in data link management mode by Message 20 "
#define STR_TXT_MSG_CH_MNG_MODE         "AIS: Operation in channel management mode by Message 22 "
#define STR_TXT_MSG_GRP_ASSGNED_MODE    "AIS: Operation in group assignment mode by Message 23 "
#define STR_TXT_MSG_RTN_DEFAULT_MODE    "AIS: Returned to default operations"

// Vendor TXT
#define STR_TXT_MSG_INT_GNSS_SBAS       "AIS: internal FGNSS in use (SBAS)"                 // TXT_ID_INT_GNSS_SBAS

/******************************************************************************
 * 
 * TXT - Text Transmission
 *
 * $--TXT,xx,xx,xx,c--c*hh<CR><LF>
 *        |  |  |   |
 *        1  2  3   4
 *
 * 1. Total number of sentences, 01 to 99
 * 2. Sentence number, 01 to 99
 * 3. Text identifier
 * 4. Text message(maximum : 61 characters)
 *
 ******************************************************************************/
CTxt::CTxt() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CTxt::Parse(const char *pszSentence)
{

    return true;
}

char* CTxt::GetTxtStringPtr(int nTxtID)
{
    char *pstrMsg = "";

    switch(nTxtID)
    {
    case TXT_ID_EXT_DGNSS:          pstrMsg = (char*)STR_TXT_MSG_EXT_DGNSS;             break;
    case TXT_ID_EXT_GNSS:           pstrMsg = (char*)STR_TXT_MSG_EXT_GNSS;              break;
    case TXT_ID_INT_DGNSS_BEACON:   pstrMsg = (char*)STR_TXT_MSG_INT_DGNSS_BEACON;      break;
    case TXT_ID_INT_DGNSS_MSG17:    pstrMsg = (char*)STR_TXT_MSG_INT_DGNSS_MSG17;       break;
    case TXT_ID_INT_GNSS:           pstrMsg = (char*)STR_TXT_MSG_INT_GNSS;              break;
    case TXT_ID_NO_SENSOR_POS:      pstrMsg = (char*)STR_TXT_MSG_NO_POS_SENSOR;         break;
    case TXT_ID_EXT_SOG_COG:        pstrMsg = (char*)STR_TXT_MSG_EXT_COGSOG;            break;
    case TXT_ID_INT_SOG_COG:        pstrMsg = (char*)STR_TXT_MSG_INT_COGSOG;            break;
    case TXT_ID_HEADING_VALID:      pstrMsg = (char*)STR_TXT_MSG_HDG_INVALID;           break;
    case TXT_ID_ROT_INDICATOR:      pstrMsg = (char*)STR_TXT_MSG_ROT_INDICATOR;         break;
    case TXT_ID_OTHER_ROT_SRC:      pstrMsg = (char*)STR_TXT_MSG_ROT_OTHER;             break;
    case TXT_ID_CH_MNG_CHANGE:      pstrMsg = (char*)STR_TXT_MSG_CHMNG_CHGED;           break;

    // refer to IEC-61993-2:2018 ED3.0 6.10.2.4
    case TXT_ID_LOW_PWR_MODE_ACT:   pstrMsg = (char*)STR_TXT_MSG_LOW_PWR_MODE_ACT;      break;
    case TXT_ID_LOW_PWR_MODE_INACT: pstrMsg = (char*)STR_TXT_MSG_LOW_PWR_MODE_INACT;    break;
    case TXT_ID_ASSIGNED_MODE:      pstrMsg = (char*)STR_TXT_MSG_ASSIGNED_MODE;         break;
    case TXT_ID_DATALINK_MNG_MODE:  pstrMsg = (char*)STR_TXT_MSG_DATALINK_MNG_MODE;     break;
    case TXT_ID_CH_MNG_MODE:        pstrMsg = (char*)STR_TXT_MSG_CH_MNG_MODE;           break;
    case TXT_ID_GRP_ASSGNED_MODE:   pstrMsg = (char*)STR_TXT_MSG_GRP_ASSGNED_MODE;      break;
    case TXT_ID_RTN_DEFAULT_MODE:   pstrMsg = (char*)STR_TXT_MSG_RTN_DEFAULT_MODE;      break;

    // Vendor TXT
    case TXT_ID_INT_GNSS_SBAS:      pstrMsg = (char*)STR_TXT_MSG_INT_GNSS_SBAS;         break;
    }

    return pstrMsg;
}

/**
 * @brief Check the text status
 * @param nTxtID The text identifier
 * @return True if the text is valid, false otherwise
 */
bool CTxt::CheckTxtStat(int nTxtID)
{
    bool bStatusTxt = false;

    switch(nTxtID)
    {
    case TXT_ID_EXT_DGNSS:
        bStatusTxt = CSensorMgr::getInst()->IsExtDGNSSMode();
        break;
    case TXT_ID_EXT_GNSS:
        bStatusTxt = CSensorMgr::getInst()->IsExtGNSSMode();
        break;
    case TXT_ID_INT_DGNSS_BEACON:
        break;
    case TXT_ID_INT_DGNSS_MSG17:
        bStatusTxt = CSensorMgr::getInst()->IsIntDGNSSMode();
        break;
    case TXT_ID_INT_GNSS:
        bStatusTxt = CSensorMgr::getInst()->IsIntGNSSMode();
        break;
    case TXT_ID_NO_SENSOR_POS:
        bStatusTxt = CSensorMgr::getInst()->IsGnssLost();
        break;
    case TXT_ID_EXT_SOG_COG:
        bStatusTxt = CSensorMgr::getInst()->IsExtSogCogMode();
        break;
    case TXT_ID_INT_SOG_COG:
        bStatusTxt = CSensorMgr::getInst()->IsIntSogCogMode();
        break;
    case TXT_ID_HEADING_VALID:
        bStatusTxt = !CSensorMgr::getInst()->IsHdgLost();
        break;
    case TXT_ID_ROT_INDICATOR:
        bStatusTxt = !CSensorMgr::getInst()->IsRotLost();
        break;
    case TXT_ID_OTHER_ROT_SRC:
        bStatusTxt = CSensorMgr::getInst()->IsRotSensorOtherSrc();
        break;
    case TXT_ID_CH_MNG_CHANGE:
        break;
    // refer to IEC-61993-2:2018 ED3.0 6.10.2.4
    case TXT_ID_LOW_PWR_MODE_ACT:
        if (CSetupMgr::getInst()->IsAisBClass())
            return false;

        bStatusTxt = (CROSMgr::getInst()->m_nLowPowerModeByNavStatus == POWER_MODE_ACTIVE_LOW);
        break;
    case TXT_ID_LOW_PWR_MODE_INACT:
        if (CSetupMgr::getInst()->IsAisBClass())
            return false;

        bStatusTxt = (CROSMgr::getInst()->m_nLowPowerModeByNavStatus == POWER_MODE_INACTIVE_LOW);
         break;
    case TXT_ID_ASSIGNED_MODE:
        bStatusTxt = CLayerNetwork::getInst()->CheckAssignedModeRunningByMsg16();
        break;
    case TXT_ID_DATALINK_MNG_MODE:
        break;
    case TXT_ID_CH_MNG_MODE:
        if( !CLayerNetwork::getInst()->CheckAssignedModeRunning() 
            && CROSMgr::getInst()->IsRosIndexValid_Ext(CROSMgr::getInst()->m_sRosData.nRosIdx) 
            && CROSMgr::getInst()->m_vAllRosDATA[CROSMgr::getInst()->m_sRosData.nRosIdx].dSrcMMSI != AIS_AB_MMSI_NULL)
        {
            bStatusTxt = true;
            CLayerNetwork::getInst()->m_uChannelManageModeBaseStMMSI = CROSMgr::getInst()->m_vAllRosDATA[CROSMgr::getInst()->m_sRosData.nRosIdx].dSrcMMSI;
        }
        break;
    case TXT_ID_GRP_ASSGNED_MODE:
        bStatusTxt = CLayerNetwork::getInst()->CheckAssignedModeRunningByMsg23();
        break;
    case TXT_ID_RTN_DEFAULT_MODE:
        bStatusTxt = (!CLayerNetwork::getInst()->CheckAssignedModeRunning() && CROSMgr::getInst()->m_sRosData.nRosIdx == ROS_IDX_HIGHSEA);
        break;
    case TXT_ID_INT_GNSS_SBAS:
        bStatusTxt = CSensorMgr::getInst()->IsIntGNSSSbasMode();
        break;
    }

    return bStatusTxt; 
}

/**
 * @brief Make the TXT sentence
 * @param pszSentence The sentence to be made
 * @param nTxtID The text identifier
 * @return The length of the sentence
 */
int32_t CTxt::MakeSentence(char *pszSentence, int nTxtID)
{
    //-------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.3.2 Using the TXT formatter
    // Status messages shall be IEC 61162-1 compliant "$AITXT"-sentences on the presentation interface output port.
    // Status messages do not activate the alarm relay and do not require an acknowledgement.
    // It shall be possible to monitor the current sensor status by means of a query sentence $xxAIQ,TXT.
    //-------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.2.1
    // ALR-sentences with "alarm numbers" greater than 099 cannot be followed by TXT-sentences containing additional
    // information by using the TXT-sentence's "text identifier". The "text identifier" is limited to the range of 01 to 99.
    // Additional numbers may be used by the manufacturer for other purposes but shall be in the range 051 to 099.
    //----------------------------------------------------------------------------------------------------------------------

    char pstrMultiMsg[RX_MAX_DATA_SIZE];
    char *pstrMsg = GetTxtStringPtr(nTxtID);

    switch (nTxtID)
    {
        case TXT_ID_ASSIGNED_MODE:
        case TXT_ID_GRP_ASSGNED_MODE:
            if (CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI != AIS_AB_MMSI_NULL)
            {
                sprintf(pszSentence, "$AITXT,02,01,%02d,%s", nTxtID, pstrMsg);
                CSentence::AddSentenceTail(pszSentence);

                memset(pstrMultiMsg, 0x00, RX_MAX_DATA_SIZE);
                sprintf(pstrMultiMsg, "$AITXT,02,02,%02d,from base station %09d", nTxtID, CLayerNetwork::getInst()->m_uSlotAssignedModeBaseStMMSI);
                CSentence::AddSentenceTail(pstrMultiMsg);

                strcat(pszSentence, pstrMultiMsg);
            }
            break;
        case TXT_ID_DATALINK_MNG_MODE:
            break;
        case TXT_ID_CH_MNG_MODE:
            if (CLayerNetwork::getInst()->m_uChannelManageModeBaseStMMSI != AIS_AB_MMSI_NULL)
            {
                sprintf(pszSentence, "$AITXT,02,01,%02d,%s", nTxtID, pstrMsg);
                CSentence::AddSentenceTail(pszSentence);

                memset(pstrMultiMsg, 0x00, RX_MAX_DATA_SIZE);
                sprintf(pstrMultiMsg, "$AITXT,02,02,%02d,from base station %09d on channels %04d and %04d", 
                    nTxtID, CLayerNetwork::getInst()->m_uChannelManageModeBaseStMMSI, CROSMgr::getInst()->m_sChSetup.uChannelIdA, CROSMgr::getInst()->m_sChSetup.uChannelIdB);
                CSentence::AddSentenceTail(pstrMultiMsg);

                strcat(pszSentence, pstrMultiMsg);
            }
            break;

        default:
            sprintf(pszSentence, "$AITXT,01,01,%02d,%s", nTxtID, pstrMsg);
            CSentence::AddSentenceTail(pszSentence);
            break;
    }

    return strlen(pszSentence);
}
