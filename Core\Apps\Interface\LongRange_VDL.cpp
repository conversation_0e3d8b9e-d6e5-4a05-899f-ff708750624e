#include <string.h>
#include <ctype.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Uart.h"
#include "Nmea.h"
#include "AisLib.h"
#include "SetupMgr.h"
#include "RosMgr.h"
#include "PI.h"
#include "Timer.h"
#include "GpsLib.h"
#include "UserDirMgr.h"
#include "LayerNetwork.h"
#include "LongRange.h"

BOOL CLongRange::IsLRTxAvail()
{
    BOOL bRet = ((CSetupMgr::getInst()->GetLongRangeCh1() == AIS_DEFAULT_TXLR_CH_1 || CSetupMgr::getInst()->GetLongRangeCh2() == AIS_DEFAULT_TXLR_CH_2) 
                && CSetupMgr::getInst()->GetLongRangeTxEnableMsg27() && m_nLongRangeTxCtrl != LR_TXCTRL_CODE_OFF);
    return bRet;
}

void CLongRange::SetLongRangeRegionCtrl(INT8 nCtrlCode)
{
    if(nCtrlCode != m_nLongRangeTxCtrl)
    {
        DEBUG_LOG("[LR-VDL] SetLRCtrl] ctrlCode: %d -> %d, LRavail:%d, s:%d\r\n",
                m_nLongRangeTxCtrl, nCtrlCode, IsLRTxAvail(), cTimerSys::getInst()->GetCurTimerSec());

        m_nLongRangeTxCtrl = nCtrlCode;
        CLayerNetwork::getInst()->InitLongRangeReportSec();
    }
}

void CLongRange::RunPeriodicallyLongRangeVDL(BOOL bRunUnconditionally)
{
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) < 10)
        return;

    if(bRunUnconditionally || cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > LR_REPORT_INTERVAL_SEC)
    {
        CUserDirMgr::getInst()->CheckLongRangeRegionDataInvalid();

        if(CSetupMgr::getInst()->GetLongRangeTxEnableMsg27())
            SetLongRangeRegionCtrl(CUserDirMgr::getInst()->CheckLongRangeTxCtrlByBaseSt());

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}
