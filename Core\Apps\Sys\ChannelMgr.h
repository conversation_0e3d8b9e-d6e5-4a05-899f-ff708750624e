#ifndef __CHANNELMGR_H__
#define __CHANNELMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "ChTxScheduler.h"
#include "FrameMapMgr.h"
#include "VdlTxMgr.h"
#include "VdlRxMgr.h"
#include "RxModem.h"

class CVdlRxMgr;
class CVdlTxMgr;

class CChannelMgr : public CFrameMapMgr, public CChTxScheduler
{
public:
    CChannelMgr(UINT16 uChNum, UINT16 nLongRangeChID, int nHwLocalRxID);
    ~CChannelMgr();

public:
    void    Initialize(void);
    void    InitChannel(void);
    void    ReloadSetup(void);
    UINT8   GetChOrdinal(void);
    void    SetRxEnableModeCh(bool bTxEnable);
    bool    IsRxAvailableCh(void);
    void    SetTxEnableModeCh(bool bTxEnable);
    bool    IsTxAvailableCh(void);
    bool    IsTxChHealthy(void);
    void    SetChOpMode(BYTE bOpMode);
    INT8    GetChOpMode(void);
    void    SetChOpPhase(BYTE bOpPhase);
    INT8    GetChOpPhase(void);
    bool    SetRxChannelNumber(UINT16 uChNum);
    UINT16  GetRxChannelNumber(void);
    bool    SetTxChannelNumber(UINT16 uChNum);
    UINT16  GetTxChannelNumber(void);
    void    SyncChannel(INT16 nShiftSlot);
    void    ProcessSlotChangeTime_Ch(bool bChangedFrameID);
    void    RunPeriodicallyCh(void);

public:
    INT8    m_nHwLocalRxID;

    UINT16  m_uInitChNum;
    UINT16  m_nChNumTx;                 // channel number(1060 ~ 2088)
    UINT16  m_nChNumRx;                 // channel number(1060 ~ 2088)
    UINT16  m_nChNumLR;

    bool    m_bRxEnableMode;
    bool    m_bTxEnableMode;

    INT8    m_nChOpMode;
    INT8    m_nChOpPhase;
    DWORD   m_dwChOpPhaseStartTick;

    std::shared_ptr<cRxModem> m_pRxModem;
    CVdlRxMgr     *m_pVdlRxMgr;
    CVdlTxMgr     *m_pVdlTxMgr;
};

#endif//__CHANNELMGR_H__
