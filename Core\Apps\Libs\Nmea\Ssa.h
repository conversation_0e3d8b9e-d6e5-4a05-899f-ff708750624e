/**
 * @file    Ssa.h
 * @brief   Ssa header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Sentence.h"

#ifndef __SSA_H__
#define __SSA_H__

#define SSA_NA      0
#define SSA_EPV     1
#define SSA_SSD     2
#define SSA_VSD     3

/******************************************************************************
 * IEC 61993-2:2018 Annex F
 * 
 * SSA - Sender signature authentication
 *
 * $--SSA,ccc,c,h--h*hh<CR><LF>
 *         |  |  |
 *         1  2  3
 *
 * 1. Signature protected sentence
 * 2. Signature calculation method
 * 3. Signature authentication
 *
 ******************************************************************************/
class CSsa : public CSentence
{
public:
    CSsa();

    bool     Parse(const char *pszSentence);
    void     ClearData(void);
    uint8_t  GetCode(void)       { return m_nCode; }
    char     GetSignMethod(void) { return m_cSignMethod; }
    char*    GetAuthenticationPtr(void){ return m_szAuthentication; }

    void     RunPeriodically(void);

protected:
    uint8_t     m_nCode;
    char        m_cSignMethod;
    char        m_szAuthentication[33];
    uint32_t    m_dwRcvTick;
};

#endif /* __SSA_H__ */

