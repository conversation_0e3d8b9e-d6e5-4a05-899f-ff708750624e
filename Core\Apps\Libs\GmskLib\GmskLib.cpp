/**
 * @file    GmskLib.c
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include "AisMsg.h"
#include "AisLib.h"
#include "SysLib.h"
#include "GmskLib.h"

//============================================================================
static xAisTxGmskDATA G_xAisTxGmskData = {0x00,};
//============================================================================
BYTE gpVdlSlotData[NUM_MAX_BITS_TXBLOCK];
BYTE gbSlotDataByteIdx = 0;
BYTE gbSlotDataBitIdx = 0;

//============================================================================
xAisTxGmskDATA *GetAisTxGmskDATA(void)
{
    return(&G_xAisTxGmskData);
}

void ClearAisTxGmskBuffData(void)
{
    int  i;

    G_xAisTxGmskData.vGmskTxHead = 0;
    G_xAisTxGmskData.vGmskTxTail = 0;
    G_xAisTxGmskData.nRawBitSize = 0;
    G_xAisTxGmskData.nTxPrevFreq = 0;

    for (i = 0; i < AIS_GMSK_TX_BUFF_SIZE; i++)
    {
        G_xAisTxGmskData.vGmskTxData[i].pGmskDaData = (HWORD*)SysAllocMemory(sizeof(HWORD) * (AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX * 8 * AIS_DATA_SAMPLES_PER_ONE_BIT));
    }
}

void SetAisTxGmskDacRefOffset(int nDacCenter, int nDacOffset)
{
    G_xAisTxGmskData.nLowerDAC[0]  = nDacOffset;
    G_xAisTxGmskData.nLowerDAC[1]  = nDacOffset;
    G_xAisTxGmskData.nUpperDAC[0]  = nDacOffset;
    G_xAisTxGmskData.nUpperDAC[1]  = nDacOffset;
    G_xAisTxGmskData.nCnterDAC[0]  = nDacCenter;
    G_xAisTxGmskData.nCnterDAC[1]  = nDacCenter;
}

HWORD GetAisTxGmskLowerDAC(int nChNo)
{
    return(G_xAisTxGmskData.nLowerDAC[nChNo]);
}

HWORD GetAisTxGmskUpperDAC(int nChNo)
{
    return(G_xAisTxGmskData.nUpperDAC[nChNo]);
}

HWORD GetAisTxGmskCnterDAC(int nChNo)
{
    return(G_xAisTxGmskData.nCnterDAC[nChNo]);
}
//============================================================================
void ClearAisTxGmskDATA(void)
{
    memset(gpVdlSlotData, 0, NUM_MAX_BITS_TXBLOCK);
    gbSlotDataByteIdx = 0;
    gbSlotDataBitIdx = 7;

    G_xAisTxGmskData.nRawBitSize   = 0;

    G_xAisTxGmskData.vGmskTxData[G_xAisTxGmskData.vGmskTxHead].nGmskAdSize = 0;
    G_xAisTxGmskData.vGmskTxData[G_xAisTxGmskData.vGmskTxHead].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;

    G_xAisTxGmskData.nNrziRunMode  = MODE_VAL_OFF;
    G_xAisTxGmskData.nNrziRunLevel = NRZI_START_LEVEL;

    G_xAisTxGmskData.nBitStuffMode = MODE_VAL_OFF;
    G_xAisTxGmskData.nBitStuffCntr = 0;
    G_xAisTxGmskData.nNumStuffBits = 0;
}

void SetAisTxNrziRunMode(int nMode)
{
      G_xAisTxGmskData.nNrziRunMode  = nMode;
}

void SetAisTxBitStuffMode(int nMode)
{
      G_xAisTxGmskData.nBitStuffMode = nMode;
      G_xAisTxGmskData.nBitStuffCntr = 0;
      G_xAisTxGmskData.nNumStuffBits = 0;
}

int   MakeRealAisTxRawData(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits, int nBitStuffBitSize)
{
    UCHAR bDataX;
    HWORD wDataX;
    int nBufferBits;
    int i;

    ClearAisTxGmskDATA();

    SetAisTxBitStuffMode(MODE_VAL_OFF);
    SetAisTxNrziRunMode(MODE_VAL_ON);

    bDataX = 0x55;
    AppendVdlBitStream(&bDataX, 8);                 // Training sequence(preamble), 010101...
    AppendVdlBitStream(&bDataX, 8);                 // Training sequence(preamble), 010101...
    AppendVdlBitStream(&bDataX, 8);                 // Training sequence(preamble), 010101...

    bDataX = 0x7E;
    AppendVdlBitStream(&bDataX, 8);                 // Start flag, 01111110

    SetAisTxBitStuffMode(MODE_VAL_ON);              // Start bit stuffing for data portion and FCS
    AppendMsgBitStream(pAisTxMsgData, nAisTxMsgSizeBits);

    wDataX = GetAisCrc16FCS(&gpVdlSlotData[4], nAisTxMsgSizeBits >> 3);

    bDataX = (UCHAR)(wDataX >> 8);                  // MSB of FCS
    AppendVdlBitStream(&bDataX, 8);                 // MSB of FCS

    bDataX = (UCHAR)wDataX;                         // LSB of FCS
    AppendVdlBitStream(&bDataX, 8);                 // LSB of FCS

    SetAisTxBitStuffMode(MODE_VAL_OFF);             // Finish bit stuffing

    bDataX = 0x7E;
    AppendVdlBitStream(&bDataX, 8);                 // End flag, 01111110

    SetAisTxNrziRunMode(MODE_VAL_OFF);

    nBufferBits = NUM_TX_END_BUFFER_BIT;
    for(i = 0 ; i < nBufferBits ; i++)
        AppendPhyBitmapData(0);

    return(G_xAisTxGmskData.nRawBitSize);
}

int   MakeTestMsgFrameAisTxRawDataRandomPRBS(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits)
{
    //-----------------------------------------------------------------------------------------
    // IEC-61993-2(ed2.0) 10.4 Standard test signal number 4 (PRBS)
    // A Pseudo Random Bit Sequence (PRBS) as specified in Recommendation ITU-T O.153 as the
    // data within an AIS message frame with header, start flag, end flag and CRC. NRZI is not
    // applied to the PRBS stream or CRC. The RF should be ramped up and down on either end of
    // the AIS message frame.
    //-----------------------------------------------------------------------------------------

    UCHAR bDataX;
    HWORD wDataX;

    ClearAisTxGmskDATA();

    SetAisTxBitStuffMode(MODE_VAL_OFF);
    SetAisTxNrziRunMode(MODE_VAL_ON);

    bDataX = 0x55;
    AppendVdlBitStream(&bDataX, 8);                 // Training sequence(preamble), 010101...
    AppendVdlBitStream(&bDataX, 8);                 // Training sequence(preamble), 010101...
    AppendVdlBitStream(&bDataX, 8);                 // Training sequence(preamble), 010101...

    bDataX = 0x7E;
    AppendVdlBitStream(&bDataX, 8);                 // Start flag, 01111110

    SetAisTxBitStuffMode(MODE_VAL_ON);              // Start bit stuffing for data portion and FCS
    SetAisTxNrziRunMode(MODE_VAL_OFF);              // NRZI 는 data, CRC에는 적용안시킴, NRZI is not applied to the PRBS stream or CRC.

    AppendMsgBitStream(pAisTxMsgData, nAisTxMsgSizeBits);

    wDataX = GetAisCrc16FCS(&gpVdlSlotData[4], nAisTxMsgSizeBits >> 3);

    bDataX = (UCHAR)(wDataX >> 8);                  // MSB of FCS
    AppendVdlBitStream(&bDataX, 8);                 // MSB of FCS

    bDataX = (UCHAR)wDataX;                         // LSB of FCS
    AppendVdlBitStream(&bDataX, 8);                 // LSB of FCS

    SetAisTxBitStuffMode(MODE_VAL_OFF);             // Finish bit stuffing
    SetAisTxNrziRunMode(MODE_VAL_ON);                // NRZI 는 data, CRC에만 적용안시킴

    bDataX = 0x7E;
    AppendVdlBitStream(&bDataX, 8);                 // End flag, 01111110

    SetAisTxNrziRunMode(MODE_VAL_OFF);

    return(G_xAisTxGmskData.nRawBitSize);
}

int   MakeTestMsgFrameAisTxRawDataFixedPRBS(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits, BYTE bPacketID, BOOL bInvertedNRZI)
{
    //-------------------------------------------------------------
    // IEC-61993-2(ed2.0) 10.5 Standard test signal number 5 (PRBS)
    // Refer to Figure 3 - Format for repeating four-packet cluster
    //
    // bPacketID == 1 : Packet 1
    // bPacketID == 2 : Packet 2
    // bInvertedNRZI == FALSE : the first Packet of a cluster
    // bInvertedNRZI == TRUE : the second Packet of a cluster
    //-------------------------------------------------------------

    UCHAR bDataX;
    HWORD wDataX;

    ClearAisTxGmskDATA();

    SetAisTxBitStuffMode(MODE_VAL_OFF);
    SetAisTxNrziRunMode(MODE_VAL_ON);

    if(bInvertedNRZI)
        G_xAisTxGmskData.nNrziRunLevel = 1;

    if(bPacketID == 1)
        bDataX = 0x55;                              // Training sequence(preamble), 010101...
    else
        bDataX = 0xAA;                              // Training sequence(preamble), 101010...

    AppendVdlBitStream(&bDataX, 8);
    AppendVdlBitStream(&bDataX, 8);
    AppendVdlBitStream(&bDataX, 6);                 // Preamble 22bit, Preamble reduced by 2 bits because of ramp-up overlap

    bDataX = 0x7E;
    AppendVdlBitStream(&bDataX, 8);                 // Start flag, 01111110

    SetAisTxBitStuffMode(MODE_VAL_ON);              // Start bit stuffing for data portion and FCS

    AppendMsgBitStream(pAisTxMsgData, nAisTxMsgSizeBits);

    wDataX = GetAisCrc16FCS(&gpVdlSlotData[4], nAisTxMsgSizeBits >> 3);

    bDataX = (UCHAR)(wDataX >> 8);                  // MSB of FCS
    AppendVdlBitStream(&bDataX, 8);                 // MSB of FCS

    bDataX = (UCHAR)wDataX;                         // LSB of FCS
    AppendVdlBitStream(&bDataX, 8);                 // LSB of FCS

    SetAisTxBitStuffMode(MODE_VAL_OFF);             // Finish bit stuffing

    bDataX = 0x7E;
    AppendVdlBitStream(&bDataX, 8);                 // End flag, 01111110

    SetAisTxNrziRunMode(MODE_VAL_OFF);

    return(G_xAisTxGmskData.nRawBitSize);
}

int   MakeTestInfiniteSeqAisTxRawData(UCHAR *pAisTxMsgData, int nAisTxMsgSizeBits)
{
    ClearAisTxGmskDATA();

    SetAisTxBitStuffMode(MODE_VAL_OFF);
    SetAisTxNrziRunMode(MODE_VAL_OFF);

    AppendVdlBitStream(pAisTxMsgData, nAisTxMsgSizeBits);

    SetAisTxNrziRunMode(MODE_VAL_OFF);

    return(G_xAisTxGmskData.nRawBitSize);
}

void AppendPhyBitmapData(UCHAR bBitMapData)
{
    //------------------------------------------------------------------------------------------
    // NRZI encoding : gives a change in the level when a zero is encountered in the bit stream
    // ex) *********... -> *********
    //------------------------------------------------------------------------------------------


    if (G_xAisTxGmskData.nNrziRunMode)
    {
        if (!bBitMapData)
            G_xAisTxGmskData.nNrziRunLevel = 1 - G_xAisTxGmskData.nNrziRunLevel;

        bBitMapData = G_xAisTxGmskData.nNrziRunLevel;
    }

    if (bBitMapData)
        G_xAisTxGmskData.vRawBitData[G_xAisTxGmskData.nRawBitSize++] = 0x01;
    else
        G_xAisTxGmskData.vRawBitData[G_xAisTxGmskData.nRawBitSize++] = 0x00;
}

void AppendVdlBitmapData(UCHAR bBitMapData)
{
    gpVdlSlotData[gbSlotDataByteIdx] |= bBitMapData;
    if(gbSlotDataBitIdx-- <= 0)
    {
        gbSlotDataByteIdx++;
        gbSlotDataBitIdx = 7;
    }

      AppendPhyBitmapData(bBitMapData);
}

void AppendVdlBitStream(UCHAR *pBitStream, int nNumBits)
{
    //-------------------------------------------------------------------------
    // pBitStream 의 각 바이트 data 를 MSB부터 nNumBits 비트 만큼 버퍼에 저장함
    // ITU-R.M.1371-5 규정 3.2.2.1 Bit Stuffing 항목
    //-------------------------------------------------------------------------
    int   nCnt;
    UCHAR bData = 0, bBitMapData = 0, iData = 0;

    for (nCnt = 0; nCnt < nNumBits; nCnt++)
    {
        if (!(nCnt % 8))
            bData = pBitStream[iData++];

        bBitMapData = (bData & 0x80) ? (1 << gbSlotDataBitIdx) : 0;
        AppendVdlBitmapData(bBitMapData);

        if (G_xAisTxGmskData.nBitStuffMode) {
            if (bBitMapData) {
                if (++G_xAisTxGmskData.nBitStuffCntr >= 5) {
					// Bit stuffing, if five consecutive 1s are found, 0 should be inserted
                    AppendPhyBitmapData(0);
                    G_xAisTxGmskData.nBitStuffCntr = 0;
                    G_xAisTxGmskData.nNumStuffBits++;
                }
            }
            else {
                G_xAisTxGmskData.nBitStuffCntr = 0;
			}
        }

        bData <<= 1;
    }
}

void AppendMsgBitStream(BYTE *pBitStream, int nNumBits)
{
    //------------------------------------------------------------------
    // pBitStream 의 각 바이트 데이터를 LSB 부터 가져와서 버퍼에 저장함
    // ITU-R.M.1371-3 규정 3.2.2.1 Bit Stuffing 항목
    //------------------------------------------------------------------
    int nCnt = 0;
    BYTE bData = 0, bBitMapData = 0, iData = 0;
    for(nCnt = 0 ; nCnt < nNumBits ; nCnt++)
    {
        if(!(nCnt % 8))
            bData = pBitStream[iData++];

        bBitMapData = (bData & 0x01) ? (1 << gbSlotDataBitIdx) : 0;
        AppendVdlBitmapData(bBitMapData);

        if (G_xAisTxGmskData.nBitStuffMode)
        {
            if(bBitMapData)
            {
                if(++G_xAisTxGmskData.nBitStuffCntr >= 5)
                {
					// Bit stuffing, if five consecutive 1s are found, 0 should be inserted
                    AppendPhyBitmapData(0);
                    G_xAisTxGmskData.nBitStuffCntr = 0;
                }
            }
            else
                G_xAisTxGmskData.nBitStuffCntr = 0;
        }

        bData >>= 1;
    }
}

//============================================================================
static const uint16_t vCrc16Table[256] = {
    0x0000,0x1021,0x2042,0x3063,0x4084,0x50a5,0x60c6,0x70e7,
    0x8108,0x9129,0xa14a,0xb16b,0xc18c,0xd1ad,0xe1ce,0xf1ef,
    0x1231,0x0210,0x3273,0x2252,0x52b5,0x4294,0x72f7,0x62d6,
    0x9339,0x8318,0xb37b,0xa35a,0xd3bd,0xc39c,0xf3ff,0xe3de,
    0x2462,0x3443,0x0420,0x1401,0x64e6,0x74c7,0x44a4,0x5485,
    0xa56a,0xb54b,0x8528,0x9509,0xe5ee,0xf5cf,0xc5ac,0xd58d,
    0x3653,0x2672,0x1611,0x0630,0x76d7,0x66f6,0x5695,0x46b4,
    0xb75b,0xa77a,0x9719,0x8738,0xf7df,0xe7fe,0xd79d,0xc7bc,
    0x48c4,0x58e5,0x6886,0x78a7,0x0840,0x1861,0x2802,0x3823,
    0xc9cc,0xd9ed,0xe98e,0xf9af,0x8948,0x9969,0xa90a,0xb92b,
    0x5af5,0x4ad4,0x7ab7,0x6a96,0x1a71,0x0a50,0x3a33,0x2a12,
    0xdbfd,0xcbdc,0xfbbf,0xeb9e,0x9b79,0x8b58,0xbb3b,0xab1a,
    0x6ca6,0x7c87,0x4ce4,0x5cc5,0x2c22,0x3c03,0x0c60,0x1c41,
    0xedae,0xfd8f,0xcdec,0xddcd,0xad2a,0xbd0b,0x8d68,0x9d49,
    0x7e97,0x6eb6,0x5ed5,0x4ef4,0x3e13,0x2e32,0x1e51,0x0e70,
    0xff9f,0xefbe,0xdfdd,0xcffc,0xbf1b,0xaf3a,0x9f59,0x8f78,
    0x9188,0x81a9,0xb1ca,0xa1eb,0xd10c,0xc12d,0xf14e,0xe16f,
    0x1080,0x00a1,0x30c2,0x20e3,0x5004,0x4025,0x7046,0x6067,
    0x83b9,0x9398,0xa3fb,0xb3da,0xc33d,0xd31c,0xe37f,0xf35e,
    0x02b1,0x1290,0x22f3,0x32d2,0x4235,0x5214,0x6277,0x7256,
    0xb5ea,0xa5cb,0x95a8,0x8589,0xf56e,0xe54f,0xd52c,0xc50d,
    0x34e2,0x24c3,0x14a0,0x0481,0x7466,0x6447,0x5424,0x4405,
    0xa7db,0xb7fa,0x8799,0x97b8,0xe75f,0xf77e,0xc71d,0xd73c,
    0x26d3,0x36f2,0x0691,0x16b0,0x6657,0x7676,0x4615,0x5634,
    0xd94c,0xc96d,0xf90e,0xe92f,0x99c8,0x89e9,0xb98a,0xa9ab,
    0x5844,0x4865,0x7806,0x6827,0x18c0,0x08e1,0x3882,0x28a3,
    0xcb7d,0xdb5c,0xeb3f,0xfb1e,0x8bf9,0x9bd8,0xabbb,0xbb9a,
    0x4a75,0x5a54,0x6a37,0x7a16,0x0af1,0x1ad0,0x2ab3,0x3a92,
    0xfd2e,0xed0f,0xdd6c,0xcd4d,0xbdaa,0xad8b,0x9de8,0x8dc9,
    0x7c26,0x6c07,0x5c64,0x4c45,0x3ca2,0x2c83,0x1ce0,0x0cc1,
    0xef1f,0xff3e,0xcf5d,0xdf7c,0xaf9b,0xbfba,0x8fd9,0x9ff8,
    0x6e17,0x7e36,0x4e55,0x5e74,0x2e93,0x3eb2,0x0ed1,0x1ef0
};

HWORD GetAisCrc16FCS(UCHAR *pData, int nLen)
{
    HWORD wCRC = 0xffff;
    int   i;

    for (i = 0; i < nLen; i++)
         wCRC = (wCRC << 8) ^ vCrc16Table[((wCRC >> 8) ^ *pData++) & 0x00FF];

    wCRC ^= 0xffff;

    return(wCRC);
}

//============================================================================
void MakeAisGmskTxSendData(int nTxChNo, int nSlotNo, int nTxMsgID)
{
    #define __USE_GMSK_BF_CIRCULAR_MODE__
    static int  vTxGmskCoefficient[TX_GMSK_BT_0_4_FIR_N] = {1,  24,  261,  1942, 10015,  35871,  89230, 154161, 184981, 154161, 89230, 35871, 10015, 1942, 261, 24, 1}; // BT=0.4

    SHORT nDataX;
    double   nTempX;
    int   i, nBuffX, nBuffY;
    int   nBitSize;
    int   nHead = G_xAisTxGmskData.vGmskTxHead;
    UCHAR *pBitData = G_xAisTxGmskData.vRawBitData;
    int   nTotalBit = G_xAisTxGmskData.nRawBitSize;
    int   nLowerDAC = G_xAisTxGmskData.nLowerDAC[0];
    int   nUpperDAC = G_xAisTxGmskData.nUpperDAC[0];
    int   nCnterDAC = G_xAisTxGmskData.nCnterDAC[0];


    memset(G_xAisTxGmskData.vGmskBfData, 0x00, sizeof(G_xAisTxGmskData.vGmskBfData));

    G_xAisTxGmskData.vGmskTxData[nHead].nTxChnlNo   = nTxChNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxFreqVal  = CAisLib::GetAisFreqByChannelNo(nTxChNo);
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSlotNo   = nSlotNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxMsgID    = nTxMsgID;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;
    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = 0;

  #if defined(__USE_GMSK_BF_CIRCULAR_MODE__)
    nBuffX = 0;
  #endif

    while (nTotalBit--)
    {
        nBitSize = AIS_DATA_SAMPLES_PER_ONE_BIT;

        if (*pBitData++)
            nDataX = +1;
        else
            nDataX = -1;

        while (nBitSize--)
        {
        #if defined(__USE_GMSK_BF_CIRCULAR_MODE__)
            nBuffY = nBuffX - 1;
            if (nBuffY < 0)
                nBuffY = nBuffY + TX_GMSK_BT_0_4_FIR_N;
            G_xAisTxGmskData.vGmskBfData[nBuffY] = nDataX;
        #else
            G_xAisTxGmskData.vGmskBfData[TX_GMSK_BT_0_4_FIR_N - 1] = nDataX;
        #endif

        #if defined(__USE_GMSK_BF_CIRCULAR_MODE__)
            nBuffY = nBuffX;
            nTempX = 0;
            for (i = 0; i < TX_GMSK_BT_0_4_FIR_N; i++) {
                nTempX = nTempX + vTxGmskCoefficient[i] * G_xAisTxGmskData.vGmskBfData[nBuffY];
                ++nBuffY;
                if (nBuffY >= TX_GMSK_BT_0_4_FIR_N)
                    nBuffY -= TX_GMSK_BT_0_4_FIR_N;
            }

            ++nBuffX;
            if (nBuffX >= TX_GMSK_BT_0_4_FIR_N) {
                nBuffX -= TX_GMSK_BT_0_4_FIR_N;
			}
        #else
            nTempX = 0;
            for (i = 0; i < TX_GMSK_BT_0_4_FIR_N; i++) {
                nTempX = nTempX + vTxGmskCoefficient[i] * G_xAisTxGmskData.vGmskBfData[i];
			}

            if (nTempX > TX_GMSK_MAX_DATA_VALUE) {
                while (1);
            }
        #endif

            if (nTempX >= 0)
                nTempX *= nUpperDAC;
            else
                nTempX *= nLowerDAC;
            nTempX = nTempX / TX_GMSK_MAX_DATA_VALUE + nCnterDAC;

            G_xAisTxGmskData.vGmskTxData[nHead].pGmskDaData[G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize++] = (HWORD)nTempX;

        #if !defined(__USE_GMSK_BF_CIRCULAR_MODE__)
            memmove(&G_xAisTxGmskData.vGmskBfData[0], &G_xAisTxGmskData.vGmskBfData[1], sizeof(G_xAisTxGmskData.vGmskBfData[0]) * (TX_GMSK_BT_0_4_FIR_N - 1));
        #endif
		}
    }

    ++G_xAisTxGmskData.vGmskTxHead;
    if (G_xAisTxGmskData.vGmskTxHead >= AIS_GMSK_TX_BUFF_SIZE)
        G_xAisTxGmskData.vGmskTxHead  = 0;
}

int MakeAisTestSignal2GmskTxSendData(int nTxChNo, int nSlotNo)
{
    HWORD pwGmskData[] = {0x091A,0x0ADC,0x0B83,0x0ADC,0x091A,0x06E5,0x0523,0x047C,0x0523,0x06E5};    // DC offset center : 2048, range : 1200

    const int SIZE_GMSK_DATA = sizeof(pwGmskData) / sizeof(pwGmskData[0]);

    int   nHead = G_xAisTxGmskData.vGmskTxHead;
     int   nTotalSamples = 256 * AIS_DATA_SAMPLES_PER_ONE_BIT / SIZE_GMSK_DATA * SIZE_GMSK_DATA;

    G_xAisTxGmskData.vGmskTxData[nHead].nTxChnlNo   = nTxChNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxFreqVal  = CAisLib::GetAisFreqByChannelNo(nTxChNo);
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSlotNo   = nSlotNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;
    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = 0;

    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = nTotalSamples;
    int j = 0;
    for(int i = 0 ; i < G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize ; i++)
    {
        G_xAisTxGmskData.vGmskTxData[nHead].pGmskDaData[i] = pwGmskData[j];
        if(++j >= SIZE_GMSK_DATA)
            j = 0;
    }

    ++G_xAisTxGmskData.vGmskTxHead;
    if (G_xAisTxGmskData.vGmskTxHead >= AIS_GMSK_TX_BUFF_SIZE)
        G_xAisTxGmskData.vGmskTxHead  = 0;
    return nTotalSamples;
}

int MakeAisTestSignal3GmskTxSendData(int nTxChNo, int nSlotNo)
{
    // 00001111,
    HWORD pwGmskData[] ={0x0350,0x0350,0x0350,0x0350,0x0350,0x0350,0x0350,0x0350,0x0350,0x0350,
                         0x0356,0x0376,0x03E6,0x04FD,0x06DE,0x0921,0x0B02,0x0C19,0x0C89,0x0CA9,
                         0x0CAF,0x0CAF,0x0CAF,0x0CAF,0x0CAF,0x0CAF,0x0CAF,0x0CAF,0x0CAF,0x0CAF,
                         0x0CA9,0x0C89,0x0C19,0x0B02,0x0921,0x06DE,0x04FD,0x03E6,0x0376,0x0356};         // DC offset center : 2048, range : 1200

    const int SIZE_GMSK_DATA = sizeof(pwGmskData) / sizeof(pwGmskData[0]);

    int   nHead = G_xAisTxGmskData.vGmskTxHead;
    int   nTotalSamples = 256 * AIS_DATA_SAMPLES_PER_ONE_BIT / SIZE_GMSK_DATA * SIZE_GMSK_DATA;

    G_xAisTxGmskData.vGmskTxData[nHead].nTxChnlNo   = nTxChNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxFreqVal  = CAisLib::GetAisFreqByChannelNo(nTxChNo);
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSlotNo   = nSlotNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;
    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = 0;

    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = nTotalSamples;
    int j = 0;
    for(int i = 0 ; i < G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize ; i++)
    {
        G_xAisTxGmskData.vGmskTxData[nHead].pGmskDaData[i] = pwGmskData[j];
        if(++j >= SIZE_GMSK_DATA) {
            j = 0;
		}
    }

    ++G_xAisTxGmskData.vGmskTxHead;
    if (G_xAisTxGmskData.vGmskTxHead >= AIS_GMSK_TX_BUFF_SIZE)
        G_xAisTxGmskData.vGmskTxHead  = 0;
    return nTotalSamples;
}

void MakeAisUnmodCarrierGmskTxSendData(int nTxChNo, int nSlotNo)
{
    int   nHead = G_xAisTxGmskData.vGmskTxHead;
    int   nTotalBit = AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX * 8;
    int   nCnterDAC = G_xAisTxGmskData.nCnterDAC[0];

    G_xAisTxGmskData.vGmskTxData[nHead].nTxChnlNo   = nTxChNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxFreqVal  = CAisLib::GetAisFreqByChannelNo(nTxChNo);
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSlotNo   = nSlotNo;
    G_xAisTxGmskData.vGmskTxData[nHead].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;
    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = 0;

    G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize = nTotalBit * AIS_DATA_SAMPLES_PER_ONE_BIT;
    for(int i = 0 ; i < G_xAisTxGmskData.vGmskTxData[nHead].nGmskAdSize ; i++)
        G_xAisTxGmskData.vGmskTxData[nHead].pGmskDaData[i] = (HWORD)nCnterDAC;

    ++G_xAisTxGmskData.vGmskTxHead;
    if (G_xAisTxGmskData.vGmskTxHead >= AIS_GMSK_TX_BUFF_SIZE)
        G_xAisTxGmskData.vGmskTxHead  = 0;
}
//============================================================================
