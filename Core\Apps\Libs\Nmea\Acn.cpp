/**
 * @file    Acn.cpp
 * @brief   Acn class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "SysLog.h"
#include "Acn.h"

/*****************************************************************************
 *
 * ACN - Acnnowledge alarm
 *
 * $--ACN,hhmmss.ss,aaa,x.x,x.x,c,a*hh<CR><LF>
 *         |         |   |   |  | |
 *         1         2   3   4  5 6
 *
 * 1. Time
 * 2. Manufacturer mnemonic code
 * 3. Alert identifier
 * 4. Alert instance, 1 to 999999
 * 5. Alert command, A, Q, O or S
 * 6. Sentence status flag. This field should be 'C' and should not be a null field. 'C' == Command
 *
 ******************************************************************************/
// Define static member variables
int32_t CAcn::m_nAlertID = 0;
int32_t CAcn::m_nAlertManufacturer = 0;
int32_t CAcn::m_nAlertInstance = 0;
char    CAcn::m_cAlertCommand = 0;
char    CAcn::m_cSentenceStatusFlag = 0;

CAcn::CAcn() : CSentence()
{

}

/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAcn::Parse(const char *pszSentence)
{
    // Manufacturer mnemonic code
    m_nAlertManufacturer = GetFieldInteger(pszSentence, 2);
    if (m_nAlertManufacturer == NMEA_NULL_INTEGER)
        m_nAlertManufacturer = 0;

    // Alert Identifier
    m_nAlertID = GetFieldInteger(pszSentence, 3);
    if (m_nAlertID == NMEA_NULL_INTEGER)
        return false;

    // Alert Instance, 1 to 999999
    m_nAlertInstance = GetFieldInteger(pszSentence, 4);
    if (m_nAlertInstance == NMEA_NULL_INTEGER)
        return false;

    // Alert command
    // acknowledge: A
    // request / repeat information: Q
    // responsibility transfer: O
    // silence: S
    m_cAlertCommand = GetFieldChar(pszSentence, 5);
    if (m_cAlertCommand == NMEA_NULL_CHAR)
        return false;

    // Sentence status flag
    // This field should be “C” and should not be a null field. 
    // This field indicates a command. 
    // A sentence without “C” is not a command.
    m_cSentenceStatusFlag = GetFieldChar(pszSentence, 6);
    if (m_cSentenceStatusFlag == NMEA_NULL_CHAR || m_cSentenceStatusFlag != 'C')
        return false;

    return true;
}

/**
 * @brief Make the ACN sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CAcn::MakeSentence(char *pszSentence)
{
    return strlen(pszSentence);
}
