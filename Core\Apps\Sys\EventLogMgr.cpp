#include "SysLog.h"
#include "Ship.h"
#include "SensorMgr.h"
#include "SetupMgr.h"
#include "Timer.h"
#include "GnssInternal.h"
#include "LayerNetwork.h"
#include "LayerPhysical.h"
#include "SysOpStatus.h"
#include "EventLogMgr.h"

CEventLogMgr::CEventLogMgr()
{
    m_pLogData = (EVENTLOG_DATA*)SysAllocMemory(sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA);
    memset(m_pLogData, 0, sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA);

    CAisLib::SetDefaultSysDateTime(&m_sPowerOffTime);
    m_bPowerOffTimeUTC = false;

    CAisLib::SetDefaultSysDateTime(&m_sMMSINullTime);
    m_bMMSINullTimeUTC = false;

    CAisLib::SetDefaultSysDateTime(&m_sRxOnlyStartTime);
    m_bRxOnlyStartTimeUTC = false;

    CAisLib::SetDefaultSysDateTime(&m_sTxMulFuncStartTime);
    m_bTxMulFuncStartTimeUTC = false;

    m_bCheckEventLogDone = false;
}

CEventLogMgr::~CEventLogMgr()
{
}

/**
 * @brief Reset event log data
 * @param pLogData The event log data to be reset
 */
void CEventLogMgr::ResetEventLog(EVENTLOG_DATA *pLogData)
{
    CAisLib::SetDefaultSysDateTime(&(pLogData->sEventTime));
    CAisLib::SetDefaultSysDateTime(&(pLogData->sEventEndTime));

    pLogData->sEventTime.nValid = false;
    pLogData->bEventTimeUTC    = false;
    pLogData->nEventLogID    = 0;
    pLogData->nDurationMin    = 0;
    pLogData->nLogFlag        = false;
}

/**
 * @brief Add event log data
 * @param nEventLogID The event log ID
 * @param pLogData The event log data to be added
 * @param nIndexToSave The index to save the log data
 * @return The added event log data
 */
EVENTLOG_DATA *CEventLogMgr::AddEventLog(int nEventLogID, EVENTLOG_DATA *pLogData, int nIndexToSave)
{
    if(nIndexToSave >= 0)
    {
        m_pLogData[nIndexToSave] = *pLogData;
        return &m_pLogData[nIndexToSave];
    }

    memmove(&m_pLogData[1], &m_pLogData[0], sizeof(EVENTLOG_DATA)*(EVENTLOG_NUM_LOGDATA-1));

    m_pLogData[0]                = *pLogData;
    m_pLogData[0].nEventLogID    = nEventLogID;
    m_pLogData[0].nLogFlag        = true;

    if(!pLogData->sEventTime.nValid)
    {
        if(cShip::getOwnShipInst()->xSysTime.nValid)
        {
            m_pLogData[0].sEventTime = cShip::getOwnShipInst()->xSysTime;
            m_pLogData[0].bEventTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_pLogData[0].sEventTime);
            m_pLogData[0].bEventTimeUTC = false;
        }
    }

    if(!pLogData->sEventEndTime.nValid)
    {
        if(cShip::getOwnShipInst()->xSysTime.nValid)
        {
            m_pLogData[0].sEventEndTime = cShip::getOwnShipInst()->xSysTime;
            m_pLogData[0].bEventTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_pLogData[0].sEventEndTime);
            m_pLogData[0].bEventTimeUTC = false;
        }
    }

    return &(m_pLogData[0]);
}

/**
 * @brief Check if the event log data is available
 * @param pLogData The event log data to be checked
 * @return True if the event log data is available, false otherwise
 */
bool CEventLogMgr::CheckAvailableEventLog(EVENTLOG_DATA *pLogData)
{
    return (ALRID_SECURITYLOG_MIN <= pLogData->nEventLogID && pLogData->nEventLogID <= ALRID_SECURITYLOG_MAX) &&
            (pLogData->bEventTimeUTC == true || pLogData->bEventTimeUTC == false) &&
            (pLogData->nLogFlag == 0 || pLogData->nLogFlag == 1);
}

/**
 * @brief Clear the event log data
 */
void  CEventLogMgr::ClearEventLog(void)
{
    for(int i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
        ResetEventLog(&m_pLogData[i]);

    CAisLib::SetDefaultSysDateTime(&m_sPowerOffTime);
    m_bPowerOffTimeUTC = false;
}

/**
 * @brief Find event log data
 * @param nEventLogID The event log ID
 * @param pEventStartTime The event start time
 * @return The index of the found event log data
 */
int CEventLogMgr::FindEventLog(int nEventLogID, SYS_DATE_TIME *pEventStartTime)
{
    for(int i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
    {
        if(m_pLogData[i].nEventLogID == nEventLogID && !memcmp(&m_pLogData[i].sEventTime, pEventStartTime, sizeof(SYS_DATE_TIME)))
            return i;
    }
    return -1;
}

/**
 * @brief Serialize the event log data
 * @param pBackData The buffer to store the serialized data
 * @return The size of the serialized data
 */
int CEventLogMgr::SerializeEventLog(UCHAR *pBackData)
{
    int    nSize;
    UCHAR  *pTemp;

    pTemp = pBackData;

    // Data header
    memcpy(pTemp, SETUP_HEADER, SETUP_HEADER_LEN);
    pTemp += SETUP_HEADER_LEN;

    memmove(pTemp, &m_sPowerOffTime,    sizeof(m_sPowerOffTime));                       pTemp += sizeof(m_sPowerOffTime);
    memmove(pTemp, &m_bPowerOffTimeUTC, sizeof(m_bPowerOffTimeUTC));                    pTemp += sizeof(m_bPowerOffTimeUTC);
    memmove(pTemp, m_pLogData,          sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA);  pTemp += sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA;

    // Checksum
    DWORD *pdwChecksum = (DWORD*)pTemp;
    nSize = pTemp - pBackData;
    *pdwChecksum = GetCrc32(pBackData, nSize);
    nSize += SETUP_CHECKSUM_LEN;

    return nSize;
}

/**
 * @brief Load the event log data
 * @param pBackData The buffer to store the serialized data
 * @return True if the data is loaded successfully, false otherwise
 */
bool CEventLogMgr::LoadEventLog(UCHAR *pBackData)
{
    UCHAR  *pTemp;

    pTemp = pBackData;
    pTemp += SETUP_HEADER_LEN;

    memmove(&m_sPowerOffTime,   pTemp,  sizeof(m_sPowerOffTime));                       pTemp += sizeof(m_sPowerOffTime);
    memmove(&m_bPowerOffTimeUTC,pTemp,  sizeof(m_bPowerOffTimeUTC));                    pTemp += sizeof(m_bPowerOffTimeUTC);
    memmove(m_pLogData,         pTemp,  sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA);  pTemp += sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA;

    DEBUG_LOG("LoadSetupData-EventLog] PwrOffTm: utc:%d, %d, %d-%d-%d %02d:%02d:%02d\r\n",
            m_bPowerOffTimeUTC, m_sPowerOffTime.nValid, m_sPowerOffTime.xDate.nYear, m_sPowerOffTime.xDate.nMon, m_sPowerOffTime.xDate.nDay,
            m_sPowerOffTime.xTime.nHour, m_sPowerOffTime.xTime.nMin, m_sPowerOffTime.xTime.nSec);

    VerifyEventLog();

    return true;
}

/**
 * @brief Verify the event log data
 * @param None
 * @return None
 */
void  CEventLogMgr::VerifyEventLog(void)
{
    if(!CAisLib::IsValidAisSysDateTime(&m_sPowerOffTime))
    {
        CAisLib::SetDefaultSysDateTime(&m_sPowerOffTime);
        m_bPowerOffTimeUTC = false;
    }

    if(m_bPowerOffTimeUTC != true && m_bPowerOffTimeUTC != false)
        m_bPowerOffTimeUTC = false;

    for(int i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
    {
        if(!CheckAvailableEventLog(&m_pLogData[i]))
        {
            ResetEventLog(&m_pLogData[i]);
        }
        if(!CAisLib::IsValidAisSysDateTime(&(m_pLogData[i].sEventTime)))
        {
            CAisLib::SetDefaultSysDateTime(&(m_pLogData[i].sEventTime));
            m_pLogData[i].bEventTimeUTC = false;
        }
    }
}

/**
 * @brief Check if the event log for invalid MMSI is needed
  * @return True if the event log is needed, false otherwise
 */
bool CEventLogMgr::CheckEventLogValidMMSI(void)
{
    static bool bOldStat = false;
    static DWORD dwEventOccurSec = 0;
    static DWORD dwEventSaveSec = 0;

    bool bUpdateFlash = false;

    // Wait for GPS fix until 2 min
    if (  !CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) 
        && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)
        return false;

    if(!cShip::getOwnShipInst()->xSysTime.nValid)
        return false;

    bool bEventOccur = !CAisLib::IsValidMMSI_MobileSt(cShip::getOwnShipInst()->GetOwnShipMMSI());
    if(bEventOccur == bOldStat)
    {
        // If invalid MMSI for more than 15 min
        if(bEventOccur && cTimerSys::getInst()->GetTimeDiffSec(dwEventOccurSec) > EVENTLOG_CHECKTIMESEC)
        {
            // Save event log every 30 sec
            if(cTimerSys::getInst()->GetTimeDiffSec(dwEventSaveSec) > 30)
            {
                bUpdateFlash = true;
                EVENTLOG_DATA sEventLog = {0};
                int nEventIndex = FindEventLog(ALRID_SECURITYLOG_MMSI_INVALID, &m_sMMSINullTime);
                if(nEventIndex >= 0)
                    sEventLog = m_pLogData[nEventIndex];

                int nDiffSec = CAisLib::GetDiffTimeSeconds(&(cShip::getOwnShipInst()->xSysTime), &m_sMMSINullTime);
                sEventLog.nEventLogID   = ALRID_SECURITYLOG_MMSI_INVALID;
                sEventLog.sEventTime    = m_sMMSINullTime;
                sEventLog.sEventEndTime = cShip::getOwnShipInst()->xSysTime;
                sEventLog.bEventTimeUTC = m_bMMSINullTimeUTC;
                sEventLog.nDurationMin  = nDiffSec / 60;
                sEventLog.nMMSI         = cShip::getOwnShipInst()->GetOwnShipMMSI();

                dwEventSaveSec = cTimerSys::getInst()->GetCurTimerSec();
                EVENTLOG_DATA *pLogData = AddEventLog(ALRID_SECURITYLOG_MMSI_INVALID, &sEventLog, nEventIndex);

                INFO_LOG("EventLog] Invalid MMSI,Add, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                    nEventIndex, cShip::getOwnShipInst()->GetOwnShipMMSI(), dwEventOccurSec,
                    pLogData->nLogFlag, pLogData->nEventLogID,
                    pLogData->sEventTime.nValid, pLogData->sEventTime.xDate.nYear, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nDay,
                    pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec, pLogData->bEventTimeUTC,
                    cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                    cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                    nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
            }
        }
    }
    else
    {
        if(bEventOccur)
        {
            // Start event log for invalid MMSI
            m_sMMSINullTime     = cShip::getOwnShipInst()->xSysTime;
            m_bMMSINullTimeUTC  = cShip::getOwnShipInst()->bSysTimeUTC;
            dwEventOccurSec     = cTimerSys::getInst()->GetCurTimerSec();

            DEBUG_LOG("EventLog] Invalid MMSI,StartEvent, %09d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, s:%d\r\n",
                cShip::getOwnShipInst()->GetOwnShipMMSI(),
                m_sMMSINullTime.nValid, m_sMMSINullTime.xDate.nYear, m_sMMSINullTime.xDate.nMon, m_sMMSINullTime.xDate.nDay,
                m_sMMSINullTime.xTime.nHour, m_sMMSINullTime.xTime.nMin, m_sMMSINullTime.xTime.nSec,
                m_bMMSINullTimeUTC, dwEventOccurSec);
        }
        else
        {
            // Finish event log for invalid MMSI
            CAisLib::SetDefaultSysDateTime(&m_sMMSINullTime);
            m_bMMSINullTimeUTC = false;
        }
    }

    bOldStat = bEventOccur;
    return bUpdateFlash;
}

/**
 * @brief Check if the event log for RX only mode is needed
 * @return True if the event log is needed, false otherwise
 */
bool CEventLogMgr::CheckEventLogRxOnlyMode(void)
{
    static bool bOldStat = false;
    static DWORD dwEventOccurSec = 0;
    static DWORD dwCheckEventChgSec = 0;

    bool bUpdateFlash = false;

    // Wait for GPS fix until 2 min
    if (  !CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) 
        && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)
        return false;

    if(!cShip::getOwnShipInst()->IsOwnShipMMSISet())
        return false;

    DWORD dwRxOnlyTimeSec = cTimerSys::getInst()->GetTimeDiffSec(CLayerNetwork::m_dwPosReportLastTxSec);
    bool bEventOccur = (dwRxOnlyTimeSec > EVENTLOG_CHECKTIMESEC);

    if(bEventOccur == bOldStat)
    {
        if(bEventOccur)
        {
            // Save event log every 10 sec
            if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckEventChgSec) > 10)
            {
                bUpdateFlash = true;
                dwCheckEventChgSec = cTimerSys::getInst()->GetCurTimerSec();
            }
        }
    }
    else
    {
        if(bEventOccur)
        {
            // Start event log for RX only mode
            m_sRxOnlyStartTime    = cShip::getOwnShipInst()->xSysTime;
            m_bRxOnlyStartTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;

            dwEventOccurSec = cTimerSys::getInst()->GetCurTimerSec();
            bUpdateFlash    = true;

            DEBUG_LOG("EventLog] RX only,StartEvent, %09d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, s:%d\r\n",
                cShip::getOwnShipInst()->GetOwnShipMMSI(),
                m_sRxOnlyStartTime.nValid, m_sRxOnlyStartTime.xDate.nYear, m_sRxOnlyStartTime.xDate.nMon, m_sRxOnlyStartTime.xDate.nDay,
                m_sRxOnlyStartTime.xTime.nHour, m_sRxOnlyStartTime.xTime.nMin, m_sRxOnlyStartTime.xTime.nSec,
                m_bRxOnlyStartTimeUTC, dwEventOccurSec);
        }
        else
        {
            // Finish event log for RX only mode
            CAisLib::SetDefaultSysDateTime(&m_sRxOnlyStartTime);
            m_bRxOnlyStartTimeUTC = false;
        }
    }

    if(bUpdateFlash)
    {
        EVENTLOG_DATA sEventLog = {0};
        EVENTLOG_DATA sOldEventLog;
        int nEventIndex = FindEventLog(ALRID_SECURITYLOG_RXONLYMODE, &m_sRxOnlyStartTime);
        if(nEventIndex >= 0)
        {
            sEventLog = m_pLogData[nEventIndex];
            sOldEventLog = m_pLogData[nEventIndex];
        }

        int nDiffSec = CAisLib::GetDiffTimeSeconds(&(cShip::getOwnShipInst()->xSysTime), &m_sRxOnlyStartTime) + EVENTLOG_CHECKTIMESEC;
        sEventLog.nEventLogID   = ALRID_SECURITYLOG_RXONLYMODE;
        sEventLog.sEventTime    = m_sRxOnlyStartTime;
        sEventLog.sEventEndTime = cShip::getOwnShipInst()->xSysTime;
        sEventLog.bEventTimeUTC = m_bRxOnlyStartTimeUTC;
        sEventLog.nDurationMin  = nDiffSec / 60;

        if(nEventIndex >= 0 && sEventLog.nDurationMin == sOldEventLog.nDurationMin)
        {
            bUpdateFlash = false;

            DEBUG_LOG("EventLog] RX only,Skip Update, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                nEventIndex, cShip::getOwnShipInst()->GetOwnShipMMSI(), dwEventOccurSec,
                sEventLog.nLogFlag, sEventLog.nEventLogID,
                sEventLog.sEventTime.nValid, sEventLog.sEventTime.xDate.nYear, sEventLog.sEventTime.xDate.nMon, sEventLog.sEventTime.xDate.nDay,
                sEventLog.sEventTime.xTime.nHour, sEventLog.sEventTime.xTime.nMin, sEventLog.sEventTime.xTime.nSec, sEventLog.bEventTimeUTC,
                cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
        }

        if(bUpdateFlash)
        {
            EVENTLOG_DATA *pLogData = AddEventLog(ALRID_SECURITYLOG_RXONLYMODE, &sEventLog, nEventIndex);

            DEBUG_LOG("EventLog] RX only,Add,Update, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                nEventIndex, cShip::getOwnShipInst()->GetOwnShipMMSI(), dwEventOccurSec,
                pLogData->nLogFlag, pLogData->nEventLogID,
                pLogData->sEventTime.nValid, pLogData->sEventTime.xDate.nYear, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nDay,
                pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec, pLogData->bEventTimeUTC,
                cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
        }
    }

    bOldStat = bEventOccur;
    return bUpdateFlash;
}

/**
 * @brief Check if the event log for TX shutdown is needed
 * @param None
 * @return True if the event log is needed, false otherwise
 */
bool CEventLogMgr::CheckEventLogTxMulFunc(void)
{
    static bool bOldStat = false;
    static DWORD dwEventOccurSec = 0;
    static DWORD dwEventSaveSec = 0;

    bool bUpdateFlash = false;

    // Wait for GPS fix until 2 min
    if (  !CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) 
        && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)
        return false;

    bool bEventOccur = CLayerPhysical::getInst()->IsTxHwShutdownOccurred();

    if(bEventOccur == bOldStat)
    {
        // If TX shutdown for more than 15 min
        if(bEventOccur && cTimerSys::getInst()->GetTimeDiffSec(dwEventOccurSec) > EVENTLOG_CHECKTIMESEC)
        {
            // Save event log every 30 sec
            if(cTimerSys::getInst()->GetTimeDiffSec(dwEventSaveSec) > 30)
            {
                bUpdateFlash = true;
                EVENTLOG_DATA sEventLog = {0};
                int nEventIndex = FindEventLog(ALRID_SECURITYLOG_TX_SHUTDOWN, &m_sTxMulFuncStartTime);
                if(nEventIndex >= 0)
                    sEventLog = m_pLogData[nEventIndex];

                int nDiffSec = CAisLib::GetDiffTimeSeconds(&(cShip::getOwnShipInst()->xSysTime), &m_sTxMulFuncStartTime);
                sEventLog.nEventLogID   = ALRID_SECURITYLOG_TX_SHUTDOWN;
                sEventLog.sEventTime    = m_sTxMulFuncStartTime;
                sEventLog.sEventEndTime = cShip::getOwnShipInst()->xSysTime;
                sEventLog.bEventTimeUTC = m_bTxMulFuncStartTimeUTC;
                sEventLog.nDurationMin  = nDiffSec / 60;

                EVENTLOG_DATA *pLogData = AddEventLog(ALRID_SECURITYLOG_TX_SHUTDOWN, &sEventLog, nEventIndex);
                dwEventSaveSec = cTimerSys::getInst()->GetCurTimerSec();

                DEBUG_LOG("EventLog] TX mulfunc,Add, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                    nEventIndex, cShip::getOwnShipInst()->GetOwnShipMMSI(), dwEventOccurSec,
                    pLogData->nLogFlag, pLogData->nEventLogID,
                    pLogData->sEventTime.nValid, pLogData->sEventTime.xDate.nYear, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nDay,
                    pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec, pLogData->bEventTimeUTC,
                    cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                    cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                    nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
            }
        }
    }
    else
    {
        if(bEventOccur)
        {
            // Start event log for TX shutdown
            m_sTxMulFuncStartTime    = cShip::getOwnShipInst()->xSysTime;
            m_bTxMulFuncStartTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;

            dwEventOccurSec = cTimerSys::getInst()->GetCurTimerSec();
            DEBUG_LOG("EventLog] TX mulfunc,StartEvent, %09d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, s:%d\r\n",
                cShip::getOwnShipInst()->GetOwnShipMMSI(),
                m_sTxMulFuncStartTime.nValid, m_sTxMulFuncStartTime.xDate.nYear, m_sTxMulFuncStartTime.xDate.nMon, m_sTxMulFuncStartTime.xDate.nDay,
                m_sTxMulFuncStartTime.xTime.nHour, m_sTxMulFuncStartTime.xTime.nMin, m_sTxMulFuncStartTime.xTime.nSec,
                m_bTxMulFuncStartTimeUTC, dwEventOccurSec);
        }
        else
        {
            // Finish event log for TX shutdown
            CAisLib::SetDefaultSysDateTime(&m_sTxMulFuncStartTime);
            m_bTxMulFuncStartTimeUTC = false;
        }
    }

    bOldStat = bEventOccur;
    return bUpdateFlash;
}

/**
 * @brief Check if the event log for power off is needed
 * @param None
 * @return True if the event log is needed, false otherwise
 */
bool CEventLogMgr::CheckPowerOffTime(void)
{
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.6 Event log
    // A security mechanism shall be provided to detect disabling of the AIS and to prevent unauthorised alteration of input or transmitted data.
    // To protect the unauthorised dissemination of data, the IMO guidelines (IMO Resolution MSC.43(64), Guidelines for Ship Reporting Systems) shall be followed.
    // Means shall be provided to automatically record all periods when the AIS installation is nonfunctioning,
    // for instance when the power is switched off, when the AIS is in receive only mode or not transmitting for other reasons, as follows.
    // The last 10 times when the equipment was non-functioning for more than 15 min shall be recorded, in UTC time and nDurationMin,
    // in a non-volatile memory. Means shall be provided to recover this data. It shall not be possible for the user to alter any information recorded in this memory.
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------------

    bool bUpdateSetupData = false;

    if(!m_bCheckEventLogDone)
    {
        if(!CAisLib::IsValidAisSysDateTime(&(m_sPowerOffTime))
            || (!CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) 
                && OPSTATUS::bFirstFrameOnBootDone 
                && cTimerSys::getInst()->GetTimeDiffSec(0) > WAIT_TIMESEC_INT_GNSS)) 
        {
            m_bCheckEventLogDone = true;

            DEBUG_LOG("EventLog] CheckPwrOffTime, old PwrOffTime invalid, utc:%d, %d, %d-%d-%d %02d:%02d:%02d, s:%d\r\n",
                m_bPowerOffTimeUTC, m_sPowerOffTime.nValid,
                m_sPowerOffTime.xDate.nYear, m_sPowerOffTime.xDate.nMon, m_sPowerOffTime.xDate.nDay,
                m_sPowerOffTime.xTime.nHour, m_sPowerOffTime.xTime.nMin, m_sPowerOffTime.xTime.nSec,
                cTimerSys::getInst()->GetCurTimerSec());
        }
        else if(cShip::getOwnShipInst()->xSysTime.nValid)
        {
            EVENTLOG_DATA *pLogData;
            int nDiffSec;

            SYS_DATE_TIME sSysTime = cShip::getOwnShipInst()->xSysTime;
            if((nDiffSec = CAisLib::GetDiffTimeSeconds(&sSysTime, &(m_sPowerOffTime))) >= EVENTLOG_CHECKTIMESEC)        // 15 min
            {
                EVENTLOG_DATA sEventLog = {0};
                sEventLog.sEventTime    = m_sPowerOffTime;
                sEventLog.sEventEndTime = sSysTime;
                sEventLog.bEventTimeUTC = m_bPowerOffTimeUTC;
                sEventLog.nDurationMin  = nDiffSec / 60;
                pLogData = AddEventLog(ALRID_SECURITYLOG_POWEROFF, &sEventLog);
                bUpdateSetupData = true;
            }

            m_bCheckEventLogDone = true;
        }
    }
    return bUpdateSetupData;
}

/**
 * @brief Save the power off time
 * @param None
 * @return True if the power off time is saved, false otherwise
 */
bool CEventLogMgr::SavePowerOffTime(void)
{
    static DWORD dwCheckSec = 0;
    static DWORD dwSaveSec = 0;

    bool bUpdateSetupData = false;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 10)    // 10 sec
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(dwSaveSec) >= 60) // 1 min
        {
            if(m_bCheckEventLogDone)
            {
                if(cShip::getOwnShipInst()->xSysTime.nValid)
                {
                    m_sPowerOffTime = cShip::getOwnShipInst()->xSysTime;
                    m_bPowerOffTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;
                    bUpdateSetupData = true;
                }
                else
                {
                    if(m_sPowerOffTime.nValid)
                    {
                        CAisLib::SetDefaultSysDateTime(&(m_sPowerOffTime));
                        m_bPowerOffTimeUTC = false;
                        bUpdateSetupData = true;
                    }
                }

                dwSaveSec = cTimerSys::getInst()->GetCurTimerSec();
            }
        }
        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
    return bUpdateSetupData;
}

/**
 * @brief Run periodically
 */
void CEventLogMgr::RunPeriodicallyEventLog(void)
{
    static DWORD dwCheckSec = 0;
    bool bUpdateSetup = false;

    bUpdateSetup |= CheckPowerOffTime();
    bUpdateSetup |= SavePowerOffTime();

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 1)
    {
        bUpdateSetup |= CheckEventLogValidMMSI();
        bUpdateSetup |= CheckEventLogRxOnlyMode();
        bUpdateSetup |= CheckEventLogTxMulFunc();

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }

    if(bUpdateSetup) {
        CSetupMgr::getInst()->ReserveToSaveLogConfigData();
    }
}
