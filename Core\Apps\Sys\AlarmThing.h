#ifndef __ALARMTHING_H__
#define __ALARMTHING_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"

class CAlarmThing
{
    friend class CAlarmMgr;
    friend class CPI;

public:
    CAlarmThing(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarmThing(){};

public:
            INT8         GetAlarmID();
          SYS_DATE_TIME* GetAlarmTime();
        _tagBAMAlertStat GetAlertStat();
            char*        GetAlarmMsg();
            int          GetBAMAlertID();
            int          GetBAMAlertInstance();
            int          GetSequentialMsgID();
            char         GetAlarmCategory();
            char         GetAlarmPriority();
            int          GetRevisionCounter();
            int          GetEscalationCounter();
            char*        GetBAMAlertMsg1();
            char*        GetBAMAlertMsg2();

    virtual void         Reset();
    virtual BOOL         IsAlarmOccurred();
    virtual BOOL         IsAlarmAcked();
    virtual BOOL         IsAlarmEscalated();
    virtual _tagBAMAlertStat UpdateAlarmStatus(BOOL bCheckImmediately=FALSE);

    virtual _tagBAMAlertStat GetAlarmStatus();
    virtual BOOL         SetAlarmStatus(_tagBAMAlertStat nStatus, BOOL bForced=FALSE);

            
    virtual BOOL         SetStatusAcked();

    static  BOOL         IsAlarmStatOccurred(_tagBAMAlertStat nAlrStat);
    static  BOOL         IsAlarmStatAcked(_tagBAMAlertStat nAlrStat);
    static  BOOL         IsAlarmStatEscalated(_tagBAMAlertStat nAlrStat);
    
    virtual void         IncSequentialNumber();
    virtual void         IncRevisionCounter();
    virtual void         SetRevisionCounter(int nCounter);
    virtual void         IncEscalationCounter();
    virtual void         SetEscalationCounter(int nCounter);

    virtual void         SendOutNmeaALR(CAlarmThing *pAlarmThing, BOOL bImmediately=FALSE);
    virtual void         SendOutNmeaALF(CAlarmThing *pAlarmThing, BOOL bImmediately=FALSE);

protected:
    virtual BOOL         CheckAlarmCondition() = 0;

protected:
            char*        m_pstrMsg;

            INT8         m_nAlarmID;
            INT16        m_nAlrStatCheckIntSec;
           SYS_DATE_TIME m_sAlrTime;

            DWORD        m_dwAlrStatCheckSec;
            DWORD        m_dwSecSendALR;

            int          m_nBAMAlertID;
            char         m_cBAMAlertCategory;            // BAM_ALERT_AIS_CATEGORY
            char         m_cBAMAlertPriority;            // BAM_ALERT_PRIORITY_WARNING / BAM_ALERT_PRIORITY_CAUTION

        _tagBAMAlertStat m_cBAMAlertState;
            int          m_nBAMAlertInstance;
            int          m_nSequentialMsgID;
            int          m_nBAMAlertRevCnter;
            int          m_nBAMAlertEscCnter;

            DWORD        m_dwSilenceCheckSec;
            DWORD        m_dwSecSendALF;

            char*        m_pstrBAMMsg1;
            char*        m_pstrBAMMsg2;
};

class CAlarm01 : public CAlarmThing
{
public:
    CAlarm01(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);

    virtual ~CAlarm01(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm02 : public CAlarmThing
{
public:
    CAlarm02(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm02(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm03 : public CAlarmThing
{
public:
    CAlarm03(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm03(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm04 : public CAlarmThing
{
public:
    CAlarm04(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm04(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm05 : public CAlarmThing
{
public:
    CAlarm05(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm05(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm06 : public CAlarmThing
{
public:
    CAlarm06(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm06(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm07 : public CAlarmThing
{
public:
    CAlarm07(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm07(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm08 : public CAlarmThing
{
public:
    CAlarm08(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm08(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm09 : public CAlarmThing
{
public:
    CAlarm09(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm09(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm10 : public CAlarmThing
{
public:
    CAlarm10(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm10(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm11 : public CAlarmThing
{
public:
    CAlarm11(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm11(){};
protected:
    virtual BOOL CheckAlarmCondition();
};

class CAlarm14 : public CAlarmThing
{
public:
    CAlarm14(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm14(){};
protected:
    virtual BOOL CheckAlarmCondition();
public:
    virtual BOOL SetStatusAcked();
};

class cGpsBoard;
class CAlarm25 : public CAlarmThing
{
public:
    CAlarm25(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm25(){};
protected:
    virtual BOOL CheckAlarmCondition();
public:
            void ResetAlarmCheckStat();

protected:
    static cGpsBoard *m_pOldExtPosSensor;
    static BOOL       m_bOldPosSensorFixed;
    static DWORD      m_dwCheckSec;
};

class CAlarm26 : public CAlarmThing
{
public:
    CAlarm26(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm26(){};
protected:
    virtual BOOL CheckAlarmCondition();

    static BOOL  m_bFirstCheck;
    static DWORD m_dwFixSec;
};

class CAlarm29 : public CAlarmThing
{
public:
    CAlarm29(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm29(){};
protected:
    virtual BOOL CheckAlarmCondition();

    static BOOL  m_bFirstCheck;
    static DWORD m_dwFixSec;
};

class CAlarm30 : public CAlarmThing
{
public:
    CAlarm30(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm30(){};
protected:
    virtual BOOL CheckAlarmCondition();

    static BOOL  m_bFirstCheck;
    static DWORD m_dwFixSec;
};

class CAlarm32 : public CAlarmThing
{
public:
    CAlarm32(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm32(){};
protected:
    virtual BOOL CheckAlarmCondition();
    virtual void SendOutNmeaALR(CAlarmThing *pAlarmThing, BOOL bImmediately);
};

class CAlarm35 : public CAlarmThing
{
public:
    CAlarm35(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec=ALR_STAT_CHECK_SEC);
    virtual ~CAlarm35(){};
protected:
    virtual BOOL CheckAlarmCondition();
    virtual void SendOutNmeaALR(CAlarmThing *pAlarmThing, BOOL bImmediately);
};

extern CAlarm01 *gpAlarmTxMalFunc;
extern CAlarm02 *gpAlarmVswr;
extern CAlarm03 *gpAlarmRxMalfuncCh1;
extern CAlarm04 *gpAlarmRxMalfuncCh2;
extern CAlarm05 *gpAlarmMalfuncCh70;
extern CAlarm06 *gpAlarmGeneralFail;
extern CAlarm07 *gpAlarmUtcSyncInvalid;
extern CAlarm08 *gpAlarmMkdConnLost;
extern CAlarm09 *gpAlarmPosMismatch;
extern CAlarm10 *gpAlarmNavStatusWrong;
extern CAlarm11 *gpAlarmHdgOffset;
extern CAlarm14 *gpAlarmActiveSART;
extern CAlarm25 *gpAlarmExtEpfsLost;
extern CAlarm26 *gpAlarmNoPosSensor;
extern CAlarm29 *gpAlarmNoSOG;
extern CAlarm30 *gpAlarmNoCOG;
extern CAlarm32 *gpAlarmHdgInvalid;
extern CAlarm35 *gpAlarmNoROT;
#endif//__ALARMTHING_H__
