/**
 * @file    RtcmLib.c
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "SysConst.h"
#include "GpsLib.h"
#include "AisLib.h"
#include "AisMsg.h"
#include "RtcmLib.h"

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

//----------------------------------------------------------------------------
static UCHAR G_vBitMaskDataX[] = {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80};
static UCHAR G_vBitMaskDataY[] = {0x80, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x01};
//----------------------------------------------------------------------------

UCHAR ReverseEndian8Bit(UCHAR bData)
{
	int i;
	UCHAR bTempX = 0;

	for (i = 0; i < 8; i++)
		if (bData & G_vBitMaskDataX[i])
			bTempX |= G_vBitMaskDataY[i];

	return bTempX;
}

UCHAR ReverseEndian6Bit(UCHAR bData)
{
	int i;
	UCHAR bTempX = 0;

	for (i = 0; i < 6; i++)
		if (bData & G_vBitMaskDataX[i])
			bTempX |= G_vBitMaskDataY[i + 2];

	return bTempX;
}

UCHAR GetParityD25(UCHAR bDataBit, DWORD dSrcData)
{
	DWORD dTempX;

	dTempX = bDataBit    ^
		(dSrcData >>  0) ^
		(dSrcData >>  1) ^
		(dSrcData >>  2) ^
		(dSrcData >>  4) ^
		(dSrcData >>  5) ^
		(dSrcData >>  9) ^
		(dSrcData >> 10) ^
		(dSrcData >> 11) ^
		(dSrcData >> 12) ^
		(dSrcData >> 13) ^
		(dSrcData >> 16) ^
		(dSrcData >> 17) ^
		(dSrcData >> 19) ^
		(dSrcData >> 22);

	return (dTempX & 0x00000001);
}

UCHAR GetParityD26(UCHAR nLastBit, DWORD dSrcData)
{
	DWORD dTempX;

	dTempX = nLastBit    ^
		(dSrcData >>  1) ^
		(dSrcData >>  2) ^
		(dSrcData >>  3) ^
		(dSrcData >>  5) ^
		(dSrcData >>  6) ^
		(dSrcData >> 10) ^
		(dSrcData >> 11) ^
		(dSrcData >> 12) ^
		(dSrcData >> 13) ^
		(dSrcData >> 14) ^
		(dSrcData >> 17) ^
		(dSrcData >> 18) ^
		(dSrcData >> 20) ^
		(dSrcData >> 23);

	return (dTempX & 0x00000001);
}

UCHAR GetParityD27(UCHAR nLastBit, DWORD dSrcData)
{
	DWORD dTempX;

	dTempX = nLastBit    ^
		(dSrcData >>  0) ^
		(dSrcData >>  2) ^
		(dSrcData >>  3) ^
		(dSrcData >>  4) ^
		(dSrcData >>  6) ^
		(dSrcData >>  7) ^
		(dSrcData >> 11) ^
		(dSrcData >> 12) ^
		(dSrcData >> 13) ^
		(dSrcData >> 14) ^
		(dSrcData >> 15) ^
		(dSrcData >> 18) ^
		(dSrcData >> 19) ^
		(dSrcData >> 21);

	return (dTempX & 0x00000001);
}

UCHAR GetParityD28(UCHAR nLastBit, DWORD dSrcData)
{
	DWORD dTempX;

	dTempX = nLastBit    ^
		(dSrcData >>  1) ^
		(dSrcData >>  3) ^
		(dSrcData >>  4) ^
		(dSrcData >>  5) ^
		(dSrcData >>  7) ^
		(dSrcData >>  8) ^
		(dSrcData >> 12) ^
		(dSrcData >> 13) ^
		(dSrcData >> 14) ^
		(dSrcData >> 15) ^
		(dSrcData >> 16) ^
		(dSrcData >> 19) ^
		(dSrcData >> 20) ^
		(dSrcData >> 22);

	return (dTempX & 0x00000001);
}

UCHAR GetParityD29(UCHAR nLastBit, DWORD dSrcData)
{
	DWORD dTempX;

	dTempX = nLastBit    ^
		(dSrcData >>  0) ^
		(dSrcData >>  2) ^
		(dSrcData >>  4) ^
		(dSrcData >>  5) ^
		(dSrcData >>  6) ^
		(dSrcData >>  8) ^
		(dSrcData >>  9) ^
		(dSrcData >> 13) ^
		(dSrcData >> 14) ^
		(dSrcData >> 15) ^
		(dSrcData >> 16) ^
		(dSrcData >> 17) ^
		(dSrcData >> 20) ^
		(dSrcData >> 21) ^
		(dSrcData >> 23);

	return (dTempX & 0x00000001);
}

UCHAR GetParityD30(UCHAR nLastBit, DWORD dSrcData)
{
	DWORD dTempX;

	dTempX = nLastBit    ^
		(dSrcData >>  2) ^
		(dSrcData >>  4) ^
		(dSrcData >>  5) ^
		(dSrcData >>  7) ^
		(dSrcData >>  8) ^
		(dSrcData >>  9) ^
		(dSrcData >> 10) ^
		(dSrcData >> 12) ^
		(dSrcData >> 14) ^
		(dSrcData >> 18) ^
		(dSrcData >> 21) ^
		(dSrcData >> 22) ^
		(dSrcData >> 23);

	return (dTempX & 0x00000001);
}

void Add01ReverseRTCMSC104Msg(UCHAR *pSrcData, UCHAR *pRtcmData, int nIndex)
{
	UCHAR bData6Bit;

	bData6Bit = (pSrcData[0] & 0xFC) >> 2;
	bData6Bit = ReverseEndian6Bit(bData6Bit);
	pRtcmData[nIndex++] = (0x01 << 6) | bData6Bit;

	bData6Bit = ((pSrcData[0] & 0x03) << 4) | ((pSrcData[1] & 0xF0) >> 4);
	bData6Bit = ReverseEndian6Bit(bData6Bit);
	pRtcmData[nIndex++] = (0x01 << 6) | bData6Bit;

	bData6Bit = ((pSrcData[1] & 0x0F) << 2) | ((pSrcData[2] & 0xC0) >> 6);
	bData6Bit = ReverseEndian6Bit(bData6Bit);
	pRtcmData[nIndex++] = (0x01 << 6) | bData6Bit;

	bData6Bit = (pSrcData[2] & 0x3F);
	bData6Bit = ReverseEndian6Bit(bData6Bit);
	pRtcmData[nIndex++] = (0x01 << 6) | bData6Bit;

	// Parity Bits
	bData6Bit = (pSrcData[3] & 0xFC) >> 2;
	bData6Bit = ReverseEndian6Bit(bData6Bit);
	pRtcmData[nIndex++] = (0x01 << 6) | bData6Bit;
}

void GetD29D30DataValue(UCHAR *pD29, UCHAR *pD30)
{
	//============================================================================
	// RTCM SC104 Msg를 보낼때 마다, D29,D30은 (0,0), (0,1), (1,1), (1,0) 변경되어진다.
	//============================================================================
	/*
	static UCHAR vD29Data[] = {0, 0, 1, 1};
	static UCHAR vD30Data[] = {0, 1, 1, 0};
	static int nCounter = 0;

	*pD29 = vD29Data[nCounter];
	*pD30 = vD30Data[nCounter];

	nCounter++;
	if (nCounter > 3)
		nCounter = 0;
	*/
	static int nD29D30Cnt = 0;

	nD29D30Cnt++;
	switch(nD29D30Cnt)
	{
	case 1:
		*pD29 = 0;
		*pD30 = 0;
		break;
	case 2:
		*pD29 = 0;
		*pD30 = 1;
		break;
	case 3:
		*pD29 = 1;
		*pD30 = 1;
		break;
	case 4:
		*pD29 = 1;
		*pD30 = 0;
		nD29D30Cnt = 0;	// Reset
		break;
	}
}

int MakeMsgDGNSSToRTCMSC104(UCHAR *pDgnssData, int nDgnssLen, UCHAR *pRtcmData)
{
	//============================================================================
	// dgnss_data(input) : 01100110 00100110 ....   (MSB)
	// pRtcmData(output) : "01"100110 "01"010001... (LSB)
	// RTCM SC104 Msg를 보낼때 마다, D29,D30은 (0,0), (0,1), (1,0), (1,1) 변경되어,
	// GPS Core Board 로 보내진다.
	//============================================================================
	UCHAR D29 = 0, D30 = 0;
	UCHAR vTempData[4];                             // 30bits
	DWORD dData3Bytes;
	int i, nLen = 0;

	GetD29D30DataValue(&D29, &D30);

	for (i = 0 ; i < nDgnssLen ; i += 3)
	{
		dData3Bytes = 0;

		if (D30)
		{
			vTempData[0] = pDgnssData[i + 0] ^ 0xff;
			vTempData[1] = pDgnssData[i + 1] ^ 0xff;
			vTempData[2] = pDgnssData[i + 2] ^ 0xff;
		}
		else
		{
			vTempData[0] = pDgnssData[i + 0] ^ 0x00;
			vTempData[1] = pDgnssData[i + 1] ^ 0x00;
			vTempData[2] = pDgnssData[i + 2] ^ 0x00;
		}

		dData3Bytes = ReverseEndian8Bit(pDgnssData[i]) | (ReverseEndian8Bit(pDgnssData[i + 1]) << 8) | (ReverseEndian8Bit(pDgnssData[i + 2]) << 16);

		vTempData[3] = 0x00;

		// Parity bits
		vTempData[3] |= (GetParityD25(D29, dData3Bytes) << 7);
		vTempData[3] |= (GetParityD26(D30, dData3Bytes) << 6);
		vTempData[3] |= (GetParityD27(D29, dData3Bytes) << 5);
		vTempData[3] |= (GetParityD28(D30, dData3Bytes) << 4);
		vTempData[3] |= (GetParityD29(D30, dData3Bytes) << 3);
		vTempData[3] |= (GetParityD30(D29, dData3Bytes) << 2);

		D29 = (vTempData[3] & 0x08) >> 3;                     // last 2 bits of previous word
		D30 = (vTempData[3] & 0x04) >> 2;

		Add01ReverseRTCMSC104Msg(vTempData, pRtcmData, nLen); // Add "01" + 6 bits reverse ==> 8bits data
		nLen += 5;
	}

	return nLen;
}

