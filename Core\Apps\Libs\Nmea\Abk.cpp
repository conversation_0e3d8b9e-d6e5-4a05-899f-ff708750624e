/**
 * @file    Abk.cpp
 * @brief   Abk class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "AisMsg.h"
#include "Abk.h"

/******************************************************************************
 *
 * ABK - Addressed and binary broadcast acknowledgement
 *
 * $--ABK,xxxxxxxxx,a,x.x,x,x*hh<CR><LF>
 *        |         |  |  | |
 *        1         2  3  4 5
 *
 *  1. MMSI of the addressed destination AIS unit
 *  2. AIS channel of reception
 *  3. ITU-R M. 1371 message ID
 *  4. Message Sequence Number
 *  5. Type of acknowledgement
 *
 ******************************************************************************/
// Define static member variables
int8_t CAbk::m_nSequentialId = 0;

CAbk::CAbk() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAbk::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the ABK sentence
 * @param pszSentence The sentence to be made
 * @param uDestMMSI The MMSI of the addressed destination AIS unit
 * @param nAckRxCh The AIS channel of reception
 * @param nVdlMsgID The ITU-R M. 1371 message ID
 * @param nMsgSeqNum The message sequence number
 * @param nAckType The type of acknowledgement
 * @return The length of the sentence
 */
int32_t CAbk::MakeSentence(char *pszSentence, 
                            UINT uDestMMSI, 
                            int nAckRxCh, 
                            int nVdlMsgID, 
                            int nMsgSeqNum, 
                            int nAckType)
{
    if(nAckRxCh == AIS_CHANNEL_NONE)
    {
        switch(nVdlMsgID)
        {
        case AIS_MSG_NO_ID_06:
        case AIS_MSG_NO_ID_12:
            sprintf((char*)pszSentence, "$AIABK,%09d,,%d,%01d,%01d", 
                    uDestMMSI, nVdlMsgID, nMsgSeqNum, nAckType);
            break;
        case AIS_MSG_NO_ID_08:
        case AIS_MSG_NO_ID_14:
            sprintf((char*)pszSentence, "$AIABK,,,%d,%01d,%01d", 
                    nVdlMsgID, nMsgSeqNum, nAckType);
            break;
        case AIS_MSG_NO_ID_10:
        case AIS_MSG_NO_ID_11:
        case AIS_MSG_NO_ID_15:
            sprintf((char*)pszSentence, "$AIABK,%09d,,%d,,%01d", 
                    uDestMMSI, nVdlMsgID, nAckType);
            break;
        case AIS_MSG_NO_ID_25:
        case AIS_MSG_NO_ID_26:
        case PI_MSGID_70:
        case PI_MSGID_71:
            if(uDestMMSI == AIS_AB_MMSI_NULL)
                sprintf((char*)pszSentence, "$AIABK,,,%d,%01d,%01d", 
                        nVdlMsgID, nMsgSeqNum, nAckType);
            else
                sprintf((char*)pszSentence, "$AIABK,%09d,,%d,%01d,%01d", 
                        uDestMMSI, nVdlMsgID, nMsgSeqNum, nAckType);
            break;
        default:
            return 0;
        }
    }
    else
    {
        char bChChannel = '\0';
        if(nAckRxCh == AIS_CHANNEL_AIS1)
            bChChannel = 'A';
        else if(nAckRxCh == AIS_CHANNEL_AIS2)
            bChChannel = 'B';

        switch(nVdlMsgID)
        {
        case AIS_MSG_NO_ID_06:
        case AIS_MSG_NO_ID_12:
            sprintf((char*)pszSentence, "$AIABK,%09d,%c,%d,%01d,%01d", 
                    uDestMMSI, bChChannel, nVdlMsgID, nMsgSeqNum, nAckType);
            break;
        case AIS_MSG_NO_ID_08:
        case AIS_MSG_NO_ID_14:
            sprintf((char*)pszSentence, "$AIABK,,%c,%d,%01d,%01d", 
                    bChChannel, nVdlMsgID, nMsgSeqNum, nAckType);
            break;
        case AIS_MSG_NO_ID_15:
            sprintf((char*)pszSentence, "$AIABK,%09d,%c,%d,,%01d", 
                    uDestMMSI, bChChannel, nVdlMsgID, nAckType);
            break;
        case AIS_MSG_NO_ID_25:
        case AIS_MSG_NO_ID_26:
        case PI_MSGID_70:
        case PI_MSGID_71:
            if(uDestMMSI == AIS_AB_MMSI_NULL)
                sprintf((char*)pszSentence, "$AIABK,,%c,%d,%01d,%01d", 
                        bChChannel, nVdlMsgID, nMsgSeqNum, nAckType);
            else
                sprintf((char*)pszSentence, "$AIABK,%09d,,%d,%01d,%01d", 
                        uDestMMSI, nVdlMsgID, nMsgSeqNum, nAckType);
            break;
        default:
            return 0;
        }
    }

    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}




