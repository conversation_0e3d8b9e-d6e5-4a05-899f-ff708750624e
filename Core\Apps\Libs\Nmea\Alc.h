/**
 * @file    Alc.h
 * @brief   Alc header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ALC_H__
#define __ALC_H__

/******************************************************************************
 * 
 * ALC - Cyclic alert list
 *
 * $--ALC,xx,xx,xx,x.x,aaa,x.x,x.x,x.x,……,aaa,x.x,x.x,x.x*hh<CR><LF>
 *        |  |  |  |   |   |   |   |   |  |___________|
 *        1  2  3  4   5   6   7   8   9  10
 *                     |_______________|
 *                     Alert entry 1
 *
 * 1. Total number of sentences for this message, 01 to 99
 * 2. Sentence number, 01 to 99
 * 3. Sequential message identifier, 00 to 99
 * 4. Number of alert entries
 * 5. Manufacturer mnemonic code
 * 6. Alert identifier
 * 7. Alert instance
 * 8. Revision counter
 * 9. Additional Alert entries
 * 10. Alert entry n
 *
 ******************************************************************************/
class CAlc : public CSentence
{
public:
    CAlc();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 99)  m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the ALC sentence
     * @param pAlarmList The alarm list
     * @param nSizeAlarmList The size of the alarm list
     * @return The length of the sentence
     */
    static int32_t MakeSentence(CAlarmThing **pAlarmList, const int nSizeAlarmList);

public:
    static int8_t m_nSequentialId;
};

#endif /* __ALC_H__ */

