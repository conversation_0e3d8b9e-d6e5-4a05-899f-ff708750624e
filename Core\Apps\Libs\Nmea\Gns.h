/**
 * @file    Gns.h
 * @brief   Gns header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __GNS_H__
#define __GNS_H__

/******************************************************************************
 * 
 * GNS - 
 *
 * $-- GNS, hhmmss.ss,llll.ll,a,yyyyy.yy,a,c--c,xx,x.x,x.x,x.x,x.x,x.x,a*hh<CR><LF>
 *           |           |----|     |----|  |   |   |   |   |   |   |  |
 *           1           2          3       4   5   6   7   8   9   10 11
 *
 * 1. UTC of position
 * 2. Latitude, N/S
 * 3. Longitude, E/W
 * 4. Mode indicator
 * 5. Total number of satellites in use, 00-99
 * 6. HDOP
 * 7. Antenna altitude
 * 8. Geoidal separation
 * 9. Age of differential data
 * 10. Differential reference station ID
 * 11. Navigational status indicator
 * 
 ******************************************************************************/
class CGns : public CSentence
{
public:
    CGns();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidPosData(void);

    UINT8 GetPosModeIndi(void);
    DWORD GetPosModeTick(void);

    double GetLatVal(void);
    double GetLonVal(void);

    SYS_TIME GetUtcTime(void);

    void RunPeriodically(void);

protected:
    SYS_TIME m_xUtcTime;
    double  m_rRcvLatVal;
    double  m_rRcvLonVal;

    UINT8   m_uPosModeIndi;
    DWORD   m_dwPosModeTick;
    DWORD   m_dwPosValidTick;
};

#endif /* __GNS_H__ */

