#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include "SysLog.h"
#include "MKD.h"

#define KNRM  "\x1B[0m"
#define KRED  "\x1B[31m"
#define KGRN  "\x1B[32m"
#define KYEL  "\x1B[33m"
#define KBLU  "\x1B[34m"
#define KMAG  "\x1B[35m"
#define KCYN  "\x1B[36m"
#define KWHT  "\x1B[37m"
#define KEND  "\x1B[0m"

int g_log_level = LOG_DISABLE_ALL;

void sys_log_set_port(int port)
{

}

void sys_log_set_priority(int level)
{

}

void sys_debug_printf(const char *pCaller, int line, const char *pFmt, ...)
{
  va_list args;
  char buf[LOG_BUF_SIZE] = { 0 };
  int n = 0;
  char fnBuf[__FNAME_MAX__+1] = { 0, };
  char fnAll[128] = { 0, };

  strncpy(fnAll, pCaller, strlen(pCaller) > 127 ? 127 : strlen(pCaller));
  char *currPos = strrchr(fnAll, '.');
  while ( currPos != NULL && *currPos != 0 ) {
    *currPos++ = 0;
  }

  strncpy(fnBuf, fnAll, strlen(fnAll) > __FNAME_MAX__ ? __FNAME_MAX__ : strlen(fnAll));
  n = snprintf(buf, sizeof(buf) - 1, "[%" MACRO_STRINGIZE_VALUE(__FNAME_MAX__) "s:%" MACRO_STRINGIZE_VALUE(__LINENUM_MAX_DIGIT__) "d] ", fnBuf, line);
  if (n == -1 || n > (int)(sizeof(buf) - 1))
    return;

  va_start(args, pFmt);
  vsnprintf(buf + n, sizeof(buf) - 1 - n, pFmt, args);
  va_end(args);

  ////printf("%s%s%s", KGRN, buf, KEND);
}

void sys_info_printf(const char *pCaller, int line, const char *pFmt, ...)
{
  va_list args;
  char buf[LOG_BUF_SIZE] = { 0 };

  int n = 0;
  char fnBuf[__FNAME_MAX__+1] = { 0, };
  char fnAll[128] = { 0, };

  strncpy(fnAll, pCaller, strlen(pCaller) > 127 ? 127 : strlen(pCaller));
  char *currPos = strrchr(fnAll, '.');
  while ( currPos != NULL && *currPos != 0 ) {
    *currPos++ = 0;
  }

  strncpy(fnBuf, fnAll, strlen(fnAll) > __FNAME_MAX__ ? __FNAME_MAX__ : strlen(fnAll));
  n = snprintf(buf, sizeof(buf) - 1, "[%" MACRO_STRINGIZE_VALUE(__FNAME_MAX__) "s:%" MACRO_STRINGIZE_VALUE(__LINENUM_MAX_DIGIT__) "d] ", fnBuf, line);
  if (n == -1 || n > (int)(sizeof(buf) - 1))
    return;

  va_start(args, pFmt);
  vsnprintf(buf + n, sizeof(buf) - 1 - n, pFmt, args);
  va_end(args);

  ////printf("%s%s%s", KBLU, buf, KEND);
}

void sys_warning_printf(const char *pCaller, int line, const char *pFmt, ...)
{
    va_list args;
    char buf[LOG_BUF_SIZE] = { 0 };

    int n = 0;
    char fnBuf[__FNAME_MAX__+1] = { 0, };
    char fnAll[128] = { 0, };

    strncpy(fnAll, pCaller, strlen(pCaller) > 127 ? 127 : strlen(pCaller));
    char *currPos = strrchr(fnAll, '.');
    while ( currPos != NULL && *currPos != 0 ) {
      *currPos++ = 0;
    }

    strncpy(fnBuf, fnAll, strlen(fnAll) > __FNAME_MAX__ ? __FNAME_MAX__ : strlen(fnAll));
    n = snprintf(buf, sizeof(buf) - 1, "[%" MACRO_STRINGIZE_VALUE(__FNAME_MAX__) "s:%" MACRO_STRINGIZE_VALUE(__LINENUM_MAX_DIGIT__) "d] ", fnBuf, line);
    if (n == -1 || n > (int)(sizeof(buf) - 1))
      return;

    va_start(args, pFmt);
    vsnprintf(buf + n, sizeof(buf) - 1 - n, pFmt, args);
    va_end(args);

    ////printf("%s%s%s", KYEL, buf, KEND);
}

void sys_error_printf(const char *pCaller, int line, const char *pFmt, ...)
{
  va_list args;
  char buf[LOG_BUF_SIZE] = { 0 };
  int n = 0;
  char fnBuf[__FNAME_MAX__+1] = { 0, };
  char fnAll[128] = { 0, };

  strncpy(fnAll, pCaller, strlen(pCaller) > 127 ? 127 : strlen(pCaller));
  char *currPos = strrchr(fnAll, '.');
  while ( currPos != NULL && *currPos != 0 ) {
    *currPos++ = 0;
  }

  strncpy(fnBuf, fnAll, strlen(fnAll) > __FNAME_MAX__ ? __FNAME_MAX__ : strlen(fnAll));
  n = snprintf(buf, sizeof(buf) - 1, "[%" MACRO_STRINGIZE_VALUE(__FNAME_MAX__) "s:%" MACRO_STRINGIZE_VALUE(__LINENUM_MAX_DIGIT__) "d] ", fnBuf, line);
  if (n == -1 || n > (int)(sizeof(buf) - 1))
    return;

  va_start(args, pFmt);
  vsnprintf(buf + n, sizeof(buf) - 1 - n, pFmt, args);
  va_end(args);

  ////printf("%s%s%s", KRED, buf, KEND);
}

void sys_direct_printf(const char *pCaller, int line, const char *pFmt, ...)
{
  va_list args;
  char buf[LOG_BUF_SIZE] = { 0 };
  int n = 0;
  char fnBuf[__FNAME_MAX__+1] = { 0, };
  char fnAll[128] = { 0, };

  strncpy(fnAll, pCaller, strlen(pCaller) > 127 ? 127 : strlen(pCaller));
  char *currPos = strrchr(fnAll, '.');
  while ( currPos != NULL && *currPos != 0 ) {
    *currPos++ = 0;
  }

  strncpy(fnBuf, fnAll, strlen(fnAll) > __FNAME_MAX__ ? __FNAME_MAX__ : strlen(fnAll));
  n = snprintf(buf, sizeof(buf) - 1, "[%" MACRO_STRINGIZE_VALUE(__FNAME_MAX__) "s:%" MACRO_STRINGIZE_VALUE(__LINENUM_MAX_DIGIT__) "d] ", fnBuf, line);
  if (n == -1 || n > (int)(sizeof(buf) - 1))
    return;

  va_start(args, pFmt);
  vsnprintf(buf + n, sizeof(buf) - 1 - n, pFmt, args);
  va_end(args);

  CMKD::getInst()->SendOutStr(buf);
  ////printf("%s%s%s", KRED, buf, KEND);
}