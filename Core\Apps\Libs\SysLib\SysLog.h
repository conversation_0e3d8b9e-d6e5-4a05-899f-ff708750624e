#ifndef __SYS_LOG_H__
#define __SYS_LOG_H__

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

extern int g_log_level;

#define LOG_DISABLE_ALL         0 
#define LOG_ERROR_LEVEL         1
#define LOG_WARNING_LEVEL       2
#define LOG_INFO_LEVEL          3
#define LOG_DEBUG_LEVEL         4

#define LOG_BUF_SIZE            1024
#define __FNAME_MAX__           15
#define __LINENUM_MAX_DIGIT__   5

#define MACRO_STRINGIZE(param)          #param
#define MACRO_STRINGIZE_VALUE(param)    MACRO_STRINGIZE(param)

#define DEBUG_LOG(...)   if (g_log_level > LOG_INFO_LEVEL)   { sys_debug_printf(__func__, __LINE__, __VA_ARGS__); }
#define INFO_LOG(...)    if (g_log_level > LOG_WARNING_LEVEL){ sys_info_printf(__func__, __LINE__, __VA_ARGS__); }
#define WARNING_LOG(...) if (g_log_level > LOG_ERROR_LEVEL)  { sys_warning_printf(__func__, __LINE__, __VA_ARGS__); }
#define ERROR_LOG(...)   if (g_log_level > LOG_DISABLE_ALL)  { sys_error_printf(__func__, __LINE__, __VA_ARGS__); }

#define NON_CONDITION_LOG(...)   { sys_direct_printf(__func__, __LINE__, __VA_ARGS__); }

void sys_log_set_port(int port);
void sys_log_set_priority(int level);
void sys_debug_printf(const char *pCaller, int line, const char *pFmt, ...);
void sys_error_printf(const char *pCaller, int line, const char *pFmt, ...);
void sys_warning_printf(const char *pCaller, int line, const char *pFmt, ...);
void sys_info_printf(const char *pCaller, int line, const char *pFmt, ...);
void sys_direct_printf(const char *pCaller, int line, const char *pFmt, ...);

#ifdef __cplusplus
}
#endif

#endif /* __SYS_LOG_H__ */
