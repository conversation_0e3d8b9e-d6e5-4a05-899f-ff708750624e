/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "Predef.h"
#include "SysLog.h"
/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */
void InitAllTasks(void);
void RunAllTasks(void);
void ReloadConfig(void);
void SetAllUartBaudRate(void);
/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define CODEC_SCL_Pin GPIO_PIN_1
#define CODEC_SCL_GPIO_Port GPIOF
#define RF_MINOR_VER_Pin GPIO_PIN_6
#define RF_MINOR_VER_GPIO_Port GPIOF
#define WING_PTT_Pin GPIO_PIN_10
#define WING_PTT_GPIO_Port GPIOF
#define CURRENT_DET_Pin GPIO_PIN_0
#define CURRENT_DET_GPIO_Port GPIOC
#define EX_TX_ENABLE_Pin GPIO_PIN_2
#define EX_TX_ENABLE_GPIO_Port GPIOC
#define RF_MAJOR_VER_Pin GPIO_PIN_3
#define RF_MAJOR_VER_GPIO_Port GPIOC
#define SP_AF_RSSI_Pin GPIO_PIN_2
#define SP_AF_RSSI_GPIO_Port GPIOH
#define BB_MAJOR_VER_Pin GPIO_PIN_3
#define BB_MAJOR_VER_GPIO_Port GPIOH
#define BB_MINOR_VER_Pin GPIO_PIN_4
#define BB_MINOR_VER_GPIO_Port GPIOH
#define CODEC_SDA_Pin GPIO_PIN_5
#define CODEC_SDA_GPIO_Port GPIOH
#define R_VSWR_Pin GPIO_PIN_3
#define R_VSWR_GPIO_Port GPIOA
#define PWR_CNT_Pin GPIO_PIN_4
#define PWR_CNT_GPIO_Port GPIOA
#define DDS_AJD_Pin GPIO_PIN_5
#define DDS_AJD_GPIO_Port GPIOA
#define DSC_FSK_Pin GPIO_PIN_0
#define DSC_FSK_GPIO_Port GPIOB
#define Nor_CLK_Pin GPIO_PIN_2
#define Nor_CLK_GPIO_Port GPIOB
#define CODEC_CAD_Pin GPIO_PIN_0
#define CODEC_CAD_GPIO_Port GPIOJ
#define CODEC_EN_Pin GPIO_PIN_1
#define CODEC_EN_GPIO_Port GPIOJ
#define TP5_Pin GPIO_PIN_3
#define TP5_GPIO_Port GPIOJ
#define TEMP_Pin GPIO_PIN_11
#define TEMP_GPIO_Port GPIOF
#define PWR_DET_Pin GPIO_PIN_12
#define PWR_DET_GPIO_Port GPIOF
#define F_VSWR_Pin GPIO_PIN_13
#define F_VSWR_GPIO_Port GPIOF
#define MAC_SCL_Pin GPIO_PIN_14
#define MAC_SCL_GPIO_Port GPIOF
#define MAC_SDA_Pin GPIO_PIN_15
#define MAC_SDA_GPIO_Port GPIOF
#define REMOTE_H_PTT_LED_Pin GPIO_PIN_0
#define REMOTE_H_PTT_LED_GPIO_Port GPIOG
#define WING_H_PTT_LED_Pin GPIO_PIN_1
#define WING_H_PTT_LED_GPIO_Port GPIOG
#define INCOMM_RX_Pin GPIO_PIN_7
#define INCOMM_RX_GPIO_Port GPIOE
#define INCOMM_TX_Pin GPIO_PIN_8
#define INCOMM_TX_GPIO_Port GPIOE
#define SPI5_CS_Pin GPIO_PIN_8
#define SPI5_CS_GPIO_Port GPIOH
#define H_SP_PWR_Pin GPIO_PIN_9
#define H_SP_PWR_GPIO_Port GPIOH
#define LOOP_EN_Pin GPIO_PIN_11
#define LOOP_EN_GPIO_Port GPIOH
#define TEST_EN_Pin GPIO_PIN_12
#define TEST_EN_GPIO_Port GPIOH
#define SDTI_Pin GPIO_PIN_14
#define SDTI_GPIO_Port GPIOB
#define SDTO_Pin GPIO_PIN_15
#define SDTO_GPIO_Port GPIOB
#define LED_3_Pin GPIO_PIN_10
#define LED_3_GPIO_Port GPIOD
#define Nor_D0_Pin GPIO_PIN_11
#define Nor_D0_GPIO_Port GPIOD
#define Nor_D1_Pin GPIO_PIN_12
#define Nor_D1_GPIO_Port GPIOD
#define Nor_D3_Pin GPIO_PIN_13
#define Nor_D3_GPIO_Port GPIOD
#define LED_2_Pin GPIO_PIN_14
#define LED_2_GPIO_Port GPIOD
#define LED_1_Pin GPIO_PIN_15
#define LED_1_GPIO_Port GPIOD
#define Debug_TX_Pin GPIO_PIN_8
#define Debug_TX_GPIO_Port GPIOJ
#define RX_EN_Pin GPIO_PIN_0
#define RX_EN_GPIO_Port GPIOK
#define TX_EN_Pin GPIO_PIN_1
#define TX_EN_GPIO_Port GPIOK
#define MAIN_DDS_LOCK_DETECT_Pin GPIO_PIN_2
#define MAIN_DDS_LOCK_DETECT_GPIO_Port GPIOK
#define TX_INDI_LED_Pin GPIO_PIN_2
#define TX_INDI_LED_GPIO_Port GPIOG
#define TX_INDI_LEDG3_Pin GPIO_PIN_3
#define TX_INDI_LEDG3_GPIO_Port GPIOG
#define PROC1_LED_Pin GPIO_PIN_4
#define PROC1_LED_GPIO_Port GPIOG
#define PROC2_LED_Pin GPIO_PIN_6
#define PROC2_LED_GPIO_Port GPIOG
#define PROC3_LED_Pin GPIO_PIN_8
#define PROC3_LED_GPIO_Port GPIOG
#define LRCK_Pin GPIO_PIN_11
#define LRCK_GPIO_Port GPIOA
#define BICK_Pin GPIO_PIN_12
#define BICK_GPIO_Port GPIOA
#define A_PDIN_Pin GPIO_PIN_14
#define A_PDIN_GPIO_Port GPIOH
#define SP_PWR_Pin GPIO_PIN_15
#define SP_PWR_GPIO_Port GPIOH
#define ETH_RESET_Pin GPIO_PIN_0
#define ETH_RESET_GPIO_Port GPIOI
#define C_F_SP_ON_Pin GPIO_PIN_2
#define C_F_SP_ON_GPIO_Port GPIOI
#define C_R_SP_ON_Pin GPIO_PIN_3
#define C_R_SP_ON_GPIO_Port GPIOI
#define ALARM_RX_Pin GPIO_PIN_0
#define ALARM_RX_GPIO_Port GPIOD
#define TP1_LED_Pin GPIO_PIN_1
#define TP1_LED_GPIO_Port GPIOD
#define DDS_DATA_Pin GPIO_PIN_7
#define DDS_DATA_GPIO_Port GPIOD
#define MAIN_DDS_CS_Pin GPIO_PIN_12
#define MAIN_DDS_CS_GPIO_Port GPIOJ
#define WKR_DDS_CS_Pin GPIO_PIN_13
#define WKR_DDS_CS_GPIO_Port GPIOJ
#define TX_EN2_Pin GPIO_PIN_15
#define TX_EN2_GPIO_Port GPIOJ
#define GPS_LOCK_INDI_LED_Pin GPIO_PIN_10
#define GPS_LOCK_INDI_LED_GPIO_Port GPIOG
#define DDS_CLK_Pin GPIO_PIN_11
#define DDS_CLK_GPIO_Port GPIOG
#define WKR_DDS_LOCK_DETECT_Pin GPIO_PIN_3
#define WKR_DDS_LOCK_DETECT_GPIO_Port GPIOK
#define ERR_INDI_LED_Pin GPIO_PIN_15
#define ERR_INDI_LED_GPIO_Port GPIOG
#define DA_SCLK_Pin GPIO_PIN_5
#define DA_SCLK_GPIO_Port GPIOB
#define DA_DIN_Pin GPIO_PIN_6
#define DA_DIN_GPIO_Port GPIOB
#define DA_NCS_Pin GPIO_PIN_7
#define DA_NCS_GPIO_Port GPIOB
#define DDS_LE_Pin GPIO_PIN_9
#define DDS_LE_GPIO_Port GPIOB
#define Debug_RX_Pin GPIO_PIN_0
#define Debug_RX_GPIO_Port GPIOE
#define MAIN_SP_MUTE_Pin GPIO_PIN_4
#define MAIN_SP_MUTE_GPIO_Port GPIOI
#define F_SP_MUTE_Pin GPIO_PIN_5
#define F_SP_MUTE_GPIO_Port GPIOI
#define R_SP_MUTE_Pin GPIO_PIN_6
#define R_SP_MUTE_GPIO_Port GPIOI

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
