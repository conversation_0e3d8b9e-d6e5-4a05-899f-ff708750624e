/**
 * @file    Nak.cpp
 * @brief   Nak class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Ship.h"
#include "Nak.h"

#define STR_NAK_QUERY_NOT_SUPPORTED          "Query functionality not supported"
#define STR_NAK_SENTENCE_NOT_SUPPORTED       "Sentence formatter not supported"
#define STR_NAK_SENTENCE_NOT_ENABLEDED       "Sentence formatter not enabled"
#define STR_NAK_SENTENCE_TEMP_UNAVAIL        "Sentence formatter temporarily unaailable"
#define STR_NAK_QUERY_FORMAT_NOT_SUPPORTED   "Query formatter is not supported"
#define STR_NAK_ACCESS_DENIED                "Access denied, for sentence foratter requested"
#define STR_NAK_SENTENCE_BAD_CHECKSUM        "Sentence not accepted due to bad checksum"
#define STR_NAK_SENTENCE_BUSY                "Sentence not accepted due to listner processing issue"
#define STR_NAK_CANNOT_PERFORM               "Cannot perform the requested operation"
#define STR_NAK_WRONG_DATAFIELD              "Cannot fulfill request or command"
#define STR_NAK_OTHER_REASON                 "Other reason as described in data field 5"

/*****************************************************************************
 *
 * NAK - Negative acknowledgement
 *
 * $--NAK,cc,ccc,c--c,x.x,c--c*hh<CR><LF>
 *        |   |   |   |   |
 *        1   2   3   4   5
 *
 * 1. Talker Identifier
 * 2. Affected sentence formatter
 * 3. Unique Identifier
 * 4. Reason code for negative acknowledgement
 * 5. Negative acknowledgement’s descriptive text
 *
 ******************************************************************************/
CNak::CNak() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CNak::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the NAK sentence
 * @param pszSentence The sentence to be made
 * @param pstrTalkerID The talker ID
 * @param pstrAffectSenctence The affected sentence formatter
 * @param nReasonCode The reason code for negative acknowledgement
 * @return The length of the sentence
 */
int32_t CNak::MakeSentence(char *pszSentence, const char *pstrTalkerID, const char *pstrAffectSenctence, int nReasonCode)
{
    char *pstrMsg = "";

    switch(nReasonCode)
    {
    case NAK_REASON_QUERY_NOT_SUPPORTED:        pstrMsg = (char*)STR_NAK_QUERY_NOT_SUPPORTED;       break;
    case NAK_REASON_SENTENCE_NOT_SUPPORTED:     pstrMsg = (char*)STR_NAK_SENTENCE_NOT_SUPPORTED;    break;
    case NAK_REASON_SENTENCE_NOT_ENABLEDED:     pstrMsg = (char*)STR_NAK_SENTENCE_NOT_ENABLEDED;    break;
    case NAK_REASON_SENTENCE_TEMP_UNAVAIL:      pstrMsg = (char*)STR_NAK_SENTENCE_TEMP_UNAVAIL;     break;
    case NAK_REASON_QUERY_FORMAT_NOT_SUPPORTED: pstrMsg = (char*)STR_NAK_QUERY_FORMAT_NOT_SUPPORTED;break;
    case NAK_REASON_ACCESS_DENIED:              pstrMsg = (char*)STR_NAK_ACCESS_DENIED;             break;
    case NAK_REASON_SENTENCE_BAD_CHECKSUM:      pstrMsg = (char*)STR_NAK_SENTENCE_BAD_CHECKSUM;     break;
    case NAK_REASON_SENTENCE_BUSY:              pstrMsg = (char*)STR_NAK_SENTENCE_BUSY;             break;
    case NAK_REASON_CANNOT_PERFORM:             pstrMsg = (char*)STR_NAK_CANNOT_PERFORM;            break;
    case NAK_REASON_WRONG_DATAFIELD:            pstrMsg = (char*)STR_NAK_WRONG_DATAFIELD;           break;
    case NAK_REASON_OTHER_REASON:
    default:                                    pstrMsg = (char*)STR_NAK_OTHER_REASON;              break;
    }

    if(strlen(pstrMsg) > 50)
        pstrMsg = "";

    sprintf(pszSentence, "$AINAK,%s,%s,%09d,%d,%s", 
            pstrTalkerID, 
            pstrAffectSenctence, 
            cShip::getOwnShipInst()->GetOwnShipMMSI(), 
            nReasonCode, 
            pstrMsg);
    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}
