/**
 * @file    Arc.h
 * @brief   Arc header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ARC_H__
#define __ARC_H__

/******************************************************************************
 * 
 * ARC - Alert command refused
 *
 * $--ARC,hhmmss.ss,aaa,x.x,x.x,c*hh<CR><LF>
 *          |        |   |   |  |
 *          1        2   3   4  5
 *
 * 1. Time
 * 2. Manufacturer mnemonic code
 * 3. Alert identifier
 * 4. Alert instance, 1 to 999999
 * 5. Refused alert command, A, Q, O or S
 *
 ******************************************************************************/
class CArc : public CSentence
{
public:
    CArc();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the ARC sentence
     * @param pszSentence The sentence to be made
     * @param pAlarmThing The alarm thing structure
     * @param cArcCommand The ARC command
     * @param pstrBuffARC The output buffer
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, CAlarmThing *pAlarmThing, char cArcCommand, char *pstrBuffARC);
};

#endif /* __ARC_H__ */

