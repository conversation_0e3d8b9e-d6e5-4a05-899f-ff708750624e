/**
 * @file    Alf.h
 * @brief   Alf header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"
#include "AlarmThing.h"

#ifndef __ALF_H__
#define __ALF_H__

/******************************************************************************
 * 
 * ALF - BAM Alert sentence
 *
 * $--ALF,x,x,x,hhmmss.ss,a,a,a,aaa,x.x,x.x,x.x,x,c--c*hh<CR><LF>
 *        | | |     |     | | |  |   |   |   |  |   |
 *        1 2 3     4     5 6 7  8   9   10  11 12  13
 *
 * 1. Total number of ALF sentences for this message, 1 to 2
 * 2. Sentence number, 1 to 2
 * 3. Sequential message identifier, 0 to 9
 * 4. Time of last change (null or UTC)
 * 5. Alert category, A, B or C
 * 6. Alert priority, E, A, W or C 
 * 7. Alert state, A, S, N, O, U or V 
        active-unacknowledged: V
        active-silenced: S
        active-acknowledged or active: A
        active-responsibility transferred: O
        rectified-unacknowledged: U
        normal: N
 * 8. Manufacturer mnemonic code
 * 9. Alert identifier
 * 10. Alert instance, 1 to 999999
 * 11. Revision counter, 1 to 99
 * 12. Escalation counter, 0 to 9
 * 13. Alert text
 *
 ** 
 ******************************************************************************/
class CAlf : public CSentence
{
public:
    CAlf();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the ALF sentence
     * @param pszSentence The sentence to be made
     * @param pAlarmThing The alarm thing structure
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, CAlarmThing *pAlarmThing);
};

#endif /* __ALF_H__ */

