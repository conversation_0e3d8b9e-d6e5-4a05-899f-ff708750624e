/**
 * @file    RxModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include "SysLib.h"
#include "FskModem.h"

cFskModem::cFskModem(void)
{
    int   i;
    float fOmega;

    m_vMarkSinData  = (int*)SysAllocMemory(FSK_MDM_SAMPLES_PER_BIT * sizeof(int));
    m_vMarkCosData  = (int*)SysAllocMemory(FSK_MDM_SAMPLES_PER_BIT * sizeof(int));
    m_vSpaceSinData = (int*)SysAllocMemory(FSK_MDM_SAMPLES_PER_BIT * sizeof(int));
    m_vSpaceCosData = (int*)SysAllocMemory(FSK_MDM_SAMPLES_PER_BIT * sizeof(int));
    m_vSamplingData = (int*)SysAllocMemory(FSK_MDM_SAMPLES_PER_BIT * sizeof(int));
    m_vRxBuffData   = (UCHAR*)SysAllocMemory(FSK_MDM_RX_BUFF_SIZE * sizeof(UCHAR));

    m_nRunSequenceCntr = 0;

    fOmega = 2 * M_PI_VALUE_F * FSK_MDM_MARK_FREQUENCY / FSK_MDM_SAMPLING_FREQUENCY;
    for (i = 0; i < FSK_MDM_SAMPLES_PER_BIT; i++) {
        m_vMarkSinData[i] = (int)(sinf(fOmega * (i + 1)) * FSK_MDM_MUL_FACTOR);
        m_vMarkCosData[i] = (int)(cosf(fOmega * (i + 1)) * FSK_MDM_MUL_FACTOR);
    }

    fOmega = 2 * M_PI_VALUE_F * FSK_MDM_SPACE_FREQUENCY / FSK_MDM_SAMPLING_FREQUENCY;
    for (i = 0; i < FSK_MDM_SAMPLES_PER_BIT; i++) {
        m_vSpaceSinData[i] = (int)(sinf(fOmega * (i + 1)) * FSK_MDM_MUL_FACTOR);
        m_vSpaceCosData[i] = (int)(cosf(fOmega * (i + 1)) * FSK_MDM_MUL_FACTOR);
    }

    for (i = 0; i < FSK_MDM_SAMPLES_PER_BIT; i++) {
        m_vSamplingData[i] = 0;
    }

    m_nFskBitDataX  = 0;
    m_nFskBitDataY  = 0;
    m_nFskBitDataZ  = 0;

    m_nFskBitDataP  = 0;
    m_nFskBitDataC  = 0;

    m_dBitCntForPrev= 0;
    m_dBitCntForSync= 0;
    m_dSwRxPllValue = 0;

    m_nRxBuffHead   = 0;
    m_nRxBuffTail   = 0;

    for (i = 0; i < FSK_MDM_RX_RAW_BUFF_SIZE; i++)
    {
        m_vRxRawData[i] = FSK_MDM_ADC_CENTER_VALUE;
    }

    m_wRxRawPntX    = 0;
    m_wRxRawAvrX    = FSK_MDM_ADC_CENTER_VALUE;
    m_dRxRawSumX    = FSK_MDM_ADC_CENTER_VALUE * FSK_MDM_RX_RAW_BUFF_SIZE;
}

cFskModem::~cFskModem(void)
{
}

int cFskModem::GetFskRxStatus(void)
{
    return(m_dRxRunStatusX);
}

int cFskModem::GetFskRawData(void)
{
    return(m_nFskBitDataC);
}

int cFskModem::GetFskBitData(void)
{
    int  nBitData = FSK_MDM_RX_BIT_DATA_NULL;

    if (m_nRxBuffHead != m_nRxBuffTail) {
        nBitData = m_vRxBuffData[m_nRxBuffTail];

        ++m_nRxBuffTail;
        if (m_nRxBuffTail >= FSK_MDM_RX_BUFF_SIZE)
            m_nRxBuffTail  = 0;
    }

    return(nBitData);
}

void cFskModem::PutFskBitData(int nBitData)
{
    m_vRxBuffData[m_nRxBuffHead] = (UCHAR)nBitData;

    ++m_nRxBuffHead;
    if (m_nRxBuffHead >= FSK_MDM_RX_BUFF_SIZE) {
        m_nRxBuffHead  = 0;
    }
}

void cFskModem::ClearBuffData(void)
{
    m_nRxBuffHead = 0;
    m_nRxBuffTail = 0;
}

void cFskModem::ResetFskModem(void)
{
    m_wRxRunBitData = 0;
    m_dRxRunStatusX = FSK_MDM_RX_STATUS_NONE;

    m_nRxBuffHead = 0;
    m_nRxBuffTail = 0;
}

void cFskModem::RunTimerIsrHandler(int nDscAdcData)
{
    int  nMarkI ,  nMarkQ;
    int  nSpaceI, nSpaceQ;
    int  nTempX , nTempY;
    int  nBitData, nSamplingData;

    ++m_wRxRawPntX;
    if (m_wRxRawPntX >= FSK_MDM_RX_RAW_BUFF_SIZE)
        m_wRxRawPntX  = 0;

    m_dRxRawSumX = m_dRxRawSumX - m_vRxRawData[m_wRxRawPntX];
    m_dRxRawSumX = m_dRxRawSumX + nDscAdcData;
    m_vRxRawData[m_wRxRawPntX] = nDscAdcData;
    m_wRxRawAvrX = m_dRxRawSumX / FSK_MDM_RX_RAW_BUFF_SIZE;

    ++m_nRunSequenceCntr;

    if (m_nRunSequenceCntr == 1)   {m_nSumOfMarkI = CalcFskFirFilter(m_vMarkSinData);  return;}
    if (m_nRunSequenceCntr == 2)   {m_nSumOfMarkQ = CalcFskFirFilter(m_vMarkCosData);  return;}
    if (m_nRunSequenceCntr == 3)   {m_nSumOfSpaceI= CalcFskFirFilter(m_vSpaceSinData); return;}
    if (m_nRunSequenceCntr == 4)   {m_nSumOfSpaceQ= CalcFskFirFilter(m_vSpaceCosData); return;}

    nSamplingData = nDscAdcData - m_wRxRawAvrX;

    memmove(&m_vSamplingData[0], &m_vSamplingData[1], sizeof(m_vSamplingData[0]) * (FSK_MDM_SAMPLES_PER_BIT - 1));
    m_vSamplingData[FSK_MDM_SAMPLES_PER_BIT - 1] = nSamplingData;

    m_nRunSequenceCntr = 0;

    nMarkI = m_nSumOfMarkI  / FSK_MDM_MUL_FACTOR;
    nMarkQ = m_nSumOfMarkQ  / FSK_MDM_MUL_FACTOR;

    nSpaceI= m_nSumOfSpaceI / FSK_MDM_MUL_FACTOR;
    nSpaceQ= m_nSumOfSpaceQ / FSK_MDM_MUL_FACTOR;

    nTempX = nMarkI  * nMarkI  + nMarkQ  * nMarkQ;
    nTempY = nSpaceI * nSpaceI + nSpaceQ * nSpaceQ;
    if (nTempX >= nTempY)
        nBitData = 1;
    else
        nBitData = 0;

    m_nFskBitDataX = m_nFskBitDataY;
    m_nFskBitDataY = m_nFskBitDataZ;
    m_nFskBitDataZ = nBitData;

    m_nFskBitDataC = m_nFskBitDataY;
    if (m_nFskBitDataX == 0 && m_nFskBitDataY == 1 && m_nFskBitDataZ == 0) m_nFskBitDataC = m_nFskBitDataZ;
    if (m_nFskBitDataX == 1 && m_nFskBitDataY == 0 && m_nFskBitDataZ == 1) m_nFskBitDataC = m_nFskBitDataZ;

    if (m_nFskBitDataC != m_nFskBitDataP) {
        if (m_dSwRxPllValue < FSK_SW_PLL_HALF_VALUE)
            m_dSwRxPllValue += FSK_SW_PLL_STEP_VALUE;
        else
            m_dSwRxPllValue -= FSK_SW_PLL_STEP_VALUE;

        if (m_dRxRunStatusX == FSK_MDM_RX_STATUS_NONE) {
            if (m_dBitCntForSync >= (FSK_MDM_SAMPLES_PER_BIT - 2) && m_dBitCntForSync <= (FSK_MDM_SAMPLES_PER_BIT + 2)) {
                nTempX = (m_dBitCntForPrev + m_dBitCntForSync) / 2;
                if (nTempX < (FSK_MDM_SAMPLES_PER_BIT - 2) || nTempX > (FSK_MDM_SAMPLES_PER_BIT + 2))
                    m_wRxRunBitData = m_nFskBitDataC;
            }
            else {
                m_wRxRunBitData = 0x0000;
            }
        }

        m_dBitCntForPrev = m_dBitCntForSync;
        m_dBitCntForSync = 0;
    }
    ++m_dBitCntForSync;

    m_dSwRxPllValue += FSK_SW_PLL_INCR_VALUE;
    if (m_dSwRxPllValue >= FSK_SW_PLL_FULL_VALUE) {
        m_dSwRxPllValue -= FSK_SW_PLL_FULL_VALUE;

        if (m_dRxRunStatusX == FSK_MDM_RX_STATUS_NONE) {
            m_wRxRunBitData <<= 1;
            m_wRxRunBitData  |= m_nFskBitDataC;

            if (m_wRxRunBitData == 0xaa)
            {
                ResetFskModem();
                m_dRxRunStatusX = FSK_MDM_RX_STATUS_RUN;
                m_wRxRunBitData = 0x0000;
            }
            else
                m_wRxRunBitData &= 0x00ff;
        }
        else {
            PutFskBitData(m_nFskBitDataC);
        }
    }

    m_nFskBitDataP = m_nFskBitDataC;
}

int cFskModem::CalcFskFirFilter(int *pCoff)
{
    int  nSum = 0;

    for (int i = 0; i < FSK_MDM_SAMPLES_PER_BIT; i++)
        nSum += (m_vSamplingData[i] * pCoff[i]);

    return(nSum);
}
