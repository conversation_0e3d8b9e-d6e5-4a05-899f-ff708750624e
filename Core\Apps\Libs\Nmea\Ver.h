/**
 * @file    Ver.h
 * @brief   Ver header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __VER_H__
#define __VER_H__

/******************************************************************************
 * 
 * VER - Version
 *
 * $--VER,x,x,aa,c--c,c--c,c--c,c--c,c--c,c--c,x*hh<CR><LF>
 *        | | |   |    |    |    |    |    |   | 
 *        1 2 3   4    5    6    7    8    9   10
 *
 * 1. Total number of sentences needed, 1 to 9
 * 2. Sentence number, 1 to 9
 * 3. Device type
 * 4. Vendor ID
 * 5. Unique identifier
 * 6. Manufacturer serial number
 * 7. Model code (product code)
 * 8. Software revision
 * 9. Hardware revision
 * 10. Sequential message identifier
 *
 ******************************************************************************/
class CVer : public CSentence
{
public:
    CVer();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the VER sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

public:
    static int8_t m_nSequentialId;
};

#endif /* __VER_H__ */

