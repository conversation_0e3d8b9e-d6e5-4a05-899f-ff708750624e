/**
 * @file    Adc.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include "main.h"
#include "SysConst.h"
#include "SysLib.h"
#include "GPIO.h"
#include "ADC.h"

#define GET_REGULAR_RANK(x)     (x==0) ? ADC_REGULAR_RANK_1 : \
                                (x==1) ? ADC_REGULAR_RANK_2 : \
                                (x==2) ? ADC_REGULAR_RANK_3 : \
                                (x==3) ? ADC_REGULAR_RANK_4 : \
                                (x==4) ? ADC_REGULAR_RANK_5 : \
                                (x==5) ? ADC_REGULAR_RANK_6 : \
                                (x==6) ? ADC_REGULAR_RANK_7 : \
                                (x==7) ? ADC_REGULAR_RANK_8 : \
                                (x==8) ? ADC_REGULAR_RANK_9 : \
                                (x==9) ? ADC_REGULAR_RANK_10 : \
                                (x==10) ? ADC_REGULAR_RANK_11 : \
                                (x==11) ? ADC_REGULAR_RANK_12 : \
                                (x==12) ? ADC_REGULAR_RANK_13 : \
                                (x==13) ? ADC_REGULAR_RANK_14 : \
                                (x==14) ? ADC_REGULAR_RANK_15 : \
                                ADC_REGULAR_RANK_16

#define GET_INJECTED_RANK(x)    (x==0) ? ADC_INJECTED_RANK_1 : \
                                (x==1) ? ADC_INJECTED_RANK_2 : \
                                (x==2) ? ADC_INJECTED_RANK_3 : \
                                ADC_INJECTED_RANK_4

//=============================================================================
CAdc::CAdc(ADC_TypeDef *pBaseAddr, int nNoOfRegular, const DWORD *pRegularChNo, int nNoOfInjected, const DWORD *pInjectedChNo)
{
    int   i;

    m_pBaseAddr = pBaseAddr;
    m_nNoOfRegular = nNoOfRegular;
    m_nNoOfInjected= nNoOfInjected;
    memmove(m_vRegularChNo, pRegularChNo, sizeof(m_vRegularChNo));
    memmove(m_vInjectedChNo, pInjectedChNo, sizeof(m_vInjectedChNo));

    if (pBaseAddr == ADC1 || pBaseAddr == ADC2)   __HAL_RCC_ADC12_CLK_ENABLE();
    if (pBaseAddr == ADC3)                        __HAL_RCC_ADC3_CLK_ENABLE();

    memset(&m_xAdcHand, 0x00, sizeof(m_xAdcHand));
    memset(&m_xAdcCnfg, 0x00, sizeof(m_xAdcCnfg));
    memset(&m_xAdcInjt, 0x00, sizeof(m_xAdcInjt));

    m_xAdcHand.Instance                   = m_pBaseAddr;
    m_xAdcHand.Init.ClockPrescaler        = ADC_CLOCK_SYNC_PCLK_DIV1;
#ifdef __USE_12BIT_ADC__
    m_xAdcHand.Init.Resolution            = ADC_RESOLUTION_12B;
#else
    m_xAdcHand.Init.Resolution            = ADC_RESOLUTION_16B;
#endif
    m_xAdcHand.Init.ScanConvMode          = ADC_SCAN_ENABLE;                 // Sequencer disabled (ADC conversion on only 1 channel: channel set on rank 1)
    m_xAdcHand.Init.EOCSelection          = ADC_EOC_SINGLE_CONV;
    m_xAdcHand.Init.LowPowerAutoWait      = DISABLE;
    m_xAdcHand.Init.ContinuousConvMode    = ENABLE;                          // Continuous mode enabled to have continuous conversion
    m_xAdcHand.Init.NbrOfConversion       = m_nNoOfRegular;
    m_xAdcHand.Init.DiscontinuousConvMode = DISABLE;
    m_xAdcHand.Init.ExternalTrigConv      = ADC_SOFTWARE_START;
    m_xAdcHand.Init.ExternalTrigConvEdge  = ADC_EXTERNALTRIGCONVEDGE_NONE;   // Conversion start not trigged by an external event
    //m_xAdcHand.Init.ConversionDataManagement = ADC_CONVERSIONDATA_DR;
    m_xAdcHand.Init.ConversionDataManagement = ADC_CONVERSIONDATA_DMA_CIRCULAR;
    m_xAdcHand.Init.Overrun               = ADC_OVR_DATA_PRESERVED;
    m_xAdcHand.Init.LeftBitShift          = ADC_LEFTBITSHIFT_NONE;
    m_xAdcHand.Init.OversamplingMode      = DISABLE;
    if (HAL_ADC_Init(&m_xAdcHand) != HAL_OK) 
    {
        Error_Handler();
    }

    /** Configure Regular Channel
    */
    for (i = 0; i < m_nNoOfRegular; i++)
    {
        m_xAdcCnfg.Channel      = m_vRegularChNo[i];
        m_xAdcCnfg.Rank         = GET_REGULAR_RANK(i);
        m_xAdcCnfg.SamplingTime = ADC_SAMPLETIME_1CYCLE_5;
        m_xAdcCnfg.SingleDiff   = ADC_SINGLE_ENDED;
        m_xAdcCnfg.OffsetNumber = ADC_OFFSET_NONE;
        m_xAdcCnfg.Offset       = 0;
        m_xAdcCnfg.OffsetSignedSaturation = DISABLE;
        if (HAL_ADC_ConfigChannel(&m_xAdcHand, &m_xAdcCnfg) != HAL_OK)
        {
            Error_Handler();
        }
    }

    /** Configure Injected Channel
    */
    for (i = 0; i < m_nNoOfInjected; i++)
    {
        m_xAdcInjt.InjectedNbrOfConversion          = m_nNoOfInjected;
        m_xAdcInjt.InjectedChannel                  = m_vInjectedChNo[i];
        m_xAdcInjt.InjectedRank                     = GET_INJECTED_RANK(i);
        m_xAdcInjt.InjectedSamplingTime             = ADC_SAMPLETIME_1CYCLE_5;
        m_xAdcInjt.InjectedSingleDiff               = ADC_SINGLE_ENDED;
        m_xAdcInjt.InjectedOffsetNumber             = ADC_OFFSET_NONE;
        m_xAdcInjt.InjectedOffset                   = 0;
        m_xAdcInjt.InjectedDiscontinuousConvMode    = DISABLE;
        m_xAdcInjt.AutoInjectedConv                 = ENABLE;
        m_xAdcInjt.QueueInjectedContext             = DISABLE;
        m_xAdcInjt.ExternalTrigInjecConv            = ADC_INJECTED_SOFTWARE_START;
        m_xAdcInjt.ExternalTrigInjecConvEdge        = ADC_EXTERNALTRIGINJECCONV_EDGE_NONE;
        m_xAdcInjt.InjecOversamplingMode            = DISABLE;
        if (HAL_ADCEx_InjectedConfigChannel(&m_xAdcHand, &m_xAdcInjt) != HAL_OK)
        {
            Error_Handler();
        }
    }

    if (m_nNoOfInjected > 0)
    {
        if(HAL_ADCEx_InjectedStart(&m_xAdcHand) != HAL_OK)
        {
            Error_Handler();
        }
    }

    if (m_nNoOfRegular > 0)
    {
        #if 0
        if (HAL_ADC_Start(&m_xAdcHand) != HAL_OK)
        {
            Error_Handler();
        }
        #else
        if (HAL_ADC_Start_DMA(&m_xAdcHand, (uint32_t*)m_vRegDmaAdData, m_nNoOfRegular) != HAL_OK)
        {
            Error_Handler();
        }
        #endif
    }
}

CAdc::~CAdc(void)
{
}

HWORD CAdc::GetADCData(DWORD dChNo)
{
#if 0
    if (dChNo == 0)  return((HWORD)m_pBaseAddr->DR);
    if (dChNo == 1)  return((HWORD)m_pBaseAddr->JDR1);
    if (dChNo == 2)  return((HWORD)m_pBaseAddr->JDR2);
    if (dChNo == 3)  return((HWORD)m_pBaseAddr->JDR3);
    if (dChNo == 4)  return((HWORD)m_pBaseAddr->JDR4);
#else

    if (dChNo < 16)  return m_vRegDmaAdData[dChNo];
    if (dChNo == 16) return((HWORD)m_pBaseAddr->JDR1);
    if (dChNo == 17) return((HWORD)m_pBaseAddr->JDR2);
    if (dChNo == 18) return((HWORD)m_pBaseAddr->JDR3);
    if (dChNo == 19) return((HWORD)m_pBaseAddr->JDR4);
#endif
    return 0;
}

void  CAdc::RunADCIsrHandler(void)
{
}
//=============================================================================

