/**
 * @file    Vbw.h
 * @brief   Vbw header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __VBW_H__
#define __VBW_H__

/******************************************************************************
 * 
 * VBW - 
 *
 * $--VBW,x.x,x.x,A,x.x,x.x,A,x.x,A,x.x,A*hh<CR><LF>
 *         |   |  |  |   |  |  |  |  |  |
 *         1   2  3  4   5  6  7  8  9  10
 *
 * 1. Longitudinal water speed
 * 2. Transverse water speed
 * 3. Status: water speed, A = data valid, V = data invalid
 * 4. Longitudinal ground speed
 * 5. Transverse ground speed
 * 6. Status: ground speed, A = data valid, V = data invalid
 * 7. Stern transverse water speed
 * 8. Status: stern water speed, A = data valid, V = data invalid
 * 9. Stern transverse ground speed
 * 10. Status: stern ground speed, A = data valid, V = data inv
 * 
 ******************************************************************************/
class CVbw : public CSentence
{
protected:
    SYS_DATE m_xUtcDate;
    SYS_TIME m_sUtcTime;
    double  m_rRcvLatVal;
    double  m_rRcvLonVal;
    int     m_nSpdData;
    int     m_nVelData;
    int     m_nCrsData;

    UINT8   m_uPosModeIndi;
    DWORD   m_dwPosModeTick;
    DWORD   m_dwPosValidTick;
    DWORD   m_dwSpdValidTick;
    DWORD   m_dwCrsValidTick;

public:
    CVbw();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidCourseData(void);
    bool IsValidSpeedData(void);
    int  GetCourse(void);
    int  GetSpeed(void);
    int  GetVelocity(void);

    void RunPeriodically(void);
};

#endif /* __VBW_H__ */

