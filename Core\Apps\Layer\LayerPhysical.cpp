/**
 * @file    LayerPhysical.cpp
 * @brief   LayerPhysical class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include "SysLog.h"
#include "ADC.h"
#include "DAC.h"
#include "GPIOExt.h"
#include "PLL.h"
#include "Timer.h"
#include "SetupMgr.h"
#include "AisModem.h"
#include "BuiltInTestMgr.h"
#include "TestModeMgr.h"
#include "MKD.h"
#include "LayerPhysical.h"


#define RAMPUP_NUM_STEP     (RAMPUP_TIME_SAMPLECNT)
#define RAMPUP_SCALE_CNT    ((RAMPUP_TIME_SAMPLECNT) / RAMPUP_NUM_STEP)


CLayerPhysical::CLayerPhysical()
{
    m_uTxPowerMode              = AIS_TX_POWER_HIGH;

    m_nTxPwrLevelToSet          = 0;
    m_fRampUpStep               = 0;
    m_nRampUpCnt                = 0;
    m_fLevelSum                 = 0;

    m_nCurPwrLevel              = 0;

    m_bTxRfOn                   = false;
    m_dwTxRfOnSec               = 0;

    m_bTxRunning                = false;
    m_nPrepTxStep               = PREPTX_STEP_NONE;

    m_wFwdPowerAdValue          = 0;
    m_wRefPowerAdValue          = 0;

    m_bEnableCheckVswr          = false;
    m_dwVswrCheckEnableTick     = 0;
    m_dwVswrCheckRunTick        = 0;
    m_bVswrErrorOccurred        = false;

    m_bEnableCheckRxMalFuncPll1 = false;
    m_dwPll1LockStartTickSec    = 0;
    m_bEnableCheckRxMalFuncPll2 = false;
    m_dwPll2LockStartTickSec    = 0;
    m_bEnableCheckRxMalFuncPll3 = false;
    m_dwPll3LockStartTickSec    = 0;

    m_bTxHwShutdownOccured      = false;
    m_dwTxHwPwrStartTick        = 0;

    m_nRegTxFreq                = 0;
    m_nSetModemTxFreq           = 0;
    m_dwTxFreqTick              = 0;
}

CLayerPhysical::~CLayerPhysical()
{
}

/**
 * @brief Initialize hardware
 */
void CLayerPhysical::InitHW(void)
{
    TurnTxOff(true);
    SetTxRfOn(false);
}

/**
 * @brief Set Rx local frequency
 * @param nHwLocalRxID Hardware local receiver ID
 * @param uChNum Channel number
 * @return true if successful, false otherwise
 */
bool CLayerPhysical::SetRxLocalFreq(int nHwLocalRxID, UINT uChNum)
{
    int nFrequency = CAisLib::GetAisFreqByChannelNo(uChNum);
    if(nFrequency <= 0)
        return false;

    switch(nHwLocalRxID)
    {
    case AIS_HW_RX_LOCAL_RX1:   // AIS-1
        m_bEnableCheckRxMalFuncPll1 = false;
        CPllMgr::getInstPLL1()->SetRxFrequency(nFrequency);
        m_dwPll1LockStartTickSec = cTimerSys::getInst()->GetCurTimerSec();
        break;
    case AIS_HW_RX_LOCAL_RX2:   // AIS-2
        m_bEnableCheckRxMalFuncPll2 = false;
        CPllMgr::getInstPLL2()->SetRxFrequency(nFrequency);
        m_dwPll2LockStartTickSec = cTimerSys::getInst()->GetCurTimerSec();
        break;
    case AIS_HW_RX_LOCAL_RX3:   // DSC
        // To be implemented.....
        //m_bEnableCheckRxMalFuncRX3 = false;
        //CPllMgr::getInstPLL3()->SetRxFrequency(nFrequency);
        //m_dwRxLockStartTickSec = cTimerSys::getInst()->GetCurTimerSec();
        break;
    }
    return true;
}

/**
 * @brief Set Tx power mode
 * @param uTxPower Tx power mode (AIS_TX_POWER_LOW / AIS_TX_POWER_HIGH)
 * @return true if successful, false otherwise
 */
bool CLayerPhysical::SetTxPowerMode(UINT8 uTxPower)
{
    if(uTxPower == AIS_TX_POWER_HIGH || uTxPower == AIS_TX_POWER_LOW)
    {
        if(m_uTxPowerMode != uTxPower)
        {
            m_uTxPowerMode = uTxPower;
            CMKD::getInst()->SendPowerMode();

            DEBUG_LOG("SetTxPwrModePhy] power:%d, setup1:%d(%d),%d(%d), setup2:%d,%d, s:%d\r\n",
                uTxPower, CSetupMgr::getInst()->GetTxPowerLevelCh1High(), CSetupMgr::getInst()->GetTxPowerLevelCh1Low(),
                CSetupMgr::getInst()->GetTxPowerLevelCh2High(), CSetupMgr::getInst()->GetTxPowerLevelCh2Low(), cTimerSys::getInst()->GetCurTimerSec());
        }
        return true;
    }

    return false;
}

/**
 * @brief Get Tx power level
 * @return Tx power level
 */
WORD CLayerPhysical::GetTxPowerLevel(void)
{
    WORD pw_level;
    DWORD tx_freq  = cAisModem::getInst()->GetAisTxFrequency();

    if(!tx_freq)
        return 0;

    if(tx_freq < VCO_BASIC_FREQ)
    {
        // Low freq. power level --> below 160MHz frequency
        if(m_uTxPowerMode == AIS_TX_POWER_HIGH)
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh1High();
        else
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh1Low();
    }
    else
    {
        // High freq. power level --> above 160MHz frequency
        if(m_uTxPowerMode == AIS_TX_POWER_HIGH)
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh2High();
        else
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh2Low();
    }

    DEBUG_LOG("TxStep-GetTxPwr] setup1:%d,%d, setup2:%d,%d, pwrHigh:%d, level:%d\r\n",
            CSetupMgr::getInst()->GetTxPowerLevelCh1High(), CSetupMgr::getInst()->GetTxPowerLevelCh1Low(),
            CSetupMgr::getInst()->GetTxPowerLevelCh2High(), CSetupMgr::getInst()->GetTxPowerLevelCh2Low(),
            m_uTxPowerMode, pw_level);

    return pw_level;
}

/**
 * @brief Initialize ramp up
 */
void CLayerPhysical::InitToRampUp(void)
{
    m_nRampUpCnt = 0;
    m_fLevelSum  = 0;
}

/**
 * @brief Run ramp up
 */
void CLayerPhysical::RunToRampUp(void)
{
    if (m_nCurPwrLevel < m_nTxPwrLevelToSet)
    {
        if (++m_nRampUpCnt >= RAMPUP_SCALE_CNT)
        {
            m_nRampUpCnt = 0;

            m_fLevelSum += m_fRampUpStep;
            m_fLevelSum = MIN(m_fLevelSum, m_nTxPwrLevelToSet);

            SetTxPowerRamp((int)m_fLevelSum);
        }
    }
    else
    {
        m_nPrepTxStep = PREPTX_STEP4;

        m_bEnableCheckVswr = true;
        m_dwVswrCheckEnableTick = SysGetSystemTimer();

        DEBUG_LOG("TxStep-RunRampUp] pwrLevel, cur:%d, sum:%.2f\r\n", m_nCurPwrLevel, m_fLevelSum);
    }
}

/**
 * @brief Recalculate ramp up preparation set
 */
void CLayerPhysical::RecalcRampUpPrepSet(void)
{
    m_nTxPwrLevelToSet = GetTxPowerLevel();

    m_fRampUpStep = 0;
    if(m_nTxPwrLevelToSet > 0)
    {
        m_fRampUpStep = (float)m_nTxPwrLevelToSet / RAMPUP_NUM_STEP;
    }
}

/**
 * @brief Set Tx power ramp
 * @param nLevel Tx power level to set
 */
void CLayerPhysical::SetTxPowerRamp(int nLevel)
{
    m_nCurPwrLevel = nLevel;

    if(!CTestModeMgr::getInst()->IsRunTxLoopbackTest())
    {
        cDac::getInst()->SetDAC1Data((HWORD)m_nCurPwrLevel);
    }
}

/**
 * @brief Set Tx RF on
 * @param bOn true to turn on, false to turn off
 */
void CLayerPhysical::SetTxRfOn(bool bOn)
{
    m_bTxRfOn = bOn;

    if(bOn)
        m_dwTxRfOnSec = cTimerSys::getInst()->GetCurTimerSec();
    else
        m_dwTxRfOnSec = 0;
}

/**
 * @brief Turn off Tx
 * @param bUnconditionally true to turn off Tx unconditionally
 */
void CLayerPhysical::TurnTxOff(bool bUnconditionally)
{
    if (bUnconditionally || m_bTxRunning)
    {
        m_nPrepTxStep = PREPTX_STEP_NONE;
        m_bTxRunning  = false;
        m_bEnableCheckVswr = false;

        InitToRampUp();
        SetTxPowerRamp(0);

        SetGpioTurnOnTx(false);
        SetGpioTxPowerOn(false);

        SetTxRfOn(false);

        SetGpioTurnOnRx(true);
        SetGpioLed_Tx(false);
    }
}

/**
 * @brief Reserve Tx frequency
 * @param nFrequency Tx frequency to reserve
 */
void CLayerPhysical::ReserveTxFreq(int nFrequency)
{
    if(nFrequency > 0)
        m_nSetModemTxFreq = nFrequency;

    m_nRegTxFreq = nFrequency;
    m_dwTxFreqTick = (nFrequency > 0 ? SysGetSystemTimer() : 0);
}

/**
 * @brief Run transmit step 1 -> Set PLL Tx frequency
 */
void CLayerPhysical::RunTransmitStep1(void)
{
    if(m_nRegTxFreq > 0)
    {
        PrepToTransmitStep1(m_nRegTxFreq);
        ReserveTxFreq(0);
    }
}

/**
 * @brief Prepare to transmit step 1
 * @param nFrequency Tx frequency to prepare
 */
void CLayerPhysical::PrepToTransmitStep1(int nFrequency)
{
    if(m_nPrepTxStep == PREPTX_STEP_NONE)
    {
        if(nFrequency > 0)
        {
            cAisModem::getInst()->SetAisTxFrequency(nFrequency);
        }

        RecalcRampUpPrepSet();

        m_nPrepTxStep = PREPTX_STEP1;
        m_bTxRunning = true;
    }
}

/**
 * @brief Prepare to transmit step 2
 */
void CLayerPhysical::PrepToTransmitStep2(void)
{
    if(m_nPrepTxStep == PREPTX_STEP1)
    {
        // Turn off RX when transmit data if not loopback test mode 
        if(!CTestModeMgr::getInst()->IsRunTxLoopbackTest())
            SetGpioTurnOnRx(false);

        SetGpioLed_Tx(true);

        SetTxRfOn(true);

        SetGpioTxPowerOn(true);

        m_nPrepTxStep = PREPTX_STEP2;
    }
}

/**
 * @brief Prepare to transmit step 3
 */
void CLayerPhysical::PrepToTransmitStep3(void)
{
    if(m_nPrepTxStep == PREPTX_STEP2)
    {
        SetGpioTurnOnTx(true);
        InitToRampUp();

        m_nPrepTxStep = PREPTX_STEP3;
    }
    else if(m_nPrepTxStep == PREPTX_STEP3)
    {
        RunToRampUp();
    }
}

/**
 * @brief Check if ready to transmit
 * @return true if ready, false otherwise
 */
bool CLayerPhysical::CheckReadyToTransmit(void)
{
    if (m_nPrepTxStep != PREPTX_STEP3)
        return true;

    return ((CTestModeMgr::getInst()->IsRunTxLoopbackTest() || !GetGpioTurnOnRx())
            && GetGpioTurnOnTx()
            && GetGpioTxPowerOn()
            );
}

/**
 * @brief Check VSWR on Tx
 * @return true if check is done, false otherwise
 */
bool CLayerPhysical::CheckVswrOnTx(void)
{
    bool bCheckDone = false;

    if(m_bEnableCheckVswr)
    {
        m_wFwdPowerAdValue = CAdc::getInstAdc2()->GetADCData(ADC_2_FWD_VSWR_CH_NO);
        m_wRefPowerAdValue = CAdc::getInstAdc2()->GetADCData(ADC_2_REV_VSWR_CH_NO);
        m_dwVswrCheckRunTick = SysGetSystemTimer();
        m_bVswrErrorOccurred = CBuiltInTestMgr::getInst()->IsVswrFail();

        //SetGpioLed_Error(m_bVswrErrorOccurred);
        bCheckDone = true;
    }

    return bCheckDone;
}

/**
 * @brief Check if Tx hardware shutdown occurred
 * @return true if Tx hardware shutdown occurred, false otherwise
 */
bool CLayerPhysical::IsTxHwShutdownOccurred(void)
{
    return m_bTxHwShutdownOccured;
}

/**
 * @brief Run periodically physical
 */
void CLayerPhysical::RunPeriodicallyPhysical(void)
{
    if(!m_bEnableCheckRxMalFuncPll1)
    {
        // Pll lock for RX1 channel should be checked 1 second after PLL configuration.
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwPll1LockStartTickSec) > 1)
            m_bEnableCheckRxMalFuncPll1 = true;
    }

    if(!m_bEnableCheckRxMalFuncPll2)
    {
        // Pll lock for RX2 channel should be checked 1 second after PLL configuration.
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwPll2LockStartTickSec) > 1)
            m_bEnableCheckRxMalFuncPll2 = true;
    }

    if(!m_bEnableCheckRxMalFuncPll3)
    {
        // Pll lock for TX/RX3(DSC) channel should be checked 1 second after PLL configuration.
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwPll3LockStartTickSec) > 1)
            m_bEnableCheckRxMalFuncPll3 = true;
    }
}

/**
 * @brief Run critical physical
 */
void CLayerPhysical::RunCriticalPhysical(void)
{
    static DWORD dwCheckTick = 0;
    const int dwCheckTermMs = CTestModeMgr::getInst()->IsTestModeRunning() ? 200 : 10;

    if(SysGetDiffTimeMili(dwCheckTick) > dwCheckTermMs)
    {
        if(CheckVswrOnTx())
        {
            CMKD::getInst()->SendDataTxSWR();
        }

        dwCheckTick = SysGetSystemTimer();
    }
}
