/**
 * @file    Sentence.h
 * @brief   Sentence class declaration
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <cstdint>

#ifndef __SENTENCE_H__
#define __SENTENCE_H__

#define RX_MAX_DATA_SIZE            256
#define MAX_06ASCII_BUFF            512

#define NMEA_NULL_INTEGER           -999999
#define NMEA_NULL_DOUBLE            -999999.
#define NMEA_NULL_CHAR              '\0'

#define MMSI_BROADCAST              0

#define NMEA_DATA_VALID_TIMEMS      5000    // 5sec

#define NMEA_POS_LASTDATA_STAYSEC   6       // 6sec
#define NMEA_POS_LASTDATA_STAYMS    6000    // 6sec
#define NMEA_SOG_LASTDATA_STAYMS    6000    // 6sec
#define NMEA_COG_LASTDATA_STAYMS    6000    // 6sec

#define NMEA_HDG_LASTDATA_STAYMS    3000    // 3sec
#define NMEA_ROT_LASTDATA_STAYMS    3000    // 3sec
#define NMEA_UTC_LASTDATA_STAYMS    3000    // 3sec

class CSentence {    
protected:
    uint8_t m_szTalkerID[4];                //! 2 + 1(NULL)

public:
    /**
     * @enum tagNMEAFormatEnum
     * @brief NMEA sentence type
     */
    enum tagNMEAFormatEnum {
        NMEA_UNKNOWN = -1,  //!< Undefined format
        NMEA_ABM = 0,       //!< ABM sentence
        NMEA_ABK,           //!< ABK sentence
        NMEA_ACA,           //!< ACA sentence
        NMEA_ACK,           //!< ACK sentence
        NMEA_ACS,           //!< ACS sentence
        NMEA_AIR,           //!< AIR sentence
        NMEA_ALF,           //!< Alert Sentence(BAM)
        NMEA_ALR,           //!< ALR sentence
        NMEA_BBM,           //!< BBM sentence
        NMEA_DTM,           //!< DTM sentence
        NMEA_GBS,           //!< GBS sentence
        NMEA_GGA,           //!< GGA sentence
        NMEA_GLL,           //!< GLL sentence
        NMEA_GNS,           //!< GNS sentence
        NMEA_GSA,           //!< GSA sentence
        NMEA_GSV,           //!< GSV sentence
        NMEA_HDT,           //!< HDT sentence
        NMEA_LR1,           //!< LR1 sentence
        NMEA_LR2,           //!< LR2 sentence
        NMEA_LR3,           //!< LR3 sentence
        NMEA_LRF,           //!< LRF sentence
        NMEA_LRI,           //!< LRI sentence
		NMEA_RMC,			//!< RMC sentence
        NMEA_ROT,           //!< ROT sentence
        NMEA_SPW,           //!< VSD sentence
        NMEA_SSD,           //!< SSD sentence
        NMEA_THS,           //!< THS sentence
        NMEA_TXT,           //!< TXT sentence
        NMEA_VBW,           //!< VBW sentence
        NMEA_VDM,           //!< VDM sentence
        NMEA_VDO,           //!< VDO sentence
        NMEA_VSD,           //!< VSD sentence
        NMEA_VTG,           //!< VTG sentence
        NMEA_ZDA,           //!< ZDA sentence
        //NMEA_ACN,         //!< Alert command(BAM)
        //NMEA_ALC,         //!< Cyclic alert list(BAM)
        //NMEA_HBT,         //!< Heartbeat supervision sentence (BAM)

        NMEA_INL,           //!< Intellian sentence
        NMEA_COUNT
    };

public:
    /**
     * @fn CSentence::CSentence()
     * @brief Default constructor.
     */
    CSentence() {
        m_szTalkerID[0] = '\0';
    }

    /**
     * @fn CSentence::~CSentence()
     * @brief Default destructor.
     */
    ~CSentence() {}

    /**
     * @fn void CSentence::SetTalkerID(const uint8_t *pszTalkerID)
     * @brief Set the talker ID.
     * @param pszTalkerID Talker ID to set.
     */
    void   SetTalkerID(const uint8_t *pszTalkerID) { strcpy((char *)m_szTalkerID, (char *)pszTalkerID); }

    /**
     * @fn void CSentence::GetTalkerID(const char *pszSentence, char *pszTalker)
     * @brief Get the talker ID.
     * @param pszSentence NMEA sentence to be parsed.
     * @param pszTalker Talker ID to get.
     */
    static  void GetTalkerID(const char *pszSentence, char *pszTalker/*[3]*/);

    /**
     * @fn void CSentence::GetSenderID(const char *pszSentence, char *pszFormat)
     * @brief Get the sender id.
     * @param pszSentence NMEA sentence to be parsed.
     * @param pszFormat Format to get.
     */
    static  void GetSenderID(const char *pszSentence, char *pszFormat/*[4]*/);

    /**
     * @fn     void CSentence::GetTalkerSenderID(const char *pszSentence, char *pszFormat)
     * @brief  Function to get the sender id from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param   szTalker [OUT] Talker ID
     * @param  pszFormat [OUT] Sender ID
     * @return None
     */
    static  void GetTalkerSenderID(const char *pszSentence, char *pszTalker, char *pszFormat);
    
    /**
     * @fn     int32_t CSentence::GetFieldInteger(int nField)
     * @brief  Function to get the integer value from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param  nField [IN] Field number
     * @return Integer value
     */
    static  int32_t  GetFieldInteger(const char *pszSentence, int nField);

    /**
     * @fn     int32_t CSentence::GetFieldHexa(int nField)
     * @brief  Function to get the hexadecimal value from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param  nField [IN] Field number
     * @return Hexadecimal value
     */
    static  int32_t  GetFieldHexa(const char *pszSentence, int nField);

    /**
     * @fn     double CSentence::GetFieldDouble(int nField)
     * @brief  Function to get the double value from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param  nField [IN] Field number
     * @return Double value
     */
    static  double   GetFieldDouble(const char *pszSentence, int nField);

    /**
     * @fn     char CSentence::GetFieldChar(int nField)
     * @brief  Function to get the character value from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param  nField [IN] Field number
     * @return Character value
     */
    static  char     GetFieldChar(const char *pszSentence, int nField);

    /**
     * @fn     int CSentence::GetFieldString(int nField, char *pszField, int nBuffLen)
     * @brief  Function to get the string value from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param  nField [IN] Field number
     * @param  pszField [OUT] String value
     * @param  nBuffLen [IN] Buffer length
     * @return Length of the string
     */
    static  int32_t  GetFieldString(const char *pszSentence, int nField, char *pszField, int nBuffLen = 0);

    /**
     * @fn     void CSentence::AddSentenceTail(const char *pszSentence)
     * @brief  Function to add sentence checksum and tail info.
     * @param  pszSentence NMEA sentence to add tail info
     * @return None
     */
    static  void  AddSentenceTail(char *pszSentence);

    /**
     * @fn     int CSentence::ComputeCheckSum(char *pszSentence)
     * @brief  Function to compute the checksum from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @return Checksum value
     */
    static  int   ComputeCheckSum(const char *pszSentence, int nLen);

    /**
     * @fn     int CSentence::ComputeCheckSum(char *pszSentence)
     * @brief  Function to compute the checksum from the NMEA sentence.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @param  pX [OUT] Checksum high value
     * @param  pY [OUT] Checksum low value
     * @return Checksum value
     */
    static  int   ComputeCheckSum(const char *pszSentence, uint8_t *pX, uint8_t *pY);

    /**
     * @fn     bool CSentence::IsValidCheckSum()
     * @brief  Function to check if the checksum is valid.
     * @param  pszSentence [IN] NMEA sentence to be parsed.
     * @return TRUE if valid, FALSE otherwise
     */
    static bool IsValidCheckSum(const char *pszSentence);

    /**
     * @fn      char *CSentence::ConvertNMEAString(const char *pszText)
     * @brief   Function to convert a string to NMEA format.
     * @param   pszText [IN] Text to be converted
     * @return  Converted NMEA string
     */
    static  char *ConvertNMEAString(const char *pszText);

    /**
     * @fn      char *CSentence::ConvertNormalString(const char *pszText)
     * @brief   Function to convert an NMEA string to normal format.
     * @param   pszText [IN] NMEA text to be converted
     * @return  Converted normal string
     */
    static  char *ConvertNormalString(const char *pszText);

    /**
     * @fn      char *CSentence::ConvertPosition(char *pLatBuf, char *pLonBuf, double *pLatVal, double *pLonVal)
     * @brief   Function to convert an latitude/longuitude string to position data
     * @param   pLatBuf [IN] latitude string
     * @param   pLonBuf [IN] longuitude string
     * @param   pLatVal [OUT] latitude value
     * @param   pLonVal [OUT] longuitude value
     * @return  Converted normal string
     */
    static  int   ConvertPosition(char *pLatBuf, char *pLonBuf, double *pLatVal, double *pLonVal);

    /**
     * @fn      bool CSentence::IsNMEAReservedCharater(char ch)
     * @brief   Function to check if a character is reserved in NMEA.
     * @param   ch [IN] Character to check
     * @return  true if reserved, false otherwise
     */
    static  bool  IsNMEAReservedCharater(char ch);

    /*    *
     * @fn static char xtod(char c)
     * @brief Convert hexadecimal character to decimal.
     * @param c Hexadecimal character to convert.
     * @return Decimal value.
     */
    static  char  xtod(char c) {
        return (c>='0' && c<='9') ? c-'0' : ((c>='A' && c<='F') ? c-'A'+10 : ((c>='a' && c<='f') ? c-'a'+10 : 0));
    }
};

#endif
