/**
 * @file    Lri.h
 * @brief   Lri header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __LRI_H__
#define __LRI_H__

/******************************************************************************
*
* LRI - Long Range Interrogation
*
* $--LRI,x,a,xxxxxxxxx,xxxxxxxxx,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a*hh<CR><LF>
*        | | |         |         |       | |        | |       | |        |
*        1 2 3         4         5       6 7        8 9      10 11       12
*
* 1.     Sequence Number , 0 to 9
* 2.     Control Flag
* 3.     MMSI of "requester"
* 4.     MMSI of "destination"
* 5.6.   Latitude  - N/S (north-east co-ordinate)
* 7.8.   Longitude - E/W (north-east co-ordinate)
* 9.10.  Latitude  - N/S (south-west co-ordinate)
* 11.12. Longitude - E/W (south-west co-ordinate)
*
******************************************************************************/
class CLri : public CSentence
{
public:
    CLri();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

public:
    static int8_t   m_wSeqNum;
    static char     m_chCtrlFlag;
    static int32_t  m_dwMMSIReq;
    static int32_t  m_dwMMSIDest;
    static double   m_fLatNE;
    static double   m_fLonNE;
    static double   m_fLatSW;
    static double   m_fLonSW;
};

#endif /* __LRI_H__ */

