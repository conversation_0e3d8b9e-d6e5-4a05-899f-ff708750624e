/**
 * @file    Vtg.h
 * @brief   Vtg header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __VTG_H__
#define __VTG_H__

/******************************************************************************
 * 
 * VTG - 
 *
 * $--VTG,x.x,T,x.x,M,x.x,N,x.x,K,a*hh<CR><LF>
 *         |--|  |--|  |--|  |--| |
 *         1     2     3     4    5
 *
 * 1. Course over ground, degrees true
 * 2. Course over ground, degrees magnetic
 * 3. Speed over ground, knots
 * 4. Speed over ground, km/h
 * 5. Mode indicator
 * 
 ******************************************************************************/
class CVtg : public CSentence
{
public:
    CVtg();
    void ClearData(void);

    bool Parse(const char *pszSentence, int nCurCrs);
    bool IsValidCourseData(void);
    bool IsValidSpeedData(void);
    int  GetCourse(void);
    int  GetSpeed(void);
    int  GetVelocity(void);

    void RunPeriodically(void);

protected:
    int     m_nSpdData;
    int     m_nVelData;
    int     m_nCrsData;

    DWORD   m_dwSpdValidTick;
    DWORD   m_dwCrsValidTick;
};

#endif /* __VTG_H__ */

