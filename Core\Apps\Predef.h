/**
 * @file    Predef.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef __PREDEFINE_H__
#define __PREDEFINE_H__

/******************************************************/
#define STR_MANUFACTURER_CODE   "INL"   // NMEA 0183
#define STR_MANUFACTURER_NUM    "606"   // NMEA 2000
/******************************************************/
#define STR_MODEL_ID            "IS-1250"
/******************************************************/
#define STR_VERSION_SW          "V0.1"
#define STR_RELEASE_DATE        "20250415"
/******************************************************/
#define MASTER_PASSWORD         "DZjKTEL16wb?RXIN"
#define ENGINEER_PASSWORD       "c9yRgKdpuYJeIn13"
/******************************************************/

#define __ENABLE_ASSIGNED_MODE__
#define __ENABLE_DSC__

#define __ENABLE_LONGRANGE__
#define __ENABLE_PILOT__
#define __ENABLE_EXT_DISP__

#define __ENABLE_CHECK_MSG15_CNT__

#define __ENABLE_GNSS_GALILEO__

#define __TESTSIGNAL_USE_PATTERN__

#define __USE_12BIT_ADC__
#define __USE_INTERNAL_FLASH_FOR_BACKUP__

//#define __ENABLE_DUMMY_VDO__
/******************************************************/
#endif    /*__PREDEFINE_H__*/
