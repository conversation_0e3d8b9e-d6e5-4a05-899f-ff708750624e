/**
 * @file    Lrf.h
 * @brief   Lrf header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __LRF_H__
#define __LRF_H__

/******************************************************************************
*
* LRF - Long Range Function
*
* $--LRF,x,xxxxxxxxx,c--c,c--c,c--c*hh<CR><LF>
*        | |         |    |    |
*        1 2         3    4    5
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of requester
* 3.  Name of requester, 1 to 20 character string
* 4.  Function request , 1 to 26 characters
* 5.  Function reply status
*
******************************************************************************/
class CLrf : public CSentence
{
public:
    CLrf();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence,
                                WORD wSequenceNum,
                                int nResquestMMSI,
                                char *requestor_name,
                                char *pFinalFuncRequest,
                                char *pFinalReplyStatus);

public:
    static  WORD    m_wSeqNum;
    static  DWORD   m_dwReqMMSI;
    static  char    m_pstrReqName[30];
    static  char    m_pstrFuncReq[30];
    static  char    m_pstrReplyStat[30];
};

#endif /* __LRF_H__ */

