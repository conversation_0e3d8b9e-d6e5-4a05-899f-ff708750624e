/**
 * @file    Ship.h
 * @brief   Ship information class
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef __SHIP_H__
#define __SHIP_H__

#include <memory>
#include <string.h>
#include "DataType.h"
#include "AisLib.h"

class cShip
{
public:
    cShip(void) { ClearOwnShipInfo(); };
    ~cShip(void) {};

    static std::shared_ptr<cShip> getOwnShipInst() {
        static std::shared_ptr<cShip> pInst = std::make_shared<cShip>();
        return pInst;
    }

    void ClearOwnShipInfo() {
    	CAisLib::SetDefaultSysDateTime(&xUtcTime);
    	CAisLib::SetDefaultSysDateTime(&xSysTime);
    	CAisLib::SetDefaultSysDateTime(&xPosFixTime);
		bSysTimeUTC = FALSE;
    	nSysTimeSrc = SYSTIMESRC_INT_GNSS;

        memset(&xStaticData, 0, sizeof(STATIC_DATA));
        memset(&xDynamicData, 0, sizeof(DYNAMIC_DATA));
        memset(&xNavData, 0, sizeof(NAV_DATA));

        memset(xStaticData.vCallSign, '\0', LEN_MAX_CALLSIGN);
        memset(xStaticData.vShipName, '\0', LEN_MAX_SHIP_NAME);
        memset(xStaticData.vVendorID, '\0', LEN_MAX_VENDORID);
        memset(xNavData.pstrDestination, '\0', LEN_MAX_DESTINATION);

    #ifdef __ENABLE_FIX_MMSI__
        xStaticData.dMMSI = AIS_TEST_AB_MMSI;
        memcpy(xStaticData.vShipName, "Intellian", 9);
    #else
        xStaticData.dMMSI    = AIS_AB_MMSI_NULL;
    #endif
        xStaticData.dwImoID  = AIS_IMOID_DFLT;
        xStaticData.nDTE     = DTE_READY;

        xNavData.uShipType   = AIS_SHIPTYPE_DFLT;
        xNavData.uDraught    = AIS_DRAUGHT_DFLT;
        xNavData.uPerson     = AIS_PERSON_DFLT;

        xNavData.xETA.nMonth = ETA_MONTH_NULL;
        xNavData.xETA.nDay   = ETA_DAY_NULL;
        xNavData.xETA.nHour  = ETA_HOUR_NULL;
        xNavData.xETA.nMin   = ETA_MIN_NULL;
        xNavData.xETA.nSec   = ETA_SEC_NULL;

        xNavData.uNavStatus  = AIS_NAV_STATUS_DFLT;
        xNavData.uManoeuvre  = AIS_MANOEUVRE_DFLT;

        memset(xOwnShipPosReportSec, 0, sizeof(DWORD)*NUM_SAVE_NONREPEAT_RCV_SEC);
    }

    bool  IsOwnShipMMSISet(void)        { return (xStaticData.dMMSI != AIS_AB_MMSI_NULL); }
    DWORD GetOwnShipMMSI(void)          { return xStaticData.dMMSI; }
    void  SetOwnShipMMSI(DWORD dMMSI)   { xStaticData.dMMSI = dMMSI; }

    bool  IsOwnShipIMOSet(void)         { return (xStaticData.dwImoID != AIS_IMOID_DFLT); }
    DWORD GetOwnShipIMO(void)           { return xStaticData.dwImoID; }
    void  SetOwnShipIMO(DWORD dwIMO)    { xStaticData.dwImoID = dwIMO; }

public:
    SYS_DATE_TIME   xUtcTime;

    SYS_DATE_TIME   xSysTime;
    BOOL            bSysTimeUTC;        // TRUE : xSysTime value is set with UTC
    INT8            nSysTimeSrc;        // SYSTIMESRC_INT_GNSS / SYSTIMESRC_EXT_SENSOR / SYSTIMESRC_EXT_SYNCSRC

    SYS_DATE_TIME   xPosFixTime;

    STATIC_DATA     xStaticData;
    DYNAMIC_DATA    xDynamicData;
    NAV_DATA        xNavData;

    DWORD	        xOwnShipPosReportSec[NUM_SAVE_NONREPEAT_RCV_SEC];
};

#endif    /*__SHIP_H__*/
