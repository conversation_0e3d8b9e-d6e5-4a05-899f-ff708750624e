/**
 * @file    Abk.h
 * @brief   Abk header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ABK_H__
#define __ABK_H__

/******************************************************************************
 *
 * ABK - Addressed and binary broadcast acknowledgement
 *
 * $--ABK,xxxxxxxxx,a,x.x,x,x*hh<CR><LF>
 *        |         |  |  | |
 *        1         2  3  4 5
 *
 *  1. MMSI of the addressed destination AIS unit
 *  2. AIS channel of reception
 *  3. ITU-R M. 1371 message ID
 *  4. Message Sequence Number
 *  5. Type of acknowledgement
 *
 ******************************************************************************/
class CAbk : public CSentence
{
public:
    CAbk();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the ABK sentence
     * @param pszSentence The sentence to be made
     * @param uDestMMSI The MMSI of the addressed destination AIS unit
     * @param nAckRxCh The AIS channel of reception
     * @param nVdlMsgID The ITU-R M. 1371 message ID
     * @param nMsgSeqNum The message sequence number
     * @param nAckType The type of acknowledgement
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, 
                                UINT uDestMMSI, 
                                int nAckRxCh, 
                                int nVdlMsgID, 
                                int nMsgSeqNum, 
                                int nAckType);

public:
    static int8_t m_nSequentialId;
};

#endif /* __ABK_H__ */

