#ifndef  __USERDIRMGR_H__
#define __USERDIRMGR_H__

#include "memory"
#include "DataType.h"
#include "SysConst.h"
#include "SysLib.h"
#include "ComLib.h"
#include "AisLib.h"
#include "AisMsg.h"
#include "VdlRxMgr.h"

//============================================================================
// ITU-R-1371-5 Annex2 3.1.1.4 Number of received stations
// A station, which is unable to attain UTC direct or UTC indirect synchronization
// and is also unable to receive transmissions from a base station, should synchronize 
// to the station indicating the highest number of other stations received during 
// the last nine frames, provided that two reports have been received from 
// that station in the last 40 s.
#define NUM_RCV_ST_SAVE_FRAME           9
#define NUM_RCV_ST_SAVE_MIN             540     // 9 minutes

#define SEMAPHORE_CHECKTIME_SEC         40      // 40 seconds
#define SEMAPHORE_VALID_TIMESTAMP_MAX   60      // 60 seconds

//----------------------------------------------------------------------------
#define AIS_MAX_DIR_BS_NUM              30      // 
#define AIS_MAX_DIR_MS_NUM              1300

#define AIS_DIR_DATA_NOT_FOUND         -1
#define AIS_DIR_DATA_RX_STN_NUM_NULL   -1
#define AIS_DIR_DATA_TIME_STAMP_NULL   -1

#define IDX_POS_PREV                    0
#define IDX_POS_LAST                    1

//============================================================================
// Structure for User Directory
typedef  struct
{
    DWORD   dMMSI;                              // MMSI

    INT8    nStType;                            // Station type
    INT8    nClassBType;                        // Class B type
    UINT16  uNumRcvStLast;                      // Count the number of stations that have received
    UINT16  uNumRcvSt9min;                      // the number of other stations received during last 9 frames
    UINT16  puNumRcvStForMin[NUM_RCV_ST_SAVE_FRAME];    // the number of received stations for 9 min

    UCHAR   bRcvChID;                           // AIS_CHANNEL_AIS1,...,AIS_CHANNEL_AIS2
    UCHAR   bRptIndicator;                      // repeat indicator: only one level
    POS_FLOAT xPosF;                            // 1 degree unit
    FLOAT   fDistance;
    UCHAR   bNavStatus;                         // Navigational status
    UCHAR   bSyncState;                         // sync.state : 0 ~ 3
    UCHAR   bPosValid;                          // 0=position invalid, 1=position valid
    UCHAR   bTimeStamp;                         // Time Stamp

    INT8    nLRTxCtrlCode;                      // Transmission control for longrange broadcast message, only for base station
    DWORD   dwLRCtrlRcvSec;                     // Transmission control for longrange broadcast    message, only for base station
    BOOL    bLRInRegion;
    DWORD   dwLRInRegionSec;

    BOOL    bCanBeSyncSrc;                      // Can be synchronization source
    DWORD   dwLastNonRepeatPosReportSec[NUM_SAVE_NONREPEAT_RCV_SEC];    // [0] : Last time non-repeated position report was received, [1] : Last time previous non-repeated position report was received

    DWORD   dwLastPosReportSec;                 // Last time position report was received
    DWORD   dwLastSlotReuseSec;                 // Last time the slot of this station was reused, countable for 0 ~ 136 years
    DWORD   dwLastMsg5RcvSec;                   // Last time MSG5 was receive
} xDIRDATA;

class CUserDirMgr
{
public:
    CUserDirMgr();
    virtual ~CUserDirMgr(void);

    static std::shared_ptr<CUserDirMgr> getInst() {
        static std::shared_ptr<CUserDirMgr> pInst = std::make_shared<CUserDirMgr>();
        return pInst;
    }

public:
    /**
     * @brief Clear directory data
     */
    void    ClearDirDataBS(void);
    /**
     * @brief Clear directory data
     */
    void    ClearDirDataMS(void);
    /**
     * @brief Clear directory data
     */
    void    ClearDirDataOne(xDIRDATA *pAisDirData);
    /**
     * @brief Clear all parameters
     */
    void    ClearAllParameter(void);

    /**
     * @brief Set class B type
     */
    void    SetClassBType(void);
    /**
     * @brief Set navigation status
     */
    void    SetNavStatus(void);
    /**
     * @brief Set synchronization state
     */
    void    SetSyncState(void);
    /**
     * @brief Set distance
     */
    void    SetDistance(void);
    /**
     * @brief Set received station numbers
     */
    void    SetRxStnNums(void);
    /**
     * @brief Set time stamp
     */
    void    SetTimeStamp(void);

    /**
     * @brief Find directory data index from table
     */
    int     FindDirDataIdxFromTbl(xDIRDATA *pAisDirData, int nMaxNo, DWORD dMMSI);
    /**
     * @brief Find directory data index for base station
     */
    int     FindDirDataIdxBS(DWORD dMMSI);
    /**
     * @brief Find directory data index for mobile station
     */
    int     FindDirDataIdxMS(DWORD dMMSI);
    /**
     * @brief Find directory data index
     */
    int     FindDirDataIdx(DWORD dMMSI);

    /**
     * @brief Find directory data pointer for base station
     */
    xDIRDATA* FindDirDataPtrBS(DWORD dMMSI);
    /**
     * @brief Find directory data pointer for mobile station
     */
    xDIRDATA* FindDirDataPtrMS(DWORD dMMSI);
    /**
     * @brief Find directory data pointer
     */
    xDIRDATA* FindDirDataPtr(DWORD dMMSI);
    /**
     * @brief Find empty slot number
     */
    int     FindEmptySlotNo(xDIRDATA *pAisDirData, int nMaxNo);
    /**
     * @brief Find empty slot number for base station
     */
    inline  int    FindEmptySlotBaseSt();
    /**
     * @brief Find empty slot number for mobile station
     */
    inline  int    FindEmptySlotMobileSt();

    /**
     * @brief Get directory data for base station
     */
    inline  xDIRDATA *GetDirDataBS(int nSlotNo);
    /**
     * @brief Get directory data for mobile station
     */
    inline  xDIRDATA *GetDirDataMS(int nSlotNo);
    /**
     * @brief Get directory data by MMSI
     */
    inline  xDIRDATA *GetDirDataByMMSI(int nMMSI);

    /**
     * @brief Detect received station type
     */
    int     DetectRcvStationType(int nMMSI, int nRcvMsgID, int nMsg18ClassBType);
    /**
     * @brief Get interrogated station type
     */
    int     GetInterrogatedStType(int nMMSI);
    /**
     * @brief Check if station type is mobile
     */
    bool    IsStationTypeMobile(int nMMSI);

    /**
     * @brief Get last received channel
     */
    UINT8   GetLastRcvCh(UINT uMMSI);

    /**
     * @brief Append directory data for base station
     */
    int     AppendDirDataBS(void);
    /**
     * @brief Append directory data for mobile station
     */
    int     AppendDirDataMS(void);
    /**
     * @brief Update directory data for base station
     */
    void    UpdateDirDataBS(int nSlotNo);
    /**
     * @brief Update directory data for mobile station
     */
    void    UpdateDirDataMS(int nSlotNo);
    /**
     * @brief Update directory data
     */
    void    UpdateDirData(CVdlRxMgr *pRxMgr);

    /**
     * @brief Set last slot reuse time tick
     */
    void    SetLastSlotReuseTimeTick(UINT uMMSI);

    /**
     * @brief Set long range transmission control code
     */
    void    SetLongRangeTxCtrlCode(UINT uBaseStMMSI, INT8 nLRTxCtrlCode);
    /**
     * @brief Set long range transmission in region
     */
    void    SetLongRangeTxInRegion(UINT uBaseStMMSI, bool bPosInRegion);

    /**
     * @brief Get own ship number of received stations for 9 minutes
     */
    int     GetOwnShipNumOfRcvStFor9Min(void);
    /**
     * @brief Get own ship number of received stations for 1 minute
     */
    int     GetOwnShipNumOfRcvStFor1Min(void);
    /**
     * @brief Get the highest received synchronization state value
     */
    int     GetHighPrioritySyncState(void);
    /**
     * @brief Check obsolete user directory data
     */
    bool    CheckObsoleteUserDirData(xDIRDATA *pAisDirData, int nMaxNo, int *pNoOfItems);

    /**
     * @brief Update distance for all stations
     */
    void    UpdateStationDistance(xDIRDATA *pDirList, int nNumSt);
    void    UpdateStationDistance(void);

    /**
     * @brief Check if the station position is valid
     */
    bool    IsStationPosAvail(DWORD dMMSI);
    /**
     * @brief Check if the base station position is valid
     */
    bool    IsBaseStationPosValid(DWORD dMMSI);
    /**
     * @brief Check if the base station is within 120 nautical miles
     */
    bool    IsBaseStationWithin120NM(DWORD dMMSI);

    /**
     * @brief Update position report receive time
     */
    void    UpdatePosReportRcvTime(xDIRDATA *pDirList, int nDirIdx, int nRepeatIndicator);
    /**
     * @brief Check if two position reports have been received within the last 40 seconds
     */
    bool    CheckTwoPosRcvWithin40sec(xDIRDATA *pDirData);
    bool    CheckTwoPosRcvWithin40sec(DWORD uMMSI);

    /**
     * @brief Check long range transmission control code by base station
     */
    uint8_t CheckLongRangeTxCtrlByBaseSt(void);
    /**
     * @brief Check long range region data invalid
     */
    void    CheckLongRangeRegionDataInvalid(void);

    /**
     * @brief Check if it's able to be UTC indirect synchronization source
     */
    bool    IsUtcIndirectSyncSrcCandi(xDIRDATA *pDirData);
    bool    IsUtcIndirectSyncSrcCandi(DWORD uMMSI);
    DWORD   FindUtcIndirectCandidate(xDIRDATA *pDirList, int nNumSt);
    DWORD   GetSyncSrcForUtcIndirect();

    /**
     * @brief Get Base station Direct synchronization source
     */
    bool    IsBaseDirectSyncSrcCandi(xDIRDATA *pDirData);
    bool    IsBaseDirectSyncSrcCandi(DWORD uMMSI);
    DWORD   GetSyncSrcForBaseDirect();

    /**
     * @brief Get Base station Indirect synchronization source
     */
    bool    IsBaseIndirectSyncSrcCandi(xDIRDATA *pDirData);
    bool    IsBaseIndirectSyncSrcCandi(DWORD uMMSI);
    DWORD   GetSyncSrcForBaseIndirectCandi(xDIRDATA *pDirList, int nNumSt);
    DWORD   GetSyncSrcForBaseIndirect();

    /**
     * @brief Get Mobile semaphore synchronization source
     */
    bool    IsMobileSemaSyncSrcCandi(xDIRDATA *pDirData);
    bool    IsMobileSemaSyncSrcCandi(DWORD uMMSI);
    DWORD   GetSyncSrcForMobileSemaphore();

    /**
     * @brief Count the number of received stations
     */
    void    CountNumberOfStations(int nSaveIdx, xDIRDATA *pDirList, int nNumSt);
    void    CountNamberOfStationsOwnShip(int nSaveIdx);
    bool    UpdateNumberOfStations(void);

    /**
     * @brief Run periodically
     */
    void    RunPeriodically();

    /**
     * @brief Debug output directory data
     */
    void    DebugOutDirList(char *pTitle, xDIRDATA *pDirList, int nNumSt);
    void    DebugOutDirBase(void);
    void    DebugOutDirMobile(void);

private:
    xDIRDATA   *m_vAisDirDataBS;        // Base station Data
    xDIRDATA   *m_vAisDirDataMS;        // Mobile station Data
    xAISMSG99  *m_pRxAisMsg99;

    int         m_nNumValidBaseSt;      // number of valid BS
    int         m_nNumValidMobileSt;    // number of valid MS

    UINT16     *m_puOwnShipNumRcvSt9min;
    int         m_nOwnShipNumRcvSt9min;
    int         m_nOwnShipNumRcvStLast;

    int         m_nRxChID;              // AIS_CHANNEL_AIS1 / AIS_CHANNEL_AIS2 / AIS_CHANNEL_NONE
    int         m_nMsgID;
    int         m_nRepeatIndicator;
    int         m_nSyncState;
    int         m_nPosValid;
    FLOAT       m_fDistance;
    POS_FLOAT   m_xPosF;
    int         m_nRxStnNums;
    int         m_nTimeStamp;
    int         m_nMsg18ClassBType;     // CLASS_B_UNIT_SO ~ CLASS_B_UNIT_UNKNOWN
    DWORD       m_dwSrcMMSI;
    int         m_nNavStatus;

    int         m_nOtherStSemaMMSI;
    DWORD       m_dwOtherStSemaCheckSec;
};

#endif

