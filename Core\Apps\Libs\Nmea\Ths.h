/**
 * @file    Ths.h
 * @brief   Ths header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __THS_H__
#define __THS_H__

/******************************************************************************
 * 
 * THS - 
 *
 * $--THS,x.x,a*hh<CR><LF>
 *         |  |
 *         1  2
 * 
 * 1. Heading, degrees true
 * 2. Mode indicator
 * 
 ******************************************************************************/
class CThs : public CSentence
{
public:
    CThs();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidHeadingData(void);
    int  GetHeading(void);
    
    void RunPeriodically(void);

protected:
    int     m_nHdgData;
    DWORD   m_dwRcvTick;
    DWORD   m_ndwLatencyTHS;
};

#endif /* __THS_H__ */

