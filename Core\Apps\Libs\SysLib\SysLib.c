/**
 * @file    SysLib.c
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"

//========================================================================
static  TIM_TypeDef       *G_SysTimer = ((TIM_TypeDef *)TIM2_BASE);
static  TIM_HandleTypeDef  G_SysTimerHandle;
//========================================================================
static BOOL  G_bSysRunOS = FALSE;
static DWORD APB_TIMER_CLOCK = 0;
volatile DWORD  G_dSystemTickCounter = 0;
//========================================================================
const unsigned char FreeRam1MemBuf[512*1024] __attribute__((section(".ram_d1_section"))) = { 0x00, };
const unsigned char FreeRam2MemBuf[288*1024] __attribute__((section(".ram_d2_section"))) = { 0x00, };
const unsigned char FreeRam3MemBuf[ 64*1024] __attribute__((section(".ram_d3_section"))) = { 0x00, };

DWORD MemSizeDump[1024];
static DWORD dFreeRam1MemS = 512 * 1024;                    // 512KB
static void *pFreeRam1MemP = (void *)FreeRam1MemBuf;
static DWORD dFreeRam2MemS = 288 * 1024;                    // 288KB
static void *pFreeRam2MemP = (void *)FreeRam2MemBuf;
static DWORD dFreeRam3MemS = 64 * 1024;                     // 64KB
static void *pFreeRam3MemP = (void *)FreeRam3MemBuf;
//========================================================================
void  SysEnableIRQ(void)
{
    __DMB();
    __enable_irq();
}

void  SysDisableIRQ(void)
{
    __disable_irq();
    __DMB();
}

void  SysReSetSystem(void)
{
    NVIC_SystemReset();
}

//========================================================================
void SysTimerInit(TIM_TypeDef *pTimer)
{
    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};

    APB_TIMER_CLOCK = (HAL_RCC_GetPCLK1Freq() * 2);

    if (pTimer != NULL)
    {
        G_SysTimer = pTimer;
    }

    G_SysTimerHandle.Instance               = G_SysTimer;
    G_SysTimerHandle.Init.Period            = 4294967295;
    G_SysTimerHandle.Init.Prescaler         = 0;
    G_SysTimerHandle.Init.ClockDivision     = TIM_CLOCKDIVISION_DIV1;
    G_SysTimerHandle.Init.CounterMode       = TIM_COUNTERMODE_UP;
    G_SysTimerHandle.Init.RepetitionCounter = 0;
    G_SysTimerHandle.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    G_SysTimerHandle.State                  = HAL_TIM_STATE_RESET;
    if (HAL_TIM_Base_Init(&G_SysTimerHandle) != HAL_OK)
    {
        Error_Handler();
    }

    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if (HAL_TIM_ConfigClockSource(&G_SysTimerHandle, &sClockSourceConfig) != HAL_OK)
    {
      Error_Handler();
    }

    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&G_SysTimerHandle, &sMasterConfig) != HAL_OK)
    {
      Error_Handler();
    }

    HAL_TIM_Base_Start(&G_SysTimerHandle);    
}

//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt)
{
    while (dDelayCnt) dDelayCnt--;
}

void  SysDelay10MicroSec(DWORD dDelay10Micro)
{
    DWORD dDelayTick;
    DWORD dStartTick;
    DWORD dCurrTickX;
    DWORD dDiffTickX;

    dStartTick = G_SysTimer->CNT;

    if (dDelay10Micro == 0)
        return;

    dDelayTick = APB_TIMER_CLOCK / 10000000 * dDelay10Micro;

    while (1)
    {
        dCurrTickX = G_SysTimer->CNT;

        if (dCurrTickX < dStartTick)
            dDiffTickX = (G_SysTimer->ARR - dStartTick) + dCurrTickX;
        else
            dDiffTickX = dCurrTickX - dStartTick;

        if (dDiffTickX >= dDelayTick)
            break;
    }
}

void  SysDelayMicroSec(DWORD dDelayMicro)
{
    DWORD dDelayTick;
    DWORD dStartTick;
    DWORD dCurrTickX;
    DWORD dDiffTickX;

    dStartTick = G_SysTimer->CNT;

    if (dDelayMicro == 0)
        return;

    dDelayTick = APB_TIMER_CLOCK / 1000000 * dDelayMicro;

    while (1)
    {
        dCurrTickX = G_SysTimer->CNT;

        if (dCurrTickX < dStartTick)
            dDiffTickX = (G_SysTimer->ARR - dStartTick) + dCurrTickX;
        else
            dDiffTickX = dCurrTickX - dStartTick;

        if (dDiffTickX >= dDelayTick)
            break;
    }
}

void  SysDelayMiliSec(DWORD dDelayMili)
{
    DWORD dDelayTick;
    DWORD dStartTick;
    DWORD dCurrTickX;
    DWORD dDiffTickX;

    dStartTick = G_SysTimer->CNT;

    if (dDelayMili == 0)
        return;

    dDelayTick = APB_TIMER_CLOCK / 1000 * dDelayMili;

    while (1)
    {
        dCurrTickX = G_SysTimer->CNT;

        if (dCurrTickX < dStartTick)
            dDiffTickX = (G_SysTimer->ARR - dStartTick) + dCurrTickX;
        else
            dDiffTickX = dCurrTickX - dStartTick;

        if (dDiffTickX >= dDelayTick)
            break;
    }
}

DWORD SysGetFreeRunTimerCount(void)
{
    return(G_SysTimer->CNT);
}

DWORD SysGetFreeRunTimerDiffCount(DWORD dOldCount, DWORD dNewCount)
{
    DWORD dTempX;

    if (dNewCount >= dOldCount)
        dTempX = dNewCount - dOldCount;
    else
        dTempX = (0xffffffff - dOldCount) + dNewCount;

    return(dTempX);
}

float SysGetFreeRunTimerDiffMicro(DWORD dOldCount, DWORD dNewCount)
{
    DWORD dTempX;
    float fMicro;

    dTempX = SysGetFreeRunTimerDiffCount(dOldCount, dNewCount);

    fMicro = (float)((double)dTempX / APB_TIMER_CLOCK * 1000000.0);

    return(fMicro);
}
//========================================================================
DWORD SysGetSystemTimer(void)
{
    return(G_dSystemTickCounter);
}

DWORD SysIncSystemTimer(void)
{
    ++G_dSystemTickCounter;

    return(G_dSystemTickCounter);
}
//========================================================================
DWORD SysCalcTickToMili(DWORD dTick)
{
    return(CALC_TICK_TO_MILI(dTick));
}

DWORD SysCalcMiliToTick(DWORD dMili)
{
    return(CALC_MILI_TO_TICK(dMili));
}

DWORD SysCalcTickToScnd(DWORD dTick)
{
    return(CALC_TICK_TO_SEC(dTick));
}

DWORD SysGetDiffTimeTick(DWORD dTime)
{
    DWORD dTemp;

    dTemp = SysGetSystemTimer();
    if (dTemp >= dTime)
        dTemp -= dTime;
    else
        dTemp += (0xffffffff - dTime);

    return(dTemp);
}

DWORD SysGetDiffTimeMili(DWORD dTime)
{
    return(CALC_TICK_TO_MILI(SysGetDiffTimeTick(dTime)));
}

DWORD SysGetDiffTimeScnd(DWORD dTime)
{
    return(SysGetDiffTimeMili(dTime) / 1000);
}
//========================================================================
void SysSetRunOSStatus(BOOL bRunOS)
{
    G_bSysRunOS = bRunOS;
}

void RunSystemDelayMs(DWORD dwDelayMs)
{
    if (G_bSysRunOS)
        osDelay(dwDelayMs);
    
    SysDelayMiliSec(dwDelayMs);
}
//========================================================================
void *SysAllocMemory(DWORD dAllocSize)
{
    void  *pMemX = NULL;
    static DWORD idx = 0;

    MemSizeDump[idx++] = dAllocSize;

    dAllocSize = (dAllocSize + 0x0f) & 0xfffffffc;
    if (dAllocSize < 16)
        dAllocSize = 16;

    if (dFreeRam1MemS >= dAllocSize)
    {
        pMemX = pFreeRam1MemP;
        pFreeRam1MemP += dAllocSize;
        dFreeRam1MemS -= dAllocSize;
    }
    else if (dFreeRam2MemS >= dAllocSize)
    {
        pMemX = pFreeRam2MemP;
        pFreeRam2MemP += dAllocSize;
        dFreeRam2MemS -= dAllocSize;
    }
    else if (dFreeRam3MemS >= dAllocSize)
    {
        pMemX = pFreeRam3MemP;
        pFreeRam3MemP += dAllocSize;
        dFreeRam3MemS -= dAllocSize;
    }
    else
    {
        pMemX = (void*)pvPortMalloc(dAllocSize);
        if (pMemX == NULL)
        {
        	Error_Handler();
        }
    }

    return(pMemX);
}
//========================================================================

