/**
 * @file    AllConst.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef __DEFINE_H__
#define __DEFINE_H__

#include "Predef.h"

//----------------------------------------------------
// Constants
//----------------------------------------------------
enum
{
    BAUD_IDX_4800 = 0,
    BAUD_IDX_38400,
    BAUD_IDX_AUTO,
};

#define UARTID_1                    1
#define UARTID_2                    2
#define UARTID_3                    3
#define UARTID_4                    4
#define UARTID_5                    5
#define UARTID_6                    6
#define UARTID_7                    7
#define UARTID_8                    8

#define SENSORID_0                  0       // Internal GNSS
#define SENSORID_1                  1       // Sensor-1
#define SENSORID_2                  2       // Sensor-2
#define SENSORID_3                  3       // Sensor-3
#define NUM_NMEA_SENSORS            4
#define SENSORID_UNDEF             -1
#define SENSORID_INTERNAL  SENSORID_0

#define HIGHSPD_PORTID_0            0       // MKD
#define HIGHSPD_PORTID_1            1       // PI1, High speed port 1 (Pilot port)
#define HIGHSPD_PORTID_2            2       // PI2, High speed port 2 (External Display)
#define HIGHSPD_PORTID_3            3       // PI3, Debugging port
#define HIGHSPD_PORTID_NA          -1       // Undefined

#define MON_PORTID_CH_SENSOR1      'X'
#define MON_PORTID_CH_SENSOR2      'Y'
#define MON_PORTID_CH_SENSOR3      'Z'
#define MON_PORTID_CH_EXTDISP      'E'
#define MON_PORTID_CH_PILOT        'P'
#define MON_PORTID_CH_LR           'L'
#define MON_PORTID_CH_DEBUG        'D'

#define LOOPBACK_PORT_ID_SENSOR1    0
#define LOOPBACK_PORT_ID_SENSOR2    1
#define LOOPBACK_PORT_ID_SENSOR3    2
#define LOOPBACK_PORT_ID_LR         3
#define LOOPBACK_PORT_ID_EXTDISP    4
#define LOOPBACK_PORT_ID_PILOT      5
#define LOOPBACK_PORT_ID_NA        -1

//----------------------------------------------------------------------------------------
// PORT_BAUDMASK_DFLT : 각 2비트는 0:4800, 1:38400, 02:auto 을 표시
//----------------------------------------------------------------------------------------
// bit [1..0] : SENSOR 1
// bit [3..2] : SENSOR 2
// bit [5..4] : SENSOR 3
// bit [7..6] : Long range
// bit [9..8] : External
//----------------------------------------------------------------------------------------
#define PORT_BAUDMASK_DFLT          0x00000140        // 0001 0100 0000
#define PORT_BAUDMASK_MIN           0x00000000
#define PORT_BAUDMASK_MAX           0x000003FF        // 0011 1111 1111

#define GET_BAUD_IDX_SENSOR1(uBaudBitMap)       ((uBaudBitMap & 0x0003))
#define GET_BAUD_IDX_SENSOR2(uBaudBitMap)       ((uBaudBitMap & 0x000C) >> 2)
#define GET_BAUD_IDX_SENSOR3(uBaudBitMap)       ((uBaudBitMap & 0x0030) >> 4)
#define GET_BAUD_IDX_LR(uBaudBitMap)            ((uBaudBitMap & 0x00C0) >> 6)
#define GET_BAUD_IDX_EXT_DISP(uBaudBitMap)      ((uBaudBitMap & 0x0300) >> 8)

#define NMEA_SCALE_SOG              10
#define NMEA_SCALE_COG              10
#define NMEA_SCALE_ROT              10
#define NMEA_SCALE_HDT              1

#define NMEA_SOG_NULL               AIS_SOG_VDL_NULL
#define NMEA_COG_NULL               AIS_COG_VDL_NULL
#define NMEA_HDG_NULL               36000
#define NMEA_ROT_NULL               80000

#define UTCTIMESRC_INT_GNSS         0
#define UTCTIMESRC_EXT_SYNCSRC      1

#define SYSTIMESRC_INT_GNSS         0
#define SYSTIMESRC_EXT_SENSOR       1
#define SYSTIMESRC_EXT_SYNCSRC      2

typedef enum
{
    PITESTMODE_NONE,
    PITESTMODE_TX,
    PITESTMODE_RX,
    PITESTMODE_BITE
} TagTestMode;

#define BITE_NO_ERROR                   0x0000
#define BITE_VOLTAGE_ERROR              0x0001
#define BITE_ROM_ERROR                  0x0002
#define BITE_EEPROM_ERROR               0x0004
#define BITE_GPS_ERROR                  0x0008
#define BITE_TXLOOPBACK_CH1_ERROR       0x0010
#define BITE_TXLOOPBACK_CH2_ERROR       0x0020
#define BITE_UNKNOWN_ERROR              0x1000

#define DC_VOLTAGE_MIN                  2.7            // Voltage fail if is lower than this value

#endif//__DEFINE_H__
