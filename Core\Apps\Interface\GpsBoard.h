/**
 * @file    GpsBoard.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "SysConst.h"
#include "SysLib.h"
#include "ComLib.h"
#include "Uart.h"
#include "Nmea.h"

#ifndef  __GPSBOARD_H__
#define  __GPSBOARD_H__

//----------------------------------------
// Constants
//----------------------------------------
#define GPS_UBLOX                   0
#define GPS_EXTERNAL                1
//---------------------------------------
#define UBX_SPD_SMOOTH_BUFF_SIZE    10
//----------------------------------------
#define HDGSAVE_BUFF_SIZE           60
//----------------------------------------
#define POS_FALLBACK_CHECKMS_DN     5000
#define POS_FALLBACK_CHECKMS_UP     30000
//----------------------------------------
#define SENSOR_CHECK_TERM_MS        900     // Need less than 1sec.

//----------------------------------------
// Structure
//----------------------------------------
typedef struct
{
    int     nHdgData;
    DWORD   dwRcvSec;
} HDGDATA;

class cGpsBoard : public cNMEA
{
public:
    cGpsBoard(INT8 nSensorID);
    cGpsBoard(INT8 nSensorID, cUart *pUartPort);
    virtual ~cGpsBoard(void);

    bool IsInternalGnss(void);
    bool IsExternalGnss(void);

    int   GetBoardType(void);
    void  SetBoardType(int nType);
    int   GetComSpdModeFound(void);
    void  SetComSpdModeFound(int nFound);
    bool  IsConnected(void);
    DWORD GetLastRcvTime(void);
    void  SetLastRcvTime(DWORD dTime);

    bool  CheckPortAvailable(void);
    int   GetPortDataByte(void);
    void  SendData(BYTE *pData, INT16 nLength);

    int   GetLoopBackPortID(void);
    virtual void  ProcRunComLoopBackResp(char *pstrCmd);

public:
    int  GetGpsStatus(void);
    void SetGpsStatus(int nStatus);
    bool IsDgnssCorrected(void);

    virtual bool IsEnabledEPFS(void);
    virtual void SetEnableEPFS(bool bEnable);
    virtual void UpdateTypeOfEPFS(char *pstrSentence);
    virtual void SetTypeOfEPFS(int nType);
    virtual int  GetTypeOfEPFS(void);

    bool IsUtcDateValid(void);
    bool IsUtcTimeValid(void);

    virtual void SetUtcDate(int nYear,int nMonth,int nDay);
    virtual void SetUtcDate(SYS_DATE xDate);
    virtual void SetUtcTime(SYS_TIME sTime);
    virtual void SetUtcTime(int nHour,int nMin,int nSec);
    int  GetUtcTimeStamp(void);
    void SetUtcDateNull(void);
    void SetUtcTimeNull(void);
    int  GetUtcYear(void);
    int  GetUtcMon(void);
    int  GetUtcDay(void);
    int  GetUtcHour(void);
    int  GetUtcMin(void);
    int  GetUtcSec(void);

    bool IsDatumValid(void);
    bool IsPosValid(void);
    bool IsPosUtcFixed(void);
    bool IsPosFixModeTrustable(int nPosMode);
    bool IsPosFixModeTrustable(void);
    int  GetPosModeIndicator(void);
    bool GetPosAccFlag(void);
    bool GetRaimFlag(void);

    void ResetHdgBuffer(int nCurHdg);
    void SaveHdgBuffer(int nHdg);
    int  GetRotDataFromHdgDiff(void);

    bool IsCrsValid(void);
    bool IsSpdValid(void);
    bool IsRotValid(void);
    bool IsHdgValid(void);

    REAL GetLatVal(void);
    void SetLatVal(REAL rVal);
    REAL GetLonVal(void);
    void SetLonVal(REAL rVal);
    int  GetSpdVal(void);
    void SetSpdVal(int nVal);
    int  GetVelVal(void);
    void SetVelVal(int nVal);
    int  GetCrsVal(void);
    void SetCrsVal(int nVal);
    int  GetHdgVal(void);
    void SetHdgVal(int nVal);
    int  GetRotVal(void);
    void SetRotVal(int nVal);
    bool IsRotOtherSrc(void);
    int  GetRotSrcType(void);

    virtual bool ProcessGBS(char *pstrCmd);
    virtual bool ProcessGGA(char *pstrCmd);
    virtual bool ProcessGLL(char *pstrCmd);
    virtual bool ProcessGNS(char *pstrCmd);
    virtual bool ProcessRMC(char *pstrCmd);
    virtual bool ProcessVTG(char *pstrCmd);
    virtual bool ProcessVBW(char *pstrCmd);
    virtual bool ProcessTHS(char *pstrCmd);
    virtual bool ProcessHDT(char *pstrCmd);
    virtual bool ProcessROT(char *pstrCmd);
    virtual bool ProcessDTM(char *pstrCmd);

    virtual int  ProcessData(cUart *pUartDbgP);
    virtual int  ProcessSentence(char *pstrCmd);

    virtual void UpdatePosByPriority(void);
    virtual void UpdateSogByPriority(void);
    virtual void UpdateCogByPriority(void);
    virtual void UpdateHdgByPriority(void);
    virtual void UpdateRotByPriority(void);
    virtual void UpdateUtcByPriority(void);

    virtual void RunPeriodicallyGps(void);

public:
    cUart   *m_pUartPort;
    int      m_nSensorID;

protected:
    int     m_nGpsStatus;
    bool    m_bEnableEPFS;
    int     m_nTypeOfEPFS;

    SYS_DATE m_xUtcDate;
    SYS_TIME m_xUtcTime;
    bool    m_bTimeStampValid;

    int     m_nRxSize;
    UCHAR  *m_pRxData;
    int     m_nBoardType;
    int     m_nComSpdFound;
    int     m_nDspMode;
    DWORD   m_dwLastRcvTick;
    DWORD   m_dwLastPeriodicCheckSec;

    int     m_nPosModeIndicator;
    int     m_nPosModeIndiTick;
    bool    m_bPosAccFlag;
    bool    m_bRAIMflag;

    int     m_nRotSrcType;
    bool    m_bRotValid;
    DWORD   m_dwRotValidTick;

    REAL    m_rLatVal;
    REAL    m_rLonVal;
    
    bool    m_bHdgValid;
    DWORD   m_dwHdgValidTick;
    HDGDATA *m_pHdgBuff;
    int     m_nHdgBuffHead;
    int     m_nNumHdgData;

    int     m_nSpdVal;           // * 10  (NM/hour)
    int     m_nVelVal;           // * 10  (KM/hour)
    int     m_nCrsVal;           // * 10  (True)
    int     m_nHdgVal;           // * 1   (True)
    int     m_nRotVal;           // * 10

protected:
    BACK16  m_nDGPSMode;
    BACK16  m_nDGPSFreq;
    BACK16  m_nSmoothFactor;
};

#endif

