/**
 * @file    Ver.cpp
 * @brief   Ver class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "AlarmThing.h"
#include "Ship.h"
#include "Ver.h"

/******************************************************************************
 * 
 * VER - Version
 *
 * $--VER,x,x,aa,c--c,c--c,c--c,c--c,c--c,c--c,x*hh<CR><LF>
 *        | | |   |    |    |    |    |    |   | 
 *        1 2 3   4    5    6    7    8    9   10
 *
 * 1. Total number of sentences needed, 1 to 9
 * 2. Sentence number, 1 to 9
 * 3. Device type
 * 4. Vendor ID
 * 5. Unique identifier
 * 6. Manufacturer serial number
 * 7. Model code (product code)
 * 8. Software revision
 * 9. Hardware revision
 * 10. Sequential message identifier
 *
 ******************************************************************************/
// Define static member variables
int8_t CVer::m_nSequentialId = 0;

 CVer::CVer() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CVer::Parse(const char *pszSentence)
{
    return true;
}

/**
 * @brief Make the VER sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CVer::MakeSentence(char *pszSentence)
{
    sprintf(pszSentence, "$AIVER,1,1,A,%s,%09d,,%s,%s,1.00,%d",
            STR_MANUFACTURER_CODE, 
            cShip::getOwnShipInst()->GetOwnShipMMSI(), 
            STR_MODEL_ID,
            STR_VERSION_SW, 
            GetSequentialId());
    CSentence::AddSentenceTail(pszSentence);

    IncSequentialId();

    return strlen(pszSentence);
}