{"files.associations": {"atomic": "cpp", "compare": "cpp", "cstddef": "cpp", "limits": "cpp", "memory": "cpp", "type_traits": "cpp", "xmemory": "cpp", "xutility": "cpp", "concepts": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "new": "cpp", "tuple": "cpp", "typeinfo": "cpp", "utility": "cpp", "xtr1common": "cpp", "iostream": "cpp", "algorithm": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "format": "cpp", "ios": "cpp", "istream": "cpp", "iterator": "cpp", "locale": "cpp", "optional": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "system_error": "cpp", "vector": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xstring": "cpp", "set": "cpp", "string": "cpp", "xtree": "cpp"}}