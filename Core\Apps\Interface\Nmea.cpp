#include "Nmea.h"

//----------------------------------
// Implementations
//----------------------------------
cNMEA::cNMEA(int nSensorID)
{

}

cNMEA::~cNMEA(void)
{
}

/**
 * @brief Process the GBS sentence
 */
bool cNMEA::ProcessGBS(char *pstrCmd)
{
    return m_hGbs.Parse(pstrCmd);
}

/**
 * @brief Process the GGA sentence
 */
bool cNMEA::ProcessGGA(char *pstrCmd)
{
    return m_hGga.Parse(pstrCmd);
}

/**
 * @brief Process the GLL sentence
 */
bool cNMEA::ProcessGLL(char *pstrCmd)
{
    return m_hGll.Parse(pstrCmd);
}

bool cNMEA::ProcessGNS(char *pstrCmd)
{
    return m_hGns.Parse(pstrCmd);
}

/**
 * @brief Process the RMC sentence
 */
bool cNMEA::ProcessRMC(char *pstrCmd)
{
    return m_hRmc.Parse(pstrCmd);
}

/**
 * @brief Process the VTG sentence
 */
bool cNMEA::ProcessVTG(char *pstrCmd, int nCrsVal)
{
    return m_hVtg.Parse(pstrCmd, nCrsVal);
}

/**
 * @brief Process the VBW sentence
 */
bool cNMEA::ProcessVBW(char *pstrCmd)
{
    return m_hVbw.Parse(pstrCmd);
}

/**
 * @brief Process the THS sentence
 */
bool cNMEA::ProcessTHS(char *pstrCmd)
{
    return m_hThs.Parse(pstrCmd);
}

/**
 * @brief Process the HDT sentence
 */
bool cNMEA::ProcessHDT(char *pstrCmd)
{
    return m_hHdt.Parse(pstrCmd);
}

/**
 * @brief Process the ROT sentence
 */
bool cNMEA::ProcessROT(char *pstrCmd)
{
    return m_hRot.Parse(pstrCmd);
}

/**
 * @brief Process the ZDA sentence
 */
bool cNMEA::ProcessZDA(char *pstrCmd)
{
    return m_hZda.Parse(pstrCmd);
}

/**
 * @brief Process the DTM sentence
 */
bool cNMEA::ProcessDTM(char *pstrCmd)
{
    return m_hDtm.Parse(pstrCmd);
}