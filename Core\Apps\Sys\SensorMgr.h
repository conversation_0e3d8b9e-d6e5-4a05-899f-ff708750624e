#ifndef __SENSORMGR_H__
#define __SENSORMGR_H__

#include "DataType.h"
#include "AllConst.h"
#include "Uart.h"
#include "GpsBoard.h"
#include "GnssInternal.h"
#include "Ublox.h"
#include "AisMsg.h"

class CSensorMgr
{
public:
    CSensorMgr();
    ~CSensorMgr();

    static std::shared_ptr<CSensorMgr> getInst() {
        static std::shared_ptr<CSensorMgr> pInst = std::make_shared<CSensorMgr>();
        return pInst;
    }

public:
    void    SetIntGnssTimePulseConfig(_tagPpsConfig nConfigPPS);
    void    SetInternalGnssConfig(BYTE bGnssConfig, BOOL bEnableSBAS);
    BOOL    IsExtDGNSSMode(void);
    BOOL    IsExtGNSSMode(void);
    BOOL    IsIntDGNSSMode(void);
    BOOL    IsIntGNSSMode(void);
    BOOL    IsExtSogCogMode(void);
    BOOL    IsIntSogCogMode(void);
    BOOL    IsIntGNSSSbasMode(void);
    BOOL    IsUtcTimeValid(int nSensorId);
    BOOL    IsPosUtcFixed(int nSensorId);
    BOOL    IsIntGNSSConnected(void);
    BOOL    ProcessDgnssDataFromMsg17(UINT nBaseStId, xAISMSG17* xMsg17);

    int     GetIntGNSSUtcSec(void);
    
    BOOL    GetSensorDate(cGpsBoard *pSensor, int *pYear,int *pMonth,int *pDay);
    BOOL    GetSensorDate(cGpsBoard *pSensor, SYS_DATE *pDate);
    BOOL    GetSensorTime(cGpsBoard *pSensor, int *pHour,int *pMin,int *pSec);
    BOOL    GetSensorTime(cGpsBoard *pSensor, SYS_TIME *pxTime);
public:
    BOOL    GetSensorDateTime(cGpsBoard *pSensor, SYS_DATE_TIME *pxDateTime);
    BOOL    GetSensorDateTime(int nSensorId, SYS_DATE_TIME *pxDateTime);

protected:
    void    UpdateUtcDateTime();
    void    UpdateSysDateTime();

public:
    inline  int GetSensorID(cGpsBoard *pSensor);

    void    SetAntType(BYTE bAntType);
	BYTE    GetAntType(void) { return m_nAntType; };
    void    UpdateOwnShipAntPos();
    BYTE    GetEpfDeviceType();

    int     GetHdgData(void);
    int     GetGpsSensorStatus(void);
    int     GetPosSensorIdx(void);

    void    SetIntGnssRecv(void);
    void    SetSensorUartBaudrate(int nAllPortSpeed);

    cGpsBoard* GetPosSensorPriority1(BOOL bCheckTrustable);
    cGpsBoard* GetPosSensorPriority2(BOOL bCheckTrustable);
    cGpsBoard* GetPosSensorPriority3(BOOL bCheckTrustable);
    cGpsBoard* GetPosSensorPriority4(BOOL bCheckTrustable);
    cGpsBoard* GetPosSensorPriority5(BOOL bCheckTrustable);
    cGpsBoard* GetPosSensorPriority6(BOOL bCheckTrustable);
    cGpsBoard* FindPosSensorByPriority(int nStartPriority, BOOL bCheckTrustable);

    BOOL    FindNewPosSensor(int nStartPriority, BOOL bCheckTrustable, cGpsBoard **ppResultSensor, int *pnResultPriority);
    void    UpdatePosSensor();
    BOOL    IsSogLost();
    BOOL    IsCogLost();

    cGpsBoard* GetValidExtSogSensor();
    cGpsBoard* GetValidExtCogSensor();

    void    UpdateSogSensor();
    void    UpdateCogSensor();
    void    SendOutSogCogTxtSentence(BOOL bUnconditionally);

    BOOL    IsRotLost();
    int     GetRotData();
    BOOL    IsRotSensorOtherSrc();
    cGpsBoard* FindRotSensor(int nRotSrcType);
    void    UpdateRotSensor();

    BOOL    IsHdgLost();
    void    UpdateHdgSensor();

    void    SetEnableEPFS(BOOL bEnable);
    void    CheckNavStatusIncorrect();
    void    CheckHeadingSensorOffset();
    void    UpdatePosData();
    void    UpdateOwnShipDynamicInfo();
    BOOL    SetPosSensor(cGpsBoard *pSensor, int nPosPriority);
    void    SendOutPosTxtSentence(BOOL bUnconditionally);
    BOOL    CheckExtDgnssInUse();
    BOOL    CheckExternalGnss();
    BOOL    IsGnssLost();
    BOOL    IsGnssFixed();
    float   GetDistBetweenIntAndExtGnssPos();
    void    CheckIntExtPosMismatch();
    void    SaveHdgAvgBuffer(int nHdg);
    float   GetHdgAvg();

    void    RunUartIsrHandler(int nSensorId);
    void    RunProcessData();
    void    RunPeriodicallySensorMgr();

public:
    enum _tagSensorConst
    {
        //----------------------------------------------------------------------------
        // refer to IEC-61993-2(ed2) Table 4 Position sensor fallback conditions
        //----------------------------------------------------------------------------
        POS_PRIORITY_1       = 1,            // External DGNSS in use (corrected)
        POS_PRIORITY_2       = 2,            // Internal DGNSS in use (corrected; Message 17)
        POS_PRIORITY_3       = 3,            // Internal DGNSS in use (corrected; beacon)
        POS_PRIORITY_4       = 4,            // External EPFS in use (uncorrected)
        POS_PRIORITY_5       = 5,            // Internal GNSS in use (uncorrected)
        POS_PRIORITY_6       = 6,            // Dead reckoning position (from the external EPFS in use)
        POS_PRIORITY_7       = 7,            // Manual position input (from the external EPFS in use)
        POS_PRIORITY_INVALID = 100,
        //----------------------------------------------------------------------------

        //----------------------------------------------------------------------------
        SOGCOG_PRIORITY_INVALID = 100,
        //----------------------------------------------------------------------------

        //----------------------------------------------------------------------------
        ROT_PRIORITY_INVALID= 100,
        ROT_PRIORITY_LAST   = NUM_NMEA_SENSORS,
        //----------------------------------------------------------------------------

        //----------------------------------------------------------------------------
        HDG_PRIORITY_INVALID= 100,
        //----------------------------------------------------------------------------

        HDG_BUFF_SIZE = 30,           // save for 30 sec
    };

public:
    cGpsBoard  *m_pPosSensor;
    cGpsBoard  *m_pSogSensor;
    cGpsBoard  *m_pCogSensor;
    cGpsBoard  *m_pRotSensor;
    cGpsBoard  *m_pHdgSensor;

    BYTE        m_nAntType;           // EPFD_ANTENNA_TYPE_NONE / EPFD_ANTENNA_TYPE_INT / EPFD_ANTENNA_TYPE_EXT

private:
    BOOL        m_bInitPosSensorOK;

    int         m_nPosPriority;
    int         m_nHdgPriority;

    int         m_nHdgBuffHead;
    int        *m_pHdgBuff;
    int         m_nNumHdgData;

    float       m_fOldHdgAvg;

    BOOL        m_bPosLostStart;

    cUart       *m_pSensorGnss;
    cUart       *m_pSensorExt1;
    cUart       *m_pSensorExt2;
    cUart       *m_pSensorExt3;

    CGnssInternal   *m_pGnssInt;
    cGpsBoard       *m_pSensor1;
    cGpsBoard       *m_pSensor2;
    cGpsBoard       *m_pSensor3;
    cGpsBoard       *m_pNmeaSensorList[NUM_NMEA_SENSORS];

public:
    DWORD       m_dwNavStatWrongStartSec;
    int         m_nHdgCogDiffCnt;
    int         m_nGpsMismatchCnt;
};

#endif//__SENSORMGR_H__
