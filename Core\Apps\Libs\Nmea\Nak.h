/**
 * @file    Nak.h
 * @brief   Nak header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __NAK_H__
#define __NAK_H__

//-----------------------------------------------------------------------------------------------------
// IEC-61993-2 NAK, reason code
//-----------------------------------------------------------------------------------------------------
// 0 = query functionality not supported;
// 1 = sentence formatter not supported;
// 2 = sentence formatter supported, but not enabled;
// 3 = sentence formatter supported and enabled, but temporarily unavailable (for instance, data field
//     problem, unit in initialize state, or in diagnostic state, etc.);
// 4 = sentence formatter supported, but query for this sentence formatter is not supported;
// 5 = access denied, for sentence formatter requested;
// 6 = sentence not accepted due to bad checksum;
// 7 = sentence not accepted due to listener processing issue;
// 8 to 9 = reserved for future use;
// 10 = cannot perform the requested operation;
// 11 = cannot fulfil request or command because of a problem with a data field in the sentence;
// 12 to 48 = reserved for future use;
// 49 = other reason as described in data field 5.
//-----------------------------------------------------------------------------------------------------
#define NAK_REASON_NO_REASON                  100
#define NAK_REASON_QUERY_NOT_SUPPORTED          0
#define NAK_REASON_SENTENCE_NOT_SUPPORTED       1
#define NAK_REASON_SENTENCE_NOT_ENABLEDED       2
#define NAK_REASON_SENTENCE_TEMP_UNAVAIL        3
#define NAK_REASON_QUERY_FORMAT_NOT_SUPPORTED   4
#define NAK_REASON_ACCESS_DENIED                5
#define NAK_REASON_SENTENCE_BAD_CHECKSUM        6
#define NAK_REASON_SENTENCE_BUSY                7
#define NAK_REASON_CANNOT_PERFORM              10
#define NAK_REASON_WRONG_DATAFIELD             11
#define NAK_REASON_OTHER_REASON                12

/******************************************************************************
 * 
 * NAK - Negative acknowledgement
 *
 * $--NAK,cc,ccc,c--c,x.x,c--c*hh<CR><LF>
 *        |   |   |   |   |
 *        1   2   3   4   5
 *
 * 1. Talker Identifier
 * 2. Affected sentence formatter
 * 3. Unique Identifier
 * 4. Reason code for negative acknowledgement
 * 5. Negative acknowledgement’s descriptive text
 *
 ******************************************************************************/
class CNak : public CSentence
{
public:
    CNak();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the NAK sentence
     * @param pszSentence The sentence to be made
     * @param pstrTalkerID The talker ID
     * @param pstrAffectSenctence The affected sentence formatter
     * @param nReasonCode The reason code for negative acknowledgement
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, const char *pstrTalkerID, const char *pstrAffectSenctence, int nReasonCode);
};

#endif /* __NAK_H__ */

