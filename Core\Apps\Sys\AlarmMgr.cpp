#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "AlarmThing.h"
#include "GpsBoard.h"
#include "PI.h"
#include "AlarmThing.h"
#include "MKD.h"
#include "GPIOExt.h"
#include "Timer.h"
#include "GnssInternal.h"
#include "Alc.h"
#include "Arc.h"
#include "AlarmMgr.h"
#include "SensorMgr.h"
#include "TestModeMgr.h"

//------------------------------------------------------------------------------------------------
// refer to IEC-61993-2 6.10.2.1
// The local alarm identifiers (alarm ID) given in Table 2 are defined for the use with formatters
// ALR, ACK, and as text identifiers in TXT sentences to link associated messages.
//------------------------------------------------------------------------------------------------
static const char* STR_ALR_MSG_TX_MALFUNC           = "AIS: Tx malfunction";
static const char* STR_ALR_MSG_VSWR_EXCEED          = "AIS: VHF Antenna VSWR exceeds limit";
static const char* STR_ALR_MSG_RX_A_ERROR           = "AIS: Rx channel 1 malfunction";
static const char* STR_ALR_MSG_RX_B_ERROR           = "AIS: Rx channel 2 malfunction";
static const char* STR_ALR_MSG_RX_DSC_ERROR         = "AIS: Rx channel 70 malfunction";
static const char* STR_ALR_MSG_GENERAL_FAIL         = "AIS: General failure";
static const char* STR_ALR_MSG_UTC_SYNC_INVALTXT    = "AIS: UTC sync invalid";
static const char* STR_ALR_MSG_MKD_LOST             = "AIS: MKD connection lost";
static const char* STR_ALR_MSG_GPS_POS_MISMATCH     = "AIS: Internal/external GNSS position mismatch";
static const char* STR_ALR_MSG_NAVSTATUS_INCORRECT  = "AIS: NavStatus incorrect";
static const char* STR_ALR_MSG_HDG_OFFSET           = "AIS: Heading sensor offset";
static const char* STR_ALR_MSG_ACTIVE_AISSART       = "AIS: Active locating device";
static const char* STR_ALR_MSG_EXT_EPFS_LOST        = "AIS: External EPFS lost";
static const char* STR_ALR_MSG_NO_SENSOR_POS        = "AIS: No position sensor in use";
static const char* STR_ALR_MSG_INVALTXT_SOG         = "AIS: No valid SOG information";
static const char* STR_ALR_MSG_INVALTXT_COG         = "AIS: No valid COG information";
static const char* STR_ALR_MSG_HEADING_LOST         = "AIS: Heading lost/invalid";
static const char* STR_ALR_MSG_INVALID_ROT          = "AIS: No valid ROT information";

static const char* BAM_ALR_MSG1_TX_MALFUNC          = "Transceiver fail";
static const char* BAM_ALR_MSG2_TX_MALFUNC          = "Not transmitting, check AIS";

static const char* BAM_ALR_MSG1_VSWR_EXCEED         = "Impaired radio";
static const char* BAM_ALR_MSG2_VSWR_EXCEED         = "Reduced coverage (antenna VSWR)";

static const char* BAM_ALR_MSG1_RX_A_ERROR          = "Impaired radio";
static const char* BAM_ALR_MSG2_RX_A_ERROR          = "Ch1 inoperative, check AIS";

static const char* BAM_ALR_MSG1_RX_B_ERROR          = "Impaired radio";
static const char* BAM_ALR_MSG2_RX_B_ERROR          = "Ch2 inoperative, check AIS";

static const char* BAM_ALR_MSG1_RX_DSC_ERROR        = "Impaired radio";
static const char* BAM_ALR_MSG2_RX_DSC_ERROR        = "DSC inoperative";

static const char* BAM_ALR_MSG1_GENERAL_FAIL        = "General fault";
static const char* BAM_ALR_MSG2_GENERAL_FAIL        = "Check AIS equipment";

static const char* BAM_ALR_MSG1_UTC_SYNC_INVALTXT   = "Sync in fallback";
static const char* BAM_ALR_MSG2_UTC_SYNC_INVALTXT   = "Check AIS for UTC time synchronisation";

static const char* BAM_ALR_MSG1_MKD_LOST            = "Lost MKD";
static const char* BAM_ALR_MSG2_MKD_LOST            = "Cannot display safety related messages";

static const char* BAM_ALR_MSG1_GPS_POS_MISMATCH    = "Doubtful GNSS";
static const char* BAM_ALR_MSG2_GPS_POS_MISMATCH    = "Int/Ext GNSS position mismatch";

static const char* BAM_ALR_MSG1_NAVSTATUS_INCORRECT = "Wrong NavStatus";
static const char* BAM_ALR_MSG2_NAVSTATUS_INCORRECT = "Check NavStatus setting";

static const char* BAM_ALR_MSG1_HDG_OFFSET          = "Doubtful heading";
static const char* BAM_ALR_MSG2_HDG_OFFSET          = "Difference with COG exceeds limit";

static const char* BAM_ALR_MSG1_ACTIVE_AISSART      = "Locating device";
static const char* BAM_ALR_MSG2_ACTIVE_AISSART      = "Check AIS targets";

static const char* BAM_ALR_MSG1_EXT_EPFS_LOST       = "Lost ext EPFS";
static const char* BAM_ALR_MSG2_EXT_EPFS_LOST       = "Check external position sensor";

static const char* BAM_ALR_MSG1_NO_SENSOR_POS       = "Lost position";
static const char* BAM_ALR_MSG2_NO_SENSOR_POS       = "Own ship position not transmitted";

static const char* BAM_ALR_MSG1_INVALTXT_SOG        = "Missing SOG";
static const char* BAM_ALR_MSG2_INVALTXT_SOG        = "Not transmitting SOG";

static const char* BAM_ALR_MSG1_INVALTXT_COG        = "Missing COG";
static const char* BAM_ALR_MSG2_INVALTXT_COG        = "Not transmitting COG";

static const char* BAM_ALR_MSG1_HEADING_LOST        = "Missing Heading";
static const char* BAM_ALR_MSG2_HEADING_LOST        = "Not transmitting Heading";

static const char* BAM_ALR_MSG1_INVALID_ROT         = "Missing ROT";
static const char* BAM_ALR_MSG2_INVALID_ROT         = "Not transmitting Rate of Turn";

CAlarmMgr::CAlarmMgr()
{
    gpAlarmTxMalFunc        = new CAlarm01(AIS_ALR_ID_TX_MALFUNC,           (char*)STR_ALR_MSG_TX_MALFUNC,          BAM_ALERT_ID_TX_MALFNC,             (char*)BAM_ALR_MSG1_TX_MALFUNC,         (char*)BAM_ALR_MSG2_TX_MALFUNC,         BAM_ALERT_PRIORITY_WARNING);
    gpAlarmVswr             = new CAlarm02(AIS_ALR_ID_VSWR_EXCEED,          (char*)STR_ALR_MSG_VSWR_EXCEED,         BAM_ALERT_ID_VSWR_EXCEED,           (char*)BAM_ALR_MSG1_VSWR_EXCEED,        (char*)BAM_ALR_MSG2_VSWR_EXCEED,        BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmRxMalfuncCh1     = new CAlarm03(AIS_ALR_ID_RX_A_ERROR,           (char*)STR_ALR_MSG_RX_A_ERROR,          BAM_ALERT_ID_RX_A_ERROR,            (char*)BAM_ALR_MSG1_RX_A_ERROR,         (char*)BAM_ALR_MSG2_RX_A_ERROR,         BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmRxMalfuncCh2     = new CAlarm04(AIS_ALR_ID_RX_B_ERROR,           (char*)STR_ALR_MSG_RX_B_ERROR,          BAM_ALERT_ID_RX_B_ERROR,            (char*)BAM_ALR_MSG1_RX_B_ERROR,         (char*)BAM_ALR_MSG2_RX_B_ERROR,         BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmMalfuncCh70      = new CAlarm05(AIS_ALR_ID_RX_DSC_ERROR,         (char*)STR_ALR_MSG_RX_DSC_ERROR,        BAM_ALERT_ID_RX_DSC_ERROR,          (char*)BAM_ALR_MSG1_RX_DSC_ERROR,       (char*)BAM_ALR_MSG2_RX_DSC_ERROR,       BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmGeneralFail      = new CAlarm06(AIS_ALR_ID_GENERAL_FAIL,         (char*)STR_ALR_MSG_GENERAL_FAIL,        BAM_ALERT_ID_GENERAL_FAIL,          (char*)BAM_ALR_MSG1_GENERAL_FAIL,       (char*)BAM_ALR_MSG2_GENERAL_FAIL,       BAM_ALERT_PRIORITY_WARNING);
    gpAlarmUtcSyncInvalid   = new CAlarm07(AIS_ALR_ID_UTC_SYNC_INVALID,     (char*)STR_ALR_MSG_UTC_SYNC_INVALTXT,   BAM_ALERT_ID_UTC_SYNC_INVALID,      (char*)BAM_ALR_MSG1_UTC_SYNC_INVALTXT,  (char*)BAM_ALR_MSG2_UTC_SYNC_INVALTXT,  BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmMkdConnLost      = new CAlarm08(AIS_ALR_ID_MKD_LOST,             (char*)STR_ALR_MSG_MKD_LOST,            BAM_ALERT_ID_MKD_LOST,              (char*)BAM_ALR_MSG1_MKD_LOST,           (char*)BAM_ALR_MSG2_MKD_LOST,           BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmPosMismatch      = new CAlarm09(AIS_ALR_ID_GPS_POS_MISMATCH,     (char*)STR_ALR_MSG_GPS_POS_MISMATCH,    BAM_ALERT_ID_GPS_POS_MISMATCH,      (char*)BAM_ALR_MSG1_GPS_POS_MISMATCH,   (char*)BAM_ALR_MSG2_GPS_POS_MISMATCH,   BAM_ALERT_PRIORITY_CAUTION, ALR_STAT_CHECK_ALR09_SEC);
    gpAlarmNavStatusWrong   = new CAlarm10(AIS_ALR_ID_NAVSTATUS_INCORRECT,  (char*)STR_ALR_MSG_NAVSTATUS_INCORRECT, BAM_ALERT_ID_NAVSTATUS_INCORRECT,   (char*)BAM_ALR_MSG1_NAVSTATUS_INCORRECT,(char*)BAM_ALR_MSG2_NAVSTATUS_INCORRECT,BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmHdgOffset        = new CAlarm11(AIS_ALR_ID_HDG_OFFSET,           (char*)STR_ALR_MSG_HDG_OFFSET,          BAM_ALERT_ID_HDG_OFFSET,            (char*)BAM_ALR_MSG1_HDG_OFFSET,         (char*)BAM_ALR_MSG2_HDG_OFFSET,         BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmActiveSART       = new CAlarm14(AIS_ALR_ID_ACTIVE_AISSART,       (char*)STR_ALR_MSG_ACTIVE_AISSART,      BAM_ALERT_ID_ACTIVE_AISSART,        (char*)BAM_ALR_MSG1_ACTIVE_AISSART,     (char*)BAM_ALR_MSG2_ACTIVE_AISSART,     BAM_ALERT_PRIORITY_WARNING);
    gpAlarmExtEpfsLost      = new CAlarm25(AIS_ALR_ID_EXT_EPFS_LOST,        (char*)STR_ALR_MSG_EXT_EPFS_LOST,       BAM_ALERT_ID_EXT_EPFS_LOST,         (char*)BAM_ALR_MSG1_EXT_EPFS_LOST,      (char*)BAM_ALR_MSG2_EXT_EPFS_LOST,      BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmNoPosSensor      = new CAlarm26(AIS_ALR_ID_NO_SENSOR_POS,        (char*)STR_ALR_MSG_NO_SENSOR_POS,       BAM_ALERT_ID_NO_SENSOR_POS,         (char*)BAM_ALR_MSG1_NO_SENSOR_POS,      (char*)BAM_ALR_MSG2_NO_SENSOR_POS,      BAM_ALERT_PRIORITY_WARNING);
    gpAlarmNoSOG            = new CAlarm29(AIS_ALR_ID_INVALID_SOG,          (char*)STR_ALR_MSG_INVALTXT_SOG,        BAM_ALERT_ID_INVALID_SOG,           (char*)BAM_ALR_MSG1_INVALTXT_SOG,       (char*)BAM_ALR_MSG2_INVALTXT_SOG,       BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmNoCOG            = new CAlarm30(AIS_ALR_ID_INVALID_COG,          (char*)STR_ALR_MSG_INVALTXT_COG,        BAM_ALERT_ID_INVALID_COG,           (char*)BAM_ALR_MSG1_INVALTXT_COG,       (char*)BAM_ALR_MSG2_INVALTXT_COG,       BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmHdgInvalid       = new CAlarm32(AIS_ALR_ID_HEADING_LOST,         (char*)STR_ALR_MSG_HEADING_LOST,        BAM_ALERT_ID_HEADING_LOST,          (char*)BAM_ALR_MSG1_HEADING_LOST,       (char*)BAM_ALR_MSG2_HEADING_LOST,       BAM_ALERT_PRIORITY_CAUTION);
    gpAlarmNoROT            = new CAlarm35(AIS_ALR_ID_INVALID_ROT,          (char*)STR_ALR_MSG_INVALID_ROT,         BAM_ALERT_ID_INVALID_ROT,           (char*)BAM_ALR_MSG1_INVALID_ROT,        (char*)BAM_ALR_MSG2_INVALID_ROT,        BAM_ALERT_PRIORITY_CAUTION);

    int i = 0;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmTxMalFunc;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmVswr;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmRxMalfuncCh1;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmRxMalfuncCh2;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmMalfuncCh70;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmGeneralFail;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmUtcSyncInvalid;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmMkdConnLost;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmPosMismatch;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmNavStatusWrong;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmHdgOffset;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmActiveSART;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmExtEpfsLost;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmNoPosSensor;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmNoSOG;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmNoCOG;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmHdgInvalid;
    m_pAlarmList[i++] = (CAlarmThing*)gpAlarmNoROT;

    m_bAlarmActive = TRUE;                // the relay output is "active" when the power is "off"
    SetAlarmRelayActive(FALSE);
}

CAlarmMgr::~CAlarmMgr()
{
}

void CAlarmMgr::SetAlarmRelayActive(BOOL bAlarmActive)
{
    //---------------------------------------------------------------------
    // Refer to IEC-61993-2 14.6.1.2
    // Verify that the relay output is "active" when the power is "off"
    //---------------------------------------------------------------------

    if(bAlarmActive != m_bAlarmActive)
    {
        DEBUG_LOG("SetALR] %s, s:%d\r\n",
                bAlarmActive ? (char*)"ON" : (char*)"OFF", cTimerSys::getInst()->GetCurTimerSec());

        m_bAlarmActive = bAlarmActive;
        SetGpioAlertRelayEn(m_bAlarmActive);
    }
}

void CAlarmMgr::CheckAlarmActive()
{
    BOOL bAlarmActive = FALSE;
    _tagBAMAlertStat  nOneAlrStat;

    for(int i = 0 ; i < NUM_ALARMS ; i++)
    {
        nOneAlrStat = m_pAlarmList[i]->UpdateAlarmStatus();

        bAlarmActive |= (  m_pAlarmList[i]->GetAlarmPriority() != BAM_ALERT_PRIORITY_CAUTION 
                        && nOneAlrStat == BAM_ALERT_STAT_ACTIVE_UNACK);
    }

    SetAlarmRelayActive(bAlarmActive);
}

void CAlarmMgr::SetAlarmAcked(int nAlarmID)
{
    //------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.2
    // The acknowledged state flag shall be set after acknowledgement of an alarm internally by
    // means of the MKD or externally by a corresponding ACK sentence.
    //------------------------------------------------------------------------------------------------

    DEBUG_LOG("AlarmMgr-SetAlarmAcked] id: %d, s:%d\r\n", nAlarmID, cTimerSys::getInst()->GetCurTimerSec());

    for(int i = 0 ; i < NUM_ALARMS ; i++)
    {
        if(m_pAlarmList[i]->m_nAlarmID == nAlarmID)
        {
            m_pAlarmList[i]->SetStatusAcked();
            break;
        }
    }
}

void CAlarmMgr::SetBAMAlertAcked(int nBAMAlertID, int nAlertInstance)
{
    DEBUG_LOG("AlarmMgr-ACN] id: %d, s:%d\r\n", nBAMAlertID, cTimerSys::getInst()->GetCurTimerSec());

    for(int i = 0 ; i < NUM_ALARMS ; i++)
    {
        if(m_pAlarmList[i]->m_nBAMAlertID == nBAMAlertID && m_pAlarmList[i]->m_nBAMAlertInstance == nAlertInstance)
        {
            m_pAlarmList[i]->SetStatusAcked();
            //break;        // BAM alert ID 는 중복되므로 끝까지 검사해야함!!!
        }
    }
}

void CAlarmMgr::CheckAlarmAckedByHW()
{
    //-----------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.2.3 Alarm relay output
    // A normally closed (NC) earth free relay contact shall be provided as an independent and simple method for triggering an external alarm.
    // The alarm relay shall be "active" in case of power "off".
    // The alarm relay shall be deactivated upon acknowledgement of an alarm either internally by
    // means of minimum display and keyboard or externally by a corresponding ACK sentence.
    //-----------------------------------------------------------------------------------------------------------------------------------------

    if(m_bAlarmActive && GetGpioAlertRelayAck())
    {
        for(int i = 0 ; i < NUM_ALARMS ; i++)
        {
            m_pAlarmList[i]->SetStatusAcked();
        }

        SetAlarmRelayActive(FALSE);
    }
}

void CAlarmMgr::CheckPeriodicallyAlarmAckByHW()
{
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > 1)
    {
        //-----------------------------------------------------------------------------------------------------------------------------------------
        // refer to IEC-61993-2 6.10.2.3 Alarm relay output
        // A normally closed (NC) earth free relay contact shall be provided as an independent and simple method for triggering an external alarm.
        // The alarm relay shall be "active" in case of power "off".
        // The alarm relay shall be deactivated upon acknowledgement of an alarm either internally by
        // means of minimum display and keyboard or externally by a corresponding ACK sentence.
        //-----------------------------------------------------------------------------------------------------------------------------------------

        CheckAlarmAckedByHW();

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}

void CAlarmMgr::SendOutAllAlarmToPI(BOOL bImmediately)
{
    for(int i = 0 ; i < NUM_ALARMS ; i++)
    {
        m_pAlarmList[i]->SendOutNmeaALR(m_pAlarmList[i], bImmediately);
        m_pAlarmList[i]->SendOutNmeaALF(m_pAlarmList[i], bImmediately);
    }
}

void CAlarmMgr::PrecessACNCommand(char cArcCommand, int nBAMAlertID, int nAlertInstance, int nAlertManufacturer, CPI *pPI)
{
    switch(cArcCommand)
    {
        case 'A':
        {
            if (nBAMAlertID == 0 && nAlertInstance == 0 && nAlertManufacturer == 0)
            {
                // Send ARC sentence to refuse ack command
                SendOutARCToPI(cArcCommand, pPI);
            }
            else
            {
                SetBAMAlertAcked(nBAMAlertID, nAlertInstance);
            }
            break;
        }
        case 'Q':
        {
            for(int i = 0 ; i < NUM_ALARMS ; i++)
            {
                if(CTestModeMgr::getInst()->IsTestModeRunning())
                {
                    // RES TestReport : Scenario BAM-8_1_4-Ed1.scn, Legend BAM-8_1_4-Ed1.leg.
                    // If the EUT in a non-operational mode, The EUT transmits, using ALF messages, 
                    // the actual state of all alerts, including those in state "normal".
                    if ((nBAMAlertID == 0 && nAlertInstance == 0 && nAlertManufacturer == 0)
                        || (m_pAlarmList[i]->m_nBAMAlertID == nBAMAlertID && m_pAlarmList[i]->m_nBAMAlertInstance == nAlertInstance))
                    {
                        if (pPI != NULL)
                            pPI->SendALFToPI(m_pAlarmList[i]);
                    }
                }
                else
                {
                    // RES TestReport : Scenario BAM-8_1_4-Ed1.scn, Legend BAM-8_1_4-Ed1.leg.
                    // excluding alerts in state "normal"
                    if ((nBAMAlertID == 0 && nAlertInstance == 0 && nAlertManufacturer == 0 && m_pAlarmList[i]->m_cBAMAlertState != BAM_ALERT_STAT_NORMAL)
                        || (m_pAlarmList[i]->m_nBAMAlertID == nBAMAlertID && m_pAlarmList[i]->m_nBAMAlertInstance == nAlertInstance))
                    {
                        if (pPI != NULL)
                            pPI->SendALFToPI(m_pAlarmList[i]);
                    }
                }
            }
            break;
        }
        case 'O':
        {
            if (nBAMAlertID == 0 && nAlertInstance == 0 && nAlertManufacturer == 0)
            {
                SendOutARCToPI(cArcCommand, pPI);
            }
            else
            {
                for(int i = 0 ; i < NUM_ALARMS ; i++)
                {
                    if (m_pAlarmList[i]->m_cBAMAlertPriority == BAM_ALERT_PRIORITY_WARNING)
                    {
                        if (   (m_pAlarmList[i]->m_nBAMAlertID == nBAMAlertID) 
                            && (m_pAlarmList[i]->m_nBAMAlertInstance == nAlertInstance))
                        {
                            m_pAlarmList[i]->SetAlarmStatus(BAM_ALERT_STAT_RESPON_TRANS);
                        }
                    }
                }
            }
            break;
        }
        case 'S':
        {
            for(int i = 0 ; i < NUM_ALARMS ; i++)
            {
                if ((nBAMAlertID == 0 && nAlertInstance == 0 && nAlertManufacturer == 0)
                    || (m_pAlarmList[i]->m_nBAMAlertID == nBAMAlertID && m_pAlarmList[i]->m_nBAMAlertInstance == nAlertInstance))
                {
                    // 230523 Warning Alert이고, Active unack 상태인 경우에만 Silence 동작하도록 수정
                    if (   m_pAlarmList[i]->m_cBAMAlertPriority == BAM_ALERT_PRIORITY_WARNING 
                        && m_pAlarmList[i]->GetAlarmStatus() == BAM_ALERT_STAT_ACTIVE_UNACK )
                    {
                        m_pAlarmList[i]->SetAlarmStatus(BAM_ALERT_STAT_ACTIVE_SILEN);
                    }
                }
            }
            break;
        }
        default:
            break;
    }
}

void CAlarmMgr::SendOutARCToPI(char cArcCommand, CPI *pPI)
{
    char pstrOutMsg[RX_MAX_DATA_SIZE];

    for(int i = 0 ; i < NUM_ALARMS ; i++)
    {
        if (m_pAlarmList[i]->m_cBAMAlertPriority == BAM_ALERT_PRIORITY_WARNING
            && (m_pAlarmList[i]->m_cBAMAlertState == BAM_ALERT_STAT_ACTIVE_UNACK || m_pAlarmList[i]->m_cBAMAlertState == BAM_ALERT_STAT_ACTIVE_SILEN))
        {
            memset(pstrOutMsg, 0x00, sizeof(pstrOutMsg));
            int nLen = CArc::MakeSentence(pstrOutMsg, m_pAlarmList[i], cArcCommand, pstrOutMsg);
            if (nLen > 0)
                pPI->SendOutData(pstrOutMsg, nLen);
        }
    }
}

void CAlarmMgr::CheckPeriodicallyAlarmStatus()
{
    static DWORD dwCheckSecAlrStat = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSecAlrStat) >= ALR_STAT_CHECK_SEC)
    {
        //----------------------------------
        // 1초 간격으로 알람 발생 여부 체크
        //----------------------------------
        CheckAlarmActive();

        dwCheckSecAlrStat = cTimerSys::getInst()->GetCurTimerSec();
    }
}

void CAlarmMgr::CheckPeriodicallyAlarmSentence()
{
    static DWORD dwCheckSecAlrSendOut = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSecAlrSendOut) >= ALR_OUT_CHECK_SEC)
    {
        //---------------------------------
        // 30초 간격으로 ALR sentence 출력
        //---------------------------------

        //---------------------------------------------------------------------------------------------------------------------
        // refer to IEC-61993-2 6.10.1
        // when alarm is initiated :
        // - the alarm shall be displayed on the MKD,
        // - the alarm relay shall be set "active"
        // an appropriate alarm message shall be output via the presentation interface upon    occurrence and repeated every 30 s.
        //---------------------------------------------------------------------------------------------------------------------

        //----------------------------------------------------------------------------------------------------------------------
        // refer to IEC-61993-2 6.10.2.1
        // The "alarm condition" field shall be set to "A" when the alarm condition threshold is exceeded,
        // and "V" when the alarm condition returns to a level that does not exceed the threshold. During
        // healthy conditions (no alarm condition) an empty ALR sentence shall be sent at one-minute intervals.
        // The local alarm identifiers (alarm ID) given in Table 2 are defined for the use with formatters
        // ALR, ACK, and as text identifiers in TXT sentences to link associated messages.
        // ALR-sentences with "alarm numbers" greater than 099 cannot be followed by TXT-sentences containing additional
        // information by using the TXT-sentence's "text identifier". The "text identifier" is limited to the range of 01 to 99.
        // Additional numbers may be used by the manufacturer for other purposes but shall be in the range 051 to 099.
        //----------------------------------------------------------------------------------------------------------------------

        SendOutAllAlarmToPI();
        dwCheckSecAlrSendOut = cTimerSys::getInst()->GetCurTimerSec();
    }

    //----------------------------------------------------------------------------------------------------------------------
    // refer IEC-61162-1 8.3.12
    // The ALC sentence provides condensed ALF sentence information. It contains the identifying
    // data for each present alert of one certain source/device so that the receiver can understand
    // which ALF has been missed (and retransmission of ALF can be requested by using ACN
    // sentence). It shall be published cyclically at least every 30 s by each alert generating device.
    //----------------------------------------------------------------------------------------------------------------------
    static DWORD dwCheckSecBAMAlertOut = 0;
    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSecBAMAlertOut) >= ALC_OUT_CHECK_SEC)
    {
        CAlc::MakeSentence(m_pAlarmList, NUM_ALARMS);
        dwCheckSecBAMAlertOut = cTimerSys::getInst()->GetCurTimerSec();
    }
}

void CAlarmMgr::RunPeriodicallyAlarmMgr()
{
    if(!CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)    // GPS fix 기다리기위해 초기 시간 대기 (Evnet log 발생 시간 기록을 위해)
        return;

    CheckPeriodicallyAlarmAckByHW();
    CheckPeriodicallyAlarmStatus();
    CheckPeriodicallyAlarmSentence();
}
