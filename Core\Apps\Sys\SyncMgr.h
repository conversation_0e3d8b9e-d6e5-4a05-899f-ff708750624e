#ifndef __SYNCMGR_H__
#define __SYNCMGR_H__

#include "DataType.h"
#include "AllConst.h"
#include "VdlRxMgr.h"

// Defines
#define AIS_SYNC_MODE_UTC_DIRECT              (0)       // 0
#define AIS_SYNC_MODE_UTC_INDIRECT            (1)       // 1
#define AIS_SYNC_MODE_BASE_DIRECT             (2)       // 2 synchronized to base station semaphore
#define AIS_SYNC_MODE_BASE_INDIRECT           (3)       // 3 synchronized to the station which is sync to a base station semaphore
#define AIS_SYNC_MODE_MOBILE_SEMAPHORE        (4)       // 3 synchronized to mobile station semaphore
#define AIS_SYNC_MODE_NONE                  (255)       // x

#define AIS_SYNC_MODE_FIELD_MAX                 3       // AIS_SYNC_MODE_BASE_INDIRECT/AIS_SYNC_MODE_MOBILE_SEMAPHORE/AIS_SYNC_MODE_NONE 일 경우 송수신 슬롯내 field data 는 모두 3의 값을 갖는다.
#define UTCINDIRECT_SYNC_HOLD_SEC              30

class CVdlRxMgr;

class CSyncMgr
{
public:
    CSyncMgr();
    ~CSyncMgr();

    static std::shared_ptr<CSyncMgr> getInst() {
        static std::shared_ptr<CSyncMgr> pInst = std::make_shared<CSyncMgr>();
        return pInst;
    }

public:
    void    Initialize(void);
    bool    IsSyncRunning(void);
    bool    IsUtcDirectRunning(void);
    bool    IsUtcDirectSyncRunning(void);
    bool    IsUtcIndirectSyncRunning(void);
    bool    IsSyncToUTC(UINT16 uSyncSource);
    bool    IsSyncToUTC(void);

    void    SetUtcDateFromVdlMsg(int nSenderMMSI, int nYear, int nMonth, int nDay);
    void    SetUtcTimeFromVdlMsg(int nSenderMMSI, int nHour, int nMin);

    void    SetSyncSource(UINT16 uSyncSource, DWORD uSyncSrcMMSI);
    void    UpdateSyncSource(UINT16 uSyncSource, DWORD uSyncSrcMMSI);
    bool    GetSyncSrcDistDelay(int *pnSyncDistDelayCnter);

    void    SetOwnShipSemaphoreMode(bool bSet);
    bool    IsOwnShipSemaphoreMode(void);
    bool    IsOwnShipSemaQualified(void);

    bool    CheckOwnShipSemaphoreStart(void);
    void    CheckOwnShipSemaphoreExpired(void);

    bool    IsSyncModeToOtherSt(void);
    void    SetSyncModeToOtherSt(bool bSet);
    bool    GetOtherSyncStUtcDate(SYS_DATE *psDate);
    bool    GetOtherSyncStUtcTime(SYS_TIME *psTime);
    bool    ProcessSyncToOtherSt(CVdlRxMgr *pRxMgr, DWORD dwSyncState, WORD wRcvSlotID);
    
    void    UpdateUtcSyncDateTime(void);
    void    CheckSyncSources(void);

    void    RunPeriodically(void);

public:
    UINT16      m_uSyncSource;
    UINT        m_uSyncSrcMMSI;
    DWORD       m_dwSyncStartSec;
    DWORD       m_dwSyncToOtherStSec;

    bool        m_bOwnShipSemaphoreMode;
    DWORD       m_dwOwnShipSemaLostStartSec;
    bool        m_bOwnShipSemaOldLostStat;

    bool        m_bSyncModeToOtherSt;

    SYS_TIME    m_utcTimeFromVdlMsg;
    DWORD       m_dwUtcTimeRcvSecFromVdlMsg;

    SYS_DATE    m_utcDateFromVdlMsg;
    DWORD       m_dwUtcDateRcvSecFromVdlMsg;

    SYS_DATE    m_utcSyncDate;
    SYS_TIME    m_utcSyncTime;
};

#endif//__SYNCMGR_H__
