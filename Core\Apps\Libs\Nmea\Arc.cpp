/**
 * @file    Arc.cpp
 * @brief   Arc class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "AlarmThing.h"
#include "Ship.h"
#include "Arc.h"

/******************************************************************************
 * 
 * ARC - Alert command refused
 *
 * $--ARC,hhmmss.ss,aaa,x.x,x.x,c*hh<CR><LF>
 *          |        |   |   |  |
 *          1        2   3   4  5
 *
 * 1. Time
 * 2. Manufacturer mnemonic code
 * 3. Alert identifier
 * 4. Alert instance, 1 to 999999
 * 5. Refused alert command, A, Q, O or S
 *
 ******************************************************************************/
 CArc::CArc() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CArc::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the ARC sentence
 * @param pszSentence The sentence to be made
 * @param pAlarmThing The alarm thing structure
 * @param cArcCommand The ARC command
 * @param pstrBuffARC The output buffer
 * @return The length of the sentence
 */
int32_t CArc::MakeSentence(char *pszSentence, CAlarmThing *pAlarmThing, char cArcCommand, char *pstrBuffARC)
{
    char pstrTmp[RX_MAX_DATA_SIZE];
    SYS_DATE_TIME *pUtcTime = &(cShip::getOwnShipInst()->xUtcTime);

    if(pUtcTime->nValid)
        sprintf(pszSentence, "$AIARC,%02d%02d%02d,,", pUtcTime->xTime.nHour, pUtcTime->xTime.nMin, pUtcTime->xTime.nSec);
    else
        strcpy(pszSentence, "$AIARC,,,");

    sprintf(pstrTmp, "%d,%d,%c", pAlarmThing->GetBAMAlertID(), pAlarmThing->GetBAMAlertInstance(), cArcCommand);
    strcat(pszSentence, pstrTmp);
    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}