/**
 * @file    Lrf.cpp
 * @brief   Lrf class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "RosMgr.h"
#include "Lrf.h"

/******************************************************************************
*
* LRF - Long Range Function
*
* $--LRF,x,xxxxxxxxx,c--c,c--c,c--c*hh<CR><LF>
*        | |         |    |    |
*        1 2         3    4    5
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of requester
* 3.  Name of requester, 1 to 20 character string
* 4.  Function request , 1 to 26 characters
* 5.  Function reply status
*
******************************************************************************/
// Define static member variables
WORD    CLrf::m_wSeqNum = 0;
DWORD   CLrf::m_dwReqMMSI = 0;
char    CLrf::m_pstrReqName[30] = {0};
char    CLrf::m_pstrFuncReq[30] = {0};
char    CLrf::m_pstrReplyStat[30] = {0};

CLrf::CLrf() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CLrf::Parse(const char *pszSentence)
{
    memset(m_pstrReqName, 0x00, sizeof(m_pstrReqName));
    memset(m_pstrFuncReq, 0x00, sizeof(m_pstrFuncReq));
    memset(m_pstrReplyStat, 0x00, sizeof(m_pstrReplyStat));

    m_wSeqNum = CSentence::GetFieldInteger(pszSentence, 1);
    m_dwReqMMSI = CSentence::GetFieldInteger(pszSentence, 2);

    CSentence::GetFieldString(pszSentence, 3, m_pstrReqName, sizeof(m_pstrReqName));
    CSentence::GetFieldString(pszSentence, 4, m_pstrFuncReq, sizeof(m_pstrFuncReq));
    CSentence::GetFieldString(pszSentence, 5, m_pstrReplyStat, sizeof(m_pstrReplyStat));

    if(strlen(m_pstrReplyStat) <= 0)
    {
        return false;
    }

    return true;
}

/**
 * @brief Make the LRF sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CLrf::MakeSentence(char *pszSentence, 
                            WORD wSequenceNum, 
                            int nResquestMMSI, 
                            char *requestor_name, 
                            char *pFinalFuncRequest, 
                            char *pFinalReplyStatus)
{
    sprintf(pszSentence, "$AILRF,%d,%09d,%s,%s,%s",
            wSequenceNum, nResquestMMSI, requestor_name, pFinalFuncRequest, pFinalReplyStatus);    // 최종 출력시 pstrFuncRequest
    CSentence::AddSentenceTail(pszSentence);

    return strlen(pszSentence);
}




