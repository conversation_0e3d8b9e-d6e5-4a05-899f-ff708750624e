#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Uart.h"
#include "Nmea.h"
#include "AisLib.h"
#include "SetupMgr.h"
#include "RosMgr.h"
#include "PI.h"
#include "Timer.h"
#include "GpsLib.h"
#include "MKD.h"
#include "SensorMgr.h"
#include "Ship.h"
#include "Lri.h"
#include "Lrf.h"
#include "LongRange.h"

CLongRange::CLongRange(void)
{
    m_pUartPort = new cUartSYS(UARTID_6, USART6, USART6_IRQn, 1024, 2048, 38400);
    m_chPortID  = MON_PORTID_CH_LR;

    m_nLongRangeTxCtrl = LR_TXCTRL_CODE_NA;

    m_nRxSize = 0;
    m_pRxData = (UCHAR*)SysAllocMemory(sizeof(UCHAR) * RX_MAX_DATA_SIZE);

    m_nBaudIdx = BAUD_IDX_38400;
    m_bEnableSendRcvDataToMKD= FALSE;
    m_nTxSequenceNum = -1;
}

CLongRange::~CLongRange()
{
}

/**
 * @brief Set the UART baud rate for long range
 * @param nBaudIdx The baud rate index
 * @return None
 */
void CLongRange::SetUartBaudIdx(int nBaudIdx)
{
    //---------------------------------------------
    // nBaudIdx : BAUD_IDX_38400 or BAUD_IDX_4800
    //---------------------------------------------
    if(nBaudIdx != BAUD_IDX_4800 && nBaudIdx != BAUD_IDX_38400)// && nBaudIdx != BAUD_IDX_AUTO)
        nBaudIdx = BAUD_IDX_38400;

    int nBaudrate = (nBaudIdx == BAUD_IDX_4800) ? 4800 : 38400;

    DEBUG_LOG("SetUartPort] LR, Baud, idx: %d -> %d, %d -> %d\r\n",
            m_nBaudIdx, nBaudIdx, m_pUartPort->GetSpeed(), nBaudrate);

    if(nBaudIdx != m_nBaudIdx)
    {
        m_nBaudIdx = nBaudIdx;
        m_pUartPort->SetUartPara(nBaudrate);

        DEBUG_LOG("SetUartPort] LR, Set-Baud ok, idx: %d -> %d, %d -> %d\r\n",
            m_nBaudIdx, nBaudIdx, m_pUartPort->GetSpeed(), nBaudrate);
    }
}

/**
 * @brief Reload the long range configuration
 * @return None
 */
void CLongRange::ReloadSetup(void)
{
    SetLongRangeConfig(CSetupMgr::getInst()->GetLongRangeAutoReply(), CSetupMgr::getInst()->GetLongRangeCfg());
}

/**
 * @brief Send the string to long range port
 * @param pstrMsg The string to send
 * @return None
 */
void CLongRange::SendOutStr(char *pstrMsg)
{
    m_pUartPort->WriteComStr(pstrMsg);  // send to LR port
}

/**
 * @brief Send the string to high speed port
 * @param pstrMsg The string to send
 * @param bSendMKD Whether to send the data to MKD
 * @return None
 */
void CLongRange::SendOutHighSpdPort(char *pstrMsg, BOOL bSendMKD)
{
    //-----------------------------------------------------------------------------------------
    // RS Test Report
    // The interrogation LRF sentences are sent simultaneously on both highspeed output ports.
    //-----------------------------------------------------------------------------------------
    CMKD::getInst()->SendAllHighSpdPortData((BYTE*)pstrMsg, strlen(pstrMsg), bSendMKD);
}

/**
 * @brief Get the function reply status
 * @param wLrFunc The function code
 * @param bAvail Whether the data is available
 * @return The function reply status
 */
BYTE CLongRange::GetLRFunctionReplyStatus(WORD wLrFunc, BYTE bAvail)
{
    BYTE bstatus;

    if(wLrFunc)
    {
        if(!bAvail)
            bstatus = LR_FRS_NP;
        else
            bstatus = LR_FRS_AP;
    }
    else
    {
        if(!bAvail)
            bstatus = LR_FRS_NP;
        else
            bstatus = LR_FRS_NP;
    }
    return bstatus;
}

BYTE CLongRange::CheckReplyEnable(BYTE cFuncRequest)
{
    //------------------------------------------------------------------------
    // * - 사용자가 지정한 항목인지 확인하는 과정
    // * - 대문자만 사용됨. 소문자시에는 ignore.
    //------------------------------------------------------------------------
    BOOL bstatus = FALSE;
    BOOL bAvail = TRUE;

    switch (cFuncRequest)
    {
    case 'A':
        if(cShip::getOwnShipInst()->GetOwnShipIMO() == AIS_IMOID_VALID_MIN
        	|| strlen(cShip::getOwnShipInst()->xStaticData.vShipName) == 0
			|| strlen(cShip::getOwnShipInst()->xStaticData.vCallSign) == 0)
        {
            bAvail = FALSE;
        }
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncA, bAvail);
        break;

    case 'B':
        if(CAisLib::IsDefaultTime(&(cShip::getOwnShipInst()->xSysTime)))
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncB, bAvail);
        break;

    case 'C':
        if(!CAisLib::IsValidAisGridPOS(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG))
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncC, bAvail);
        break;

    case 'E':
        if(cShip::getOwnShipInst()->xDynamicData.nCOG == DEFAULT_COG)
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncE, bAvail);
        break;

    case 'F':
        if(cShip::getOwnShipInst()->xDynamicData.nSOG == DEFAULT_SOG)
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncF, bAvail);
        break;

    case 'I':
        if(strlen(cShip::getOwnShipInst()->xNavData.pstrDestination) != 0
        	&& !CAisLib::IsValidETATime(&(cShip::getOwnShipInst()->xNavData.xETA)))
        {
            bAvail = FALSE;
        }
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncI, bAvail);
        break;

    case 'O':
        if(cShip::getOwnShipInst()->xNavData.uDraught == 0)
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncO, bAvail);
        break;

    case 'P':
        if(cShip::getOwnShipInst()->xNavData.uShipType == 0)
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncP, bAvail);
        break;

    case 'U':
        switch(CSensorMgr::getInst()->GetAntType())
        {
        case EPFD_ANTENNA_TYPE_INT :
            if(!CAisLib::IsValidDimension(CSetupMgr::getInst()->GetIntAntennaPos()))
                bAvail = FALSE;
            break;

        default:
            if(!CAisLib::IsValidDimension(CSetupMgr::getInst()->GetExtAntennaPos()))
                bAvail = FALSE;
            break;
        }
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncU, bAvail);
        break;

    case 'W':
        if(cShip::getOwnShipInst()->xNavData.uPerson == 0)
            bAvail = FALSE;
        bstatus = GetLRFunctionReplyStatus(m_sConfigLR.uLRFuncW, bAvail);
        break;

    default:
        bstatus = LR_FRS_NA;
        break;
    }

    if(bstatus != LR_FRS_AP)
    {
        INFO_LOG("LR] CheckReplayEnable, disabled, func: %c\r\n", cFuncRequest);
    }

    return bstatus;
}

void CLongRange::SetLongRangeConfig(BOOL bEnableAutoReply, DWORD dwLongRangeCfg)
{
    m_sConfigLR.uLRFuncA = (dwLongRangeCfg & LR_FR_A) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncB = (dwLongRangeCfg & LR_FR_B) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncC = (dwLongRangeCfg & LR_FR_C) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncE = (dwLongRangeCfg & LR_FR_E) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncF = (dwLongRangeCfg & LR_FR_F) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncI = (dwLongRangeCfg & LR_FR_I) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncO = (dwLongRangeCfg & LR_FR_O) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncP = (dwLongRangeCfg & LR_FR_P) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncU = (dwLongRangeCfg & LR_FR_U) ? TRUE : FALSE;
    m_sConfigLR.uLRFuncW = (dwLongRangeCfg & LR_FR_W) ? TRUE : FALSE;

    DEBUG_LOG("[LR-NMEA] SetConfig] mode:%d, 0x%x, a:%d,b:%d,c:%d,e:%d,f:%d,i:%d,o:%d,p:%d,u:%d,w:%d\r\n",
        CSetupMgr::getInst()->GetLongRangeAutoReply(), dwLongRangeCfg,
        m_sConfigLR.uLRFuncA, m_sConfigLR.uLRFuncB, m_sConfigLR.uLRFuncC, m_sConfigLR.uLRFuncE, m_sConfigLR.uLRFuncF,
        m_sConfigLR.uLRFuncI, m_sConfigLR.uLRFuncO, m_sConfigLR.uLRFuncP, m_sConfigLR.uLRFuncU, m_sConfigLR.uLRFuncW);
}

BOOL CLongRange::CheckSentenceLRI(char *ppstrFields[], int nCount)
{
    /* first field is sequence number */
    if(!CAisLib::CheckFieldSeqNum(ppstrFields[1]))
        return FALSE;

    /* second field is control flag */
    if(!CAisLib::CheckFieldStatus(ppstrFields[2], '0', '1'))
        return FALSE;
    return TRUE;
}

BOOL CLongRange::CheckSentenceLRF(char *ppstrFields[], int nCount)
{
    /* first field is sequence number */
    if(!CAisLib::CheckFieldSeqNum(ppstrFields[1]))
        return FALSE;
    return TRUE;
}

void CLongRange::GetLR1sentence(char *pstrBuff, WORD wSequenceNum, int nResquestMMSI, BOOL bA)
{
    //------------------------------------------------------------------------
    //* - LR1 Sentence를 리턴한다.
    //------------------------------------------------------------------------
    char pstrRespName[LEN_MAX_SHIP_NAME*3 + 1];
    char pstrRespCallSign[LEN_MAX_CALLSIGN*3 + 1];
    char pstrRespIMO[16];

    memset((BYTE *)pstrRespName, 0x00, sizeof(pstrRespName));
    memset((BYTE *)pstrRespCallSign, 0x00, sizeof(pstrRespCallSign));
    memset((BYTE *)pstrRespIMO,    0x00, sizeof(pstrRespIMO));

    DEBUG_LOG("[LR-NMEA] Resp, LR1, seq: %d, resq: %09d, config: 0x%08x, bA: %d\r\n",
         wSequenceNum, nResquestMMSI, CSetupMgr::getInst()->GetLongRangeCfg(), bA);

    if(bA)
    {
        if(strlen(cShip::getOwnShipInst()->xStaticData.vShipName) > 0)
        	CAisLib::ToIEC61162Chars(cShip::getOwnShipInst()->xStaticData.vShipName, pstrRespName, sizeof(pstrRespName));

        if(strlen(cShip::getOwnShipInst()->xStaticData.vCallSign) > 0)
        	CAisLib::ToIEC61162Chars(cShip::getOwnShipInst()->xStaticData.vCallSign, pstrRespCallSign, sizeof(pstrRespCallSign));

        if(cShip::getOwnShipInst()->GetOwnShipIMO() != AIS_IMOID_VALID_MIN)
            sprintf(pstrRespIMO, "%09d", cShip::getOwnShipInst()->GetOwnShipIMO());
    }

    sprintf(pstrBuff, "$AILR1,%d,%09d,%09d,%s,%s,%s",
            wSequenceNum, cShip::getOwnShipInst()->GetOwnShipMMSI(), nResquestMMSI, pstrRespName, pstrRespCallSign, pstrRespIMO);
    CSentence::AddSentenceTail(pstrBuff);
}

void CLongRange::GetLR2sentence(char *pstrBuff, WORD wSequenceNum, BOOL bB, BOOL bC, BOOL bE, BOOL bF)
{
    //------------------------------------------------------------------------
    //* - LR2 Sentence를 리턴한다.
    //------------------------------------------------------------------------

    char    pstrDate[8];
    char    pstrTime[8];
    char    pstrLat[100];
    char    pstrLon[100];
    char    pstrCOG[16];
    char    pstrSOG[16];
    char    pstrLatNS[2];
    char    pstrLonEW[2];
    char    pstrCOGT[2];
    char    pstrSOGN[2];
    UINT    uPosDegree = 0;
    UINT    uPosMin = 0;
    UINT    uPosMinDigits = 0;

    memset(pstrDate, 0x00, sizeof(pstrDate));
    memset(pstrTime, 0x00, sizeof(pstrTime));
    memset(pstrLat,  0x00, sizeof(pstrLat));
    memset(pstrLon,  0x00, sizeof(pstrLon));
    memset(pstrCOG,  0x00, sizeof(pstrCOG));
    memset(pstrSOG,  0x00, sizeof(pstrSOG));

    memset(pstrLatNS, 0x00, sizeof(pstrLatNS));
    memset(pstrLonEW, 0x00, sizeof(pstrLonEW));
    memset(pstrCOGT,  0x00, sizeof(pstrCOGT));
    memset(pstrSOGN,  0x00, sizeof(pstrSOGN));

    DEBUG_LOG("[LR-NMEA] Resp, LR2 seq: %d, config: 0x%08x, bB:%d, bC:%d, bE:%d, bF:%d, PosFix:%d\r\n",
            wSequenceNum, CSetupMgr::getInst()->GetLongRangeCfg(), bB, bC, bE, bF, CSensorMgr::getInst()->IsGnssFixed());

    if(bB && CSensorMgr::getInst()->IsGnssFixed())
    {
        if(cShip::getOwnShipInst()->xUtcTime.xDate.nDay != DTTM_DAY_NULL &&
            cShip::getOwnShipInst()->xUtcTime.xDate.nMon != DTTM_MONTH_NULL &&
            cShip::getOwnShipInst()->xUtcTime.xDate.nYear != DTTM_YEAR_NULL)
        {
            sprintf(pstrDate, "%02d%02d%04d",
                    cShip::getOwnShipInst()->xUtcTime.xDate.nDay, cShip::getOwnShipInst()->xUtcTime.xDate.nMon, cShip::getOwnShipInst()->xUtcTime.xDate.nYear);
        }

        if(cShip::getOwnShipInst()->xUtcTime.xTime.nHour != DTTM_HOUR_NULL &&
            cShip::getOwnShipInst()->xUtcTime.xTime.nMin  != DTTM_MIN_NULL &&
            cShip::getOwnShipInst()->xUtcTime.xTime.nSec  != DTTM_SEC_NULL)
        {
            sprintf(pstrTime, "%02d%02d%02d",
                    cShip::getOwnShipInst()->xUtcTime.xTime.nHour, cShip::getOwnShipInst()->xUtcTime.xTime.nMin, cShip::getOwnShipInst()->xUtcTime.xTime.nSec);
        }
    }

    if(bC)
    {
        if(CSensorMgr::getInst()->IsGnssFixed() && CAisLib::IsValidAisGridPOS(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG))
        {
            ///*
        	CAisLib::ConvertPosDataToDMM(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLAT, &uPosDegree, &uPosMin, &uPosMinDigits, MIN_10000);
            sprintf(pstrLat, "%02d%02d.%01d", uPosDegree, uPosMin, uPosMinDigits);

            if(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLAT > 0)
                pstrLatNS[0] = 'N';
            else
                pstrLatNS[0] = 'S';

            CAisLib::ConvertPosDataToDMM(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLON, &uPosDegree, &uPosMin, &uPosMinDigits, MIN_10000);
            sprintf(pstrLon, "%03d%02d.%01d", uPosDegree, uPosMin, uPosMinDigits);

            if(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLON > 0)
                pstrLonEW[0]='E';
            else
                pstrLonEW[0]='W';
        }
    }

    if(bE && cShip::getOwnShipInst()->xDynamicData.nCOG != DEFAULT_COG)
    {
    	CAisLib::FTOA((cShip::getOwnShipInst()->xDynamicData.nCOG) / NMEA_SCALE_COG, 1, pstrCOG);
        pstrCOGT[0]= 'T';
    }

    if(bF && cShip::getOwnShipInst()->xDynamicData.nSOG != DEFAULT_SOG)
    {
    	CAisLib::FTOA((cShip::getOwnShipInst()->xDynamicData.nSOG) / NMEA_SCALE_SOG, 1, pstrSOG);
        pstrSOGN[0]= 'N';
    }

     sprintf(pstrBuff, "$AILR2,%d,%09d,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s",
        wSequenceNum, cShip::getOwnShipInst()->GetOwnShipMMSI(),    pstrDate, pstrTime, pstrLat, pstrLatNS, pstrLon, pstrLonEW, pstrCOG, pstrCOGT, pstrSOG, pstrSOGN);
    CSentence::AddSentenceTail(pstrBuff);
}

void CLongRange::GetLR3sentence(char *pstrBuff, WORD wSequenceNum, BOOL bI, BOOL bO, BOOL bP, BOOL bU, BOOL bW)
{
    //------------------------------------------------------------------------
    //* - LR3 Sentence를 리턴한다.
    //------------------------------------------------------------------------
    char    pstrDestination[LEN_MAX_DESTINATION*3+1];
    char    pstrEtaDate[8];
    char    pstrEtaTime[8];
    char    pstrDraughtO[16];
    char    pstrShipTypeP[8];
    char    pstrShipLengthU[8];
    char    pstrShipBreadthU[8];
    char    pstrShipTypeU[8];
    char    pstrPersonsW[8];
    char    pstrTmp[16];

    memset(pstrDestination, 0x00, sizeof(pstrDestination));
    memset(pstrEtaDate,     0x00, sizeof(pstrEtaDate));
    memset(pstrEtaTime,     0x00, sizeof(pstrEtaTime));
    memset(pstrDraughtO,    0x00, sizeof(pstrDraughtO));
    memset(pstrShipTypeP,   0x00, sizeof(pstrShipTypeP));
    memset(pstrShipLengthU, 0x00, sizeof(pstrShipLengthU));
    memset(pstrShipBreadthU,0x00, sizeof(pstrShipBreadthU));
    memset(pstrShipTypeU,   0x00, sizeof(pstrShipTypeU));
    memset(pstrPersonsW,    0x00, sizeof(pstrPersonsW));
    memset(pstrTmp,         0x00, sizeof(pstrTmp));

    if(bI)
    {
        CAisLib::ToIEC61162Chars(cShip::getOwnShipInst()->xNavData.pstrDestination, pstrDestination, sizeof(pstrDestination));

        if(cShip::getOwnShipInst()->xNavData.xETA.nDay != ETA_DAY_NULL && cShip::getOwnShipInst()->xNavData.xETA.nMonth != ETA_MONTH_NULL)
        {
            sprintf(pstrEtaDate, "%02d%02d00", cShip::getOwnShipInst()->xNavData.xETA.nDay, cShip::getOwnShipInst()->xNavData.xETA.nMonth);
        }

        if(cShip::getOwnShipInst()->xNavData.xETA.nHour != ETA_HOUR_NULL && cShip::getOwnShipInst()->xNavData.xETA.nMin != ETA_MIN_NULL)
        {
            sprintf(pstrEtaTime, "%02d%02d%02d",
                    cShip::getOwnShipInst()->xNavData.xETA.nHour, cShip::getOwnShipInst()->xNavData.xETA.nMin, cShip::getOwnShipInst()->xNavData.xETA.nSec);
        }
    }

    if(bO && cShip::getOwnShipInst()->xNavData.uDraught != 0)
    {
        sprintf(pstrDraughtO, "%02d.%1d", cShip::getOwnShipInst()->xNavData.uDraught/10, cShip::getOwnShipInst()->xNavData.uDraught%10);
    }

    /*SHIP TYPE가 2번 사용되며 bP 필드는 cc이므로 문자 2개가 들어가야 된다.*/
    if(bP && cShip::getOwnShipInst()->xNavData.uShipType != 0)
    {
        sprintf(pstrShipTypeP, "%d", cShip::getOwnShipInst()->xNavData.uShipType);
    }

    if(bU)
    {
        if(CSensorMgr::getInst()->GetAntType() == EPFD_ANTENNA_TYPE_INT)
        {
            sprintf(pstrShipLengthU, "%d", (CSetupMgr::getInst()->GetIntAntennaPosA() + CSetupMgr::getInst()->GetIntAntennaPosB()));
            sprintf(pstrShipBreadthU, "%d", (CSetupMgr::getInst()->GetIntAntennaPosC() + CSetupMgr::getInst()->GetIntAntennaPosD()));
        }
        else
        {
            sprintf(pstrShipLengthU, "%d", (CSetupMgr::getInst()->GetExtAntennaPosA() + CSetupMgr::getInst()->GetExtAntennaPosB()));
            sprintf(pstrShipBreadthU, "%d", (CSetupMgr::getInst()->GetExtAntennaPosC() + CSetupMgr::getInst()->GetExtAntennaPosD()));
        }

        if(cShip::getOwnShipInst()->xNavData.uShipType != 0)
            sprintf(pstrShipTypeU, "%d", cShip::getOwnShipInst()->xNavData.uShipType);
    }

    if(bW && cShip::getOwnShipInst()->xNavData.uPerson != 0)
        sprintf(pstrPersonsW, "%d", cShip::getOwnShipInst()->xNavData.uPerson);

    sprintf(pstrBuff, "$AILR3,%d,%09d,%s,%s,%s,%s,%s,%s,%s,%s,%s",
            wSequenceNum, cShip::getOwnShipInst()->GetOwnShipMMSI(), pstrDestination,
            pstrEtaDate, pstrEtaTime, pstrDraughtO, pstrShipTypeP, pstrShipLengthU, pstrShipBreadthU, pstrShipTypeU, pstrPersonsW);
    CSentence::AddSentenceTail(pstrBuff);
}

/**
 * @brief Get the sequential message identifier
 * @return The sequential message identifier
 */
int CLongRange::GetSequentialLRMsgID(void)
{
    //----------------------------------------------
    // - New LRI/LRF Sentence 전송시에 1씩 증가됨.
    //----------------------------------------------
    m_nTxSequenceNum++;

    if(m_nTxSequenceNum == 10)
        m_nTxSequenceNum = 0;

    return m_nTxSequenceNum;
}

/**
 * @brief Process the LRF sentence
 * @param wSequenceNum The sequence number
 * @param nRequestMMSI The requestor MMSI
 * @param pstrRequestName The requestor name
 * @param pstrFuncRequest The function request
 * @param pstrReplyStat The reply status
 * @return None
 */
void CLongRange::ProcessLRF(WORD wSequenceNum, int nRequestMMSI, char *pstrRequestName, char *pstrFuncRequest, char *pstrReplyStat)
{
    int     i, j;
    char    pFinalFuncRequest[MAX_FR_CHAR];
    char    pFinalReplyStatus[MAX_FRS_CHAR];
    char    pstrLRF[RX_MAX_DATA_SIZE];
    char    pstrLR1[RX_MAX_DATA_SIZE];
    char    pstrLR2[RX_MAX_DATA_SIZE];
    char    pstrLR3[RX_MAX_DATA_SIZE];
    BYTE    cLrRequest;
    WORD    nFinalFuncRequestCnt = 0;
    WORD    bEnableA = FALSE;
    BOOL    bEnableB = FALSE;
    BOOL    bEnableC = FALSE;
    BOOL    bEnableE = FALSE;
    BOOL    bEnableF = FALSE;
    BOOL    bEnableI = FALSE;
    BOOL    bEnableO = FALSE;
    BOOL    bEnableP = FALSE;
    BOOL    bEnableU = FALSE;
    BOOL    bEnableW = FALSE;

    WORD    wFuncRequestCnt = strlen((char*)pstrFuncRequest);

    memset((BYTE *)pFinalFuncRequest, 0x00, sizeof(pFinalFuncRequest));
    memset((BYTE *)pFinalReplyStatus, 0x00, sizeof(pFinalReplyStatus));

    if(pstrReplyStat && strlen(pstrReplyStat) > 0)
    {
        nFinalFuncRequestCnt = wFuncRequestCnt;
        strcpy(pFinalFuncRequest, pstrFuncRequest);
        strncpy(pFinalReplyStatus, pstrReplyStat, MIN(strlen(pstrReplyStat), nFinalFuncRequestCnt));
    }
    else
    {
        for(i = 0, j = 0; i < wFuncRequestCnt; i++, j++)
        {
            pFinalFuncRequest[j] = pstrFuncRequest[i];
            pFinalReplyStatus[j] = CheckReplyEnable(pstrFuncRequest[i]);
        }
        nFinalFuncRequestCnt = j;

        /*LR1~3 중 출력할 항목(pstrFuncRequest)을 확인*/
        for (i = 0 ; i < nFinalFuncRequestCnt ; i++)
        {
            cLrRequest = pFinalFuncRequest[i];

            //A = Ship’s: name, call sign, and IMO number;
            //B = Date and time of message composition;
            //C = Position;
            //E = Course over ground;
            //F = Speed over ground;
            //I = Destination and Estimated Time of Arrival (ETA);
            //O = Draught;
            //P = Ship/cargo;
            //U = Ship’s: length, breadth, type;
            //W = Persons on board.
            switch(toupper(cLrRequest))
            {
            case 'A':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableA = TRUE;    break;
            case 'B':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableB = TRUE;    break;
            case 'C':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableC = TRUE;    break;
            case 'E':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableE = TRUE;    break;
            case 'F':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableF = TRUE;    break;
            case 'I':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableI = TRUE;    break;
            case 'O':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableO = TRUE;    break;
            case 'P':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableP = TRUE;    break;
            case 'U':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableU = TRUE;    break;
            case 'W':    if(pFinalReplyStatus[i] == LR_FRS_AP)    bEnableW = TRUE;    break;
            }
        }
    }

    /*LRF 출력*/
    memset((BYTE *)pstrLRF, 0x00, sizeof(pstrLRF));
    memset((BYTE *)pstrLR1, 0x00, sizeof(pstrLR1));
    memset((BYTE *)pstrLR2, 0x00, sizeof(pstrLR2));
    memset((BYTE *)pstrLR3, 0x00, sizeof(pstrLR3));

    CLrf::MakeSentence(pstrLRF, wSequenceNum, nRequestMMSI, pstrRequestName, pFinalFuncRequest, pFinalReplyStatus);
    SendOutStr(pstrLRF);

    /*LR1~3 항목을 출력*/
    GetLR1sentence(pstrLR1, wSequenceNum, nRequestMMSI, bEnableA);
    SendOutStr(pstrLR1);

    GetLR2sentence(pstrLR2, wSequenceNum, bEnableB, bEnableC, bEnableE, bEnableF);
    SendOutStr(pstrLR2);

    GetLR3sentence(pstrLR3, wSequenceNum, bEnableI, bEnableO, bEnableP, bEnableU, bEnableW);
    SendOutStr(pstrLR3);
}

//-------------------------------------------------------------------------------------------------------------------------------
// - Long Range
// - LRI, LRF의 Sequence number가 동일해야 유효한 값이된다.(IEC 61993-2 p.92)
// - LRI, LRF가 항상 같이 입력이 된다.(IEC 61993-2 p.92)
// - LRI가 먼저 입력되고 LRF가 입력이된다.(IEC 61993-2 p.93)
// - LRI의 Control flag = 0 - 현재 선박의 위치가 영역내에 있다. (AND) Destination MMSI가 NULL 필드이다.
// - LRI의 Control flag = 0 - Destination MMSI 필드와 내 MMSI가 같을때
// - LRI의 Control flag = 1 - 현재 선박의 위치가 영역내에 있다.
// - LRF의 Function request 1~26가지의 알파벳으로 A,B,C,E,F,I,O,P,U,W 사용된다.
// - LRF의 Function reply status항목은 질의: LRI, LRF로 입력받을때는 NULL이며, 응답 :LRF,LR1~3 출력될때는 필드가 2,3,4로 채워진다.
//-------------------------------------------------------------------------------------------------------------------------------
void CLongRange::ProcessLRILRF(WORD wLrType, char *pstrSentence, WORD nSrcPortID)
{
    static char    pstrRcvCmdLRI[80];
    static char    pstrRcvCmdLRF[80];
    static char    pstrRcvCmdSy_LRI[80];
    static char    pstrRcvCmdSy_LRF[80];

    char    *pDataLRI;
    char    *pDataLRF;

    char    pstrSplitted[200];
    char    *ppstrFields[32];
    int     nCount = 0;
    BOOL    bLrCheckValid = TRUE;

    strncpy(pstrSplitted, pstrSentence, strlen(pstrSentence));

    if(wLrType == LRI)
    {
        strncpy(pstrSplitted, pstrSentence, strlen(pstrSentence));
        if(!(nCount = CAisLib::SplitNMEASentence(pstrSplitted, ppstrFields, strlen(pstrSentence))))
            return;

        if(!CheckSentenceLRI(ppstrFields, nCount))
        {
            return;
        }

        if(nSrcPortID == LR_FROM_PORT_LR)
            strncpy(pstrRcvCmdLRI, pstrSentence, strlen(pstrSentence));
        else
            strncpy(pstrRcvCmdSy_LRI, pstrSentence, strlen(pstrSentence));
    }
    else
    {
        strncpy(pstrSplitted, pstrSentence, strlen(pstrSentence));
        if(!(nCount = CAisLib::SplitNMEASentence(pstrSplitted, ppstrFields, strlen(pstrSentence))))
            bLrCheckValid = FALSE;

        if(!CheckSentenceLRF(ppstrFields, nCount))
        {
            bLrCheckValid = FALSE;
        }

        if(bLrCheckValid)
        {
            if(nSrcPortID == LR_FROM_PORT_LR)
            {
                strncpy(pstrRcvCmdLRF, pstrSentence, strlen(pstrSentence));
                pDataLRI = pstrRcvCmdLRI;
                pDataLRF = pstrRcvCmdLRF;
            }
            else
            {
                strncpy(pstrRcvCmdSy_LRF, pstrSentence, strlen(pstrSentence));
                pDataLRI = pstrRcvCmdSy_LRI;
                pDataLRF = pstrRcvCmdSy_LRF;
            }

            if(pDataLRI[0] != '\0')
            {
                CLri::Parse(pDataLRI);
                CLrf::Parse(pDataLRF);

                //------------------------------------------------------------------------------------------
                // IEC 61162-1 8.3.60 LRI – AIS long-range interrogation
                // The control flag is a single character that qualifies the request for information. 
                // The control flag affects AIS unit’s reply logic. The control flag cannot be a null field. 
                // When the control flag is “0”, the logic is normal. Under “normal” operation, 
                // the AIS unit responds if either:
                // – the AIS unit is within the geographic rectangle provided, and
                // – the AIS unit has not responded to the requesting MMSI in the last 24 hours, and
                // – the MMSI “destination” field is null.
                // or
                // – The AIS unit’s MMSI appears in the MMSI “destination” field in the LRI sentence.
                //   When the control flag is “1”, the AIS unit responds if:
                // – the AIS unit is within the geographic rectangle provided.
                if(CLri::m_wSeqNum == CLrf::m_wSeqNum)
                {
                    if(CLri::m_chCtrlFlag == '0')    //Control Flag=0
                    {
                        if(CLri::m_dwMMSIDest != NMEA_NULL_INTEGER)
                        {
                            // MMSI of desination is not null, addressed LR interrogation
                            if((int)cShip::getOwnShipInst()->GetOwnShipMMSI() == CLri::m_dwMMSIDest)
                            {
                                if(nSrcPortID == LR_FROM_PORT_LR)
                                {
                                    // Send the "LRI/LRF" received at LR.Port to Ext.Port(PI)
                                    SendOutHighSpdPort(pDataLRI, TRUE);
                                    SendOutHighSpdPort(pDataLRF, TRUE);
                                }

                                if(CSetupMgr::getInst()->GetLongRangeAutoReply() || nSrcPortID == LR_FROM_PORT_PI)
                                {
                                    ProcessLRF(CLrf::m_wSeqNum, CLrf::m_dwReqMMSI, CLrf::m_pstrReqName, CLrf::m_pstrFuncReq);
                                }
                            }
                        }
                        else
                        {
                            POS_ALLF sAllPosLRI_NE;
                            POS_ALLF sAllPosLRI_SW;

                            /* If the destination MMSI is NULL, the ship must be within the area and there must be no record of the requester's MMSI. */
                            sAllPosLRI_NE.xPosF.fLAT = CLri::m_fLatNE;
                            sAllPosLRI_NE.xPosF.fLON = CLri::m_fLonNE;
                            sAllPosLRI_SW.xPosF.fLAT = CLri::m_fLatSW;
                            sAllPosLRI_SW.xPosF.fLON = CLri::m_fLonSW;

                            CAisLib::CalcFullPosByFLOAT(&sAllPosLRI_NE);
                            CAisLib::CalcFullPosByFLOAT(&sAllPosLRI_SW);

                            if(CROSMgr::getInst()->CheckInsideRectangleXX(&(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG), &sAllPosLRI_NE.xPosG, &sAllPosLRI_SW.xPosG))
                            {
                                if(nSrcPortID == LR_FROM_PORT_LR)
                                {
                                    /* If within the area, verify whether the requester's MMSI information 
                                        was transmitted within 24 hours. */
                                    if(CSetupMgr::getInst()->SetLongRangeTime(CLri::m_dwMMSIReq))
                                    {
                                        /* Send the "LRI/LRF" received at LR.Port to Ext.Port(PI) */
                                        SendOutHighSpdPort(pDataLRI, TRUE);
                                        SendOutHighSpdPort(pDataLRF, TRUE);

                                        ProcessLRF(CLrf::m_wSeqNum, CLrf::m_dwReqMMSI, CLrf::m_pstrReqName, CLrf::m_pstrFuncReq);
                                    }
                                }
                                else
                                {
                                    if(!CSetupMgr::getInst()->GetLongRangeAutoReply())
                                    {
                                        ProcessLRF(CLrf::m_wSeqNum, CLrf::m_dwReqMMSI, CLrf::m_pstrReqName, CLrf::m_pstrFuncReq);
                                    }
                                }
                            }
                        }
                    }
                    else if(CLri::m_chCtrlFlag == '1')    //Control Flag=1
                    {
                        POS_ALLF sAllPosLRI_NE;
                        POS_ALLF sAllPosLRI_SW;

                        /* Add a process to check if it is within the area */
                        sAllPosLRI_NE.xPosF.fLAT = CLri::m_fLatNE;
                        sAllPosLRI_NE.xPosF.fLON = CLri::m_fLonNE;
                        sAllPosLRI_SW.xPosF.fLAT = CLri::m_fLatSW;
                        sAllPosLRI_SW.xPosF.fLON = CLri::m_fLonSW;

                        CAisLib::CalcFullPosByFLOAT(&sAllPosLRI_NE);
                        CAisLib::CalcFullPosByFLOAT(&sAllPosLRI_SW);

                        if(CROSMgr::getInst()->CheckInsideRectangleXX(&(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG), &sAllPosLRI_NE.xPosG, &sAllPosLRI_SW.xPosG))
                        {
                            if(nSrcPortID == LR_FROM_PORT_LR)
                            {
                                // Send the "LRI/LRF" received at LR.Port to Ext.Port(PI)
                                SendOutHighSpdPort(pDataLRI, TRUE);
                                SendOutHighSpdPort(pDataLRF, TRUE);
                            }

                            if(CSetupMgr::getInst()->GetLongRangeAutoReply() ||
                                (!CSetupMgr::getInst()->GetLongRangeAutoReply() && nSrcPortID == LR_FROM_PORT_PI))
                            {
                                ProcessLRF(CLrf::m_wSeqNum, CLrf::m_dwReqMMSI, CLrf::m_pstrReqName, CLrf::m_pstrFuncReq);
                            }
                        }
                    }
                    else
                    {
                        bLrCheckValid = FALSE;
                    }
                }
                else
                {
                    // If LRI and LRF are different, initialize the data.
                    bLrCheckValid = FALSE;
                }
            }
            else
            {
                // If LRI is empty, the data is initialized because LRI is not input.
                bLrCheckValid = FALSE;
            }
        }
        else
        {
            // If LRF Sentence Element is ERROR, initialize the data.
            bLrCheckValid = FALSE;
        }

        if(!bLrCheckValid)
        {
            if(nSrcPortID == LR_FROM_PORT_LR)
            {
                memset(pstrRcvCmdLRI, 0x00, sizeof(pstrRcvCmdLRI));
                memset(pstrRcvCmdLRF, 0x00, sizeof(pstrRcvCmdLRF));
            }
            else    // LR_FROM_PORT_PI
            {
                memset(pstrRcvCmdSy_LRI, 0x00, sizeof(pstrRcvCmdSy_LRI));
                memset(pstrRcvCmdSy_LRF, 0x00, sizeof(pstrRcvCmdSy_LRF));
            }
        }
    }
}

/**
 * @brief Process the LRF sentence
 * @param pstrSentence The sentence to be parsed
 * @param nSrcPortID The source port ID
 * @return TRUE if the sentence is processed, FALSE otherwise
 */
BOOL CLongRange::ProcessAckLRF(char *pstrSentence, WORD nSrcPortID)
{
    bool bReplyStat = CLrf::Parse(pstrSentence);

    ProcessLRF( CLrf::m_wSeqNum, 
                CLrf::m_dwReqMMSI, 
                CLrf::m_pstrReqName, 
                CLrf::m_pstrFuncReq, 
                bReplyStat ? CLrf::m_pstrReplyStat : NULL );

    CMKD::getInst()->SendOutStr(pstrSentence);
    return TRUE;
}

/**
 * @brief Enable the sending and receiving data to MKD
 * @param bEnable Whether to enable the sending and receiving data to MKD
 * @return None
 */
void CLongRange::EnableSendRcvDataToMKD(BOOL bEnable)
{
    m_bEnableSendRcvDataToMKD = bEnable;
}

/**
 * @brief Send the UART port monitoring setup to MKD
 * @return None
 */
void CLongRange::SendSetupUartPortMon()
{
    char pstrSycBuff[32];

    sprintf((char*)pstrSycBuff, "$AIINL,MON,%c,%d", m_chPortID, m_bEnableSendRcvDataToMKD);
    CSentence::AddSentenceTail(pstrSycBuff);

    CMKD::getInst()->SendOutStr(pstrSycBuff);
}

/**
 * @brief Process the loop back response
 * @param pstrCmd The sentence to be parsed
 * @return None
 */
void CLongRange::ProcRunComLoopBackResp(char *pstrCmd)
{
    //-----------------------------------------
    // $AIINL,LBP,%d
    //-----------------------------------------
    //  %d : Loop-back test를 실행할 타겟 포트
    //         0 : Sensor1
    //         1 : Sensor2
    //         2 : Sensor3
    //         3 : Long - Range Port
    //         4 : External display Port
    //         5 : Pilot Port
    //-----------------------------------------
    char pstrSubData1[RX_MAX_DATA_SIZE];
    int  nPortID;

    CSentence::GetFieldString(pstrCmd, 2, pstrSubData1, sizeof(pstrSubData1));
    nPortID = atoi(pstrSubData1);

    if(nPortID == LOOPBACK_PORT_ID_LR)
    {
        CMKD::getInst()->SendOutStr(pstrCmd);                // send it back
    }
}

/**
 * @brief Process the sentence
 * @param pstrCmd The sentence to be parsed
 * @return None
 */
void CLongRange::ProcessSentence(char *pstrCmd)
{
    char pstrSentence[RX_MAX_DATA_SIZE];
    int   nLen;

    strcpy(pstrSentence, pstrCmd);
    nLen = strlen((char*)pstrCmd);

    if (!CSentence::IsValidCheckSum(pstrCmd))
    {
        return;
    }

    if(m_bEnableSendRcvDataToMKD)
    {
        //--------------------------------------------------------------------------------
        // For long range port
        // See PROTOCOL_OVERHEAD(3) value of MainCPU SW
        // ex) $GPRMC,005957.12,A,0004.9972,N,00000.0000,E,0,0,200103,,,D*57\r\n
        // => $GPRMC,005957.12,A,0004.9972,N,00000.0000,E,0,0,200103,,,D*57,#L\r\n 로 송신
        // ,#L 추가됨
        //--------------------------------------------------------------------------------
        sprintf(&pstrSentence[nLen-2], ",#%c\r\n", m_chPortID);
        CMKD::getInst()->SendOutStr(pstrSentence);
    }

    char pstrTalkerID[LEN_NMEA_TALKER];
    char pstrSentID[LEN_NMEA_SENT];
    CSentence::GetTalkerSenderID((char*)pstrCmd, pstrTalkerID, pstrSentID);

    if(!strcmp((char*)pstrSentID, "LRI"))
    {
        ProcessLRILRF(LRI, pstrCmd, LR_FROM_PORT_LR);
        return;
    }
    if(!strcmp((char*)pstrSentID, "LRF"))
    {
        ProcessLRILRF(LRF, pstrCmd, LR_FROM_PORT_LR);
        return;
    }

    if(!strcmp(pstrSentID, "INL"))
    {
        char pstrSubID[8];
        CSentence::GetFieldString(pstrCmd, 1, pstrSubID, sizeof(pstrSubID));

        if(!strncmp(pstrCmd, "LBP", 3))
        {
            ProcRunComLoopBackResp(pstrCmd);
            return;
        }
    }
}

/**
 * @brief Process the data from the long range port
 * @param pUartDbgP The UART port to read the data
 * @return None
 */
void CLongRange::ProcessData(cUart *pUartDbgP)
{
    int nData;

    if(!m_pUartPort)
        return;

    nData = m_pUartPort->GetComData();
    while (nData != UART_NULL_CHAR)
    {
        if(m_nRxSize || (m_nRxSize == 0 && nData == '$') || (m_nRxSize == 0 && nData == '!'))
            m_pRxData[m_nRxSize++] = nData;

        if(m_nRxSize > 5 && nData == ASC_CHR_LF)
        {
            m_pRxData[m_nRxSize] = 0x00;

            ProcessSentence((char*)m_pRxData);

            m_nRxSize = 0;
        }

        if(m_nRxSize >= (RX_MAX_DATA_SIZE - 2))
            m_nRxSize = 0;

        nData = m_pUartPort->GetComData();
    }
}

/**
 * @brief Run the UART ISR handler
 * @return None
 */
void CLongRange::RunUartIsrHandler(void)
{
    if (m_pUartPort != NULL)
    {
        m_pUartPort->RunUartIsrHandler();
    }
}
