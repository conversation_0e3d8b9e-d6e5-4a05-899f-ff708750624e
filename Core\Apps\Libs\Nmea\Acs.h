/**
 * @file    Acs.h
 * @brief   Acs header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ACS_H__
#define __ACS_H__

/******************************************************************************
*
* ACS - Channel management information Source
*
* $--ACS,x,xxxxxxxxx,hhmmss.ss,xx,xx,xxxx*hh<CR><LF>
*        | |         |         |  |  |
*        1 2         3         4  5  6
*
* 1. Sequence number , 0 to 9
* 2. MMSI of originator
* 3. UTC at receipt of regional operating settings
* 4. UTC day, 01 to 31
* 5. UTC month, 01 to 12
* 6. UTC year
*
******************************************************************************/
class CAcs : public CSentence
{
public:
    CAcs();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the sentence
     * @param pszSentence The sentence to be made
     * @param psRosData The ROS data
     * @param nRosIndex The ROS index
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, xROSDATA *psRosData, int nRosIndex);
};

#endif /* __ACS_H__ */

