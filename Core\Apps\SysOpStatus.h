/**
 * @file    SysOpStatus.h
 * @brief   Ais system operation status
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef __SYS_OPSTATUS_H__
#define __SYS_OPSTATUS_H__

#include "DataType.h"

class OPSTATUS
{
public:
    static  BOOL    bEnableToRunTasks;
    static  BOOL    bEnableCalcNumRcvSt;
    static  BOOL    bRunSyncProcess;
    static  BOOL    bSysInit2minDone;
    static  BOOL    bFirstFrameOnBootDone;
    static  DWORD   dwRcvClearSec;

    static  INT16   nCurFrameID;
    static  INT16   nCurSlotID;
    static  INT16   nCurFrameMapSlotID;
};

#endif    /*__SYS_OPSTATUS_H__*/
