/**
 * @file    Ack.cpp
 * @brief   Ack class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Ship.h"
#include "Ack.h"

/*****************************************************************************
 *
 * ACK - Acknowledge alarm
 *
 * $--ACK,xxx*hh<CR><LF>
 *        |
 *        1
 *
 * 1. Unique alarm number (identifier) at alarm source
 *
 ******************************************************************************/
// Define static member variables
int32_t CAck::m_nAlarmNum = 0;

CAck::CAck() : CSentence()
{

}

/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAck::Parse(const char *pszSentence)
{
    m_nAlarmNum = GetFieldInteger(pszSentence, 1);

    if (m_nAlarmNum == NMEA_NULL_INTEGER)
    {
        return false;
    }

    return true;
}

/**
 * @brief Make the ACK sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CAck::MakeSentence(char *pszSentence)
{
    return strlen(pszSentence);
}
