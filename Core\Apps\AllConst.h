/**
 * @file    AllConst.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __ALLCONST_H__
#define  __ALLCONST_H__

#include "Define.h"
#include "Define_AIS.h"

//=============================================================================
#define  M_PI_VALUE_D                           3.14159265358979323846
#define  M_PI_VALUE_F                           3.14159265358979323846f
#define  M_PI_MUL_2_D                           (M_PI_VALUE_D * 2.0)
#define  M_PI_MUL_2_F                           (M_PI_VALUE_F * 2.0f)
#define  M_PI_DIV_2_D                           (M_PI_VALUE_D / 2.0)
#define  M_PI_DIV_2_F                           (M_PI_VALUE_F / 2.0f)
#define  M_DEG_TO_RAD_D                         (M_PI_VALUE_D / 180.0)
#define  M_DEG_TO_RAD_F                         (M_PI_VALUE_F / 180.0f)
#define  M_RAD_TO_DEG_D                         (180.0  / M_PI_VALUE_D)
#define  M_RAD_TO_DEG_F                         (180.0f / M_PI_VALUE_F)
//=============================================================================
#ifdef __USE_12BIT_ADC__
    #define  ADC_RES_MAX_VALUE                  (4095)  // 12-bit ADC
#else
    #define  ADC_RES_MAX_VALUE                  (65535) // 16-bit ADC
#endif
//=============================================================================
#define  ASC_CHR_SOH                            0x01  // start of 128-byte data packet
#define  ASC_CHR_STX                            0x02  // start of 1024-byte data packet
#define  ASC_CHR_ETX                            0x03
#define  ASC_CHR_EOT                            0x04  // end of transmission
#define  ASC_CHR_ACK                            0x06  // acknowledge
#define  ASC_CHR_CR                             0x0d
#define  ASC_CHR_LF                             0x0a
#define  ASC_CHR_NAK                            0x15  // negative acknowledge
#define  ASC_MDM_CA                             0x18  // two of these in succession aborts transfer
#define  ASC_MDM_CRC16                          0x43  // 'C' == 0x43, request 16-bit CRC
//=============================================================================
#define  MODE_VAL_NULL                         -1
#define  MODE_VAL_OFF                           0
#define  MODE_VAL_ON                            1
//=============================================================================
#define  GPS_FIX_STATUS_LOST                    0
#define  GPS_FIX_STATUS_GPS                     1
#define  GPS_FIX_STATUS_DGPS                    2
#define  GPS_FIX_STATUS_WAAS                    3
#define  GPS_FIX_STATUS_EGNOS                   4
#define  GPS_FIX_STATUS_MSAS                    5
#define  GPS_FIX_STATUS_SBAS                    6
#define  GPS_FIX_STATUS_SIM                     7
//=============================================================================
#define  GPS_USE_MODE_INTGPS                    0
#define  GPS_USE_MODE_EXT232                    1
#define  GPS_USE_MODE_EXT422                    2
#define  GPS_USE_MODE_EXTMKD                    3
//=============================================================================
#define  EPFD_ANTENNA_TYPE_NONE                 0
#define  EPFD_ANTENNA_TYPE_INT                  1
#define  EPFD_ANTENNA_TYPE_EXT                  2
//=============================================================================
#define  DATUM_NOT_WGS84                        0
#define  DATUM_WGS84                            1
//=============================================================================
#define  DIST_UNIT_NM                           0
#define  DIST_UNIT_KM                           1
#define  DIST_UNIT_MI                           2
//-----------------------------------------------------------------------------
#define  SPD_UNIT_KN                            0
#define  SPD_UNIT_MPH                           1
#define  SPD_UNIT_KPH                           2
//-----------------------------------------------------------------------------
#define  CMPS_UNIT_T                            0
#define  CMPS_UNIT_M                            1
//=============================================================================
// Macros
#define  Round(x)                               ceil((x)-0.5)
#define  TO_DEG360(x)                           (fmod((x) + 360.0, 360.0))
#define  RadianToDegree(x)                      ((x) * M_RAD_TO_DEG_F)
#endif

//=============================================================================
