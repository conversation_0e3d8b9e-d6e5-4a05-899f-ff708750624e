#ifndef __TESTMODEMGR_H__
#define __TESTMODEMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "PI.h"
#include "ChannelMgr.h"

enum {
    // Get Tx Params
    TM_GET_TX_PARAM         = 0x001,
    TM_GET_TX_POWER,
    TM_GET_TX_FREQ_OFFSET,
    TM_GET_TX_DC_OFFSET,
    TM_GET_VSWRLIMIT,
    // Get Rx Params
    TM_GET_RX1,
    TM_GET_RX2,
    TM_GET_RX3,
    // Set Tx Params
    TM_SET_TX_PARAM         = 0x011,
    TM_SET_TX_POWER,
    TM_SET_TX_FREQ_OFFSET,
    TM_SET_TX_DC_OFFSET,
    TM_SET_VSWRLIMIT,
    // Set RX Params
    TM_SET_RX1,
    TM_SET_RX2,
    TM_SET_RX3,

    TM_RES_DSC_RX_COUNT     = 0x021,

    TM_MODE_START           = 0xC01,
    TM_MODE_CANCLE          = 0xC02,
} TM_MODE;


#define TM_TX_DATA_NON              0
#define TM_TX_DATA_DSC_01010101     1    // IEC-61993-2(ed2.0) 10.1 Standard test signal number 1 (DSC) DSC dot pattern
#define TM_TX_DATA_0101             2    // IEC-61993-2(ed2.0) 10.2 Standard test signal number 2 (TDMA)
#define TM_TX_DATA_0000_1111        3    // IEC-61993-2(ed2.0) 10.3 Standard test signal number 3 (TDMA)
#define TM_TX_DATA_RANDOM_PRBS      4    // IEC-61993-2(ed2.0) 10.4 Standard test signal number 4 (PRBS) MKD menu : "Random Data"
#define TM_TX_DATA_FIXED_PRBS       5    // IEC-61993-2(ed2.0) 10.5 Standard test signal number 5 (PRBS) MKD menu : "Fixed Msg."
#define TM_TX_DATA_UNMODE_CARRIER   6
#define TM_TX_DATA_FIXED_MSG1       7
#define TM_TX_LOOPBACK_TEST         8

#define INFINITE_TESTDATA_NUMSLOT   1
#define INFINITE_TESTDATA_BITS      256
#define INFINITE_TESTDATA_BYTES     (INFINITE_TESTDATA_BITS >> 3)

#define MMSI_TESTMODE               273000000

class CTestModeMgr
{
public:
    CTestModeMgr();
    ~CTestModeMgr();

    static std::shared_ptr<CTestModeMgr> getInst() {
        static std::shared_ptr<CTestModeMgr> pInst = std::make_shared<CTestModeMgr>();
        return pInst;
    }

public:
    TagTestMode     m_nTestModeRunning;

    CPI            *m_pCommPI;
    CChannelMgr    *m_pTestChTx;

    int     m_nOrgMMSI;

    int     m_nTxCntRandomPRBS;
    WORD    m_wTxSlotRandomPRBS;

    int     m_nTxCntFixedPRBS;
    int     m_nTxSentCntFixedPRBS;
    WORD    m_wTxSlotFixedPRBS;

    WORD    m_wTxSlotMsg1;

    UINT8   m_bRxTestSignalRunning;

    UINT8   m_bTxTestSignalRunning;
    UINT8   m_uTxTestSignalType;

    UINT8   m_uTxTestMsgRun;
    UINT8   m_uTxTestMsgType;
    UINT16  m_uTxTestMsgLen;

    BYTE   *m_pTxTestMsgBuff;
    BYTE   *m_pTxParam;

    int     m_nDscRcvTestCnt;

    BOOL    m_bRunOnBootBITE;
    WORD    m_wErrorCodeBITE;

    BOOL    m_bRunningTxLoopbackTest;
    WORD    m_wLoopbackTxSlot;
    int     m_nLoopbackTxCnt;
    DWORD   m_dwLoopbackTestCheckSec;
    int     m_nTxLoopbackRcvCntCH1;
    int     m_nTxLoopbackRcvCntCH2;

public:
    BOOL    IsTestModeRunning();
    BOOL    IsTransmitTestRunning();
    BOOL    IsReceiveTestRunning();
    BOOL    IsBuiltInTestRunning();
    BOOL    IsRunTxLoopbackTest();

    void    SendOutTestModeStr(char *pstrData);
    void    SendOutNak(int nCmd);

    void    SetTestMode(TagTestMode nTestMode, CPI *pPortPI);
    void    SetAisTestModeTxCh(CChannelMgr *pCh);

    void    TMGetTxParam();
    BOOL    TMSetTxParam(WORD txFreqScaled, WORD wTxPower);
    void    TMGetTxPower();
    void    TMSetTxPower(WORD wPowerLevel);
    void    TMGetFreqOffset();
    void    TMSetFreqOffset(WORD wOffset);
    void    TMGetDcOffset();
    void    TMSetDcOffset(WORD wOffset, WORD wShift);
    void    TMGetVswrLimit();
    void    TMSetVswrLimit(WORD wVswr);

    void    TMTransmitterTestProc();
    void    TMTransmitterTestStart(BYTE uTestSignalType);
    void    TMTransmitterTestStop(void);
    BOOL    TMSetTranmitterTest(WORD wSignalPattern);

    void    TMTransmitRandomPRBSMsgRun(BOOL bRun);
    void    TMTransmitRandomPRBSMsgProc();
    void    TMTransmitFixedPRBSMsgRun(BOOL bRun);
    void    TMTransmitFixedPRBSMsgProc();
    void    TMTransmitFixedMsg1Proc();

    void    TMGetRxParam(BYTE bRxCH);
    BOOL    TMSetRxParam(BYTE bRxCH, WORD wTxChNum);

    void    TMReceiveTestStart(void);
    void    TMReceiveTestStop(void);

    void    TMSetDscRcvTestCnt(int nCnt);
    void    TMIncDscRcvTestCnt();
    void    TMSendOutDscRcvTestCnt();

    void    InitTxLoopbackTest();
    void    RunTxLoopbackTest();

    void    IncTxLoopbackRcvCntCH1();
    void    IncTxLoopbackRcvCntCH2();

    void    RunBITE(BOOL bRunOnBoot);
    void    FinishBITE();

    BOOL    TestModeProcess();
};

#endif //__TESTMODEMGR_H__
