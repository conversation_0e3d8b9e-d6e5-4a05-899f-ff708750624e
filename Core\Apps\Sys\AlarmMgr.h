#ifndef __ALARMMGR_H__
#define __ALARMMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AlarmThing.h"
#include "PI.h"

class CAlarmMgr
{
public:
	CAlarmMgr();
	~CAlarmMgr();

    static std::shared_ptr<CAlarmMgr> getInst() {
        static std::shared_ptr<CAlarmMgr> pInst = std::make_shared<CAlarmMgr>();
        return pInst;
    }

public:
	enum _tagAlarmMgr
	{
		NUM_ALARMS = 18,
	};

protected:
 	CAlarmThing	*m_pAlarmList[NUM_ALARMS];

public:
	BOOL		m_bAlarmActive;

public:
	void	SetAlarmRelayActive(BOOL bAlarmActive);
	void	CheckAlarmActive();

	void	SetAlarmAcked(int nAlarmID);
	void	SetBAMAlertAcked(int nBAMAlertID, int nAlertInstance);

	void	CheckAlarmAckedByHW();

	void	SendOutAllAlarmToPI(BOOL bImmediately=FALSE);
	void	SendOutARCToPI(char cArcCommand, CPI *pPI);

	void	PrecessACNCommand(char cArcCommand, int nBAMAlertID, int nAlertInstance, int nAlertManufacturer, CPI *pPI);

	void	CheckPeriodicallyAlarmAckByHW();
	void	CheckPeriodicallyAlarmStatus();
	void	CheckPeriodicallyAlarmSentence();
	void	RunPeriodicallyAlarmMgr();
};

#endif//__ALARMMGR_H__
