/**
 * @file    Acn.h
 * @brief   Acn header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __ACN_H__
#define __ACN_H__

/*****************************************************************************
 *
 * ACN - Acnnowledge alarm
 *
 * $--ACN,hhmmss.ss,aaa,x.x,x.x,c,a*hh<CR><LF>
 *         |         |   |   |  | |
 *         1         2   3   4  5 6
 *
 * 1. Time
 * 2. Manufacturer mnemonic code
 * 3. Alert identifier
 * 4. Alert instance, 1 to 999999
 * 5. Alert command, A, Q, O or S
 * 6. Sentence status flag. This field should be 'C' and should not be a null field. 'C' == Command
 *
 ******************************************************************************/
class CAcn : public CSentence
{
public:
    CAcn();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the ACN sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

    /**
     * @brief Get the alert number
     * @return The alert number
     */
    static int32_t GetAlertID(void) { return m_nAlertID; }

    /**
     * @brief Get the alert manufacturer
     * @return The alert manufacturer
     */
    static int32_t GetAlertManufacturer(void) { return m_nAlertManufacturer; }

    /**
     * @brief Get the alert instance
     * @return The alert instance
     */
    static int32_t GetAlertInstance(void) { return m_nAlertInstance; }

    /**
     * @brief Get the alert command
     * @return The alert command
     */
    static char GetAlertCommand(void) { return m_cAlertCommand; }

    /**
     * @brief Get the sentence status flag
     * @return The sentence status flag
     */
    static char GetSentenceStatusFlag(void) { return m_cSentenceStatusFlag; }

public:
    static int32_t m_nAlertID;
    static int32_t m_nAlertManufacturer;
    static int32_t m_nAlertInstance;
    static char    m_cAlertCommand;
    static char    m_cSentenceStatusFlag;
};

#endif /* __ACN_H__ */

