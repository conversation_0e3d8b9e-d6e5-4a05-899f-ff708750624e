/**
 * @file    Gpio.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include "SysConst.h"
#include "AllConst.h"
#include "DataType.h"

#include <sys\stat.h>

#ifndef  __GPIO_H__
#define __GPIO_H__

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
// GPIOD
#define GPIO_TP1                    (1)     // Test point 1
//#define GPIO_GPS_1_PPS              (11)    // 
//========================================================================
#define GPIO_EEPROM_CS              (11)    // EEPROM Chip Select
#define GPIO_EEPROM_WP              (15)    // EEPROM Write Protection
//========================================================================
// GPIOG
#define GPIO_RX_LED                 (2)     // Rx LED
#define GPIO_TX_LED                 (3)     // Tx LED
#define GPIO_TP2                    (3)     // Test point 2
#define GPIO_SYS_LED1               (4)     // Main proc alive LED
#define GPIO_SYS_LED2               (6)     // Slot mgr proc alive LED
#define GPIO_SYS_LED3               (8)     // Freq set proc alive LED
#define GPIO_TIME_LED               (10)    // GPS Lock LED
#define GPIO_ER_LED                 (15)    // Error LED
//========================================================================
// GPIOJ
#define GPIO_DDS1_LE                (12)    // PLL1 Chip Select
#define GPIO_DDS2_LE                (13)    // PLL2 Chip Select
#define GPIO_DDS3_LE                (14)    // PLL3 Chip Select
#define GPIO_VCXO_EN                (15)    // TX 5V ON/OFF
//========================================================================
// GPIOK
#define GPIO_RXRF_ON                (0)     // RX ON/OFF
#define GPIO_TXRF_ON                (1)     // TX ON/OFF
#define GPIO_PLL_LD1                (2)     // PLL1 Lock Detect
#define GPIO_PLL_LD2                (3)     // PLL2 Lock Detect
#define GPIO_PLL_LD3                (4)     // PLL3 Lock Detect
//========================================================================

//========================================================================
// Function prototype
void  SysSetGPIOxBitData(GPIO_TypeDef *pGPIO, int nPinNo, DWORD dSetVal);
DWORD SysGetGPIOxBitData(GPIO_TypeDef *pGPIO, int nPinNo);
//========================================================================

#ifdef  __cplusplus
}
#endif

#endif

