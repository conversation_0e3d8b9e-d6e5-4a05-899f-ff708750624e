#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "GPIOExt.h"

void InitGpioEtc()
{
    SetLedAllOn(FALSE);
}

void SetLedAllOn(BOOL bOn)
{
    SetGpioLed_Rx(bOn);
    SetGpioLed_Tx(bOn);
    SetGpioLed_Error(bOn);
    SetGpioLed_Time(bOn);
    SetGpioLed_Sys1(bOn);
    SetGpioLed_Sys2(bOn);
    SetGpioLed_Sys3(bOn);
}

void BlinkLed_Tx()
{
    static int nLedOn = 0;
    SetGpioLed_Tx(nLedOn);
    nLedOn = 1 - nLedOn;
}

void BlinkLed_Rx()
{
    static int nLedOn = 0;
    SetGpioLed_Rx(nLedOn);
    nLedOn = 1 - nLedOn;
}

void BlinkLed_Error()
{
    static int nLedOn = 0;
    SetGpioLed_Error(nLedOn);
    nLedOn = 1 - nLedOn;
}

void BlinkLed_Time()
{
    static int nLedOn = 0;
    SetGpioLed_Time(nLedOn);
    nLedOn = 1 - nLedOn;
}

void BlinkLed_Sys1()
{
    static int nLedOn = 0;
    SetGpioLed_Sys1(nLedOn);
    nLedOn = 1 - nLedOn;
}

void BlinkLed_Sys2()
{
    static int nLedOn = 0;
    SetGpioLed_Sys2(nLedOn);
    nLedOn = 1 - nLedOn;
}

void BlinkLed_Sys3()
{
    static int nLedOn = 0;
    SetGpioLed_Sys3(nLedOn);
    nLedOn = 1 - nLedOn;
}

void ToggleTP1()
{
    static int nOn = 0;
    SetGpioTP1(nOn);
    nOn = 1 - nOn;
}

void ToggleTP2()
{
    static int nOn = 0;
    SetGpioTP2(nOn);
    nOn = 1 - nOn;
}
