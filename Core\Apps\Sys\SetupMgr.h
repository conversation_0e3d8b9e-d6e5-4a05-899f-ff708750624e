#ifndef __SETUPMGR_H__
#define __SETUPMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "Ublox.h"
#include "DataBackMgr.h"
#include "md5.h"

// Setup data header
#define SETUP_HEADER                    "INLA"  // interllian AIS

// Setup data signature and checksum length
#define SETUP_HEADER_LEN                4
#define SETUP_CHECKSUM_LEN              4

// Setup data offset
#define SETUP_OFFSET_HEADER             0
#define SETUP_OFFSET_CHKSUM             SETUP_HEADER_LEN
#define SETUP_OFFSET_DATA_START         (SETUP_HEADER_LEN + SETUP_CHECKSUM_LEN)

// Internal gnss min/max
#define INT_GNSS_CFG_IDX_MIN            INT_GNSS_CFG_IDX_GPS_ONLY
#define INT_GNSS_CFG_IDX_MAX            INT_GNSS_CFG_IDX_GPS_GALILEO
#define INT_GNSS_CFG_IDX_DFLT           INT_GNSS_CFG_IDX_GPS_ONLY

// External gnss min/max
#define EXT_GNSS_CFG_IDX_MIN            AIS_EPFD_MIN
#define EXT_GNSS_CFG_IDX_MAX            AIS_EPFD_MAX
#define EXT_GNSS_CFG_IDX_DFLT           AIS_EPFD_UNDEFINED

// Power level scale shift bits
#define POWER_LEVEL_SCALE_SHIFT_BITS    4
// Default Tx power level
#define DEFAULT_TX_PW_HIGH              (150 << POWER_LEVEL_SCALE_SHIFT_BITS)    // 12.5W Power 설정
#define DEFAULT_TX_PW_LOW               ( 50 << POWER_LEVEL_SCALE_SHIFT_BITS)    // 1W Power 설정

// Default VSWR limit
#define DEFAULT_VSWR_REFADC             1600
// Default TCXO level
#define DEFAULT_TCXO_LEVEL              496

// requested to ues this value by HW team
#define DEFAULT_DC_OFFSET               3250    // DC offset
#define DEFAULT_DC_SHIFT                750     // DC shift

//The long-range application broadcast shall be disabled by default. The Class B "SO" AIS shall
//have the capability of enabling this function during normal operation.
#define DEFAULT_LR_TX                   MODE_VAL_ON
#define DEFAULT_LR_AUTOREPLY            MODE_VAL_ON

// Maximum length of SSA key and password
#define MAX_SSA_KEY_LEN                 20
#define MAX_PASSWORD_LEN                32

typedef struct
{
    uint16_t wPowerLevelCh1_High;   // Tx Power (156M ~ 159.975M)
    uint16_t wPowerLevelCh1_Low;    // Tx Power (156M ~ 159.975M)
    uint16_t wPowerLevelCh2_High;   // Tx Power (160M ~ 162.025M)
    uint16_t wPowerLevelCh2_Low;    // Tx Power (160M ~ 162.025M)

    uint16_t wTcxoLevel;            // Reference level for TCXO
    uint16_t wVswrLimit;            // Config VSWR limit

    uint16_t wDcOffset;             // Config DC offset
    uint16_t wDcShift;              // Config DC shift 

    uint16_t wReserved[8];          // Reserved for future use
} RF_HW_CONFIG;

typedef struct {
    int32_t         uPortSpeedBitmap;

    bool            bEnableExtEPFS;
    bool            bEnableExtHeading;
    bool            bEnableExtROT;
    bool            bEnableAlr14;
    bool            bEnableDgnssByMsg17;

    xANTPOS         sIntAntPos;
    xANTPOS         sExtAntPos;
    xANTPOS         sExtendAntPos;
    int16_t         nIntGnssType;
    int16_t         nExtGnssType;
    bool            bEnableSBAS;

    int16_t         bLongRangeTxEnableMsg27;
    int16_t         bLongRangeAutoReply;
    int32_t         dwLongRangeCfg;
    int32_t         dwLongRangeCh1;
    int32_t         dwLongRangeCh2;
    LR_SHIP_LOG     pLongRangeTime[MAX_MMSI_LR_REQ];

    bool            bEnableSilentMode;
    bool            bPilotPortRestricted;

    int16_t         nAddrMsgTxRetryCnt;
    bool            bShowTestingSART;

    uint8_t         pstrUserKeySSA[MAX_SSA_KEY_LEN];
    uint8_t         pstrPwdAdmin[MAX_PASSWORD_LEN];
    uint8_t         pstrPwdUser[MAX_PASSWORD_LEN];

    uint8_t         nReserved[38];
} SYS_CONFIG;

typedef struct
{
    uint16_t    bEnableRMC;
    uint16_t    bEnableGSV;
    uint16_t    bEnableGLL;
    uint16_t    bEnableVTG;
    uint16_t    bEnableGBS;
    uint16_t    bEnableGGA;
    uint16_t    bEnableZDA;
    uint16_t    bEnableGNS;
    uint16_t    bEnableGSA;
} SETUP_NMEA_OUT;

class CSetupMgr
{
public:
    CSetupMgr();
    ~CSetupMgr();

    static std::shared_ptr<CSetupMgr> getInst() {
        static std::shared_ptr<CSetupMgr> pInst = std::make_shared<CSetupMgr>();
        return pInst;
    }

public:
    void    InitHwConfigData(void);
    int     SaveHwConfigData(void);
    bool    LoadHwConfigData(void);
    void    VerifyHwConfigData(void);

    void    InitSysConfigData(void);
    int     SaveSysConfigData(void);
    bool    LoadSysConfigData(void);
    void    VerifySysConfigData(void);

    int     SaveRosConfigData(void);
    bool    LoadRosConfigData(void);

    bool    SaveLogConfigData(void);
    bool    LoadLogConfigData(void);

    void    SetBackAllClearMode(int nMode);

    void    LoadAllConfigData(void);
    void    SaveAllConfigData(bool bForceSave);

    void    ReserveToSaveHwConfigData(void);
    void    ReserveToSaveSysConfigData(void);
    void    ReserveToSaveRosConfigData(void);
    void    ReserveToSaveLogConfigData(void);

    void    FactoryResetSys(void);
    void    FactoryResetROS(void);
    void    FactoryResetEventLog(void);

    void    FactoryResetAll(void);
    void    FactoryResetMode3(void);
    void    RestoreAllData(void);

    bool    VerifySetupData(UCHAR *pBackData, int nSize);
    bool    CompareSetupData(UCHAR *pBackData1, UCHAR *pBackData2, int nSize);

    bool    CheckSetupValueOnOff(int nValue);
    bool    CheckIntGnssCfgValue(int nSetupValue);
    bool    CheckExtGnssCfgValue(int nSetupValue);

    void    ResetAddrMsgRetransmitCount(void);
    void    SetAddrMsgRetransmitCount(int nTxRetryCnt);
    void    CheckAddrMsgRetransmitCount(void);

    void    SetSetupNmeaOutToMKD(DWORD dwSetupBitmap);
    DWORD   GetSetupNmeaOutToMKD(void);
    void    SetSetupNmeaOutToEXT(DWORD dwSetupBitmap);
    DWORD   GetSetupNmeaOutToEXT(void);

    bool    IsAisAClass(void);
    bool    IsAisBClass(void);
    bool    IsAisBClassSO(void);
    bool    IsAisBClassCS(void);

    void    RunPeriodicallySetup(void);

    int     GetBaudrateFromIdx(int nIdx);
    int     GetIdxFromBaudrate(int nBaudrate);

    bool    SetBaudrateSensor1(int nPropertyData);
    int     GetBaudrateSensor1(void);
    bool    SetBaudrateSensor2(int nPropertyData);
    int     GetBaudrateSensor2(void);
    bool    SetBaudrateSensor3(int nPropertyData);
    int     GetBaudrateSensor3(void);
    bool    SetBaudrateLongRange(int nPropertyData);
    int     GetBaudrateLongRange(void);
    void    ChangeLongRangeCH1(int nNumCH);
    void    ChangeLongRangeCH2(int nNumCH);

    bool    SetSetupLoadError(bool bError)   { m_bSetupLoadError = bError; }
    bool    IsSetupLoadError(void)           { return m_bSetupLoadError; }

    //--------------------------------------------------------------------------------------
    /**
     * @brief Get the Tx power level for CH1 in high mode
     * @return The Tx power level for CH1 in high mode
     */
    uint16_t GetTxPowerLevelCh1High(void)   { return m_sHwConfigData.wPowerLevelCh1_High; }
    /**
     * @brief Get the Tx power level for CH1 in low mode
     * @return The Tx power level for CH1 in low mode
     */
    uint16_t GetTxPowerLevelCh1Low(void)    { return m_sHwConfigData.wPowerLevelCh1_Low; }
    /**
     * @brief Get the Tx power level for CH2 in high mode
     * @return The Tx power level for CH2 in high mode
     */
    uint16_t GetTxPowerLevelCh2High(void)   { return m_sHwConfigData.wPowerLevelCh2_High; }
    /**
     * @brief Get the Tx power level for CH2 in low mode
     * @return The Tx power level for CH2 in low mode
     */
    uint16_t GetTxPowerLevelCh2Low(void)    { return m_sHwConfigData.wPowerLevelCh2_Low; }
    /**
     * @brief Get the TCXO level
     * @return The TCXO level
     */
    uint16_t GetTcxoLevel(void)             { return m_sHwConfigData.wTcxoLevel; }
    /**
     * @brief Get the VSWR limit
     * @return The VSWR limit
     */
    uint16_t GetVswrLimit(void)             { return m_sHwConfigData.wVswrLimit; }
    /**
     * @brief Get the DC offset
     * @return The DC offset
     */
    uint16_t GetDcOffset(void)              { return m_sHwConfigData.wDcOffset; }
    /**
     * @brief Get the DC shift
     * @return The DC shift
     */
    uint16_t GetDcShift(void)               { return m_sHwConfigData.wDcShift; }

    /**
     * @brief Set the Tx power level for CH1 in high mode
     * @param nValue The Tx power level for CH1 in high mode
     */
    void SetTxPowerLevelCh1High(uint16_t nValue) { 
        if (m_sHwConfigData.wPowerLevelCh1_High != nValue) {
            m_sHwConfigData.wPowerLevelCh1_High = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the Tx power level for CH1 in low mode
     * @param nValue The Tx power level for CH1 in low mode
     */
    void SetTxPowerLevelCh1Low(uint16_t nValue) { 
        if (m_sHwConfigData.wPowerLevelCh1_Low != nValue) {
            m_sHwConfigData.wPowerLevelCh1_Low = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the Tx power level for CH2 in high mode
     * @param nValue The Tx power level for CH2 in high mode
     */
    void SetTxPowerLevelCh2High(uint16_t nValue) { 
        if (m_sHwConfigData.wPowerLevelCh2_High != nValue) {
            m_sHwConfigData.wPowerLevelCh2_High = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the Tx power level for CH2 in low mode
     * @param nValue The Tx power level for CH2 in low mode
     */
    void SetTxPowerLevelCh2Low(uint16_t nValue) { 
        if (m_sHwConfigData.wPowerLevelCh2_Low != nValue) {
            m_sHwConfigData.wPowerLevelCh2_Low = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the TCXO level
     * @param nValue The TCXO level
     */
    void SetTcxoLevel(uint16_t nValue)
    { 
        if (m_sHwConfigData.wTcxoLevel != nValue) {
            m_sHwConfigData.wTcxoLevel = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the VSWR limit
     * @param nValue The VSWR limit
     */
    void SetVswrLimit(uint16_t nValue) { 
        if (m_sHwConfigData.wVswrLimit != nValue) {
            m_sHwConfigData.wVswrLimit = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the DC offset
     * @param nValue The DC offset
     */
    void SetDcOffset(uint16_t nValue) { 
        if (m_sHwConfigData.wDcOffset != nValue) {
            m_sHwConfigData.wDcOffset = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }
    /**
     * @brief Set the DC shift
     * @param nValue The DC shift
     */
    void SetDcShift(uint16_t nValue) { 
        if (m_sHwConfigData.wDcShift != nValue) {
            m_sHwConfigData.wDcShift = nValue; 
            ReserveToSaveHwConfigData(); 
        }
    }

    //--------------------------------------------------------------------------------------
    /**
     * @brief Get the port speed bitmap
     * @return The port speed bitmap
     */
    int     GetPortSpeedBitmap(void)        { return m_sSysConfigData.uPortSpeedBitmap; }

    /**
     * @brief Get the enable external EPFS
     * @return The enable external EPFS
     */
    bool    GetEnableExtEPFS(void)          { return m_sSysConfigData.bEnableExtEPFS; }
    /**
     * @brief Get the enable external heading
     * @return The enable external heading
     */
    bool    GetEnableExtHeading(void)       { return m_sSysConfigData.bEnableExtHeading; }
    /**
     * @brief Get the enable external ROT
     * @return The enable external ROT
     */
    bool    GetEnableExtROT(void)           { return m_sSysConfigData.bEnableExtROT; }
    /**
     * @brief Get the enable alert 14
     * @return The enable alert 14
     */
    bool    GetEnableAlert14(void)          { return m_sSysConfigData.bEnableAlr14; }
    /**
     * @brief Get the pilot port restricted
     * @return The pilot port restricted
     */
    bool    GetPilotPortRestricted(void)    { return m_sSysConfigData.bPilotPortRestricted; }

    /**
     * @brief Get the enable silent mode
     * @return The enable silent mode
     */
    bool    GetEnableSilentMode(void)       { return m_sSysConfigData.bEnableSilentMode; }
    
    /**
     * @brief Get the internal gnss type
     * @return The internal gnss type
     */
    int16_t GetIntGnssType(void)            { return m_sSysConfigData.nIntGnssType; }
    /**
     * @brief Get the external gnss type
     * @return The external gnss type
     */
    int16_t GetExtGnssType(void)            { return m_sSysConfigData.nExtGnssType; }
    /**
     * @brief Get the enable SBAS
     * @return The enable SBAS
     */
    bool    GetEnableSBAS(void)             { return m_sSysConfigData.bEnableSBAS; }

    /**
     * @brief Get the long range tx enable msg27
     * @return The long range tx enable msg27
     */
    bool    GetLongRangeTxEnableMsg27(void) { return m_sSysConfigData.bLongRangeTxEnableMsg27; }
    /**
     * @brief Get the long range auto reply
     * @return The long range auto reply
     */
    bool    GetLongRangeAutoReply(void)     { return m_sSysConfigData.bLongRangeAutoReply; }
    /**
     * @brief Get the long range configuration
     * @return The long range configuration
     */
    int32_t GetLongRangeCfg(void)           { return m_sSysConfigData.dwLongRangeCfg; }
    /**
     * @brief Get the long range channel 1
     * @return The long range channel 1
     */
    int32_t GetLongRangeCh1(void)           { return m_sSysConfigData.dwLongRangeCh1; }
    /**
     * @brief Get the long range channel 2
     * @return The long range channel 2
     */
    int32_t GetLongRangeCh2(void)           { return m_sSysConfigData.dwLongRangeCh2; }
    /**
     * @brief Get the long range time
     * @return The long range time
     */
    LR_SHIP_LOG* GetLongRangeTime(void)     { return m_sSysConfigData.pLongRangeTime; }
    /**
     * @brief Get the enable DGNSS by msg17
     * @return The enable DGNSS by msg17
     */
    bool    GetEnableDgnssByMsg17(void)     { return m_sSysConfigData.bEnableDgnssByMsg17; }
    /**
     * @brief Get the show testing SART
     * @return The show testing SART
     */
    bool    GetShowTestingSART(void)        { return m_sSysConfigData.bShowTestingSART; }
    /**
     * @brief Get the addressed message retransmit count
     * @return The addressed message retransmit count
     */
    int8_t  GetAddrMsgTxRetryCnt(void)      { return m_sSysConfigData.nAddrMsgTxRetryCnt; }
    /**
     * @brief Get the admin password
     * @return The admin password
     */
    uint8_t* GetAdminPassword(void)         { return m_sSysConfigData.pstrPwdAdmin; }
    /**
     * @brief Get the user password
     * @return The user password
     */
    uint8_t* GetUserPassword(void)          { return m_sSysConfigData.pstrPwdUser; }
    /**
     * @brief Get the user key for SSA
     * @return The user key for SSA
     */
    uint8_t* GetUserKeySSA(void)            { return m_sSysConfigData.pstrUserKeySSA; }

    /**
     * @brief Get the internal antenna position
     * @return The internal antenna position
     */
    xANTPOS* GetIntAntennaPos(void) { return &m_sSysConfigData.sIntAntPos; }
    int16_t GetIntAntennaPosA(void) { return m_sSysConfigData.sIntAntPos.wA; }
    int16_t GetIntAntennaPosB(void) { return m_sSysConfigData.sIntAntPos.wB; }
    int16_t GetIntAntennaPosC(void) { return m_sSysConfigData.sIntAntPos.wC; }
    int16_t GetIntAntennaPosD(void) { return m_sSysConfigData.sIntAntPos.wD; }

    /**
     * @brief Get the external antenna position
     * @return The external antenna position
     */
    xANTPOS* GetExtAntennaPos(void) { return &m_sSysConfigData.sExtAntPos; }
    int16_t GetExtAntennaPosA(void) { return m_sSysConfigData.sExtAntPos.wA; }
    int16_t GetExtAntennaPosB(void) { return m_sSysConfigData.sExtAntPos.wB; }
    int16_t GetExtAntennaPosC(void) { return m_sSysConfigData.sExtAntPos.wC; }
    int16_t GetExtAntennaPosD(void) { return m_sSysConfigData.sExtAntPos.wD; }

    /**
     * @brief Get the extended antenna position
     * @return The extended antenna position
     */
    xANTPOS* GetExtendAntennaPos(void) { return &m_sSysConfigData.sExtendAntPos; }
    int16_t GetExtendAntennaPosA(void) { return m_sSysConfigData.sExtendAntPos.wA; }
    int16_t GetExtendAntennaPosB(void) { return m_sSysConfigData.sExtendAntPos.wB; }
    int16_t GetExtendAntennaPosC(void) { return m_sSysConfigData.sExtendAntPos.wC; }
    int16_t GetExtendAntennaPosD(void) { return m_sSysConfigData.sExtendAntPos.wD; }

    /**
     * @brief Set the port speed bitmap
     * @param nValue The port speed bitmap
     */
    void SetPortSpeedBitmap(int nValue)         { m_sSysConfigData.uPortSpeedBitmap = nValue; ReserveToSaveSysConfigData();}

    /**
     * @brief Set the enable external EPFS
     * @param bEnable The enable external EPFS
     */
    void SetEnableExtEPFS(bool bEnable)         { m_sSysConfigData.bEnableExtEPFS = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the enable external heading
     * @param bEnable The enable external heading
     */
    void SetEnableExtHeading(bool bEnable)      { m_sSysConfigData.bEnableExtHeading = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the enable external ROT
     * @param bEnable The enable external ROT
     */
    void SetEnableExtROT(bool bEnable)          { m_sSysConfigData.bEnableExtROT = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the enable alert 14
     * @param bEnable The enable alert 14
     */
    void SetEnableAlr14(bool bEnable)           { m_sSysConfigData.bEnableAlr14 = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the pilot port restricted
     * @param bEnable The pilot port restricted
     */
    void SetPilotPortRestricted(bool bEnable)   { m_sSysConfigData.bPilotPortRestricted = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the enable silent mode
     * @param bEnable The enable silent mode
     */
    void SetEnableSilentMode(bool bEnable)      { m_sSysConfigData.bEnableSilentMode = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the long range tx enable msg27
     * @param bEnable The long range tx enable msg27
     */
    void SetLongRangeTxEnableMsg27(bool bEnable){ m_sSysConfigData.bLongRangeTxEnableMsg27 = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the long range auto reply
     * @param bEnable The long range auto reply
     */
    void SetLongRangeAutoReply(bool bEnable)    { m_sSysConfigData.bLongRangeAutoReply = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the long range configuration
     * @param nValue The long range configuration
     */
    void SetLongRangeCfg(int32_t nValue)        { m_sSysConfigData.dwLongRangeCfg = nValue; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the long range channel 1
     * @param nValue The long range channel 1
     */
    void SetLongRangeCh1(int32_t nValue)        { m_sSysConfigData.dwLongRangeCh1 = nValue; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the long range channel 2
     * @param nValue The long range channel 2
     */
    void SetLongRangeCh2(int32_t nValue)        { m_sSysConfigData.dwLongRangeCh2 = nValue; ReserveToSaveSysConfigData(); } 

    /**
     * @brief Set the enable DGNSS by msg17
     * @param bEnable The enable DGNSS by msg17
     */
    void SetEnableDgnssByMsg17(bool bEnable){ m_sSysConfigData.bEnableDgnssByMsg17 = bEnable; ReserveToSaveSysConfigData(); }
    /**
     * @brief Set the show testing SART
     * @param bEnable The show testing SART
     */
    void SetShowTestingSART(bool bEnable)   { m_sSysConfigData.bShowTestingSART = bEnable; ReserveToSaveSysConfigData(); }

    /**
     * @brief Set the internal gnss type
     * @param nValue The internal gnss type
     */
    void SetIntGnssType(int16_t nValue) { 
        if(m_sSysConfigData.nIntGnssType != nValue) {
            m_sSysConfigData.nIntGnssType = nValue; 
            ReserveToSaveSysConfigData();
        }
    }
    /**
     * @brief Set the external gnss type
     * @param nValue The external gnss type
     */
    void SetExtGnssType(int16_t nValue) { 
        if(m_sSysConfigData.nExtGnssType != nValue) {
            m_sSysConfigData.nExtGnssType = nValue; 
            ReserveToSaveSysConfigData(); 
        }
    }
    /**
     * @brief Set the enable SBAS
     * @param bEnable The enable SBAS
     */
    void SetEnableSBAS(bool bEnable) { 
        if(m_sSysConfigData.bEnableSBAS != bEnable) {
            m_sSysConfigData.bEnableSBAS = bEnable; 
            ReserveToSaveSysConfigData();
        }
    }
    /**
     * @brief Set the long range time
     * @param nMMSI The MMSI to be set
     * @return True if success, false otherwise
     */
    bool SetLongRangeTime(int nMMSI);

    /**
     * @brief Set the admin password
     * @param pstrPwd The password to be set
     * @param nLen The length of the password
     */
    void SetAddminPassword(char *pstrPwd, int nLen);
    /**
     * @brief Check the admin password
     * @param pstrPwd The password to be compared
     * @param nLen The length of the password
     * @return True if matched, false otherwise
     */
    bool CheckAdminPassword(char *pstrPwd, int nLen);
    /**
     * @brief Set the user password
     * @param pstrPwd The password to be set
     * @param nLen The length of the password
     */
    void SetUserPassword(char *pstrPwd, int nLen);
    /**
     * @brief Check the user password
     * @param pstrPwd The password to be compared
     * @param nLen The length of the password
     * @return True if matched, false otherwise
     */
    bool CheckUserPassword(char *pstrPwd, int nLen);
    /**
     * @brief Set the user key for SSA
     * @param pstrKey The key to be set
     * @param nLen The length of the key
     */
    void SetUserKeySSA(char *pstrKey, int nLen);
    /**
     * @brief Check the user key for SSA
     * @param pstrKey The key to be compared
     * @param nLen The length of the key
     * @return True if matched, false otherwise
     */
    bool CheckUserKeySSA(char *pstrKey, int nLen);

    /**
     * @brief Set the internal antenna position
     * @param sAntPos The antenna position to be set
     */
    void SetIntAntennaPos(xANTPOS sAntPos) { m_sSysConfigData.sIntAntPos = sAntPos; }
    void SetIntAntennaPosA(int16_t nValue) { m_sSysConfigData.sIntAntPos.wA = nValue; }
    void SetIntAntennaPosB(int16_t nValue) { m_sSysConfigData.sIntAntPos.wB = nValue; }
    void SetIntAntennaPosC(int16_t nValue) { m_sSysConfigData.sIntAntPos.wC = nValue; }
    void SetIntAntennaPosD(int16_t nValue) { m_sSysConfigData.sIntAntPos.wD = nValue; }

    /**
     * @brief Set the external antenna position
     * @param sAntPos The antenna position to be set
     */
    void SetExtAntennaPos(xANTPOS sAntPos) { m_sSysConfigData.sExtAntPos = sAntPos; }
    void SetExtAntennaPosA(int16_t nValue) { m_sSysConfigData.sExtAntPos.wA = nValue; }
    void SetExtAntennaPosB(int16_t nValue) { m_sSysConfigData.sExtAntPos.wB = nValue; }
    void SetExtAntennaPosC(int16_t nValue) { m_sSysConfigData.sExtAntPos.wC = nValue; }
    void SetExtAntennaPosD(int16_t nValue) { m_sSysConfigData.sExtAntPos.wD = nValue; }

    /**
     * @brief Set the extended antenna position
     * @param sAntPos The antenna position to be set
     */
    void SetExtendAntennaPos(xANTPOS sAntPos) { m_sSysConfigData.sExtendAntPos = sAntPos; ReserveToSaveSysConfigData(); }
    void SetExtendAntennaPosA(int16_t nValue) { m_sSysConfigData.sExtendAntPos.wA = nValue; ReserveToSaveSysConfigData();}
    void SetExtendAntennaPosB(int16_t nValue) { m_sSysConfigData.sExtendAntPos.wB = nValue; ReserveToSaveSysConfigData();}
    void SetExtendAntennaPosC(int16_t nValue) { m_sSysConfigData.sExtendAntPos.wC = nValue; ReserveToSaveSysConfigData();}
    void SetExtendAntennaPosD(int16_t nValue) { m_sSysConfigData.sExtendAntPos.wD = nValue; ReserveToSaveSysConfigData();}

protected:
    CDataBackMgr   *m_pDataBackHw;
    CDataBackMgr   *m_pDataBackSys;
    CDataBackMgr   *m_pDataBackRos;
    CDataBackMgr   *m_pDataBackLog;

    RF_HW_CONFIG    m_sHwConfigData;
    SYS_CONFIG      m_sSysConfigData;

    UCHAR  *m_pSetupDataBuff;
    UCHAR  *m_pSetupVerifyBuff;

    bool    m_bSetupLoadError;

    bool    m_bSetupDataChgSys;
    bool    m_bSetupDataChgROS;
    bool    m_bSetupDataChgLog;
    bool    m_bSetupDataChgHwConfig;

    //--------------------------
    // not saved setup data
    //--------------------------
    INT16   m_nAddrMsgRetransmitCnt;
    bool    m_bAddrMsgRetransmitCntChg;
    DWORD   m_dwAddrMsgRetransmitCntChgSec;

public:
    //----------------------------------------------------------------------------------------
    // .xxxx : 0000 0000 0111 1111
    // .filed: ZDA,GGA,GBS,VTG,GLL,GSV,RMC
    //----------------------------------------------------------------------------------------
    SETUP_NMEA_OUT    m_sNmeaToMKD;
    SETUP_NMEA_OUT    m_sNmeaToEXT;
};

#endif//__SETUPMGR_H__
