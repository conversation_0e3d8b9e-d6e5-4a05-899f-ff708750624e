/**
 * @file    Timer.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <memory>
#include "SysConst.h"

#ifndef  __TIMER_H__
#define  __TIMER_H__

//=============================================================================
class cTimerSys
{
public:
    cTimerSys(TIM_TypeDef *pBaseAddr, DWORD dSysIrqNo, DWORD dRunFreq, DWORD dIrqEnable);
    virtual ~cTimerSys(void);

    static std::shared_ptr<cTimerSys> getInst() {
        static std::shared_ptr<cTimerSys> pInst = std::make_shared<cTimerSys>(TIM7, TIM7_IRQn, 48000, 1);	// 48 KHz interrupt
        return pInst;
    }

public:
    DWORD GetCounterValue(void);
    DWORD GetCurTimerSec();
    int   GetTimeDiffSec(DWORD dwOldTmrSec);
    int   GetTimeDiffSecToFuture(DWORD dwNewTmrSec);
    void  RunTimerIsrHandler(void);

protected:
    TIM_TypeDef       *m_pBaseAddr;
    TIM_HandleTypeDef  m_xTimerHand;
    DWORD              m_dSysIrqNo;
    DWORD              m_dRunFreq;

};
//=============================================================================

#endif /*__TIMER_H__*/
