#ifndef __EVENTLOGMGR_H__
#define __EVENTLOGMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"

//-------------------------------------------------------------------------------------------------
// IEC61993-2 6.6 Event log
// Means shall be provided to automatically record all periods when the AIS installation is not
// transmitting position reports according to required reporting interval as described in Table 2
// for more than 15 min, for instance when the power is switched off, when the AIS is in silent
// mode or not transmitting for other reasons.
// The last 10 times when the equipment was non-functioning for more than 15 min shall be
// recorded, in UTC time and duration, in a non-volatile event log. The recorded data shall be
// displayed on the MKD and output on request using the TRL sentence. It shall not be possible
// for the user to alter any information recorded in the event log.
//-------------------------------------------------------------------------------------------------
class CEventLogMgr
{
public:
    CEventLogMgr();
    ~CEventLogMgr();

    static std::shared_ptr<CEventLogMgr> getInst() {
        static std::shared_ptr<CEventLogMgr> pInst = std::make_shared<CEventLogMgr>();
        return pInst;
    }

public:
    void    ResetEventLog(EVENTLOG_DATA *pLogData);
    EVENTLOG_DATA *AddEventLog(int nEventLogID, EVENTLOG_DATA *pLogData, int nIndexToSave=-1);
    bool    CheckAvailableEventLog(EVENTLOG_DATA *pLogData);
    void    ClearEventLog(void);
    int     FindEventLog(int nEventLogID, SYS_DATE_TIME *pEventStartTime);

    int     SerializeEventLog(UCHAR *pBackData);
    bool    LoadEventLog(UCHAR *pBackData);
    void    VerifyEventLog(void);

    bool    CheckEventLogValidMMSI(void);
    bool    CheckEventLogRxOnlyMode(void);
    bool    CheckEventLogTxMulFunc(void);
    bool    CheckPowerOffTime(void);
    bool    SavePowerOffTime(void);
    void    RunPeriodicallyEventLog(void);

public:
    //-------------------------------------------------------------------------------------------------
    // IEC61993-2 6.6 Event log
    // Means shall be provided to automatically record all periods when the AIS installation is not
    // transmitting position reports according to required reporting interval as described in Table 2
    // for more than 15 min, for instance when the power is switched off, when the AIS is in silent
    // mode or not transmitting for other reasons.
    #define EVENTLOG_CHECKTIMESEC   900

    EVENTLOG_DATA  *m_pLogData;

private:
    SYS_DATE_TIME   m_sPowerOffTime;
    bool            m_bPowerOffTimeUTC;

    SYS_DATE_TIME   m_sMMSINullTime;
    bool            m_bMMSINullTimeUTC;

    SYS_DATE_TIME   m_sRxOnlyStartTime;
    bool            m_bRxOnlyStartTimeUTC;

    SYS_DATE_TIME   m_sTxMulFuncStartTime;
    bool            m_bTxMulFuncStartTimeUTC;

    bool            m_bCheckEventLogDone;
};

#endif  //__EVENTLOGMGR_H__
