/**
 * @file    Alc.cpp
 * @brief   Alc class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "AlarmThing.h"
#include "MKD.h"
#include "Alc.h"

#define MAX_ALC_ENTRIES_COUNT   5

/******************************************************************************
 * 
 * ALC - Cyclic alert list
 *
 * $--ALC,xx,xx,xx,x.x,aaa,x.x,x.x,x.x,……,aaa,x.x,x.x,x.x*hh<CR><LF>
 *        |  |  |  |   |   |   |   |   |  |___________|
 *        1  2  3  4   5   6   7   8   9  10
 *                     |_______________|
 *                     Alert entry 1
 *
 * 1. Total number of sentences for this message, 01 to 99
 * 2. Sentence number, 01 to 99
 * 3. Sequential message identifier, 00 to 99
 * 4. Number of alert entries
 * 5. Manufacturer mnemonic code
 * 6. Alert identifier
 * 7. Alert instance
 * 8. Revision counter
 * 9. Additional Alert entries
 * 10. Alert entry n
 *
 ******************************************************************************/
// Define static member variables
int8_t CAlc::m_nSequentialId = 0;

 CAlc::CAlc() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAlc::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the ALC sentence
 * @param pAlarmList The alarm list
 * @param nSizeAlarmList The size of the alarm list
 * @return The length of the sentence
 */
int32_t CAlc::MakeSentence(CAlarmThing **pAlarmList, const int nSizeAlarmList)
{
    //------------------------------------------------------------------------------------------------------
    // IEC 61162-1:2016 RLV 7.3 Sentence 7.3.1 General structure
    //------------------------------------------------------------------------------------------------------
    // The maximum number of characters in a sentence shall be 82, consisting of a maximum of 79 characters
    // between the starting delimiter "$" or "!" and the terminating delimiter <CR><LF>
    //------------------------------------------------------------------------------------------------------
    int iAlert = 0;
    int iSentence = 1;
    int nNumEntries = 0;
    int nNumSentences = 1;
    char pstrOutMsg[RX_MAX_DATA_SIZE];
    char pstrTmp[RX_MAX_DATA_SIZE];

    memset(pstrOutMsg, 0x00, sizeof(pstrOutMsg));

    int nTotalAlertEntries = 0;
    for(int i = 0 ; i < nSizeAlarmList ; i++)
    {
        if(pAlarmList[i]->GetAlarmStatus() != BAM_ALERT_STAT_NORMAL)
            nTotalAlertEntries++;
    }

    nNumSentences = nTotalAlertEntries / MAX_ALC_ENTRIES_COUNT;
    if(nTotalAlertEntries % MAX_ALC_ENTRIES_COUNT)
        nNumSentences++;
    if(nNumSentences <= 0)
        nNumSentences = 1;

    do
    {
        nNumEntries = 0;
        memset(pstrTmp, 0x00, sizeof(pstrTmp));

        do
        {
            if(pAlarmList[iAlert]->GetAlarmStatus() != BAM_ALERT_STAT_NORMAL)
            {
                sprintf(pstrTmp, ",,%d,%d,%d",
                        pAlarmList[iAlert]->GetBAMAlertID(), 
                        pAlarmList[iAlert]->GetBAMAlertInstance(), 
                        pAlarmList[iAlert]->GetRevisionCounter());

                ++nNumEntries;
            }

            ++iAlert;
        } while ((nNumEntries < MAX_ALC_ENTRIES_COUNT) && (iAlert < nSizeAlarmList));

        sprintf(pstrOutMsg, "$AIALC,%02d,%02d,%02d,%d", nNumSentences, iSentence, GetSequentialId(), nNumEntries);
        strcat(pstrOutMsg, pstrTmp);
        CSentence::AddSentenceTail(pstrOutMsg);
        CMKD::getInst()->SendAllHighSpdPortData((BYTE*)pstrOutMsg, strlen(pstrOutMsg), false);

        ++iSentence;
    } while (iAlert < nSizeAlarmList);

    IncSequentialId();

    return strlen(pstrOutMsg);
}