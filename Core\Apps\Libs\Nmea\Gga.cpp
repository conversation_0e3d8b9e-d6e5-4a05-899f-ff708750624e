/**
 * @file    Gga.cpp
 * @brief   Gga class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Gga.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * GGA - 
 *
 * $--GGA, hhmmss.ss, llll.ll,a,yyyyy.yy,a,x,xx,x.x,x.x,M,x.x,M,x.x,xxxx*hh<CR><LF>
 *          |            |----|     |----| |  |  |   |  |  |  |  |    |
 *          1            2          3      4  5  6   7  8  9  10 11   12
 *
 * 1. UTC of position
 * 2. Latitude N/S
 * 3. Longitude E/W
 * 4. GPS quality indicator
 * 5. Number of satellites in use, 00 12, may be different from the number in view
 * 6. Horizontal dilution of precision
 * 7. Antenna altitude above/below mean sea level (geoid)
 * 8. Units of antenna altitude, m
 * 9. Geoidal separation
 * 10. Units of geoidal separation, m
 * 11. Age of differential GPS data
 * 12. Differential reference station ID, 0000-1023
 * 
 ******************************************************************************/
CGga::CGga() : CSentence()
{
    ClearData();
}

void CGga::ClearData(void)
{
	CAisLib::SetDefaultSysTime(&m_xUtcTime);

    m_rRcvLatVal = 0.0;
    m_rRcvLonVal = 0.0;

    m_uPosModeIndi = POS_MODE_NONE;

    m_dwPosModeTick = 0;
    m_dwPosValidTick = 0;
    m_dwPosValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CGga::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    bool bFixed = 0;
    int  nHour,nMin,nSec;
    char vLatBuf[32];
    char vLonBuf[32];

    GetFieldString(pszSentence, 1, pstrTmp);     // UTC of position
    nHour   = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
    nMin    = (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
    nSec    = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';

    BOOL bTimeStampValid = (strlen(pstrTmp) >= 6 && CAisLib::IsValidAisSysTime(nHour, nMin, nSec));
    if(bTimeStampValid)
    {
        m_xUtcTime.nHour = nHour;
        m_xUtcTime.nMin    = nMin;
        m_xUtcTime.nSec    = nSec;
    }
    else
    {
    	CAisLib::SetDefaultSysTime(&m_xUtcTime);
    }

    GetFieldString(pszSentence, 2, pstrTmp);     // Latitude
    memmove(vLatBuf,pstrTmp,12);
    vLatBuf[12] = 0x00;

    GetFieldString(pszSentence, 3, pstrTmp);     // N or S
    vLatBuf[15] = pstrTmp[0];

    GetFieldString(pszSentence, 4, pstrTmp);     // Longitude
    memmove(vLonBuf,pstrTmp,12); vLonBuf[12] = 0x00;

    GetFieldString(pszSentence, 5, pstrTmp);     // E or W
    vLonBuf[15] = pstrTmp[0];

    GetFieldString(pszSentence, 6, pstrTmp);     // GPS quality indicator
    if(strlen(pstrTmp) > 0)
    {
        m_uPosModeIndi = CGps::ParsePosModeIndicatorGGA(pstrTmp[0]);     // '0','1','2'
        m_dwPosModeTick = SysGetSystemTimer();
    }
    else
    {
        m_uPosModeIndi = POS_MODE_NONE;
        m_dwPosModeTick = 0;
    }

    //GetFieldString(pszSentence, 7, pstrTmp);     // Number of satellites in use
    //GetFieldString(pszSentence, 8, pstrTmp);     // Horizontal dilution of precision

    GetFieldString(pszSentence, 9, pstrTmp);     // Antenna altitude above/below mean sea level (geoid)
    if(strlen(pstrTmp) > 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
        {
            return false;
        }
    }

    //GetFieldString(pszSentence, 10, pstrTmp);    // Units of antenna altitude, m
    //GetFieldString(pszSentence, 11, pstrTmp);    // Geoidal separation
    //GetFieldString(pszSentence, 12, pstrTmp);    // Units of geoidal separation, m
    //GetFieldString(pszSentence, 13, pstrTmp);    // Age of differential GPS data

    GetFieldString(pszSentence, 14, pstrTmp);    // Differential reference station ID,
    if(bTimeStampValid && CGps::IsModeIndicatorTrustable(m_uPosModeIndi))
    {
        bFixed = true;

        if(ConvertPosition(vLatBuf, vLonBuf, &m_rRcvLatVal, &m_rRcvLonVal))
        {
            m_dwPosValidTick = SysGetSystemTimer();
        }
    }

    return (bFixed);
}

/**
 * @brief Check received position data is valid or not
 */
bool CGga::IsValidPosData(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_DATA_VALID_TIMEMS)
    {
        return true;
    }

    return false;
}

/**
 * @brief Get position mode indicatior
 */
UINT8 CGga::GetPosModeIndi(void)
{
    return m_uPosModeIndi;
}

/**
 * @brief Get position mode tick counter
 */
DWORD CGga::GetPosModeTick(void)
{
    return m_dwPosValidTick;
}

/**
 * @brief Get latitude positon
 */
double CGga::GetLatVal(void)
{
    return m_rRcvLatVal;
}

/**
 * @brief Get longitude position
 */
double CGga::GetLonVal(void)
{
    return m_rRcvLonVal;
}

/**
 * @brief Get UTC time
 */
SYS_TIME CGga::GetUtcTime(void)
{
    return m_xUtcTime;
}

/**
 * @brief Call function periodically
 */
void CGga::RunPeriodically(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosValidTick = 0;
    }

    if (m_dwPosModeTick && SysGetDiffTimeMili(m_dwPosModeTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosModeTick = 0;
        m_uPosModeIndi = 0;
    }
}
