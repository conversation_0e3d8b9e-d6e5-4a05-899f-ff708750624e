/**
 * @file    Hbt.h
 * @brief   Hbt header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "SysLib.h"
#include "Sentence.h"

#ifndef __HBT_H__
#define __HBT_H__

#define HBT_INTERVAL_DFLT   30    // 30sec

/*****************************************************************************
 *
 * HBT - Heartbeat supervision sentence
 *
 * $--HBT,x.x,A,x*hh<CR><LF>
 *         |  | |
 *         1  2 3
 *
 * 1. Configured repeat interval
 * 2. Equipment status
 * 3. Sequential sentence identifier
 *
 ******************************************************************************/
class CHbt : public CSentence
{
public:
    CHbt();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    bool Parse(const char *pszSentence);

    /**
     * @brief Make the HBT sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    int32_t MakeSentence(char *pszSentence);

    /**
     * @brief Get the normal operation flag
     * @return The normal operation flag
     */
    bool GetNormalOp(void) { return m_bNormalOp; }

    /**
     * @brief Get the configured repeat interval
     * @return The configured repeat interval
     */
    int16_t GetRepeatInterval(void) { return m_nRepeatInterval; }

    bool IsTimeout(void)
    {
        return (SysGetDiffTimeScnd(m_dwRcvTick) > m_nRepeatInterval+HBT_INTERVAL_DFLT);
    }

public:
    bool    m_bNormalOp;
    int16_t m_nRepeatInterval;
    uint32_t m_dwRcvTick;
};

#endif /* __HBT_H__ */

