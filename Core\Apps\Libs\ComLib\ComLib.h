/**
 * @file    ComLib.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */


#include "DataType.h"

#ifndef  __COMLIB_H__
#define  __COMLIB_H__

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
int   MakeSizeOfDoubleWord(int nSize);
BOOL  RangeCheckBack08(BACK08 *pDataValue,BACK08 nLower,BACK08 nUpper,BACK08 nSetValue);
BOOL  RangeCheckBack16(BACK16 *pDataValue,BACK16 nLower,BACK16 nUpper,BACK16 nSetValue);
BOOL  RangeCheckBack32(BACK32 *pDataValue,BACK32 nLower,BACK32 nUpper,BACK32 nSetValue);
BOOL  RangeCheckBackDD(BACKDD *pDataValue,BACKDD nLower,BACKDD nUpper,BACKDD nSetValue);
DWORD GetCrc32(const UCHAR *pData,DWORD dSize);
DWORD GetCrc32Cont(const UCHAR *pData,DWORD dSize,DWORD dPrevCRC);
DWORD GetUnAlignedLongData(UCHAR *pData);
INT32 PowerOf10(int nPower);
void  SwapInt(int *pX,int *pY);
void  SwapLong(long *pX,long *pY);
void  SwapReal(REAL *pX,REAL *pY);
void  SwapFloat(float *pX,float *pY);
int   CirCularDec(int nValue,int nLast);
int   CirCularInc(int nValue,int nLast);
int   GetCircularSize(int nHead,int nTail,int nSize);
int   GetMinInt(int X,int Y);
int   GetMaxInt(int X,int Y);
int   IsAllSameCharacters(char *pStr,char bChr);
int   IsAllDigit(char *pData,int nSize,char bFill);
int   IsAllAscii(char *pData,int nSize,char bFill);
void  IsOneAscii(char *pData,int nSize,char bFill);
int   IsInRangeLong(int nData,int nMin,int nMax);
void  RemoveNullChar(char *pData,int nSize,char bFill);
char *FullAllTrimStr(char *pStr);
char *RightTrimStr(char *pStr,char bTrimChar);
//char *RightAlignStr(char *pStr,int nLen);
int   StrToInt(char *pStr,int nStart,int nLen);
char  UpperChar(char bData);
char  LowerChar(char bData);
void  UpperString(char *pStr);
void  LowerString(char *pStr);
UCHAR HexStrToByte(char *pHexStr);
DWORD HexStrToLong(char *pHexStr,int nSize);
UCHAR GetHexDigit(int nData,int nFlag);
char *ByteToBinStr(UCHAR bData);
char *ByteToHexStr(UCHAR bData,int nFlag);
char *ByteArrayToHexStr(BYTE *pData, int nLen, int nFlag, char *pOutBuff);
char *WordToHexStr(HWORD wData,int nFlag);
char *LongToHexStr(DWORD dData,int nFlag);
int   GetReverseBitValue(UCHAR cReverseBitData);
//========================================================================
float RemezSinFloat32(float x);
float RemezCosFloat32(float x);
//========================================================================
UCHAR CalcNmeaCheckSum(UCHAR *pData, UCHAR *pX, UCHAR *pY);
void  MakeNmeaCheckSum(UCHAR *pData, int nAppendCS, int nAppendCrLf);
//========================================================================

#ifdef  __cplusplus
}
#endif

#endif

