#ifndef  __GNSSINTERNAL_H__
#define  __GNSSINTERNAL_H__

#include "DataType.h"
#include "SysConst.h"
#include "SysLib.h"
#include "ComLib.h"
#include "Uart.h"
#include "GpsBoard.h"
#include "AisMsg.h"

#define DGNSS_HEALTH_UNHEALTHY      7
#define DGNSS_ZCOUNT_MAX         5999       // time value in 0.6 sec 0-3599.4 sec
#define DGNSS_TIMEOUT_SEC          45       // default value of dgnssTimeout of CFG-NAV5, Ublox
#define MAX_DGNSSST_DIST          100

class CGnssInternal : public cGpsBoard
{
public:
    CGnssInternal(INT8 nSensorID, cUart *pUartPort);
    virtual ~CGnssInternal(void);

public:
    void    InitializeRcvr();
    void    SetGnssConfig(BYTE bGnssConfig, BOOL bEnableSBAS);

    BOOL    IsDgnssCorrectedByMsg17(void);
    BOOL    IsDgnssCorrectedByBeacon(void);
    void    SetDgnssDataSrcBaseSt(UINT uBaseMMSI, POS_ALLH *psRefStPos=NULL, WORD wZcount=DGNSS_ZCOUNT_MAX, BYTE bHealth=DGNSS_HEALTH_UNHEALTHY);
    void    CheckDgnssRefStLifeTime();
    BOOL    IsMsg17RefStAvailable(xAISMSG17 *pRxMsg17);
    BOOL    IsRcvrBackupDateTimeValid();

    virtual void    SetUtcDate(int nYear,int nMonth,int nDay);
    virtual void    SetUtcTime(int nHour,int nMin,int nSec);

    virtual void    UpdatePosByPriority();

    virtual int     ProcessSentence(char *pstrCmd);
    virtual void    RunPeriodicallyGps();

    virtual int     GetTypeOfEPFS();

public:
    SYS_DATE_TIME   m_sGnssDateTime;

    UINT            m_uMsg17BaseStId;
    POS_ALLH        m_sMsg17RefStPos;
    WORD            m_wMsg17RefStZcount;
    BYTE            m_bMsg17RefStHealth;
    DWORD           m_dwDgnssByMsg17Sec;
};

#endif /*__GNSSINTERNAL_H__*/
