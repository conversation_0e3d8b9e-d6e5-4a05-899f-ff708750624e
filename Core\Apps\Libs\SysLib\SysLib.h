/**
 * @file    SysLib.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include "SysConst.h"

#include <sys\stat.h>

#ifndef  __SYSLIB_H__
#define  __SYSLIB_H__

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
void  SysEnableIRQ(void);
void  SysDisableIRQ(void);
void  SysReSetSystem(void);
//========================================================================
void  SysTimerInit(TIM_TypeDef *pTimer);
//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt);
void  SysDelay10MicroSec(DWORD dDelay10Micro);
void  SysDelayMicroSec(DWORD dDelayMicro);
void  SysDelayMiliSec(DWORD dDelayMili);
DWORD SysGetFreeRunTimerCount(void);
DWORD SysGetFreeRunTimerDiffCount(DWORD dOldCount, DWORD dNewCount);
float SysGetFreeRunTimerDiffMicro(DWORD dOldCount, DWORD dNewCount);
//========================================================================
DWORD SysGetSystemTimer(void);
DWORD SysIncSystemTimer(void);
//========================================================================
DWORD SysCalcTickToMili(DWORD dTick);
DWORD SysCalcMiliToTick(DWORD dMili);
DWORD SysCalcTickToScnd(DWORD dTick);
DWORD SysGetDiffTimeTick(DWORD dTime);
DWORD SysGetDiffTimeMili(DWORD dTime);
DWORD SysGetDiffTimeScnd(DWORD dTime);
//========================================================================
void  SysSetRunOSStatus(BOOL bRunOS);
void  RunSystemDelayMs(DWORD dwDelayMs);
//========================================================================
void *SysAllocMemory(DWORD dAllocSize);
//========================================================================

#ifdef  __cplusplus
}
#endif

#endif

