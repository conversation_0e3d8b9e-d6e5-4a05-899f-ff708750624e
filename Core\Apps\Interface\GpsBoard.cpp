/**
 * @file    GpsBopard.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include "DataType.h"
#include "AllConst.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "ComLib.h"
#include "SetupMgr.h"
#include "AisLib.h"
#include "Timer.h"
#include "MKD.h"
#include "GpsBoard.h"

cGpsBoard::cGpsBoard(INT8 nSensorID) : cNMEA(nSensorID)
{
    m_pUartPort = NULL;
    m_nSensorID = nSensorID;

    SetGpsStatus(GPS_FIX_STATUS_LOST);
    m_bEnableEPFS = true;
    m_nTypeOfEPFS = AIS_EPFD_UNDEFINED;

    m_nRxSize   = 0;
    m_pRxData   = (UCHAR*)SysAllocMemory(sizeof(UCHAR) * RX_MAX_DATA_SIZE);

    SetBoardType(GPS_EXTERNAL);
    SetComSpdModeFound(1);

    SetLastRcvTime(0);
    m_dwLastPeriodicCheckSec = 0;

    m_pHdgBuff  = (HDGDATA*)SysAllocMemory(sizeof(HDGDATA) * HDGSAVE_BUFF_SIZE);
    ResetHdgBuffer(NMEA_HDG_NULL);
    m_dwHdgValidTick = 0;
    m_bHdgValid = false;

    m_nPosModeIndicator = POS_MODE_NONE;
    m_nPosModeIndiTick  = 0;
    m_bPosAccFlag = false;
    m_bRAIMflag   = false;

    m_nRotSrcType = ROTSCR_NONE;
    m_bRotValid = false;
    m_dwRotValidTick = 0;

    CAisLib::SetDefaultSysDate(&m_xUtcDate);
    CAisLib::SetDefaultSysTime(&m_xUtcTime);
    m_bTimeStampValid = false;

    SetLatVal(AIS_REAL_LAT_NULL_VAL);
    SetLonVal(AIS_REAL_LON_NULL_VAL);
    SetSpdVal(NMEA_SOG_NULL);
    SetVelVal(NMEA_SOG_NULL);
    SetCrsVal(NMEA_COG_NULL);
    SetHdgVal(NMEA_HDG_NULL);
    SetRotVal(NMEA_ROT_NULL);
}

cGpsBoard::cGpsBoard(INT8 nSensorID, cUart *pUartPort) : cNMEA(nSensorID)
{
    m_pUartPort = pUartPort;
    m_nRxSize   = 0;
    m_pRxData   = (UCHAR*)SysAllocMemory(sizeof(UCHAR) * RX_MAX_DATA_SIZE);

    SetBoardType(GPS_EXTERNAL);
    SetComSpdModeFound(1);

    SetLastRcvTime(0);
    m_dwLastPeriodicCheckSec = 0;

}

cGpsBoard::~cGpsBoard(void)
{
}

bool cGpsBoard::IsInternalGnss(void)
{
    return (m_nSensorID == SENSORID_INTERNAL);
}

bool cGpsBoard::IsExternalGnss()
{
    return (m_nSensorID == SENSORID_1);
}

int cGpsBoard::GetBoardType(void)
{
    return(m_nBoardType);
}

void cGpsBoard::SetBoardType(int nType)
{
    m_nBoardType = nType;
}

int cGpsBoard::GetComSpdModeFound(void)
{
    return(m_nComSpdFound);
}

void cGpsBoard::SetComSpdModeFound(int nFound)
{
    m_nComSpdFound = nFound;
}

bool cGpsBoard::IsConnected(void)
{
    return (SysGetDiffTimeScnd(m_dwLastRcvTick) < 120);
}

DWORD cGpsBoard::GetLastRcvTime(void)
{
    return(m_dwLastRcvTick);
}
void cGpsBoard::SetLastRcvTime(DWORD dTime)
{
    m_dwLastRcvTick = dTime;
}

bool cGpsBoard::CheckPortAvailable(void)
{
    if(m_pUartPort)
        return (GetComSpdModeFound() != 0);
    return false;
}

int cGpsBoard::GetPortDataByte(void)
{
    if(m_pUartPort)
        return m_pUartPort->GetComData();
    return UART_NULL_CHAR;
}

void cGpsBoard::SendData(BYTE *pData, INT16 nLength)
{
    if(m_pUartPort)
    {
        m_pUartPort->WriteComData(pData, nLength);
        return;
    }
}

int cGpsBoard::GetLoopBackPortID(void)
{
    switch(m_nSensorID)
    {
    case SENSORID_1:
        return LOOPBACK_PORT_ID_SENSOR1;
    case SENSORID_2:
        return LOOPBACK_PORT_ID_SENSOR2;
    case SENSORID_3:
        return LOOPBACK_PORT_ID_SENSOR3;
    }
    return LOOPBACK_PORT_ID_NA;
}

void cGpsBoard::ProcRunComLoopBackResp(char *pstrCmd)
{
    //-----------------------------------------
    // $AIINL,LBP,%d
    //-----------------------------------------
    //  %d : Loop-back test를 실행할 타겟 포트
    //         0 : Sensor1
    //         1 : Sensor2
    //         2 : Sensor3
    //         3 : Long - Range Port
    //         4 : External display Port
    //         5 : Pilot Port
    //-----------------------------------------
    char pstrSubData1[RX_MAX_DATA_SIZE];
    int  nPortID;

    CSentence::GetFieldString(pstrCmd, 2, pstrSubData1, sizeof(pstrSubData1));
    nPortID = atoi(pstrSubData1);

    if(nPortID == GetLoopBackPortID())
    {
        CMKD::getInst()->SendOutStr(pstrCmd);                // send it back
        DEBUG_LOG("[Sensor-%d] loopback ID OK! : %d, ", m_nSensorID, nPortID);
    }
    else
    {
        WARNING_LOG("[Sensor-%d] loopback ID mismatch : %d, ", m_nSensorID, nPortID);
    }
}

/*****************************************************************/
int cGpsBoard::GetGpsStatus(void)
{
    return(m_nGpsStatus);
}

void cGpsBoard::SetGpsStatus(int nStatus)
{
    m_nGpsStatus = nStatus;
}

bool cGpsBoard::IsDgnssCorrected(void)
{
    return (GetGpsStatus() == GPS_FIX_STATUS_DGPS);
}

/// @brief 
/// @return 
bool cGpsBoard::IsEnabledEPFS(void)
{
    return m_bEnableEPFS;
}

void cGpsBoard::SetEnableEPFS(bool bEnable)
{
    m_bEnableEPFS = bEnable;
}

void cGpsBoard::UpdateTypeOfEPFS(char *pstrSentence)
{
    char pstrTalkerID[LEN_NMEA_TALKER];
    char pstrSentID[LEN_NMEA_SENT];
    CSentence::GetTalkerSenderID((char*)pstrSentence, pstrTalkerID, pstrSentID);

    if(!strcmp(pstrTalkerID, "GP"))
        m_nTypeOfEPFS = AIS_EPFD_GPS;
    else if(!strcmp(pstrTalkerID, "GL"))
        m_nTypeOfEPFS = AIS_EPFD_GLONASS;
    else if(!strcmp(pstrTalkerID, "GN"))
        m_nTypeOfEPFS = AIS_EPFD_GPS_GLONASS;
    else if(!strcmp(pstrTalkerID, "LC"))
        m_nTypeOfEPFS = AIS_EPFD_LORAN_C;
    else if(!strcmp(pstrTalkerID, "IN"))
        m_nTypeOfEPFS = AIS_EPFD_INS;
    else if(!strcmp(pstrTalkerID, "GA"))
        m_nTypeOfEPFS = AIS_EPFD_GALILEO;
    else
        m_nTypeOfEPFS = AIS_EPFD_UNDEFINED;
}

void cGpsBoard::SetTypeOfEPFS(int nType)
{
    m_nTypeOfEPFS = nType;
}

int cGpsBoard::GetTypeOfEPFS(void)
{
    return m_nTypeOfEPFS;
}

bool cGpsBoard::IsUtcDateValid(void)
{
    return CAisLib::IsValidAisSysDate(&m_xUtcDate);
}

bool cGpsBoard::IsUtcTimeValid(void)
{
    return CAisLib::IsValidAisSysTime(m_xUtcTime.nHour, m_xUtcTime.nMin, m_xUtcTime.nSec);
}

void cGpsBoard::SetUtcDate(int nYear,int nMonth,int nDay)
{
    if(CAisLib::IsValidAisSysDate(nYear, nMonth, nDay))
    {
        m_xUtcDate.nYear = nYear;
        m_xUtcDate.nMon  = nMonth;
        m_xUtcDate.nDay  = nDay;
        m_bTimeStampValid= true;
    }
    else
    {
    	CAisLib::SetDefaultSysDate(&m_xUtcDate);
        m_bTimeStampValid= false;
    }
}

void cGpsBoard::SetUtcDate(SYS_DATE xDate)
{
	SetUtcDate(xDate.nYear, xDate.nMon, xDate.nDay);
}

void cGpsBoard::SetUtcTime(int nHour,int nMin,int nSec)
{
    if (CAisLib::IsValidAisSysTime(nHour, nMin, nSec))
    {
        m_xUtcTime.nHour = nHour;
        m_xUtcTime.nMin  = nMin;
        m_xUtcTime.nSec  = nSec;
    }
    else
    {
    	CAisLib::SetDefaultSysTime(&m_xUtcTime);
    }
}

void cGpsBoard::SetUtcTime(SYS_TIME sTime)
{
    SetUtcTime(sTime.nHour, sTime.nMin, sTime.nSec);
}

void cGpsBoard::SetUtcDateNull(void)
{
	CAisLib::SetDefaultSysDate(&m_xUtcDate);
}

void cGpsBoard::SetUtcTimeNull(void)
{
	CAisLib::SetDefaultSysTime(&m_xUtcTime);
}

int cGpsBoard::GetUtcYear(void)
{
    return m_xUtcDate.nYear;
}

int cGpsBoard::GetUtcMon(void)
{
    return m_xUtcDate.nMon;
}

int cGpsBoard::GetUtcDay(void)
{
    return m_xUtcDate.nDay;
}

int cGpsBoard::GetUtcHour(void)
{
    return m_xUtcTime.nHour;
}

int cGpsBoard::GetUtcMin(void)
{
    return m_xUtcTime.nMin;
}

int cGpsBoard::GetUtcSec(void)
{
    return m_xUtcTime.nSec;
}

int cGpsBoard::GetUtcTimeStamp(void)
{
    int nTmpStamp = AIS_TIME_STAMP_DEFAULT;            // Ivan Fault-#184

    int nPosMode = GetPosModeIndicator();
    if(IsPosUtcFixed())
    {
        switch(nPosMode)
        {
        case POS_MODE_MANUAL:
            nTmpStamp = AIS_TIME_STAMP_MANUAL;
            break;
        case POS_MODE_SIM:
            nTmpStamp = AIS_TIME_STAMP_DEFAULT;//AIS_TIME_STAMP_INVALID;
            break;
        case POS_MODE_ESTIMATED:
            nTmpStamp = AIS_TIME_STAMP_ESTIMATED;
            break;
        default:
            if(IsUtcTimeValid())
                nTmpStamp = m_xUtcTime.nSec;
            else
                nTmpStamp = AIS_TIME_STAMP_DEFAULT;
            break;
        }
    }

    return nTmpStamp;
}




bool cGpsBoard::IsDatumValid(void)
{
    return m_hDtm.IsValidDatumLoc();
}

bool cGpsBoard::IsPosValid(void)
{
    return (IsDatumValid() && GetGpsStatus() != GPS_FIX_STATUS_LOST); 
}

bool cGpsBoard::IsPosUtcFixed(void)
{
    return (IsUtcTimeValid() && GetGpsStatus() != GPS_FIX_STATUS_LOST);
}

bool cGpsBoard::IsPosFixModeTrustable(int nPosMode)
{
    return (m_bTimeStampValid && CGps::IsModeIndicatorTrustable(nPosMode));
}

bool cGpsBoard::IsPosFixModeTrustable(void)
{
    return IsPosFixModeTrustable(GetPosModeIndicator());
}

int cGpsBoard::GetPosModeIndicator(void)
{
    return m_nPosModeIndicator;
}

bool cGpsBoard::GetPosAccFlag(void)
{
    return m_bPosAccFlag;
}

bool cGpsBoard::GetRaimFlag(void)
{
    return m_bRAIMflag;
}

void cGpsBoard::ResetHdgBuffer(int nCurHdg)
{
    m_nHdgBuffHead = 0;
    m_nNumHdgData  = 0;
}

void cGpsBoard::SaveHdgBuffer(int nHdg)
{
    if(nHdg == NMEA_HDG_NULL)
    {
        m_nHdgBuffHead = 0;
        m_nNumHdgData  = 0;
    }
    else
    {
        m_pHdgBuff[m_nHdgBuffHead].nHdgData = nHdg;
        m_pHdgBuff[m_nHdgBuffHead].dwRcvSec = cTimerSys::getInst()->GetCurTimerSec();
        if(++m_nHdgBuffHead >= HDGSAVE_BUFF_SIZE)
            m_nHdgBuffHead = 0;
        if(m_nNumHdgData < HDGSAVE_BUFF_SIZE)
            m_nNumHdgData++;
    }
}

int cGpsBoard::GetRotDataFromHdgDiff(void)
{
    float fROT = NMEA_ROT_NULL;
    const float SCALE_HDG_TO_ROT = (NMEA_SCALE_ROT / NMEA_SCALE_HDT);
    float fHdgDiff = NMEA_HDG_NULL;
    int nElapSec = -1;

    if(m_nNumHdgData > 1)
    {
        int nStart, nEnd;
        if(m_nNumHdgData < HDGSAVE_BUFF_SIZE)
        {
            nStart = 0;
            nEnd = m_nHdgBuffHead-1;
        }
        else
        {
            nStart = m_nHdgBuffHead;
            nEnd = m_nHdgBuffHead-1;
            if(nEnd < 0)
                nEnd += HDGSAVE_BUFF_SIZE;
        }

        fHdgDiff = CAisLib::GetDiffYaw((float)m_pHdgBuff[nEnd].nHdgData/NMEA_SCALE_HDT, (float)m_pHdgBuff[nStart].nHdgData/NMEA_SCALE_HDT);
        if(fHdgDiff != NMEA_HDG_NULL)
        {
            DWORD dwSecOld = m_pHdgBuff[nStart].dwRcvSec;
            DWORD dwSecNew = m_pHdgBuff[nEnd].dwRcvSec;
            if(dwSecNew < dwSecOld)
            {
                DWORD dwTmp = dwSecOld;
                dwSecOld = dwSecNew;
                dwSecNew = dwTmp;
            }
            nElapSec = CAisLib::GetDiffDwordMax(dwSecOld, dwSecNew);
            if(nElapSec > 0)
                fROT = fHdgDiff / nElapSec * 60 * SCALE_HDG_TO_ROT;
        }
    }

    return (int)fROT;
}

bool cGpsBoard::IsCrsValid(void)
{
    return (m_nCrsVal != NMEA_COG_NULL);
}

bool cGpsBoard::IsSpdValid(void)
{
    return (m_nSpdVal != NMEA_SOG_NULL);
}

bool cGpsBoard::IsRotValid(void)
{
    return m_bRotValid && SysGetDiffTimeMili(m_dwRotValidTick) <= NMEA_ROT_LASTDATA_STAYMS;
}

bool cGpsBoard::IsHdgValid(void)
{
    return (m_bHdgValid && SysGetDiffTimeMili(m_dwHdgValidTick) <= NMEA_HDG_LASTDATA_STAYMS);
}

REAL cGpsBoard::GetLatVal(void)
{
    return(m_rLatVal);
}

void cGpsBoard::SetLatVal(REAL rVal)
{
    m_rLatVal = rVal;
}

REAL cGpsBoard::GetLonVal(void)
{
    return(m_rLonVal);
}

void cGpsBoard::SetLonVal(REAL rVal)
{
    m_rLonVal = rVal;
}

int cGpsBoard::GetSpdVal(void)
{
    return(m_nSpdVal);
}

void cGpsBoard::SetSpdVal(int nVal)
{
    m_nSpdVal = nVal;
}

int cGpsBoard::GetVelVal(void)
{
    return(m_nVelVal);
}

void cGpsBoard::SetVelVal(int nVal)
{
    m_nVelVal = nVal;
}

int cGpsBoard::GetCrsVal(void)
{
    return(m_nCrsVal);
}

void cGpsBoard::SetCrsVal(int nVal)
{
    m_nCrsVal = nVal;
}

int cGpsBoard::GetHdgVal(void)
{
    return m_nHdgVal;
}

void cGpsBoard::SetHdgVal(int nVal)
{
    m_nHdgVal = nVal;
}

int cGpsBoard::GetRotVal(void)
{
    return(m_nRotVal);
}
void cGpsBoard::SetRotVal(int nVal)
{
    m_nRotVal = nVal;
}

bool cGpsBoard::IsRotOtherSrc(void)
{
    return (m_nRotSrcType != ROTSCR_TI);
}

int cGpsBoard::GetRotSrcType(void)
{
    return m_nRotSrcType;
}



/**
 * @brief Process the GBS sentence
 */
bool cGpsBoard::ProcessGBS(char *pstrCmd)
{
    return(cNMEA::ProcessGBS(pstrCmd));
}

/**
 * @brief Process the GGA sentence
 */
bool cGpsBoard::ProcessGGA(char *pstrCmd)
{
    if (!IsEnabledEPFS())
        return false;

    if (cNMEA::ProcessGGA(pstrCmd))
    {
        UpdateTypeOfEPFS(pstrCmd);
    }

    return true;
}

/**
 * @brief Process the GLL sentence
 */
bool cGpsBoard::ProcessGLL(char *pstrCmd)
{
    if (!IsEnabledEPFS())
        return false;

    if (cNMEA::ProcessGLL(pstrCmd))
    {
        UpdateTypeOfEPFS(pstrCmd);
    }

    return true;
}

/**
 * @brief Process the GNS sentence
 */
bool cGpsBoard::ProcessGNS(char *pstrCmd)
{
    if (!IsEnabledEPFS())
        return false;

    if (cNMEA::ProcessGNS(pstrCmd))
    {
        UpdateTypeOfEPFS(pstrCmd);
    }

    return true;
}

/**
 * @brief Process the RMC sentence
 */
bool cGpsBoard::ProcessRMC(char *pstrCmd)
{
    if (!IsEnabledEPFS())
        return false;

    if (cNMEA::ProcessRMC(pstrCmd))
    {
        UpdateTypeOfEPFS(pstrCmd);

        // The internal GPS must be updated immediately for UTC direct sync mode!
        if (m_nSensorID == SENSORID_INTERNAL)
        {
            UpdatePosByPriority();
        }
    }

    return 0;
}

/**
 * @brief Process the VTG sentence
 */
bool cGpsBoard::ProcessVTG(char *pstrCmd)
{
    return cNMEA::ProcessVTG(pstrCmd, GetCrsVal());
}

/**
 * @brief Process the VBW sentence
 */
bool cGpsBoard::ProcessVBW(char *pstrCmd)
{
    return(cNMEA::ProcessVBW(pstrCmd));
}

/**
 * @brief Process the THS sentence
 */
bool cGpsBoard::ProcessTHS(char *pstrCmd)
{
    return(cNMEA::ProcessTHS(pstrCmd));
}

/**
 * @brief Process the HDT sentence
 */
bool cGpsBoard::ProcessHDT(char *pstrCmd)
{
    return(cNMEA::ProcessHDT(pstrCmd));
}

/**
 * @brief Process the ROT sentence
 */
bool cGpsBoard::ProcessROT(char *pstrCmd)
{
    return(cNMEA::ProcessROT(pstrCmd));
}

/**
 * @brief Process the DTM sentence
 */
bool cGpsBoard::ProcessDTM(char *pstrCmd)
{
    return(cNMEA::ProcessDTM(pstrCmd));
}

int cGpsBoard::ProcessData(cUart *pUartDbgP)
{
    int  nData;
    int  nProcess = 0;

    if (!CheckPortAvailable())
        return(nProcess);

    nData = GetPortDataByte();
    while (nData != UART_NULL_CHAR)
    {
        if (m_nRxSize || (m_nRxSize == 0 && nData == '$'))
            m_pRxData[m_nRxSize++] = nData;

        if (m_nRxSize > 5 && nData == ASC_CHR_LF)
        {
            m_pRxData[m_nRxSize] = 0x00;

            ProcessSentence((char*)m_pRxData);

            m_nRxSize = 0;
            nProcess  = 1;
        }

        if (m_nRxSize >= (RX_MAX_DATA_SIZE - 2))
            m_nRxSize = 0;

        nData = GetPortDataByte();
    }

    return 1;
}

int cGpsBoard::ProcessSentence(char *pstrCmd)
{
    char pstrTalkerID[LEN_NMEA_TALKER];
    char pstrSentID[LEN_NMEA_SENT];

    if (!CSentence::IsValidCheckSum(pstrCmd))
    {
        return 0;
    }

    CSentence::GetTalkerSenderID((char*)pstrCmd, pstrTalkerID, pstrSentID);

         if (strcmp((char*)pstrSentID, "GGA") == 0) ProcessGGA(pstrCmd);
    else if (strcmp((char*)pstrSentID, "GLL") == 0) ProcessGLL(pstrCmd);
    else if (strcmp((char*)pstrSentID, "RMC") == 0) ProcessRMC(pstrCmd);
    else if (strcmp((char*)pstrSentID, "VTG") == 0) ProcessVTG(pstrCmd);
    else if (strcmp((char*)pstrSentID, "GBS") == 0) ProcessGBS(pstrCmd);
    else if (strcmp((char*)pstrSentID, "HDT") == 0) ProcessHDT(pstrCmd);
    else if (strcmp((char*)pstrSentID, "ROT") == 0) ProcessROT(pstrCmd);
    else if (strcmp((char*)pstrSentID, "GNS") == 0) ProcessGNS(pstrCmd);
    else if (strcmp((char*)pstrSentID, "DTM") == 0) ProcessDTM(pstrCmd);
    else if (strcmp((char*)pstrSentID, "VBW") == 0) ProcessVBW(pstrCmd);
    else if (strcmp((char*)pstrSentID, "THS") == 0) ProcessTHS(pstrCmd);
    else if (strcmp((char*)pstrSentID, "ZDA") == 0) ProcessZDA(pstrCmd);

    if(!strcmp(pstrSentID, "INL"))
    {
        char pstrSubID[8];
        CSentence::GetFieldString(pstrCmd, 1, pstrSubID, sizeof(pstrSubID));

        if(!strncmp(pstrSubID, "LBP", 3))
        {
            ProcRunComLoopBackResp(pstrCmd);
            return 1;
        }
    }

    SetLastRcvTime(SysGetSystemTimer());

    return 1;
}

void cGpsBoard::UpdatePosByPriority(void)
{
    int nPosNmeaID = CSentence::NMEA_UNKNOWN;
    bool bPosDiffCorrected = false;

    // Priority position sentence
    // RMC -> GGA -> GNS -> GLL
    if(m_hDtm.IsValidDatumLoc())
    {
        if (m_hRmc.IsValidPosData())
        {
            nPosNmeaID = CSentence::NMEA_RMC;
            m_nPosModeIndicator = m_hRmc.GetPosModeIndi();
            m_nPosModeIndiTick  = m_hRmc.GetPosModeTick();

            SetLatVal(m_hRmc.GetLatVal());
            SetLonVal(m_hRmc.GetLonVal());
            SetUtcTime(m_hRmc.GetUtcTime());
        }
        else if (m_hGga.IsValidPosData())
        {
            nPosNmeaID = CSentence::NMEA_GGA;
            m_nPosModeIndicator = m_hGga.GetPosModeIndi();
            m_nPosModeIndiTick  = m_hGga.GetPosModeTick();

            SetLatVal(m_hGga.GetLatVal());
            SetLonVal(m_hGga.GetLonVal());
            SetUtcTime(m_hGga.GetUtcTime());
        }
        else if (m_hGns.IsValidPosData())
        {
            nPosNmeaID = CSentence::NMEA_GNS;
            m_nPosModeIndicator = m_hGns.GetPosModeIndi();
            m_nPosModeIndiTick  = m_hGns.GetPosModeTick();

            SetLatVal(m_hGns.GetLatVal());
            SetLonVal(m_hGns.GetLonVal());
            SetUtcTime(m_hGns.GetUtcTime());
        }
        else if (m_hGll.IsValidPosData())
        {
            nPosNmeaID = CSentence::NMEA_GLL;
            m_nPosModeIndicator = m_hGll.GetPosModeIndi();
            m_nPosModeIndiTick  = m_hGll.GetPosModeTick();

            SetLatVal(m_hGll.GetLatVal());
            SetLonVal(m_hGll.GetLonVal());
            SetUtcTime(m_hGll.GetUtcTime());
        }
    }

    if(nPosNmeaID == CSentence::NMEA_UNKNOWN)
    {
        m_nPosModeIndicator = POS_MODE_NONE;
        m_nPosModeIndiTick  = 0;
        
        SetGpsStatus(GPS_FIX_STATUS_LOST);
        m_bPosAccFlag = false;
        m_bRAIMflag   = false;
        CAisLib::SetDefaultSysTime(&m_xUtcTime);
    }
    else
    {
        bPosDiffCorrected = (m_nPosModeIndicator == POS_MODE_DIFFERENTIAL);
        if(bPosDiffCorrected && SysGetDiffTimeMili(m_nPosModeIndiTick) < NMEA_POS_LASTDATA_STAYMS)
        {
            SetGpsStatus(GPS_FIX_STATUS_DGPS);
        }
        else
        {
            SetGpsStatus(GPS_FIX_STATUS_GPS);
        }

        if(m_hGbs.GetGbsValidStatus())
        {
            // When GBS has been receiving, determine with only HPL.
            m_bPosAccFlag = m_hGbs.IsGbsPosAccFlag();
            m_bRAIMflag = true;
        }
        else
        {
            m_bPosAccFlag = (bPosDiffCorrected || m_nPosModeIndicator == POS_MODE_PRECISE);
            m_bRAIMflag = false;
        }
    }
}

void cGpsBoard::UpdateSogByPriority(void)
{
    // Priority SOG sentence
    // RMC -> VTG -> VBW
    if(m_hRmc.IsValidSpeedData())
    {
        SetSpdVal(m_hRmc.GetSpeed());
        SetVelVal(m_hRmc.GetVelocity());
    }
    else if(m_hVtg.IsValidSpeedData())
    {
        SetSpdVal(m_hVtg.GetSpeed());
        SetVelVal(m_hVtg.GetVelocity());
    }
    else if(m_hVbw.IsValidSpeedData())
    {
        SetSpdVal(m_hVbw.GetSpeed());
        SetVelVal(m_hVbw.GetVelocity());
    }
    else
    {
        SetSpdVal(NMEA_SOG_NULL);
        SetVelVal(NMEA_SOG_NULL);
    }
}

void cGpsBoard::UpdateCogByPriority(void)
{
    // Priority COG sentence
    // RMC -> VTG -> VBW
    if(m_hRmc.IsValidCourseData())
    {
        SetCrsVal(m_hRmc.GetCourse());
    }
    else if(m_hVtg.IsValidCourseData())
    {
        SetCrsVal(m_hVtg.GetCourse());
    }
    else if(m_hVbw.IsValidCourseData())
    {
        SetCrsVal(m_hVbw.GetCourse());
    }
    else
    {
        SetCrsVal(NMEA_COG_NULL);
    }
}

void cGpsBoard::UpdateHdgByPriority(void)
{
    if(!m_dwHdgValidTick)
    {
        ResetHdgBuffer(NMEA_HDG_NULL);
    }

    // Priority Heading sentence
    // THS -> HDT
    if(m_hThs.IsValidHeadingData())
    {
        SaveHdgBuffer(m_hThs.GetHeading());
        SetHdgVal(m_hThs.GetHeading());

        m_bHdgValid = true;
        m_dwHdgValidTick = SysGetSystemTimer();
    }
    else if(m_hHdt.IsValidHeadingData())
    {
        SaveHdgBuffer(m_hHdt.GetHeading());
        SetHdgVal(m_hHdt.GetHeading());

        m_bHdgValid = true;
        m_dwHdgValidTick = SysGetSystemTimer();
    }
    else
    {
        ResetHdgBuffer(NMEA_HDG_NULL);
        SetHdgVal(NMEA_HDG_NULL);
        m_bHdgValid = false;
        m_dwHdgValidTick = 0;
    }
}

void cGpsBoard::UpdateRotByPriority(void)
{
    int nRotSrcType = ROTSCR_NONE;
    int nRotData = NMEA_ROT_NULL;

    //------------------------------------------------------------
    // ROT priority
    //------------------------------------------------------------
    if(m_hRot.GetRotSrcType() == ROTSCR_TI || m_hRot.GetRotSrcType() == ROTSCR_NOT_TI)
    {
        if(m_hRot.IsValidRotData())
        {
            nRotSrcType = m_hRot.GetRotSrcType();
            nRotData    = m_hRot.GetRot();
        }
    }

    if(nRotSrcType == ROTSCR_NONE)
    {
        // Enable Ext. Heading인 경우에만 사용하도록 수정.
        if(IsHdgValid() && CSetupMgr::getInst()->GetEnableExtHeading())
        {
            int nRotDataFromHdg = GetRotDataFromHdgDiff();
            if(nRotDataFromHdg != NMEA_ROT_NULL)
            {
                nRotSrcType = ROTSCR_HDG;
                nRotData    = nRotDataFromHdg;
            }
        }
    }

    if(nRotSrcType == ROTSCR_NONE)
    {
        if(m_hRot.IsValidRotData())
        {
            nRotSrcType = m_hRot.GetRotSrcType();
            nRotData    = m_hRot.GetRot();
        }
    }

    m_nRotSrcType = nRotSrcType;
    if(m_nRotSrcType == ROTSCR_NONE || nRotData == NMEA_ROT_NULL)
    {
        SetRotVal(NMEA_ROT_NULL);
        m_bRotValid = false;
        m_dwRotValidTick= 0;
    }
    else
    {
        SetRotVal(nRotData);
        m_bRotValid = true;
        m_dwRotValidTick= SysGetSystemTimer();
    }
}

void cGpsBoard::UpdateUtcByPriority(void)
{
    if (m_hRmc.IsValidUtcData())
    {
        SetUtcDate(m_hRmc.GetUtcDate());
        //SetUtcTime(m_hRmc.GetUtcTime());
    }
    else if (m_hZda.IsValidUtcData())
    {
        SetUtcDate(m_hZda.GetUtcDate());
        //SetUtcTime(m_hZda.GetUtcTime());
    }
    else
    {
        SetUtcDateNull();
        SetUtcTimeNull();
    }
}

void cGpsBoard::RunPeriodicallyGps(void)
{
    UpdatePosByPriority();
    UpdateSogByPriority();
    UpdateCogByPriority();
    UpdateHdgByPriority();
    UpdateRotByPriority();
    UpdateUtcByPriority();

    m_hRmc.RunPeriodically();
    m_hGga.RunPeriodically();
    m_hGns.RunPeriodically();
    m_hGll.RunPeriodically();

    m_hGbs.RunPeriodically();

    m_hVtg.RunPeriodically();
    m_hVbw.RunPeriodically();
    
    m_hThs.RunPeriodically();
    m_hHdt.RunPeriodically();

    m_hRot.RunPeriodically();

    m_hZda.RunPeriodically();
}
