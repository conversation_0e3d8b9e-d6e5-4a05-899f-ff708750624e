#ifndef __PI_H__
#define __PI_H__

#include "DataType.h"
#include "AllConst.h"
#include "AisLib.h"
#include "Uart.h"
#include "AlarmThing.h"
#include "Spw.h"
#include "Ssa.h"
#include "Hbt.h"

#define HIGHSPD_PORTID_MKD  HIGHSPD_PORTID_0

#define STR_TALKER_AI       (char*)"AI"
#define STR_TALKER_IT       (char*)"IT"

class CPI
{
public:
    CPI(UINT8 uHighSpdPortID, char chPortID, cUart *pUartPort);
    ~CPI();

public:
    INT8    m_nHighSpdPortID;
    char    m_chPortID;
    cUart*  m_pUartPort;
    int     m_nBaudIdx;

protected:
    int     m_nRxSize;
    UCHAR*  m_pRxData;

    static char   *m_pstrOutBuff;

    CSpw    m_xSpw;
    CSsa    m_xSsa;
    CHbt    m_xHbt;

public:
    BOOL    m_bEnableSendRcvDataToMKD;

public:
    void    SetUartBaudIdx(int nBaudIdx);
    void    SendOutStr(const char *pstrMsg);
    void    SendOutData(const char *pstrMsg, UINT16 uSize);
    void    SendHighSpdPortData(BYTE *pData, int nSize);
    void    SendAllHighSpdPortData(BYTE *pData, int nSize, BOOL bSendMKD);

    void    EnableSendRcvDataToMKD(BOOL bEnable);
    void    SendSetupUartPortMon();

    void    CheckTxChPI(UINT uDestMMSI, BYTE tx_ch, BOOL *pbEnableAis1, BOOL *pbEnableAis2);
    BOOL    CheckValidInterrMsg(int nInterrogatedStType, int nInterrogatedMsgID);
    BOOL    CheckInterrRequestInfo(BYTE bAirType, UINT uMMSI1, int bMsgID11, int bMsgID12, UINT uMMSI2, int bMsgID21);

    /****************************************************************************/
    void    SendRxMsgToAllPI(DWORD dwMsgID, int nRxChNo, UCHAR *pPacketData, int nPacketSize);
    void    SendTxMsgToAllPI(int nTxChNo, UCHAR *pPacketData, int nPacketSize);
    void    SendDummyVDO(void);

    void    SendSSDToPI(char *pstrTalker);
    void    SendVSDToPI(char *pstrTalker);

    void    SendSysDateTime();
    void    SendOwnShipMMSI();
    void    SendOwnShipIMO();
    void    SendVERToPI();

    void    SendALRToAllPI(CAlarmThing *pAlarmThing);
    void    SendALFToPI(CAlarmThing *pAlarmThing, bool bSendAllPI=false);

    void    SendABKtoPI(UINT uDestMMSI, int nAckRxCh, int nVdlMsgID, int nMsgSeqNum, int nAckType);
    void    SendACAACStoPI(int nRosIndex, bool bSendAllPI=false);
    virtual void SendAllACAACStoPI();
    void    SendSecurityLogData(EVENTLOG_DATA *pLogData, SYS_DATE_TIME *SYS_DATE_TIME, int nAlrIndex, _tagBAMAlertStat nAlrStat);

    void    SendTXTtoPI(int nTxtID, bool bSendAllPI=false);
    void    CheckStatAndSendTXT(int nTxtID);
    void    SendAllTXTtoPI(BOOL bCheckStatChged);
    void    SendRfTestModeMsgToPI(char *pstrCmd, int nMode);
    void    SendSetupShowTestingSART();
    void    SendSetupEnableSilentMode();
    void    SendINLALRToPI(int nAlrStartID, int nAlrEndID);

    void    SendTRLtoPI();
    void    SendAllEPVtoPI();
    void    SendNAK(char *pstrAffectSenctence, int nReasonCode, const char *pstrTalkerID=(const char*)"AI");

    /****************************************************************************/
    void    ProcessSPW(char *pstrCmd);
    void    GetMd5HashKey(const char* pstrUserKey, BYTE *pHashKey);
    BOOL    CheckMd5MatchSSA(char *pstrCmd, char *pstrHashKey);
    void    ProcessSSA(char *pstrCmd);
    void    ProcessEPV(char *pstrCmd);
    BOOL    IsPropProtectedEPV(int nProperty);

    void    ProcessSSD(char *pstrCmd);
    void    ProcessVSD(char *pstrCmd);
    void    ProcessABM(char *pstrCmd);
    BOOL    ProcessABMSub_06_12(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT uDestMMSI, UINT8 uChID, int nTxMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    BOOL    ProcessABMSub_25(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT uDestMMSI, UINT8 uChID, int nTxMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    BOOL    ProcessABMSub_26(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT uDestMMSI, UINT8 uChID, int nTxMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    void    ProcessBBM(char *pstrCmd);
    BOOL    ProcessBBMSub(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nMsgNumPI, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    BOOL    ProcessBBMSub_08_14(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nVdlMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    BOOL    ProcessBBMSub_25(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nMsgNumPI, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    BOOL    ProcessBBMSub_26(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nMsgNumPI, int nNumFillBits, char *pstrEncData, int nNumDataBits);
    int     ProcessAIR(char *pstrCmd);
    int     ProcessACK(char *pstrCmd);
    int     ProcessACN(char *pstrCmd);
    void    ProcessHBT(char *pstrCmd);
    BOOL    CheckTimeoutHBT();

    virtual void    ProcessAIQ(char *pstrCmd);
    virtual void    ProcessACA(char *pstrCmd);
    virtual void    ProcessINL(char *pstrCmd);

    //-------------------------
    // Test mode commands
    //-------------------------
    void    ProcessINLAIQ(char *pstrCmd);
    void    ProcessINLDIG(char *pstrCmd);
    void    ProcessINLRFT(char *pstrCmd);

    int     GetLoopBackPortID();
    virtual void    ProcRunComLoopBackResp(char *pstrCmd);

    virtual void    ProcessData(cUart *pUartDbgP);
    virtual int     ProcessSentence(char *pstrCmd);

    virtual void    RunUartIsrHandler();

    void    RunPeriodicallyPI();
};

#endif//__PI_H__
