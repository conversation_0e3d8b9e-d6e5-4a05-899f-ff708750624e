#include <string.h>
#include <SartMgr.h>
#include "SysConst.h"
#include "SysLib.h"
#include "MKD.h"
#include "ExtDisp.h"
#include "Pilot.h"
#include "Nmea.h"
#include "Ship.h"
#include "SensorMgr.h"
#include "UserDirMgr.h"
#include "VdlTxMgr.h"
#include "SyncMgr.h"
#include "BuiltInTestMgr.h"
#include "GpsBoard.h"
#include "Timer.h"
#include "LayerPhysical.h"
#include "SetupMgr.h"
#include "SysOpStatus.h"
#include "AlarmThing.h"

CAlarm01    *gpAlarmTxMalFunc       = NULL;
CAlarm02    *gpAlarmVswr            = NULL;
CAlarm03    *gpAlarmRxMalfuncCh1    = NULL;
CAlarm04    *gpAlarmRxMalfuncCh2    = NULL;
CAlarm05    *gpAlarmMalfuncCh70     = NULL;
CAlarm06    *gpAlarmGeneralFail     = NULL;
CAlarm07    *gpAlarmUtcSyncInvalid  = NULL;
CAlarm08    *gpAlarmMkdConnLost     = NULL;
CAlarm09    *gpAlarmPosMismatch     = NULL;
CAlarm10    *gpAlarmNavStatusWrong  = NULL;
CAlarm11    *gpAlarmHdgOffset       = NULL;
CAlarm14    *gpAlarmActiveSART      = NULL;
CAlarm25    *gpAlarmExtEpfsLost     = NULL;
CAlarm26    *gpAlarmNoPosSensor     = NULL;
CAlarm29    *gpAlarmNoSOG           = NULL;
CAlarm30    *gpAlarmNoCOG           = NULL;
CAlarm32    *gpAlarmHdgInvalid      = NULL;
CAlarm35    *gpAlarmNoROT           = NULL;

cGpsBoard   *CAlarm25::m_pOldExtPosSensor   = NULL;
BOOL        CAlarm25::m_bOldPosSensorFixed  = FALSE;
DWORD       CAlarm25::m_dwCheckSec          = 0;

BOOL        CAlarm26::m_bFirstCheck = TRUE;
DWORD       CAlarm26::m_dwFixSec = 0;

BOOL        CAlarm29::m_bFirstCheck = TRUE;
DWORD       CAlarm29::m_dwFixSec = 0;

BOOL        CAlarm30::m_bFirstCheck = TRUE;
DWORD       CAlarm30::m_dwFixSec = 0;

CAlarmThing::CAlarmThing(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
{
    m_nAlarmID              = nAlarmID;
    m_pstrMsg               = pstrMsg;
    m_nAlrStatCheckIntSec   = nAlrStatCheckIntSec;

    m_dwAlrStatCheckSec     = 0;
    m_dwSecSendALR          = 0;
    m_dwSecSendALF          = 0;
    m_dwSilenceCheckSec     = 0;

    CAisLib::SetDefaultSysDateTime(&m_sAlrTime);

    m_nBAMAlertID           = nBAMAlertID;
    m_pstrBAMMsg1           = pstrBAMAlertMsg1;
    m_pstrBAMMsg2           = pstrBAMAlertMsg2;
    m_cBAMAlertCategory     = BAM_ALERT_AIS_CATEGORY;
    m_cBAMAlertPriority     = cBAMAlertPriority;

    m_cBAMAlertState        = BAM_ALERT_STAT_NORMAL;
    m_nBAMAlertInstance     = nAlarmID;                    // BIIT ID와 동일한 값으로 사용
    m_nSequentialMsgID      = BAM_SEQUENTIAL_NUM_MIN;
    m_nBAMAlertRevCnter     = BAM_REVISION_CNTER_MIN;
    m_nBAMAlertEscCnter     = BAM_ESCALATION_CNT_MIN;
}

INT8 CAlarmThing::GetAlarmID()
{
    return m_nAlarmID;
}

SYS_DATE_TIME* CAlarmThing::GetAlarmTime()
{
    return &(m_sAlrTime);
}

_tagBAMAlertStat CAlarmThing::GetAlertStat()
{
    return m_cBAMAlertState;
}

char* CAlarmThing::GetAlarmMsg()
{
    return m_pstrMsg;
}

int CAlarmThing::GetBAMAlertID()
{
    return m_nBAMAlertID;
}

int CAlarmThing::GetBAMAlertInstance()
{
    return m_nBAMAlertInstance;
}

int CAlarmThing::GetSequentialMsgID()
{
    return m_nSequentialMsgID;
}

char CAlarmThing::GetAlarmCategory()
{
    return m_cBAMAlertCategory;
}

int CAlarmThing::GetRevisionCounter()
{
    return m_nBAMAlertRevCnter;
}

int CAlarmThing::GetEscalationCounter()
{
    return m_nBAMAlertEscCnter;
}

char* CAlarmThing::GetBAMAlertMsg1()
{
    return m_pstrBAMMsg1;
}

char* CAlarmThing::GetBAMAlertMsg2()
{
    return m_pstrBAMMsg2;
}

void CAlarmThing::Reset()
{
    SetAlarmStatus(BAM_ALERT_STAT_NORMAL);
}

BOOL CAlarmThing::IsAlarmStatOccurred(_tagBAMAlertStat nAlrStat)
{
    return (   nAlrStat == BAM_ALERT_STAT_ACTIVE_UNACK 
            || nAlrStat == BAM_ALERT_STAT_ACTIVE_ACKED
            || nAlrStat == BAM_ALERT_STAT_RESPON_TRANS
            || nAlrStat == BAM_ALERT_STAT_ACTIVE_SILEN);
}

BOOL CAlarmThing::IsAlarmOccurred()
{
    return IsAlarmStatOccurred(m_cBAMAlertState);
}

BOOL CAlarmThing::IsAlarmStatAcked(_tagBAMAlertStat nAlrStat)
{
    return (   nAlrStat == BAM_ALERT_STAT_ACTIVE_ACKED
            || nAlrStat == BAM_ALERT_STAT_RESPON_TRANS
            || nAlrStat == BAM_ALERT_STAT_ACTIVE_SILEN);
}

BOOL CAlarmThing::IsAlarmAcked()
{
    return IsAlarmStatAcked(m_cBAMAlertState);
}

BOOL CAlarmThing::IsAlarmStatEscalated(_tagBAMAlertStat nAlrStat)
{
    return (nAlrStat == BAM_ALERT_STAT_ACTIVE_UNACK /*|| nAlrStat == BAM_ALERT_STAT_ACTIVE_SILEN*/);
}

BOOL CAlarmThing::IsAlarmEscalated()
{
    if (m_cBAMAlertPriority == BAM_ALERT_PRIORITY_CAUTION)
        return FALSE;

    return IsAlarmStatEscalated(m_cBAMAlertState);
}

_tagBAMAlertStat CAlarmThing::GetAlarmStatus()
{
    return m_cBAMAlertState;
}

BOOL CAlarmThing::SetAlarmStatus(_tagBAMAlertStat nStatus, BOOL bForced)
{
    // Caution priority
    if (m_cBAMAlertPriority == BAM_ALERT_PRIORITY_CAUTION)
    {
        switch(nStatus)
        {
            case BAM_ALERT_STAT_ACTIVE_UNACK:
            case BAM_ALERT_STAT_ACTIVE_SILEN:
            case BAM_ALERT_STAT_RESPON_TRANS:
                nStatus = BAM_ALERT_STAT_ACTIVE_ACKED;
                break;

            case BAM_ALERT_STAT_RECTIF_UNACK:
                nStatus = BAM_ALERT_STAT_NORMAL;
                break;
            default:
                break;
        }
    }

    if(bForced || m_cBAMAlertState != nStatus)
    {
        m_dwSilenceCheckSec = cTimerSys::getInst()->GetCurTimerSec();

        // Exceptions are state changes related to activation of silence and related to return from silence 
        // (i.e. state changes AT6 and WT6 in Figure G.2 and AT7 and WT7 in Figure G.3). 
        // These state changes shall not update the "time of last state change".
        if (   !(m_cBAMAlertState == BAM_ALERT_STAT_ACTIVE_SILEN && nStatus == BAM_ALERT_STAT_ACTIVE_UNACK)
            && !(nStatus == BAM_ALERT_STAT_ACTIVE_SILEN))
        {
            if(CSyncMgr::getInst()->IsUtcDirectSyncRunning())
                m_sAlrTime = cShip::getOwnShipInst()->xUtcTime;        // Time of alarm condition change, UTC
            else
            {
                if(!CSyncMgr::getInst()->GetOtherSyncStUtcDate(&m_sAlrTime.xDate))
                    CAisLib::SetDefaultSysDate(&m_sAlrTime.xDate);

                if(!CSyncMgr::getInst()->GetOtherSyncStUtcTime(&m_sAlrTime.xTime))
                    CAisLib::SetDefaultSysTime(&m_sAlrTime.xTime);
            }
        }

        m_cBAMAlertState = nStatus;
        SendOutNmeaALR(this, TRUE);
        SendOutNmeaALF(this, TRUE);
        return TRUE;
    }

    return FALSE;
}

char CAlarmThing::GetAlarmPriority()
{
    return m_cBAMAlertPriority;
}

_tagBAMAlertStat CAlarmThing::UpdateAlarmStatus(BOOL bCheckImmediately)
{
    if (bCheckImmediately || cTimerSys::getInst()->GetTimeDiffSec(m_dwAlrStatCheckSec) > m_nAlrStatCheckIntSec)
    {
        if (CheckAlarmCondition())    
        {
            if (   m_cBAMAlertState == BAM_ALERT_STAT_NORMAL 
                || m_cBAMAlertState == BAM_ALERT_STAT_RECTIF_UNACK
                || (m_cBAMAlertState == BAM_ALERT_STAT_ACTIVE_SILEN && cTimerSys::getInst()->GetTimeDiffSec(m_dwSilenceCheckSec) > SILINCE_TIMEOUT_SEC)
            #if defined(__ENABLE_PILOT__) && defined(__ENABLE_EXT_DISP__)
                || (m_cBAMAlertState == BAM_ALERT_STAT_RESPON_TRANS && CPILOT::getInst()->CheckTimeoutHBT() && CEXTDISP::getInst()->CheckTimeoutHBT())
            #elif defined(__ENABLE_PILOT__)
                || (m_cBAMAlertState == BAM_ALERT_STAT_RESPON_TRANS && CPILOT::getInst()->CheckTimeoutHBT())
            #elif defined(__ENABLE_EXT_DISP__)
                || (m_cBAMAlertState == BAM_ALERT_STAT_RESPON_TRANS && CEXTDISP::getInst()->CheckTimeoutHBT())
            #else
                || (m_cBAMAlertState == BAM_ALERT_STAT_RESPON_TRANS)
            #endif
                )
            {
                // Normal상태에서 Alert 발생시에만 Revision/escalation counter 초기화한다.
                if (m_cBAMAlertState == BAM_ALERT_STAT_NORMAL)
                {
                    SetRevisionCounter(BAM_REVISION_CNTER_MIN);
                    SetEscalationCounter(BAM_ESCALATION_CNT_MIN);
                }

                SetAlarmStatus(BAM_ALERT_STAT_ACTIVE_UNACK);
            }
        }
        else
        {
            // Start the Scenario BAM-6_3_5_4-Ed1.scn and Legend BAM-6_3_5_4-Ed2.leg
            // Locating device의 경우 alert disable시 normal 상태로 가야함.
            if (m_nBAMAlertID == BAM_ALERT_ID_ACTIVE_AISSART && !CSetupMgr::getInst()->GetEnableAlert14())
            {
                SetAlarmStatus(BAM_ALERT_STAT_NORMAL);
            }
            else
            {
                switch(m_cBAMAlertState)
                {
                    case BAM_ALERT_STAT_ACTIVE_UNACK:
                    case BAM_ALERT_STAT_ACTIVE_SILEN:
                        SetAlarmStatus(BAM_ALERT_STAT_RECTIF_UNACK);
                        break;
                    case BAM_ALERT_STAT_ACTIVE_ACKED:
                    case BAM_ALERT_STAT_RESPON_TRANS:
                        SetAlarmStatus(BAM_ALERT_STAT_NORMAL);
                        break;
                    default:
                        break;
                }
            }
        }

        m_dwAlrStatCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }

    return m_cBAMAlertState;
}

BOOL CAlarmThing::SetStatusAcked()
{
    switch(m_cBAMAlertState)
    {
        case BAM_ALERT_STAT_ACTIVE_UNACK:
        case BAM_ALERT_STAT_ACTIVE_SILEN:
        case BAM_ALERT_STAT_RESPON_TRANS:
            SetAlarmStatus(BAM_ALERT_STAT_ACTIVE_ACKED);
            break;
        case BAM_ALERT_STAT_RECTIF_UNACK:
            SetAlarmStatus(BAM_ALERT_STAT_NORMAL);
            break;
        default:
            return FALSE;
    }

    return TRUE;
}

void CAlarmThing::SendOutNmeaALR(CAlarmThing *pAlarmThing, BOOL bImmediately)
{
    //---------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.1
    // when alarm is initiated :
    // an appropriate alarm message shall be output via the presentation interface upon    occurrence and repeated every 30 s.
    //---------------------------------------------------------------------------------------------------------------------
    //----------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.2.1
    // During healthy conditions (no alarm condition) an empty ALR sentence shall be sent at one-minute intervals.
    //----------------------------------------------------------------------------------------------------------------------

    if(bImmediately ||
        (!pAlarmThing->IsAlarmOccurred() && cTimerSys::getInst()->GetTimeDiffSec(pAlarmThing->m_dwSecSendALR) >= ALR_OUT_SEC_INACTIVE) ||
        (pAlarmThing->IsAlarmOccurred() && cTimerSys::getInst()->GetTimeDiffSec(pAlarmThing->m_dwSecSendALR) >= ALR_OUT_SEC_ACTIVE))
    {
        CMKD::getInst()->SendALRToAllPI(pAlarmThing);
        pAlarmThing->m_dwSecSendALR = cTimerSys::getInst()->GetCurTimerSec();
    }
}

void CAlarmThing::IncSequentialNumber()
{
    if (++m_nSequentialMsgID > BAM_SEQUENTIAL_NUM_MAX)
        m_nSequentialMsgID = BAM_SEQUENTIAL_NUM_MIN;
};

void CAlarmThing::SetRevisionCounter(int nCounter)
{
    m_nBAMAlertRevCnter = nCounter;
}

void CAlarmThing::IncRevisionCounter()
{
    if(++m_nBAMAlertRevCnter > BAM_REVISION_CNTER_MAX)
        m_nBAMAlertRevCnter = BAM_REVISION_CNTER_MIN;
}

void CAlarmThing::SetEscalationCounter(int nCounter)
{
    m_nBAMAlertEscCnter = nCounter;
}

void CAlarmThing::IncEscalationCounter()
{
    if(++m_nBAMAlertEscCnter > BAM_ESCALATION_CNT_MAX)
        m_nBAMAlertEscCnter = BAM_ESCALATION_CNT_MIN;
}

void CAlarmThing::SendOutNmeaALF(CAlarmThing *pAlarmThing, BOOL bImmediately)
{
    //---------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.1
    // when alarm is initiated :
    // an appropriate alarm message shall be output via the presentation interface upon    occurrence and repeated every 30 s.
    //---------------------------------------------------------------------------------------------------------------------
    //----------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.2.1
    // During healthy conditions (no alarm condition) an empty ALR sentence shall be sent at one-minute intervals.
    //----------------------------------------------------------------------------------------------------------------------

    if ( bImmediately ||
        (pAlarmThing->IsAlarmEscalated() && cTimerSys::getInst()->GetTimeDiffSec(pAlarmThing->m_dwSecSendALF) >= ALF_OUT_SEC_ACTIVE))
    {
        if (!bImmediately)
        {
            pAlarmThing->IncEscalationCounter();
        }

        CMKD::getInst()->SendALFToPI(pAlarmThing, true);
        pAlarmThing->IncSequentialNumber();
        pAlarmThing->IncRevisionCounter();
        pAlarmThing->m_dwSecSendALF = cTimerSys::getInst()->GetCurTimerSec();
    }
}

////////////////////////////////////////////////////
// CAlarm01
////////////////////////////////////////////////////
CAlarm01::CAlarm01(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm01::CheckAlarmCondition()
{
    //--------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2:2018 6.10.1.2
    // BIIT ID 1 shall be activated when:
    //? the integrity of the VDL would be degraded by incorrect transmitter behaviour (for instance
    //  in case of the Tx shutdown procedure has operated);
    //? the unit is not able to transmit for technical reasons or missing or invalid MMSI.
    //--------------------------------------------------------------------------------------------------------------------------------------------------

    if(!OPSTATUS::bRunSyncProcess)
        return FALSE;

    return (CVdlTxMgr::IsTxStatMalfunction());
}

////////////////////////////////////////////////////
// CAlarm02
////////////////////////////////////////////////////
CAlarm02::CAlarm02(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm02::CheckAlarmCondition()
{
    return CLayerPhysical::getInst()->IsVswrErrorOccurred();
}

////////////////////////////////////////////////////
// CAlarm03
////////////////////////////////////////////////////
CAlarm03::CAlarm03(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm03::CheckAlarmCondition()
{
    return CBuiltInTestMgr::getInst()->IsRxMalFunctionCH1();
}

////////////////////////////////////////////////////
// CAlarm04
////////////////////////////////////////////////////
CAlarm04::CAlarm04(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm04::CheckAlarmCondition()
{
    return CBuiltInTestMgr::getInst()->IsRxMalFunctionCH2();
}

////////////////////////////////////////////////////
// CAlarm05
////////////////////////////////////////////////////
CAlarm05::CAlarm05(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm05::CheckAlarmCondition()
{
    return CBuiltInTestMgr::getInst()->IsRxMalFunctionChDSC();
}

////////////////////////////////////////////////////
// CAlarm06
////////////////////////////////////////////////////
CAlarm06::CAlarm06(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm06::CheckAlarmCondition()
{
    // currently, it's not used
    return FALSE;
}

////////////////////////////////////////////////////
// CAlarm07
////////////////////////////////////////////////////
CAlarm07::CAlarm07(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm07::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    return !CSyncMgr::getInst()->IsUtcDirectSyncRunning();
}

////////////////////////////////////////////////////
// CAlarm08
////////////////////////////////////////////////////
CAlarm08::CAlarm08(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm08::CheckAlarmCondition()
{
    //----------------------------------------
    // 0 = DTE(Data Terminal Equipment) ready
    // 1 = DTE not available
    //----------------------------------------
    return CMKD::getInst()->GetDTEFlag();
}

////////////////////////////////////////////////////
// CAlarm09
////////////////////////////////////////////////////
CAlarm09::CAlarm09(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm09::CheckAlarmCondition()
{
    //-----------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ********
    // When the external position source is used and both external and internal positions are valid
    // then the external and internal positions shall be compared once per minute and an alarm
    // generated if the difference between the two positions is greater than 100 m + distance
    // between the two GNSS antennas, for a period of 15 min.
    //-----------------------------------------------------------------------------------------------

    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    return (CSensorMgr::getInst()->m_nGpsMismatchCnt >= GPS_MISMATCH_MIN_CNT);
}

////////////////////////////////////////////////////
// CAlarm10
////////////////////////////////////////////////////
CAlarm10::CAlarm10(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm10::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    //----------------------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 6.5.2
    // When NavStatus is "at anchor", "moored, "aground" or "undefined" and the vessel is moving 
    // faster than 3kn alarm ID 10 should be generated
    //----------------------------------------------------------------------------------------------------------
    if((cShip::getOwnShipInst()->xNavData.uNavStatus == AIS_NAV_STATUS_AT_ANCHOR ||
        cShip::getOwnShipInst()->xNavData.uNavStatus == AIS_NAV_STATUS_MOORED ||
        cShip::getOwnShipInst()->xNavData.uNavStatus == AIS_NAV_STATUS_AGROUND) &&
        cShip::getOwnShipInst()->xDynamicData.nSOG > SOG_THRESHOLD_DONTMOVE)
    {
        return TRUE;
    }

    //----------------------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 6.5.2
    // When NavStatus is under way and SOG is less than 1 kn for more than 2 h, alarm ID 10 should be generated.
    //----------------------------------------------------------------------------------------------------------
    if(cTimerSys::getInst()->GetTimeDiffSec(CSensorMgr::getInst()->m_dwNavStatWrongStartSec) >= NAVSTAT_WRONG_SEC)
    {
        return TRUE;
    }

    return FALSE;
}

////////////////////////////////////////////////////
// CAlarm11
////////////////////////////////////////////////////
CAlarm11::CAlarm11(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm11::CheckAlarmCondition()
{
    //-----------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2:2018 6.10.1.2
    // BIIT ID 11 shall be activated when SOG is greater than 5 kn and the difference between COG and HDT is greater than 45° for 5 min.
    //-----------------------------------------------------------------------------------------------------------------------------------
    return (CSensorMgr::getInst()->m_nHdgCogDiffCnt >= HDG_COG_DIFF_SEC_CNT);
}

////////////////////////////////////////////////////
// CAlarm14
////////////////////////////////////////////////////
CAlarm14::CAlarm14(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm14::CheckAlarmCondition()
{
    //-------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.11.3.1 Alarms
    // reception of Message 1 NavStatus 14. Once acknowledged for a given User ID the relay will not be activated and
    // the ALR will indicate acknowledge. The acknowledgement shall    stay in effect until it is removed from the target list due to time out.
    //-------------------------------------------------------------------------------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 18.1.5 Behaviour of NavStatus 14 reception
    // This test verifies the correct behaviour of the received Message 1 with NavStatus 14
    // Navigational status 14 = AIS-SART (active), MOB-AIS, EPIRB-AIS
    // => 새로 수신한 스테이션의 NavStatus 가 14 이면 : ALR 14 발생
    //--------------------------------------------------------------------------------------------------------------------------------------------------------------

    return (CSetupMgr::getInst()->GetEnableAlert14() && CSartMgr::getInst()->CheckAlarmNewActiveSART());
}

BOOL CAlarm14::SetStatusAcked()
{
    CSartMgr::getInst()->SetActiveSartAlarmAcked();

    return CAlarmThing::SetStatusAcked();
}

////////////////////////////////////////////////////
// CAlarm25
////////////////////////////////////////////////////
CAlarm25::CAlarm25(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
    ResetAlarmCheckStat();
}

BOOL CAlarm25::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    if(!CSetupMgr::getInst()->GetEnableExtEPFS())
        return FALSE;

    BOOL bRet = FALSE;

    {
        if(CSensorMgr::getInst()->CheckExternalGnss())
            m_pOldExtPosSensor = CSensorMgr::getInst()->m_pPosSensor;

        if(m_pOldExtPosSensor)
        {
            m_bOldPosSensorFixed = m_pOldExtPosSensor->IsPosUtcFixed();
            bRet = !m_bOldPosSensorFixed;
        }

        m_dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
    return bRet;
}

void CAlarm25::ResetAlarmCheckStat()
{
    m_pOldExtPosSensor= NULL;
    m_bOldPosSensorFixed = FALSE;
    m_dwCheckSec = 0;
}

////////////////////////////////////////////////////
// CAlarm26
////////////////////////////////////////////////////
CAlarm26::CAlarm26(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
    m_bFirstCheck = TRUE;
    m_dwFixSec = 0;
}

BOOL CAlarm26::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    BOOL bRet = FALSE;

    if(m_bFirstCheck)
    {
        m_bFirstCheck = FALSE;
        m_dwFixSec = cTimerSys::getInst()->GetCurTimerSec();
    }
    else
    {
        if(!CSensorMgr::getInst()->IsGnssLost())
            m_dwFixSec = cTimerSys::getInst()->GetCurTimerSec();
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwFixSec) > NMEA_POS_LASTDATA_STAYSEC)
            bRet = TRUE;
    }

    return bRet;
}

////////////////////////////////////////////////////
// CAlarm29
////////////////////////////////////////////////////
CAlarm29::CAlarm29(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
    m_bFirstCheck = TRUE;
    m_dwFixSec = 0;
}

BOOL CAlarm29::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    static BOOL m_bFirstCheck = TRUE;
    static DWORD m_dwFixSec = 0;
    BOOL bRet = FALSE;

    if(m_bFirstCheck)
    {
        m_bFirstCheck = FALSE;
        m_dwFixSec = cTimerSys::getInst()->GetCurTimerSec();
    }
    else
    {
        if(!CSensorMgr::getInst()->IsSogLost())
            m_dwFixSec = cTimerSys::getInst()->GetCurTimerSec();
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwFixSec) > NMEA_POS_LASTDATA_STAYSEC)
            bRet = TRUE;
    }
    return bRet;
}

////////////////////////////////////////////////////
// CAlarm30
////////////////////////////////////////////////////
CAlarm30::CAlarm30(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
    m_bFirstCheck = TRUE;
    m_dwFixSec = 0;
}

BOOL CAlarm30::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    BOOL bRet = FALSE;

    if(m_bFirstCheck)
    {
        m_bFirstCheck = FALSE;
        m_dwFixSec = cTimerSys::getInst()->GetCurTimerSec();
    }
    else
    {
        if(!CSensorMgr::getInst()->IsCogLost())
            m_dwFixSec = cTimerSys::getInst()->GetCurTimerSec();
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwFixSec) > NMEA_POS_LASTDATA_STAYSEC)
            bRet = TRUE;
    }

    return bRet;
}

////////////////////////////////////////////////////
// CAlarm32
////////////////////////////////////////////////////
CAlarm32::CAlarm32(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm32::CheckAlarmCondition()
{
//    static const char* STR_ALR_MSG_HEADING_LOST            = "AIS: Heading lost/invalid";

    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    return CSetupMgr::getInst()->GetEnableExtHeading() && CSensorMgr::getInst()->IsHdgLost();
}

void CAlarm32::SendOutNmeaALR(CAlarmThing *pAlarmThing, BOOL bImmediately)
{
    // 14.8.3.3 Heading sensor
    // g) Disable heading and ROT input by configuration to prevent raising an alert in their
    // absence. Disconnect the inputs for HDG and ROT or set their data to invalid (for example
    // by wrong checksum, "valid/invalid" flag). (1)

    // If facilities to configuration “heading sensor not connected” is provided activate this function.
    // The ALR sentence BIIT ID 032 is NOT output on the PI.
    if (CSetupMgr::getInst()->GetEnableExtHeading())
    {
        CAlarmThing::SendOutNmeaALR(pAlarmThing, bImmediately);
    }
}

////////////////////////////////////////////////////
// CAlarm35
////////////////////////////////////////////////////
CAlarm35::CAlarm35(INT8 nAlarmID, char *pstrMsg, int nBAMAlertID, char *pstrBAMAlertMsg1, char *pstrBAMAlertMsg2, char cBAMAlertPriority, INT16 nAlrStatCheckIntSec)
    : CAlarmThing(nAlarmID, pstrMsg, nBAMAlertID, pstrBAMAlertMsg1, pstrBAMAlertMsg2, cBAMAlertPriority, nAlrStatCheckIntSec)
{
}

BOOL CAlarm35::CheckAlarmCondition()
{
    if(!OPSTATUS::bSysInit2minDone)
        return FALSE;

    return CSetupMgr::getInst()->GetEnableExtROT() && CSensorMgr::getInst()->IsRotLost();
}

void CAlarm35::SendOutNmeaALR(CAlarmThing *pAlarmThing, BOOL bImmediately)
{
    // 14.8.3.3 Heading sensor
    // g) Disable heading and ROT input by configuration to prevent raising an alert in their
    // absence. Disconnect the inputs for HDG and ROT or set their data to invalid (for example
    // by wrong checksum, "valid/invalid" flag). (1)

    // If facilities to configuration “rate of turn sensor not connected” is provided activate this function.
    // The ALR sentence BIIT ID 035 is NOT output on the PI.
    if (CSetupMgr::getInst()->GetEnableExtROT())
    {
        CAlarmThing::SendOutNmeaALR(pAlarmThing, bImmediately);
    }
}
