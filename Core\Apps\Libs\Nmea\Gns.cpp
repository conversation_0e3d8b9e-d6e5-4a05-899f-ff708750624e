/**
 * @file    Gns.cpp
 * @brief   Gns class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Gns.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * GNS - 
 *
 * $-- GNS, hhmmss.ss,llll.ll,a,yyyyy.yy,a,c--c,xx,x.x,x.x,x.x,x.x,x.x,a*hh<CR><LF>
 *           |           |----|     |----|  |   |   |   |   |   |   |  |
 *           1           2          3       4   5   6   7   8   9   10 11
 *
 * 1. UTC of position
 * 2. Latitude, N/S
 * 3. Longitude, E/W
 * 4. Mode indicator
 * 5. Total number of satellites in use, 00-99
 * 6. HDOP
 * 7. Antenna altitude
 * 8. Geoidal separation
 * 9. Age of differential data
 * 10. Differential reference station ID
 * 11. Navigational status indicator
 * 
 ******************************************************************************/
CGns::CGns() : CSentence()
{
    ClearData();
}

void CGns::ClearData(void)
{
	CAisLib::SetDefaultSysTime(&m_xUtcTime);

    m_rRcvLatVal = 0.0;
    m_rRcvLonVal = 0.0;

    m_uPosModeIndi = POS_MODE_NONE;

    m_dwPosModeTick = 0;
    m_dwPosValidTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CGns::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    int   nHour,nMin,nSec;
    char vLatBuf[32];
    char vLonBuf[32];
    BOOL bTimeStampValid;

    GetFieldString(pszSentence, 1, pstrTmp);     // UTC of position
    nHour   = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
    nMin    = (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
    nSec    = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';

    bTimeStampValid = (strlen(pstrTmp) >= 6 && CAisLib::IsValidAisSysTime(nHour, nMin, nSec));
    if(bTimeStampValid)
    {
        m_xUtcTime.nHour = nHour;
        m_xUtcTime.nMin    = nMin;
        m_xUtcTime.nSec    = nSec;
    }
    else
    {
    	CAisLib::SetDefaultSysTime(&m_xUtcTime);
    }

    GetFieldString(pszSentence, 2, pstrTmp);     // Latitude
    memmove(vLatBuf,pstrTmp,12); vLatBuf[12] = 0x00;

    GetFieldString(pszSentence, 3, pstrTmp);     // N or S
    vLatBuf[15] = pstrTmp[0];

    GetFieldString(pszSentence, 4, pstrTmp);     // Longitude
    memmove(vLonBuf,pstrTmp,12); vLonBuf[12] = 0x00;

    GetFieldString(pszSentence, 5, pstrTmp);     // E or W
    vLonBuf[15] = pstrTmp[0];

    memset(pstrTmp, 0, 2);
    GetFieldString(pszSentence, 6, pstrTmp);     // Mode indicator

    int nPosModeGps     = POS_MODE_NONE;
    int nPosModeGlonass = POS_MODE_NONE;
    int nPosModeGalileo = POS_MODE_NONE;
    int nPosModeIndi    = POS_MODE_NONE;

    int nModeIndiLen = strlen(pstrTmp);
    if(nModeIndiLen > 0)
    {
        nPosModeGps = CGps::ParsePosModeIndicator(pstrTmp[0]);
        nPosModeIndi= MIN(nPosModeIndi, nPosModeGps);
    }
    else
        nPosModeGps = POS_MODE_NONE;

    if(nModeIndiLen > 1)
    {
        nPosModeGlonass    = CGps::ParsePosModeIndicator(pstrTmp[1]);
        nPosModeIndi    = MIN(nPosModeIndi, nPosModeGlonass);
    }
    else
        nPosModeGlonass= POS_MODE_NONE;

#ifdef __ENABLE_GNSS_GALILEO__
    if(nModeIndiLen > 2)
    {
        nPosModeGalileo = CGps::ParsePosModeIndicator(pstrTmp[2]);
        nPosModeIndi    = MIN(nPosModeIndi, nPosModeGalileo);
    }
    else
        nPosModeGalileo    = POS_MODE_NONE;
#endif

    if(CGps::IsModeIndicatorTrustable(nPosModeIndi))
    {
        m_uPosModeIndi = nPosModeIndi;
        m_dwPosModeTick = SysGetSystemTimer();

        if(ConvertPosition(vLatBuf, vLonBuf, &m_rRcvLatVal, &m_rRcvLonVal))
        {
            m_dwPosValidTick = SysGetSystemTimer();
        }
    }

    return true;
}

/**
 * @brief Check received position data is valid or not
 */
bool CGns::IsValidPosData(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_DATA_VALID_TIMEMS)
    {
        return true;
    }

    return false;
}

/**
 * @brief Get position mode indicatior
 */
UINT8 CGns::GetPosModeIndi(void)
{
    return m_uPosModeIndi;
}

/**
 * @brief Get position mode tick counter
 */
DWORD CGns::GetPosModeTick(void)
{
    return m_dwPosValidTick;
}

/**
 * @brief Get latitude positon
 */
double CGns::GetLatVal(void)
{
    return m_rRcvLatVal;
}

/**
 * @brief Get longitude position
 */
double CGns::GetLonVal(void)
{
    return m_rRcvLonVal;
}

/**
 * @brief Get UTC time
 */
SYS_TIME CGns::GetUtcTime(void)
{
    return m_xUtcTime;
}

/**
 * @brief Call function periodically
 */
void CGns::RunPeriodically(void)
{
    if (m_dwPosValidTick && SysGetDiffTimeMili(m_dwPosValidTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosValidTick = 0;
    }

    if (m_dwPosModeTick && SysGetDiffTimeMili(m_dwPosModeTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        m_dwPosModeTick = 0;
        m_uPosModeIndi = 0;
    }
}
