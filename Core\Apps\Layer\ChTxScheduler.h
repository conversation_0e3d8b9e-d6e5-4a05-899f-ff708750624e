#ifndef __CHTXSCHEDULER_H__
#define __CHTXSCHEDULER_H__

#include "DataType.h"
#include "AllConst.h"
#include "ChannelMgr.h"
#include "FrameMapMgr.h"

class CChTxScheduler
{
public:    
    enum consts
    {
        MAX_ITDMA_TXCNT = 10,
    };

    CChTxScheduler(CChannelMgr *pChannel);
    ~CChTxScheduler(void);

public:
    void    Initialize(void);

    void    SetChReportInterval(int nTotalNI, float fChReportIntervalSec);
    void    SetChNewReportIntervalSec(float fReportIntervalSec);
    void    SetStaticReportIntervalSec(DWORD dwStaticIntervalSec);
    void    SetLongRangeReportInterval(UINT16 uLongRangeIntervalSec);
    void    InitLongRangeReportSecCh(DWORD dwSec);
    void    SetNSS(INT16 uSlotID);
    void    InitStaticReportSecCh(DWORD dwSec);
    INT16   GetNSS(void);
    void    IncNS(void);
    void    ResetNextStartSI(void);
    void    IncNextStartSI(void);
    void    DecNextStartSI(void);
    bool    CheckRRvalid(void);
    void    ShiftFrameIndex(INT16 nShiftSlot);

    void    SetPosMsgSlotColumnRA(int wNextTxSlotID, int nSlotOffset, int nPosMsgID, int nSlotTimeOut=TMO_UNKNOWN);
    int     SetPosSlotColumnCheckTmoSO(int wTxSlotID, int nPosMsgID, int nCurStartSI, int nSizeSI, bool bCheckTmoZero);

    bool    ProcessPeriodicStaticVoyageMsgTx(bool bTxUnconditionally);
    bool    ProcessLongRangeMsgTx(void);
    void    SetNextTempItdmaSI(void);
    bool    SetReportRateByITDMA(bool bSet);
    bool    ProcessTempUseReportByITDMA_Ch(void);

    bool    AllocUnscheduledMultiSlotMsg(UINT16 uMsgID, WORD wStartSI, BYTE uNumSlot, bool bCheckSoTdmaSlot, BYTE nBuffIdx, INT16 nSizeSI=RATDMA_SI, int *pnTxSlot=NULL, bool bFailNoExistingSO=FALSE);
    int     UrgencyAllocSO(int nMsgID, const int nStartSI, const int nSizeSI);

    bool    ProcessPhaseNetworkEntry(int nEntrySlot, int nNSS);
    void    ProcessPhaseFirstFrame(void);
    void    ProcessPhaseRoutine(void);
    void    ProcessPhaseRoutine_SO(void);
    void    ProcessPhaseRoutine_IT(void);
    void    TerminatePosReportSO(void);
    bool    ProcessPhaseChgReportRateFirst(void);
    bool    ProcessPhaseChgReportRateFirst_MasterCH(void);
    bool    ProcessPhaseChgReportRateFirst_SlaveCH(void);
    void    ProcessPhaseChgReportRateSecond(void);
    bool    RunPhaseAssignedSlot(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD wIncrementTotal, WORD wIncrementCh, int nTimeOut);
    void    ReissueSlotAssignedMode(WORD wFrSlotIDStart, int nTimeOut);
    void    TerminateSlotAssignedMode(WORD wLastAssignedTxSlot);

    void    ResetLastScheduledTxSlotID(void);
    void    ProcessTransmit(void);
    bool    PrepareTransmitFrameMapSlot(WORD wSlotID);

    void    ProcOpPhase(void);
    void    ProcessSlotChangeTime_Scheduler(void);

    void    RunProcessTxScheduler(void);
    void    RunPeriodicallyTxScheduler(void);

    static  void    SetAssignedRRTimeOut(void);

public:
    CChannelMgr *m_pChannel;

    float   m_fChReportIntervalSec;
    float   m_fChReportRate;
    float   m_fChReportRateHalf;

    int     m_nSizeSI;
    int     m_nSizeHalfSI;
    int     m_nNI;          //Nominal increment

    float   m_fNewReportIntervalSecSO;

    INT16   m_nNSS;         // Nominal start slot
    INT16   m_nNS;          // Nominal slot
    INT16   m_nNextStartSI;

    INT8    m_nAddCntITDMA;
    int     m_nNextItdmaTxCnt;
    int     m_nItdmaStartSI;
    int     m_nItdmaNI;
    int     m_nItdmaSizeSI;
    int     m_nNextItdmaTxSlotId;

    int     m_nFirstFrameTxCnt;
    INT16   m_nFirstFrameTxSlotId;

    int     m_nChangeRRphaseTxCnt;
    INT16   m_nChangeRRphaseTxSlotId;

    DWORD   m_dwStaticReportIntervalSec;
    DWORD   m_dwStaticReportNextSec;

    DWORD   m_dwLongRangeReportIntervalSec;
    DWORD   m_dwLongRangeTxNextSec;

    INT16   m_nLastScheduledTxSlotID;

    DWORD   m_dwSendPosReportTick;

    DWORD   m_dwRoutineAllocLastCheckSec;
    WORD    m_wRoutineAllocLastCheckSlot;
    int     m_nRoutinePosMsgID;

    DWORD   m_dwChLastPosTxSec;

    int     m_nRoutineNextTxSlot;
    int     m_nUrgencyTxSlotID;

    static  int     m_nRRChgTimeOutMin;
    static  DWORD   m_dwRRTimeOutStartTick;
    static  DWORD   m_dwRRTimeOutMS;
    static  int     m_nRRChgSlaveNewSI;
};
#endif//__CHTXSCHEDULER_H__
