#include "SysConst.h"
#include "SysLib.h"
#include "UserDirMgr.h"
#include "RosMgr.h"
#include "LayerNetwork.h"
//#include "ChannelMgr.h"
#include "DSCMgr.h"
#include "LayerPhysical.h"
#include "PI.h"
#include "MKD.h"
#include "LayerTransport.h"

CLayerTransport::CLayerTransport()
{
    CLayerNetwork::getInst();
}

CLayerTransport::~CLayerTransport()
{
}

/**
 * @brief Initialize the transport layer
 */
void CLayerTransport::Initialize(void)
{
    CLayerNetwork::getInst()->Initialize();

    CROSMgr::getInst()->SetTxPowerModeROS(AIS_TX_POWER_HIGH);    // 12.5W(default)
    CDSCMgr::getInst()->SetRxChannelNumber(DSC_DEFAULT_CH_NUM);
}

/**
 * @brief Initialize all channels
 */
void CLayerTransport::InitAllChannels(void)
{
    CLayerNetwork::getInst()->GetChPrimary()->InitChannel();
    CLayerNetwork::getInst()->GetChSecondary()->InitChannel();

    CROSMgr::getInst()->SetTxPowerModeROS(AIS_TX_POWER_HIGH);    // 12.5W(default)
    CDSCMgr::getInst()->SetRxChannelNumber(DSC_DEFAULT_CH_NUM);
}
