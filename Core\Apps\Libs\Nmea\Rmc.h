/**
 * @file    Rmc.h
 * @brief   Rmc header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType.h"
#include "Sentence.h"

#ifndef __RMC_H__
#define __RMC_H__

/******************************************************************************
 * 
 * RMC - 
 *
 * $--RMC,hhmmss,A,llll.ll,a,yyyyy.yy,a,x.x.x.x,xxxxxx,x.x,a,a,a*hh<CR><LF>
 *          |    |    |         |        |   |     |    |  |   |
 *          1    2    3         4        5   6     7    8  9   10
 *
 * 1. UTC of positon fix
 * 2. Status A=data valid V = navigation receiver warning
 * 3. Latitude, N/S
 * 4. Longitude, E/W
 * 5. Speed over ground, knots
 * 6. Course over ground, degrees true
 * 7. Date: dd/mm/yy
 * 8. Magnetic variation, degrees, E/W
 * 9. Mode indicator
 * 10. Navigational status
 * 
 ******************************************************************************/
class CRmc : public CSentence
{
public:
    CRmc();
    void ClearData(void);

    bool Parse(const char *pszSentence);
    bool IsValidPosData(void);
    bool IsValidSpeedData(void);
    bool IsValidCourseData(void);
    bool IsValidUtcData(void);

    UINT8 GetPosModeIndi(void);
    DWORD GetPosModeTick(void);

    double GetLatVal(void);
    double GetLonVal(void);

    SYS_DATE GetUtcDate(void);
    SYS_TIME GetUtcTime(void);

    int  GetCourse(void);
    int  GetSpeed(void);
    int  GetVelocity(void);

    void RunPeriodically(void);

protected:
    SYS_DATE m_xUtcDate;
    SYS_TIME m_xUtcTime;
    double  m_rRcvLatVal;
    double  m_rRcvLonVal;
    int     m_nSpdData;
    int     m_nVelData;
    int     m_nCrsData;

    UINT8   m_uPosModeIndi;
    DWORD   m_dwPosModeTick;
    DWORD   m_dwPosValidTick;
    DWORD   m_dwSpdValidTick;
    DWORD   m_dwCrsValidTick;
    DWORD   m_dwUtcValidTick;
};

#endif /* __RMC_H__ */

