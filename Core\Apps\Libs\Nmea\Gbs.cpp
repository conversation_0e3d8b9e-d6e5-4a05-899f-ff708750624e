/**
 * @file    Gbs.cpp
 * @brief   Gbs class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Gbs.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * GBS - 
 *
 * $--GBS,hhmmss.ss,x.x,x.x,x.x,xx,x.x,x.x,x.x,h,h*hh
 *            |      |   |   |   |  |   |   |  | |
 *            1      2   3   4   5  6   7   8  9 10
 * 
 * 1. UTC time of the GGA or GNS fixassociated with this sentence.
 * 2. Expected Error in latitude
 * 3. Expected Error in longitude
 * 4. Expected Error in altitude
 * 5. ID number of most likely failed satellite
 * 6. Probalility of missed detection for most likely failed satellite
 * 7. Estimate of bias in meters on most likely failed stellite
 * 8. Standard deviation of bias estimate
 * 9. GNSS System ID
 * 10. GNSS Signal ID
 * 
 ******************************************************************************/
CGbs::CGbs() : CSentence()
{
    ClearData();
}

void CGbs::ClearData(void)
{
	CAisLib::SetDefaultSysTime(&m_xUtcTime);

    m_rErrLat = 0;
    m_rErrLon = 0;
    m_rErrAlt = 0;
    m_nFailedSatID = 0;
    m_rProbability = 0;
    m_rBiasMeter = 0;
    m_rDeviation = 0;
    m_rHPL = 0;

    m_bGbsValid = false;
    m_dwRcvTick = 0;
}

/**
 * @brief Parse the sentence
 */
bool CGbs::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    int   nHour,nMin,nSec;

    GetFieldString(pszSentence, 1, pstrTmp);     // hhmmss

    if(strlen(pstrTmp) < 6)
    {
        return false;
    }

    nHour   = (pstrTmp[0] - '0') * 10 + pstrTmp[1] - '0';
    nMin    = (pstrTmp[2] - '0') * 10 + pstrTmp[3] - '0';
    nSec    = (pstrTmp[4] - '0') * 10 + pstrTmp[5] - '0';

    if(!CAisLib::IsValidAisSysTime(nHour, nMin, nSec))
    {
        return false;
    }

    m_xUtcTime.nHour = nHour;
    m_xUtcTime.nMin  = nMin;
    m_xUtcTime.nSec  = nSec;

    GetFieldString(pszSentence, 2, pstrTmp);     // Expected error in latitude
    if (strlen(pstrTmp) < 1)
        return false;

    m_rErrLat = (float)atof(pstrTmp);

    GetFieldString(pszSentence, 3, pstrTmp);     // Expected error in longitude
    if (strlen(pstrTmp) < 1)
        return false;

    m_rErrLon = (float)atof(pstrTmp);

    GetFieldString(pszSentence, 4, pstrTmp);     // Expected error in altitude
    if (strlen(pstrTmp) < 1)
        return false;

    m_rErrAlt = (float)atof(pstrTmp);

    GetFieldString(pszSentence, 5, pstrTmp);     // ID number of most likely failed satellite
    m_nFailedSatID = atoi(pstrTmp);

    GetFieldString(pszSentence, 6, pstrTmp);     // Probability of missed detection for most likely failed satellite
    m_rProbability = (float)atof(pstrTmp);

    GetFieldString(pszSentence, 7, pstrTmp);     // Estimate of bias on most likely failed satellite
    m_rBiasMeter = (float)atof(pstrTmp);

    GetFieldString(pszSentence, 8, pstrTmp);     // Standard deviation of bias estimate
    m_rDeviation = (float)atof(pstrTmp);

    m_rHPL = sqrtf(m_rErrLat * m_rErrLat + m_rErrLon * m_rErrLon);

    m_dwRcvTick = SysGetSystemTimer();
    m_bGbsValid = true;

    return true;
}

/**
 * @brief Get GBS data valid status
 */
bool CGbs::GetGbsValidStatus(void)
{
    return m_bGbsValid;
}

/**
 * @brief Check sentence valid status
 */
bool CGbs::IsValidGbsData(void)
{
    return (m_bGbsValid && SysGetDiffTimeMili(m_dwRcvTick) > NMEA_POS_LASTDATA_STAYMS);
}

/**
 * @brief Get GBS position accuracy flag
 */
bool CGbs::IsGbsPosAccFlag(void)
{
    return (m_rHPL <= 10.0);
}

/**
 * @brief Call function periodically
 */
void CGbs::RunPeriodically(void)
{
    if (m_bGbsValid && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_POS_LASTDATA_STAYMS)
    {
        ClearData();
    }
}
