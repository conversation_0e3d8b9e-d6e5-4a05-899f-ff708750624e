/**
 * @file    Hdt.cpp
 * @brief   Hdt class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Hdt.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"

/******************************************************************************
 * 
 * HDT - 
 *
 * $--HDT,x.x,T*hh<CR><LF>
 *         |__|
 *         1
 * 
 * 1. Heading, degrees true
 * 
 ******************************************************************************/
CHdt::CHdt() : CSentence()
{
    ClearData();
}

void CHdt::ClearData(void)
{
    m_nHdgData = NMEA_HDG_NULL;
    m_dwRcvTick = 0;
}
    
/**
 * @brief Parse the sentence
 */
bool CHdt::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    int  nTrue;

    GetFieldString(pszSentence, 1, pstrTmp);         // Heading
    if (strlen(pstrTmp) > 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
        {
            return false;
        }

        nTrue   = (int)(atof(pstrTmp));
        if(nTrue < 0 || nTrue > 360)
        {
            return false;
        }

        GetFieldString(pszSentence, 2, pstrTmp);     // True
        if (pstrTmp[0] == 'T')
        {
            m_nHdgData = nTrue;
            m_dwRcvTick = SysGetSystemTimer();
        }
    }

    return true;
}

/**
 * @brief Check received heading data is valid or not
 */
bool CHdt::IsValidHeadingData(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_DATA_VALID_TIMEMS)
    {
        return true;
    }

    return false;
}

/**
 * @brief Get received heading data
 */
int CHdt::GetHeading(void)
{
    return m_nHdgData;
}

/**
 * @brief Call function periodically
 */
void CHdt::RunPeriodically(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_HDG_LASTDATA_STAYMS)
    {
        ClearData();
    }
}
