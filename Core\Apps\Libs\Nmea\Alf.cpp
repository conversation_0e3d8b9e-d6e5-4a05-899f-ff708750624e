/**
 * @file    Alf.cpp
 * @brief   Alf class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "Alf.h"

/******************************************************************************
 * 
 * ALF - BAM Alert sentence
 *
 * $--ALF,x,x,x,hhmmss.ss,a,a,a,aaa,x.x,x.x,x.x,x,c--c*hh<CR><LF>
 *        | | |     |     | | |  |   |   |   |  |   |
 *        1 2 3     4     5 6 7  8   9   10  11 12  13
 *
 * 1. Total number of ALF sentences for this message, 1 to 2
 * 2. Sentence number, 1 to 2
 * 3. Sequential message identifier, 0 to 9
 * 4. Time of last change (null or UTC)
 * 5. Alert category, A, B or C
 * 6. Alert priority, E, A, W or C 
 * 7. Alert state, A, S, N, O, U or V 
        active-unacknowledged: V
        active-silenced: S
        active-acknowledged or active: A
        active-responsibility transferred: O
        rectified-unacknowledged: U
        normal: N
 * 8. Manufacturer mnemonic code
 * 9. Alert identifier
 * 10. Alert instance, 1 to 999999
 * 11. Revision counter, 1 to 99
 * 12. Escalation counter, 0 to 9
 * 13. Alert text
 *
 ** 
 ******************************************************************************/
CAlf::CAlf() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CAlf::Parse(const char *pszSentence)
{

    return true;
}

/**
 * @brief Make the ALF sentence
 * @param pszSentence The sentence to be made
 * @param pAlarmThing The alarm thing structure
 * @return The length of the sentence
 */
int32_t CAlf::MakeSentence(char *pszSentence, CAlarmThing *pAlarmThing)
{
    SYS_DATE_TIME *pAlrTime = pAlarmThing->GetAlarmTime();

    if(pAlarmThing->GetAlarmStatus() != BAM_ALERT_STAT_NORMAL)
    {
        char pstrOutMsg[RX_MAX_DATA_SIZE];

        if(pAlrTime->nValid)
        {
            sprintf(pszSentence, "$AIALF,2,1,%d,%02d%02d%02d,%c,%c,%c,,%d,%d,%d,%d,",
                    pAlarmThing->GetSequentialMsgID(), 
                    pAlrTime->xTime.nHour, pAlrTime->xTime.nMin, pAlrTime->xTime.nSec,
                    pAlarmThing->GetAlarmCategory(), 
                    pAlarmThing->GetAlarmPriority(), 
                    pAlarmThing->GetAlarmStatus(),
                    pAlarmThing->GetBAMAlertID(), 
                    pAlarmThing->GetBAMAlertInstance(), 
                    pAlarmThing->GetRevisionCounter(), 
                    pAlarmThing->GetEscalationCounter());
        }
        else
        {
            sprintf(pszSentence, "$AIALF,2,1,%d,,%c,%c,%c,,%d,%d,%d,%d,",
                    pAlarmThing->GetSequentialMsgID(),
                    pAlarmThing->GetAlarmCategory(), 
                    pAlarmThing->GetAlarmPriority(), 
                    pAlarmThing->GetAlarmStatus(),
                    pAlarmThing->GetBAMAlertID(), 
                    pAlarmThing->GetBAMAlertInstance(), 
                    pAlarmThing->GetRevisionCounter(), 
                    pAlarmThing->GetEscalationCounter());
        }

        strcat(pszSentence, pAlarmThing->GetBAMAlertMsg1());            // 1st ALF
        CSentence::AddSentenceTail(pszSentence);

        sprintf(pstrOutMsg, "$AIALF,2,2,%d,,,,,,%d,%d,%d,%d,",
            pAlarmThing->GetSequentialMsgID(), 
            pAlarmThing->GetBAMAlertID(), 
            pAlarmThing->GetBAMAlertInstance(), 
            pAlarmThing->GetRevisionCounter(), 
            pAlarmThing->GetEscalationCounter());
        strcat(pstrOutMsg, pAlarmThing->GetBAMAlertMsg2());            // 2st ALF
        CSentence::AddSentenceTail(pstrOutMsg);

        strcat(pszSentence, pstrOutMsg);
    }
    else
    {
        sprintf(pszSentence, "$AIALF,1,1,0,,%c,%c,%c,,%d,%d,%d,%d,",
                pAlarmThing->GetAlarmCategory(), 
                pAlarmThing->GetAlarmPriority(), 
                pAlarmThing->GetAlarmStatus(),
                pAlarmThing->GetBAMAlertID(), 
                pAlarmThing->GetBAMAlertInstance(), 
                pAlarmThing->GetRevisionCounter(), 
                pAlarmThing->GetEscalationCounter());
        strcat(pszSentence, pAlarmThing->GetBAMAlertMsg1());
        CSentence::AddSentenceTail(pszSentence);
    }

    return strlen(pszSentence);
}
