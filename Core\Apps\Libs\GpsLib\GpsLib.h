/**
 * @file    GpsLib.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-06-09
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __GPSLIB_H__
#define  __GPSLIB_H__

#include "DataType.h"

//-------------------------------------------
// Mode indicator, refer GNS of NMEA
//-------------------------------------------
#define POS_MODE_DIFFERENTIAL	0
#define POS_MODE_PRECISE        1
#define POS_MODE_AUTO           2
#define POS_MODE_FLOATRTK       3
#define POS_MODE_RTK            4
#define POS_MODE_ESTIMATED      5
#define POS_MODE_MANUAL         6
#define POS_MODE_NOFIX          7
#define POS_MODE_SIM            8
#define POS_MODE_NONE           9

//-------------------------------------------
#define GRID_ABS_COOR_000       0
#define GRID_ABS_COOR_090       324000000
#define GRID_ABS_COOR_180       648000000
#define GRID_ABS_COOR_360       1296000000

class CGps
{
public:
    // Positioning system mode Indicator
    static int   ParsePosModeIndicator(char cMode);
    static int   ParsePosModeIndicatorGGA(char cMode);
    static bool  IsModeIndicatorTrustable(int nPosMode);

    static REAL  ConvertDstToDst(REAL nDstVal, int nFromUnit, int nToUnit);
    static int   ConvertSpdToSpd(int nSpdVal, int nFromUnit, int nToUnit);
    static int   ConvertCrsToCrs(int nCrsVal, int nFromUnit, int nToUnit);

    static int   CheckCompassValue(int nValue);

    static LGRID RealLatToGrid(LREAL rData);
    static LGRID RealLonToGrid(LREAL rData);
    static LREAL GridLatToReal(LGRID nData);
    static LREAL GridLonToReal(LGRID nData);

    static LREAL DiffLatReal(REAL rLat1, REAL rLat2);
    static LREAL DiffLonReal(REAL rLon1, REAL rLon2);
    static FLOAT DiffLatFloat(FLOAT fLat1, FLOAT fLat2);
    static FLOAT DiffLonFloat(FLOAT fLon1, FLOAT fLon2);

    static bool  CheckLatInGridLatRange(LGRID nLat, LGRID nLatDN, LGRID nLatUP, int nEqualMode);
    static bool  CheckLonInGridLonRange(LGRID nLon, LGRID nLonLeft, LGRID nLonRight, int nEqualMode);

    static FLOAT GetDistanceByFLOAT(FLOAT fLat1, FLOAT fLon1, FLOAT fLat2, FLOAT fLon2, int nDistMode);

    static void  GetDistanceAndCourse(LREAL rLat1, LREAL rLon1, LREAL rLat2, LREAL rLon2, REAL *pDist, LREAL *pCourse, int nDistMode);
    static void  GetDistanceAndCourseInGRC(LREAL rLat1, LREAL rLon1, LREAL rLat2, LREAL rLon2, REAL *pDist, REAL *pCourse, int nDistMode);
    static void  GetDistanceAndCourseInMCR(LREAL rLat1, LREAL rLon1, LREAL rLat2, LREAL rLon2, REAL *pDist, REAL *pCourse, int nDistMode);
};

#endif