/**
 * @file    Hbt.cpp
 * @brief   Hbt class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "SysLib.h" 
#include "SysLog.h"
#include "Hbt.h"

/*****************************************************************************
 *
 * HBT - Heartbeat supervision sentence
 *
 * $--HBT,x.x,A,x*hh<CR><LF>
 *         |  | |
 *         1  2 3
 *
 * 1. Configured repeat interval
 * 2. Equipment status
 * 3. Sequential sentence identifier
 *
 ******************************************************************************/
CHbt::CHbt() : CSentence()
{
    m_bNormalOp = false;
    m_nRepeatInterval = HBT_INTERVAL_DFLT;
    m_dwRcvTick = 0;
}

/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CHbt::Parse(const char *pszSentence)
{
    //-------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.11.1 Minimum keyboard and display
    //-------------------------------------------------------------------------------------------
    // The DTE flag (refer to Recommendation ITU-R M.1371-4/A8-3.3) shall only be set to "1" when
    // there is no means of displaying received text messages. External equipment indicates the
    // availability of a remote MKD functionality by a HBT sentence sent every 30 s. If an SSD
    // sentence is applied the DTE field should be evaluated together with the HBT sentence to
    // define if the external equipment is able to display text messages.
    //-------------------------------------------------------------------------------------------

    char pstrTmp[8];
    GetFieldString(pszSentence, 2, pstrTmp);
    if (pstrTmp[0] == 'A')
    {
        m_bNormalOp = true;
        m_nRepeatInterval = GetFieldInteger(pszSentence, 1);
        m_dwRcvTick = SysGetSystemTimer();
    }

    return true;
}

/**
 * @brief Make the HBT sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CHbt::MakeSentence(char *pszSentence)
{
    return strlen(pszSentence);
}
