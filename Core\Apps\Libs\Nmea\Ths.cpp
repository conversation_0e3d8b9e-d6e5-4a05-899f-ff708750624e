/**
 * @file    Ths.cpp
 * @brief   Ths class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "Ths.h"
#include "SysLib.h"
#include "AisLib.h"
#include "GpsLib.h"
#include "Pilot.h"

#define LATENCY_THS_SEND_TO_PILOTPORT   150     // less than 200ms

/******************************************************************************
 * 
 * THS - 
 *
 * $--THS,x.x,a*hh<CR><LF>
 *         |  |
 *         1  2
 * 
 * 1. Heading, degrees true
 * 2. Mode indicator
 * 
 ******************************************************************************/
CThs::CThs() : CSentence()
{
    ClearData();
}

void CThs::ClearData(void)
{
    m_nHdgData = NMEA_HDG_NULL;
    m_dwRcvTick = 0;
    m_ndwLatencyTHS = 0;
}

/**
 * @brief Parse the sentence
 */
bool CThs::Parse(const char *pszSentence)
{
    char pstrTmp[128];
    int  nTrue;

    GetFieldString(pszSentence, 1, pstrTmp);     // Heading
    if (strlen(pstrTmp) > 0)
    {
        if(!CAisLib::IsValidAisFloatNumStr(pstrTmp, TRUE))
        {
            return false;
        }

        nTrue   = (int)(atof(pstrTmp));
        if(nTrue < 0 || nTrue > 360)
        {
            return false;
        }

        GetFieldString(pszSentence, 2, pstrTmp); // Mode indicator
        if (pstrTmp[0] == 'A')
        {
            m_nHdgData = nTrue;
            m_dwRcvTick = SysGetSystemTimer();

        #ifdef __ENABLE_PILOT__
            // [IEC 61993-2, Ed.3, clause 19.5.10.2]
            // Confirm that the heading and rate of turn values from sources used for position reports are output at the pilot port.
            // [IEC 61993-2, Ed.3, clause 19.7.2]
            // Confirm that heading and rate of turn sentences are output with a latency of less than 200ms.
            // Confirm that heading is output at a rate of 5 sentences per second and rate of turn is output at a rate of 1 sentence per second.
            if (SysGetDiffTimeMili(m_ndwLatencyTHS) >= LATENCY_THS_SEND_TO_PILOTPORT)
            {
                CPILOT::getInst()->SendOutStr(pszSentence);
                m_ndwLatencyTHS = SysGetSystemTimer();
            }
        #endif
        }
    }

    return true;
}

/**
 * @brief Check received heading data is valid or not
 */
bool CThs::IsValidHeadingData(void)
{
    return (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_DATA_VALID_TIMEMS);
}

/**
 * @brief Get received heading data
 */
int CThs::GetHeading(void)
{
    return m_nHdgData;
}

/**
 * @brief Call function periodically
 */
void CThs::RunPeriodically(void)
{
    if (m_dwRcvTick && SysGetDiffTimeMili(m_dwRcvTick) < NMEA_HDG_LASTDATA_STAYMS)
    {
        ClearData();
    }
}
