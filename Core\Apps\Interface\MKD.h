#ifndef __MKD_H__
#define __MKD_H__

#include "DataType.h"
#include "AllConst.h"
#include "PI.h"
#include "GpsBoard.h"

class CMKD : public CPI
{
public:
    CMKD(void);
    ~CMKD();

    static std::shared_ptr<CMKD> getInst() {
        static std::shared_ptr<CMKD> pInst = std::make_shared<CMKD>();
        return pInst;
    }

public:
    DWORD    m_dwRcvSecVFD;

public:
    virtual BYTE    GetDTEFlag();
    virtual BOOL    CheckDTEFlag();

    virtual void    ProcSetupUartSpeed(char *pstrSentence);
    virtual void    SendSetupUartPortSpeed();

    virtual void    ProcSetupUartPortMon(char *pstrSentence);
    virtual void    ProcUartPortTxTest(char *pstrSentence);

    virtual void    ProcSetupLongRange(char *pstrSentence);
    virtual void    SendSetupLongRange();

    virtual void    ProcSetupLongRangeCH1(char *pstrSentence);
    virtual void    SendSetupLongRangeCH1();
    virtual void    ProcSetupLongRangeCH2(char *pstrSentence);
    virtual void    SendSetupLongRangeCH2();

    virtual void    ProcSetupAddrMsgTxRetryCnt(char *pstrSentence);
    virtual void    SendAddrMsgRetransmitCount();

    virtual void    ProcSetupEnableSBAS(char *pstrSentence);
    virtual void    SendSetupEnableSBAS();

    virtual void    ProcSetupEPFS(char *pstrSentence);
    virtual void    SendSetupEPFS();

    virtual void    ProcSetupEnableSilentMode(char *pstrSentence);

    virtual void    ProcSetupEnableExtEPFS(char *pstrSentence);
    virtual void    SendSetupEnableExtEPFS();
    virtual void    ProcSetupEnableHeading(char *pstrSentence);
    virtual void    SendSetupEnableHeading();
    virtual void    ProcSetupEnableROT(char *pstrSentence);
    virtual void    SendSetupEnableROT();
    virtual void    ProcSetupEnableALR14(char *pstrSentence);
    virtual void    SendSetupEnableALR14();
    virtual void    ProcSetupUserKeySSA(char *pstrSentence);
    virtual void    SendSetupUserKeySSA();
    virtual void    ProcSetupExtDimInt(char *pstrSentence);
    virtual void    SendSetupExtDimInt();
    virtual void    ProcSetupPilotPortAccessLevel(char *pstrSentence);
    virtual void    SendSetupPilotPortAccessLevel();

    virtual void    ProcSetupEnableNMEA(char *pstrSentence);
    virtual void    ProcSetupEnableNMEAforEXT(char *pstrSentence);

    virtual void    ProcSetupEnableDgnssFromMsg17(char *pstrSentence);
    virtual void    SendSetupEnableDgnssFromMsg17();

    virtual void    SendOutVoltageDC();
    virtual void    SendOutResultBITE();
    virtual void    SendOutTxLoopbackRcvCnt();
    virtual void    ProcRunComLoopBackCmd(char *pstrCmd);

    virtual void    ProcRunFactoryReset(char *pstrSentence);

    virtual void    ProcAliveMsgVFD(void);

    virtual void    ProcessSUG(char *pstrCmd);
    virtual void    ProcRespSendAllSetupData(char *pstrCmd);

    virtual void    SendInitCmd();
    virtual void    ProcChangeMMSI(char *pstrSentence);
    virtual void    ProcChangeIMO(char *pstrSentence);
    virtual void    SendPowerMode();
    virtual void    SendRequestAllSetupData();
    virtual void    SendFirmwareVersion();
    virtual void    SendSetupNMEA();
    virtual void    SendSetupNMEAforEXT();

    virtual void    SendNotifyFactoryResetDone();
    virtual void    SendAllACAACStoPI();
    virtual void    SendDataTxSWR();
    virtual void    SendAllSetupData();

    virtual void    SendOutNMEA(char *pstrCmd);

    virtual void    RunPeriodicallyMkd();

    virtual void    ProcessACA(char *pstrCmd);
    virtual void    ProcessINLAIQ(char *pstrSentence);
    virtual void    ProcessAIQ(char *pstrCmd);
    virtual void    ProcessINL(char *pstrCmd);

    virtual int     ProcessSentence(char *pstrCmd);
    virtual void    ProcessData(cUart *pUartDbgP);
};

#endif//__MKD_H__
