/**
 * @file    Define_AIS.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef __DEFINE_AIS_H__
#define __DEFINE_AIS_H__


#define AIS_CLASS_B_SO                          0
#define AIS_CLASS_B_CS                          1
//===========================================================================
#define AIS_GRID_MUL_FACTOR                     (3600000)
#define AIS_HIGH_SCALE                          (600000)
#define AIS_POS_LOW_SCALE                       (600)
#define MUL_FACTOR_FULL_AIS_TO_GRID             (AIS_GRID_MUL_FACTOR / AIS_HIGH_SCALE)
#define MUL_FACTOR_HALF_AIS_TO_GRID             (AIS_GRID_MUL_FACTOR / AIS_POS_LOW_SCALE)
//-----------------------------------------------------------------------------
#define AIS_LAT_NULL_INT                        91
#define AIS_LON_NULL_INT                        181
#define AIS_GRID_LAT_NULL_VAL                   (AIS_LAT_NULL_INT * AIS_GRID_MUL_FACTOR)
#define AIS_GRID_LON_NULL_VAL                   (360 * AIS_GRID_MUL_FACTOR)
#define AIS_FULL_LAT_NULL_VAL                   (AIS_LAT_NULL_INT * AIS_HIGH_SCALE)
#define AIS_FULL_LON_NULL_VAL                   (AIS_LON_NULL_INT * AIS_HIGH_SCALE)
#define AIS_HALF_LAT_NULL_VAL                   (AIS_LAT_NULL_INT * AIS_POS_LOW_SCALE)
#define AIS_HALF_LON_NULL_VAL                   (AIS_LON_NULL_INT * AIS_POS_LOW_SCALE)
#define AIS_FLOAT_LAT_NULL_VAL                  (91.0f)
#define AIS_FLOAT_LON_NULL_VAL                  (181.0f)
#define AIS_REAL_LAT_NULL_VAL                   (91.0)
#define AIS_REAL_LON_NULL_VAL                   (181.0)
#define AIS_LOW_LAT_NULL_VAL                    (AIS_LAT_NULL_INT * AIS_POS_LOW_SCALE)
#define AIS_LOW_LON_NULL_VAL                    (AIS_LON_NULL_INT * AIS_POS_LOW_SCALE)
//=============================================================================
#define AIS_FLOAT_LAT_MIN_VAL                   (-90.0f)
#define AIS_FLOAT_LAT_MAX_VAL                   (90.0f)
#define AIS_FLOAT_LON_MIN_VAL                   (-180.0f)
#define AIS_FLOAT_LON_MAX_VAL                   (180.0f)

#define AIS_GRID_LAT_MIN_VAL                    (-90 * AIS_GRID_MUL_FACTOR)
#define AIS_GRID_LAT_MAX_VAL                    (+90 * AIS_GRID_MUL_FACTOR)
#define AIS_GRID_LON_MIN_VAL                    (-180 * AIS_GRID_MUL_FACTOR)
#define AIS_GRID_LON_MAX_VAL                    (+180 * AIS_GRID_MUL_FACTOR)

#define AIS_FULL_LAT_MIN_VAL                    (-90 * AIS_HIGH_SCALE)
#define AIS_FULL_LAT_MAX_VAL                    (+90 * AIS_HIGH_SCALE)
#define AIS_FULL_LON_MIN_VAL                    (-180* AIS_HIGH_SCALE)
#define AIS_FULL_LON_MAX_VAL                    (+180* AIS_HIGH_SCALE)

#define AIS_HALF_LAT_MIN_VAL                    (-90 * AIS_POS_LOW_SCALE)
#define AIS_HALF_LAT_MAX_VAL                    (+90 * AIS_POS_LOW_SCALE)
#define AIS_HALF_LON_MIN_VAL                    (-180* AIS_POS_LOW_SCALE)
#define AIS_HALF_LON_MAX_VAL                    (+180* AIS_POS_LOW_SCALE)
//=============================================================================

//=============================================================================
#define AIS_DATA_BAUD_RATE                      (9600)
#define AIS_DATA_SAMPLES_PER_ONE_BIT            (5)
#define AIS_DATA_SAMPLING_RATE                  (AIS_DATA_BAUD_RATE * AIS_DATA_SAMPLES_PER_ONE_BIT)

#define AIS_BITS_PER_ONE_SLOT                   (256)
#define AIS_MAX_SLOTS_PER_ONE_RX                (5)
#define AIS_MAX_BITS_PER_ONE_RX                 (AIS_BITS_PER_ONE_SLOT * AIS_MAX_SLOTS_PER_ONE_RX)

#define AIS_MAX_RAW_BITS_PER_RX                 1280    // Max. 5 slots
#define AIS_MAX_REAL_RAW_BITS_PER_ONE_RX        1536    // Max. 5 slots + extra 256
#define AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX        (AIS_MAX_REAL_RAW_BITS_PER_ONE_RX / 8 + 11)

#define AIS_SLOTS_PER_ONE_FRAME                 (2250)

#define AIS_SAMPLE_COUNTER_MAX_VAL              (AIS_BITS_PER_ONE_SLOT * AIS_DATA_SAMPLES_PER_ONE_BIT)
#define AIS_SLOT_COUNTER_MAX_VAL                (AIS_SLOTS_PER_ONE_FRAME)
//========================================================================
#define AIS_SLOT_START_DELAY_TICKS_LEN          (48)    // RF-POWER (1ms)
#define AIS_SLOT_RF_RAMP_UP_BITS_LEN            (8)
#define AIS_SLOT_PREAMBLE_BITS_LEN              (24)
#define AIS_SLOT_STARTFLAG_BITS_LEN             (8)
//========================================================================
#define ASC_6BITS_CHR_ERROR_CODE                (0xff)
//========================================================================
#define AIS_IMO_NO_NULL                         0       // 0 = not available = default
#define AIS_EPFS_NULL                           0       // 0 = undefined = default
#define AIS_DRAUGHT_NULL                        0       // 0 = not available = default
#define AIS_ETA_MONTH_NULL                      0       // 0 = not available = default
#define AIS_ETA_DAY_NULL                        0       // 0 = not available = default
#define AIS_ETA_HOUR_NULL                       24      // 24 = not available = default
#define AIS_ETA_MINUTE_NULL                     60      // 60 = not available = default
#define AIS_LAT_VDL_NULL                        (AIS_FULL_LAT_NULL_VAL) // !<  91 * DEG_MUL_FACTOR
#define AIS_LON_VDL_NULL                        (AIS_FULL_LON_NULL_VAL) // !< 181 * DEG_MUL_FACTOR
#define AIS_SOG_VDL_MAX                         1022    // 1022 = 102.2 knots or higher
#define AIS_SOG_VDL_NULL                        1023    // 1023 = not available
#define AIS_COG_VDL_MAX                         3599
#define AIS_COG_VDL_NULL                        3600    // 3600 = not available
#define AIS_HDG_VDL_NULL                        511     // 511 : not available=default
#define AIS_ROT_VDL_NULL                        -128    //-128 : not available
#define AIS_ROT_VDL_MAX                         126     //-128 : not available
#define AIS_ROT_VDL_MIN                         -126    //-128 : not available
#define AIS_ROT_VDL_ABOVE_10DEG_LEFT            -127
#define AIS_ROT_VDL_BELOW_10DEG_LEFT            127
#define AIS_NAV_STATUS_NULL                     15      // 15 = not defined = default
#define AIS_GNSS_ANT_POS_A_NULL                 0       // A : 0 ~ 511, 0 = default
#define AIS_GNSS_ANT_POS_A_MAX                  511     // A : 0 ~ 511, 0 = default
#define AIS_GNSS_ANT_POS_B_NULL                 0       // B : 0 ~ 511, 0 = default
#define AIS_GNSS_ANT_POS_B_MAX                  511     // B : 0 ~ 511, 0 = default
#define AIS_GNSS_ANT_POS_C_NULL                 0       // C : 0 ~ 63,  0 = default
#define AIS_GNSS_ANT_POS_C_MAX                  63      // C : 0 ~ 63,  0 = default
#define AIS_GNSS_ANT_POS_D_NULL                 0       // D : 0 ~ 63,  0 = default
#define AIS_GNSS_ANT_POS_D_MAX                  63      // D : 0 ~ 63,  0 = default
//========================================================================
#define MMSI_NONE                               0
#define AIS_AB_MMSI_NULL                        0
#define AIS_AB_MMSI_START                       200000000
#define AIS_AB_MMSI_LAST                        799999999
#define AIS_SART_MMSI_START                     970000000
#define AIS_SART_MMSI_LAST                      971999999
#define AIS_MOB_MMSI_START                      972000000
#define AIS_MOB_MMSI_LAST                       972999999

#define MMSI_MIN_TYPEAPPMODE_SART               970000000   // AIS-SART MMSI, 970xxyyyy, After being programmed, xx = Manufacturer ID 01 ~ 99, yyyy = The sequence number 0000 ~ 9999
#define MMSI_MAX_TYPEAPPMODE_SART               970009999   // AIS-SART MMSI, 970xxyyyy, After being programmed, xx = Manufacturer ID 01 ~ 99, yyyy = The sequence number 0000 ~ 9999

// refer to IEC-61993-2 6.11.1 Minimum keyboard and display (MKD)
// MOB : 972xxyyyy
// EPIRB : 974xxyyyy
#define MMSI_MIN_ACTIVE_SART                    970010000   // AIS-SART MMSI, xx = Manufacturer ID 01 ~ 99, yyyy = The sequence number 0000 ~ 9999
#define MMSI_MAX_ACTIVE_SART                    979999999   // AIS-SART MMSI, xx = Manufacturer ID 01 ~ 99, yyyy = The sequence number 0000 ~ 9999

#define MMSI_MIN_SART                           MMSI_MIN_TYPEAPPMODE_SART
#define MMSI_MAX_SART                           MMSI_MAX_ACTIVE_SART
//=============================================================================
#define AIS_IMOID_NAN                           0
#define AIS_IMOID_VALID_MIN                     1000000
#define AIS_IMOID_VALID_MAX                     9999999
#define AIS_IMOID_VALID_OFFICIAL_MIN            10000000
#define AIS_IMOID_VALID_OFFICIAL_MAX            1073741823
#define AIS_IMOID_DFLT                          AIS_IMOID_NAN
//============================================================================
// ITU 1371-5, Message 23: Group assignment command
#define AIS_ST_TYPE_ALL_MOBILES                 (0)
#define AIS_ST_TYPE_CLASS_A_ONLY                (1)
#define AIS_ST_TYPE_CLASS_B_ALL                 (2)
#define AIS_ST_TYPE_SAR_AIRBORNE                (3)
#define AIS_ST_TYPE_CLASS_B_SOTDMA_ONLY         (4)
#define AIS_ST_TYPE_CLASS_B_CSTDMA_ONLY         (5)
#define AIS_ST_TYPE_INLAND_WATERWAYS            (6)
#define AIS_ST_TYPE_REGIONAL_USE_7              (7)
#define AIS_ST_TYPE_REGIONAL_USE_8              (8)
#define AIS_ST_TYPE_REGIONAL_USE_9              (9)
#define AIS_ST_TYPE_BS_COVERAGE_AREA            (10)
#define AIS_ST_TYPE_FUTURE_USE_11               (11)
#define AIS_ST_TYPE_FUTURE_USE_12               (12)
#define AIS_ST_TYPE_FUTURE_USE_13               (13)
#define AIS_ST_TYPE_FUTURE_USE_14               (14)
#define AIS_ST_TYPE_FUTURE_USE_15               (15)
#define AIS_ST_TYPE_BASE_STATION                (16)
#define AIS_ST_TYPE_ATON                        (17)
#define AIS_ST_TYPE_INVALID                     (18)
//============================================================================
// IEC 1371-5 Annex8 Table 52
#define AIS_SHIPTYPE_NULL                       0  // 0 = not available or no ship = default
#define AIS_SHIPTYPE_MIN                        0
#define AIS_SHIPTYPE_MAX                        199
#define AIS_SHIPTYPE_DFLT                       AIS_SHIPTYPE_NULL
//============================================================================
// ITU-R M.1371-5 Table-48 (Page: 111)----- MSG01
#define AIS_NAV_STATUS_UNDER_WAY_ENGINE         (0)
#define AIS_NAV_STATUS_AT_ANCHOR                (1)
#define AIS_NAV_STATUS_NOT_UNDER_COMMAND        (2)
#define AIS_NAV_STATUS_MOORED                   (5)
#define AIS_NAV_STATUS_AGROUND                  (6)
#define AIS_NAV_STATUS_UNDER_WAY_SAILING        (8)

#define AIS_NAV_STATUS_RESERVED_HIGHSPEED_CRAFT (9)
#define AIS_NAV_STATUS_RESERVED_WING_IN_GROUND  (10)
#define AIS_NAV_STATUS_TOWING_ASTERN            (11)
#define AIS_NAV_STATUS_PUSHING_TOWING_ALONGSIDE (12)
#define AIS_NAV_STATUS_RESERVED_FOR_THEFUTURE   (13)

#define AIS_NAV_STATUS_AIS_SART                 (14)
#define AIS_NAV_STATUS_UNDEFINED                (15)
#define AIS_NAV_STATUS_NONE_NONE                (255)  // Navigational Status 필드가 없는 Station에서 사용됨
#define AIS_NAV_STATUS_MIN                      AIS_NAV_STATUS_UNDER_WAY_ENGINE
#define AIS_NAV_STATUS_MAX                      AIS_NAV_STATUS_UNDEFINED
#define AIS_NAV_STATUS_DFLT                     AIS_NAV_STATUS_UNDEFINED
//============================================================================
#define AIS_MANOEUVRE_NOT_AVAIL                 0                        // not availaBLE = default
#define AIS_MANOEUVRE_NOT_ENGAGED               1                        // not engaged in special manoeuvre
#define AIS_MANOEUVRE_ENGAGED                   2                        // engaged in special manoeuvre (i.e. regional passing arrangement on Inland Waterway)
#define AIS_MANOEUVRE_MIN                       AIS_MANOEUVRE_NOT_AVAIL
#define AIS_MANOEUVRE_MAX                       AIS_MANOEUVRE_ENGAGED
#define AIS_MANOEUVRE_DFLT                      AIS_MANOEUVRE_MIN
//============================================================================
#define AIS_DRAUGHT_MIN          AIS_DRAUGHT_NULL
#define AIS_DRAUGHT_MAX                       255
#define AIS_DRAUGHT_DFLT         AIS_DRAUGHT_NULL
//============================================================================
#define AIS_PERSON_MIN                          0
#define AIS_PERSON_MAX                       8191
#define AIS_PERSON_DFLT            AIS_PERSON_MIN
//============================================================================
#define AIS_RR_MODE_NORMAL                    (0)
#define AIS_RR_MODE_DOUBLE                    (1)
//============================================================================
#define LEN_MAX_SHIP_NAME                      20
#define LEN_MAX_DESTINATION                    20
#define LEN_MAX_VENDORID                        7
#define LEN_MAX_CALLSIGN                        7
//============================================================================
#define AIS_ERROR_CODE_GENERAL_ERROR          (0)
#define AIS_ERROR_CODE_NO_ERROR               (1)
//============================================================================
#define AIS_SENTENCE_TYPE_ALARM               (0)
#define AIS_SENTENCE_TYPE_TXT                 (1)
//============================================================================
// refer to ITU-R-1371-5 Annex8 Table 52
#define AIS_EPFD_UNDEFINED                      0
#define AIS_EPFD_GPS                            1
#define AIS_EPFD_GLONASS                        2
#define AIS_EPFD_GPS_GLONASS                    3
#define AIS_EPFD_LORAN_C                        4
#define AIS_EPFD_CHAYKA                         5
#define AIS_EPFD_INS                            6
#define AIS_EPFD_SURVEYED                       7
#define AIS_EPFD_GALILEO                        8
#define AIS_EPFD_NOT_USED9                      9
#define AIS_EPFD_NOT_USED10                    10
#define AIS_EPFD_NOT_USED11                    11
#define AIS_EPFD_NOT_USED12                    12
#define AIS_EPFD_NOT_USED13                    13
#define AIS_EPFD_NOT_USED14                    14
#define AIS_EPFD_INT_GNSS                      15

#define AIS_EPFD_MIN           AIS_EPFD_UNDEFINED
#define AIS_EPFD_MAX            AIS_EPFD_INT_GNSS
//=============================================================================
#define DTE_READY                               0
#define DTE_NOT_AVAIL                           1
//=============================================================================
#define OPMODE_CONTINUOUS                       0       // Continuous operation mode
#define OPMODE_ASSIGNED_RR                      1       // Assigned mode of reporting rate
#define OPMODE_ASSIGNED_SLOT                    2       // Assigned mode of transmission slots
#define OPMODE_NONE                             3

#define OPPHASE_ONBOOT_WAITSYNC                -1
#define OPPHASE_MONITOR_VDL                     0       // Initialization phase
#define OPPHASE_NETWORKENTRY                    1       // Network entry phase
#define OPPHASE_FIRST_FRAME                     2       // first frame phase
#define OPPHASE_ROUTINE                         3       // normal operation phase
#define OPPHASE_CHG_RR_PREP_FIRST               4       // Changing report interval phase step-1
#define OPPHASE_CHG_RR_PREP_SECOND              5       // Changing report interval phase step-2
#define OPPHASE_CH_CHG_PHASE                    6
#define OPPHASE_DIAGNOSTIC                      7

#define ASSIGNED_MODE_NONE                      0
#define ASSIGNED_MODE_BY_MSG16                  1
#define ASSIGNED_MODE_BY_MSG23                  2

#define SLOTSTAT_FREE                           0
#define SLOTSTAT_INT_ALLOC                      1
#define SLOTSTAT_EXT_ALLOC                      2
#define SLOTSTAT_AVAIL                          3
#define SLOTSTAT_UNAVAIL                        4

#define TDMA_SOTDMA                             0
#define TDMA_ITDMA                              1
#define TDMA_RATDMA                             2
#define TDMA_FATDMA                             3
#define TDMA_NONE                               4

//============================================================================
// IEC 62287-1, Table-17 (Page: 39)----- MSG23
#define AIS_RI_MODE_ERROR                    (-1)
#define AIS_RI_MODE_AUTONOMOUS                (0)       // As given by the autonomous mode (MSG23)
#define AIS_RI_MODE_10_MIN                    (1)
#define AIS_RI_MODE_06_MIN                    (2)
#define AIS_RI_MODE_03_MIN                    (3)
#define AIS_RI_MODE_01_MIN                    (4)
#define AIS_RI_MODE_30_SEC                    (5)
#define AIS_RI_MODE_15_SEC                    (6)
#define AIS_RI_MODE_10_SEC                    (7)
#define AIS_RI_MODE_05_SEC                    (8)
#define AIS_RI_MODE_NEXT_SHORTER              (9)
#define AIS_RI_MODE_NEXT_LONGER              (10)
#define AIS_RI_MODE_02_SEC                   (11)       // not applicable to the Class B "CS" or Class B "SO"
//============================================================================
#define POS_REPORT_NONE                         0
#define POS_REPORT_SCHEDULED                    1
#define POS_REPORT_UNSCHEDULED                  2

#define STATIC_REPORT_INTSEC                  360       // 6 minutes

#define NOMINAL_REPORT_180SEC              180.0f       // 3 minutes
#define NOMINAL_REPORT_30SEC                30.0f       // 30 seconds
#define NOMINAL_REPORT_15SEC                15.0f       // 15 seconds
#define NOMINAL_REPORT_10SEC                10.0f       // 10 seconds
#define NOMINAL_REPORT_6SEC                  6.0f       // 6 seconds
#define NOMINAL_REPORT_5SEC                  5.0f       // 5 seconds
#define NOMINAL_REPORT_2SEC                  2.0f       // 2 seconds
#define NOMINAL_REPORT_1SEC                  1.0f       // 1 seconds

#define NOMINAL_REPORT_MAX  NOMINAL_REPORT_180SEC
#define NOMINAL_REPORT_MIN    NOMINAL_REPORT_1SEC
#define NOMINAL_REPOR_NA                  1000.0f

#define REPORT_RATE_DFLT                        6       // Report interval for position report, default : 10 sec

#define STATIC_REPORT_FIRST24_INTSEC          720       // should be within 12 minutes, 720 at the latest
#define STATIC_REPORT_NEXT24_INTSEC         86400       // should be within 24 hours, 86400 at the latest

#define SLOTID_MIN                              0
#define SLOTID_MAX                           2249
#define NUM_SLOT_PER_FRAME                   2250
#define NUM_SLOT_PER_HALFFRAME               1125
#define SLOTID_NONE                         30000

#define NUM_BITS_PER_SLOT                     256
#define NUM_BYTES_PER_SLOT                     32

#define NUM_FRAME_FRAMEMAP                      9
#define NUM_SLOT_FRAMEMAP                       (NUM_SLOT_PER_FRAME * NUM_FRAME_FRAMEMAP)
#define MAX_SLOT_FRAMEMAP                       (NUM_SLOT_FRAMEMAP-1)
#define MAX_SLOT_FRAMEMAP_HALF                  (MAX_SLOT_FRAMEMAP >> 1)

#define MOD_SLOT_RESERVE_SEC             (9 * 60)
#define VALID_SLOT_RESERVE_SEC           (8 * 60)       // 8 min

#define NUM_TXSLOT_AHEAD                        3       // 현재 슬롯으로 부터 NUM_TXSLOT_AHEAD 개만큼 앞선 슬롯까지 송신한다!

#define TX_RESERVE_SLOT_SPACE                  10       // NUM_TXSLOT_AHEAD 보다 큰 값이어야한다!

#define TESTMODE_START_SPACE                   75       // 2초후 테스트 모드 송신 시작
#define TESTMODE_SLOT_SPACE                     3       // 테스트 모드 송신 메시지 간 슬롯 간격

#define TX_CHECK_SLOT_SPACE                    20       // NUM_TXSLOT_AHEAD 보다 큰 값이어야한다!
#define TX_CHECK_SLOT_SPACE_MSG5               35       // NUM_TXSLOT_AHEAD 보다 큰 값이어야한다!

#define SLOT_SPACE_1SEC                        37       // NUM_TXSLOT_AHEAD 보다 큰 값이어야한다! 1초(37.5 슬롯) 이하이어야한다? 최대 1초 앞의 슬롯까지 검색하여 예정된 송신슬롯을 찾는다.

//============================================================================
#define AIS_SLOT_STATUS_FREE                  (0)       // used for free slot
#define AIS_SLOT_STATUS_INT_SC                (1)       // used for scheduled transmission
#define AIS_SLOT_STATUS_INT_UC                (2)       // used for unscheduled transmission
#define AIS_SLOT_STATUS_INT_RS                (3)       // used for response by interrogation (MSG15)
#define AIS_SLOT_STATUS_EXT_EX                (4)       // used for external allocation
#define AIS_SLOT_STATUS_EXT_FT                (5)       // used for FTDMA
//============================================================================
#define ADDR_MSG_ACK_WAIT_MS                 4000
#define ADDR_MSG_ACK_WAIT_SEC                   4
#define ADDR_MSG_ACK_WAIT_SLOT                150

#define SLOT_AHEAD                             -2
#define SLOT_DELAY                              2

#define NUM_MOBILE_ST_MAX                    1000
#define NUM_BASE_ST_MAX                        10

#define NUM_MAX_RESERVE_FRAME                   8

#define TMO_MIN                                 3       // slot time out min.
#define TMO_MAX                                 7       // slot time out max.
#define TMO_UNKNOWN                            -1

#define ITDMA_ALLOCSLOT_TMO                     1

#define FRAMEMAP_NUMFRAME                       1

#define NUM_SLOT_CANDI_MIN                      4       // ITU-R-1371-5 Annex2 3.3.1.2
#define NUM_SLOT_CANDI_MAX                    150       // ITU-R-1371-5 Annex2 3.3.1.2

#define SLOT_REUSE_UNAVAIL_SEC                 61       // 1 min + 1 sec(some space)
#define POS_VALID_SEC                         180

#define RATDMA_SI                             150       // ITU-R-1371-5 Annex2 3.3.4.2.1 The SI for RATDMA should be 150 time slots
#define RATDMA_SI_SEC                           5       // RATDMA_SI * 26.67 = about 4 sec
#define RATDMA_SI_MS                         4000       // RATDMA_SI * 26.67 = about 4 sec

#define RATDMA_SLOT_TIMEOUT         (RATDMA_SI+5)

#define MAX_SOTDMA_INTERVAL_SEC                59       // 보고간격이 1분 미만일때만 SOTDMA 사용

#define MAX_SIZE_SI                           150

#define MSG_TX_FIFO_SIZE                        8

#define CH_BW_25KHZ                             0
#define CH_BW_UNMODE                            3

#define AIR_NONE                                0
#define AIR_ID1_MSG1                            1
#define AIR_ID1_MSG12                           2
#define AIR_ID12_MSG13                          3
#define AIR_ID12_MSG123                         4

#define NUM_BROADMSG                            3
#define MAX_UNSCHE_ADDRMSG                      3       // AIS Msg 6, 12
#define MAX_ADDRMSG_DATA                      160       // Max. 5 slot("RATDMA") : 32x5

#define MAX_LEN_ABM                           936
#define MAX_LEN_BBM                           968

#define SIZE_BUFF_ABM           (MAX_LEN_ABM+100)
#define SIZE_BUFF_BBM           (MAX_LEN_BBM+100)

#define MAX_UNSCHE_BROADMSG                     2       // AIS Msg 8, 14
#define MAX_BROADMSG_DATA                     160       // Max. 5 slot("RATDMA"): 32x5

#define NUM_MAX_SLOTS_TXBLOCK                   5       // the default number of consecutive slots
#define NUM_MAX_BITS_TXBLOCK                  160       // 160 bytes == Max. 5 slots (NUM_MAX_SLOTS_TXBLOCK)

#define MAX_UNSCHE_MSG25                        3
#define MAX_UNSCHE_MSG26                        3

#define AIS_VDM_NMEA_MAX_CHARS                 60       // IEC-61162-1 (Ed.4.0) 8.3.90
#define TYPE_VDM                                0
#define TYPE_VDO                                1


#define TYPE_ABM                                0
#define TYPE_BBM                                1

#define ABK_ADDRESSED_ACK_OK                    0       // 0 = Message (6 or 12) successfully received by the addressed AIS unit;
#define ABK_ADDRESSED_NACK                      1       // 1 = Message (6 or 12) was broadcast, but no acknowledgement by the addressed AIS unit;
#define ABK_TX_FAIL                             2       // 2 = message could not be broadcast (i.e. quantity of encapsulated data exceeds five slots) (6,8,12,14,15 모두에 사용)
#define ABK_BROADCAST_OK                        3       // 3 = requested broadcast of Message (8, 14, 15, 25 or 26) has been successfully completed;
#define ABK_ADDRESSED_ACK_LATE                  4       // 4 = late reception of a Message 7 or 13 acknowledgement that was addressed to this AIS unit (own ship) and referenced as a valid transaction.

#define ABMBBM_SEQNUM_MAX                       9
#define ABMBBM_SEQNUM_NA                       -1
#define ABMBBM_SENTENCENUM_MAX                  9
#define ABMBBM_SENTENCENUM_NA                  -1

//-------------------------------------------------------------------------------------------------
// ITU-R M.1371-5 Annex2 5.2.1 Conversion to transmission packets
// If the length of the data requires a transmission using FATDMA reserved slots exceeding
// five (5) slots (see Table 21 for guidance) or, for a mobile AIS station, if the total number of
// RATDMA transmissions of Messages 6, 8, 12, 14 and 25 in this frame exceeds 20 slots the AIS
// should not transmit the data, and it should respond with a negative acknowledgement to the
// presentation interface.
//-------------------------------------------------------------------------------------------------
#define MAX_ABM1_BIT                           64
#define MAX_ABM2_BIT                          288
#define MAX_ABM3_BIT                          512
#define MAX_ABM4_BIT                          736
#define MAX_ABM5_BIT                          936       // Msg 6,12 - Max.5 solt (968-32= 936)

#define MAX_BBM1_BIT                           96
#define MAX_BBM2_BIT                          320
#define MAX_BBM3_BIT                          544
#define MAX_BBM4_BIT                          768
#define MAX_BBM5_BIT                          968       // Msg 8,14 - Max.5 slot

#define MAX_TX_BITS_MSG25                     128

#define ABM_CH_NO_CH                            0
#define ABM_CH_A                                1
#define ABM_CH_B                                2
#define ABM_CH_BOTH                             3

#define MSG_SLOT1                               1
#define MSG_SLOT2                               2
#define MSG_SLOT3                               3
#define MSG_SLOT4                               4
#define MSG_SLOT5                               5
#define MSG_SLOT_OVER                          -1

#define ADDR_TX_RETRYCNT_MIN                    0
#define ADDR_TX_RETRYCNT_MAX                    3
#define ADDR_TX_RETRYCNT_DFLT                   3

#define MIN_10000                           10000
#define MIN_10                                 10

#define LR_FROM_PORT_PI                         1
#define LR_FROM_PORT_LR                         2

#define TM_YEAR_BASE                         2000
#define TIME_HOUR                            3600       // 60분*60 = 3600 Sec
#define TIME_ONE_DAY               (24*TIME_HOUR)
#define TIME_FIVE_WEEKS         (35*TIME_ONE_DAY)       // 5주=35일*24시간*3600초

#define UTC_SEC_NORMAL                          0       // 홀수초,짝수초
#define UTC_SEC_ODD                             1       // 홀수초
#define UTC_SEC_EVEN                            2       // 짝수초

#define DEFAULT_POS_LONG                      181
#define DEFAULT_POS_LAT                        91

#define DEFAULT_COG                          3600
#define DEFAULT_SOG                          1023

#define COOR_UNIT                         3600000       // 최대한 정밀도를 높이기 위해 3600000으로 곱하거나 나눈다.

#define MAX_MMSI_LR_REQ                        10

#define EVENTLOG_NUM_LOGDATA                   10

#define TYPE_ALARM                              0
#define TYPE_TXT                                1

#define CLASS_B_UNIT_SO                         0
#define CLASS_B_UNIT_CS                         1
#define CLASS_B_UNIT_UNKNOWN                    2

#define NUM_SAVE_NONREPEAT_RCV_SEC              2

#define COMSTAT_SELECTOR_SOTDMA                 0
#define COMSTAT_SELECTOR_ITDMA                  1

#define MAX_ITDMA_SLOT_OFFSET                8192

#define MAX_SLOT_RESERVED_BLOCK                 5       // Max. 5 slots of reservation block

#define SOG_THRESHOLD_DONTMOVE                 30       // 3NM, 3 * NMEA_SCALE_SOG

#define BINARY_UNSTRUCTURED                     0       // unstructured message
#define BINARY_ASM                              1       // Application specific message

#define DESTINATION_BROAST                      0
#define DESTINATION_ADDRES                      1

#define MESSAGE24_PART_A                        0
#define MESSAGE24_PART_B                        1

#define NAVSTAT_WRONG_SEC                    7200       // 2 hour : 2 * 60 * 60, check per every second
#define HDG_COG_DIFF_SEC_CNT                  300       // 5 min : 5 * 60, check per every second
#define GPS_MISMATCH_MIN_CNT                   15       // 15 min, check per every minute

#define ALR_STAT_CHECK_SEC                      1 
#define ALR_STAT_CHECK_ALR09_SEC               60       // 알림 09 는 1분에 한번 체크해야함!

#define ALR_OUT_CHECK_SEC                       1
#define ALR_OUT_SEC_ACTIVE                     30
#define ALR_OUT_SEC_INACTIVE                   60

#define ALC_OUT_CHECK_SEC                      30
#define ALF_OUT_SEC_ACTIVE                    290       // Active 상태인 경우 AFL sentence 송신 주기 5분이내

#define SILINCE_TIMEOUT_SEC                    30

#define VCO_IDLE_FREQ                   162050000       // 162.050MHz : VCO를 Idle상태 유지하는 주파수
#define VCO_BASIC_FREQ                  160000000       // power level을 분리하여 Backup메모리에 저장할 때 사용하는 기준 주파수

#define LR_TXCTRL_CODE_NA                      -1
#define LR_TXCTRL_CODE_ON                       1
#define LR_TXCTRL_CODE_OFF                      0

#define TIMER_SEC_NA                            0

//=============================================================================
#define AIS_DIST_NM_INVALID                100000
#define AIS_DIST_NM_VALID_MAX                 120       // 120 NM
//============================================================================

#define WAIT_TIMESEC_INT_GNSS                 120       // 2 min, maximum wait time for internal GNSS fixed after power on

//============================================================================
// Alarm ID
#define AIS_ALR_ID_TX_MALFUNC                   1
#define AIS_ALR_ID_VSWR_EXCEED                  2
#define AIS_ALR_ID_RX_A_ERROR                   3
#define AIS_ALR_ID_RX_B_ERROR                   4
#define AIS_ALR_ID_RX_DSC_ERROR                 5
#define AIS_ALR_ID_GENERAL_FAIL                 6
#define AIS_ALR_ID_UTC_SYNC_INVALID             7
#define AIS_ALR_ID_MKD_LOST                     8
#define AIS_ALR_ID_GPS_POS_MISMATCH             9
#define AIS_ALR_ID_NAVSTATUS_INCORRECT         10
#define AIS_ALR_ID_HDG_OFFSET                  11
#define AIS_ALR_ID_ACTIVE_AISSART              14
#define AIS_ALR_ID_EXT_DGNSS_INUSE             21
#define AIS_ALR_ID_EXT_GNSS_INUSE              22
#define AIS_ALR_ID_EXT_EPFS_LOST               25
#define AIS_ALR_ID_NO_SENSOR_POS               26
#define AIS_ALR_ID_EXT_SOGCOG_INUSE            27
#define AIS_ALR_ID_INVALID_SOG                 29
#define AIS_ALR_ID_INVALID_COG                 30
#define AIS_ALR_ID_HEADING_LOST                32
#define AIS_ALR_ID_INVALID_ROT                 35

enum _tagAlarmStat
{
    ALARM_STAT_NOALR = 0,   // normal : Inactive, unacknowledged, clear, no alarm, == legacy V,V
    ALARM_STAT_NEW_ALR,     // Active, unacknowledged, == legacy A,V
    ALARM_STAT_ACKED,       // Active, acknowledged, == legacy A,A
    ALARM_STAT_RECTIFIED,   // Rectified, unacknowledged  == legacy V,V
};

//============================================================================
// BAM Alert ID
#define BAM_ALERT_ID_NULL                       0
#define BAM_ALERT_ID_TX_MALFNC               3008
#define BAM_ALERT_ID_VSWR_EXCEED             3116
#define BAM_ALERT_ID_RX_A_ERROR              3008
#define BAM_ALERT_ID_RX_B_ERROR              3008
#define BAM_ALERT_ID_RX_DSC_ERROR            3116
#define BAM_ALERT_ID_GENERAL_FAIL            3062
#define BAM_ALERT_ID_UTC_SYNC_INVALID        3113
#define BAM_ALERT_ID_MKD_LOST                3009
#define BAM_ALERT_ID_GPS_POS_MISMATCH        3013
#define BAM_ALERT_ID_NAVSTATUS_INCORRECT     3019
#define BAM_ALERT_ID_HDG_OFFSET              3013
#define BAM_ALERT_ID_ACTIVE_AISSART          3108
#define BAM_ALERT_ID_EXT_EPFS_LOST           3003
#define BAM_ALERT_ID_NO_SENSOR_POS           3015
#define BAM_ALERT_ID_INVALID_SOG             3119
#define BAM_ALERT_ID_INVALID_COG             3119
#define BAM_ALERT_ID_HEADING_LOST            3119
#define BAM_ALERT_ID_INVALID_ROT             3119

// BAM Alert category
#define BAM_ALERT_AIS_CATEGORY                'B'
// BAM Alert priority
#define BAM_ALERT_PRIORITY_WARNING            'W'       // Warning
#define BAM_ALERT_PRIORITY_CAUTION            'C'       // Caution

enum _tagBAMAlertStat
{
    //-----------------------------------
    // refer to IEC61993-2 ed3 Table5
    //-----------------------------------
    BAM_ALERT_STAT_NORMAL       = 'N',          // normal : Inactive, unacknowledged, clear, no alarm, == legacy V,V
    BAM_ALERT_STAT_ACTIVE_UNACK = 'V',          // Active, unacknowledged, == legacy A,V
    BAM_ALERT_STAT_ACTIVE_SILEN = 'S',          // Active, silenced : until silence period ends, == legacy A,A
    BAM_ALERT_STAT_ACTIVE_ACKED = 'A',          // Active, acknowledged, == legacy A,A
    BAM_ALERT_STAT_RESPON_TRANS = 'O',          // Responsibility transferred : Active, acknowledged, == legacy A,A
    BAM_ALERT_STAT_RECTIF_UNACK = 'U',          // Rectified, unacknowledged  == legacy V,V, None of the alerts required by this document are required to use state Rectified, unacknowledged
};

enum _tagBAMCautionStat
{
    BAM_CAUTION_STAT_NORMAL     = 'N',          // normal : Inactive, unacknowledged, clear, no alarm, == legacy V,V
    BAM_CAUTION_STAT_ACTIVE     = 'A',          // Active : Active, acknowledged, == legacy A,A
};

#define BAM_SEQUENTIAL_NUM_MIN                  0
#define BAM_SEQUENTIAL_NUM_MAX                  9

#define BAM_REVISION_CNTER_MIN                  1
#define BAM_REVISION_CNTER_MAX                 99

#define BAM_ESCALATION_CNT_MIN                  0
#define BAM_ESCALATION_CNT_MAX                  9

//============================================================================
// ID 90~104, 119는 Security Log에서 사용되므로 사용금지
//============================================================================
#define ALRID_SECURITYLOG_MIN                  90
#define ALRID_SECURITYLOG_MAX                 119

#define ALRID_SECURITYLOG_POWEROFF             90
#define ALRID_SECURITYLOG_TX_SHUTDOWN          91
#define ALRID_SECURITYLOG_MMSI_INVALID         92
#define ALRID_SECURITYLOG_RXONLYMODE           93
#define ALRID_SECURITYLOG_05                   94
#define ALRID_SECURITYLOG_06                   95
#define ALRID_SECURITYLOG_07                   96
#define ALRID_SECURITYLOG_08                   97
#define ALRID_SECURITYLOG_09                   98
#define ALRID_SECURITYLOG_10                   99
//============================================================================
// Security log ID
#define SECURITYLOG_POWEROFF                    1
#define SECURITYLOG_SILENT_MODE                 2
#define SECURITYLOG_TX_OFF_BY_CHMGR             3
#define SECURITYLOG_MALFUNCTION                 4
#define SECURITYLOG_INVALID_CONFIG              5
#define SECURITYLOG_RESERVED1                   6
#define SECURITYLOG_RESERVED2                   7
#define SECURITYLOG_RESERVED3                   8
#define SECURITYLOG_RESERVED4                   9

//============================================================================
#define AIS_TIME_STAMP_DEFAULT               (60)
#define AIS_TIME_STAMP_MANUAL                (61)
#define AIS_TIME_STAMP_ESTIMATED             (62)
#define AIS_TIME_STAMP_INVALID               (63)

//============================================================================
// 채널 관련 상수
#define AIS_CHANNEL_AIS1                      (0)
#define AIS_CHANNEL_AIS2                      (1)
#define AIS_CHANNEL_DSC                       (2)
#define AIS_CHANNEL_NONE                      (3)
//============================================================================
#define AIS1_DEFAULT_CH_NUM                (2087)
#define AIS2_DEFAULT_CH_NUM                (2088)

#define DSC_DEFAULT_CH_NUM                   (70)
#define AIS_CH_NUM_NONE                       (0)
//============================================================================
#define AIS_CH_BW_25_0_KHZ                    (0)
#define AIS_CH_BW_12_5_KHZ                    (1)
//============================================================================
#define AIS_TX_POWER_HIGH                     (0)       // 12.5W
#define AIS_TX_POWER_LOW                      (1)       // 1W
#define AIS_TX_POWER_NAN                      (2)
#define AIS_TX_POWER_MAXVALUE    AIS_TX_POWER_LOW
//============================================================================
#define AIS_DEFAULT_TXLR_CH_1                (75)
#define AIS_DEFAULT_TXLR_CH_2                (76)
#define AIS_LR_CH_MIN                         (0)       // LR 송신채널 최소
#define AIS_LR_CH_MAX                      (2300)       // LR 송신채널 최대
//============================================================================
#define AIS_HW_RX_LOCAL_RX1                     0       // RX, AIS-1
#define AIS_HW_RX_LOCAL_TX                      1       // TX, AIS-1/AIS-2
#define AIS_HW_RX_LOCAL_RX3                     2       // RX, DSC
#define AIS_HW_RX_LOCAL_RX2                     3       // RX, AIS-2

//----------------------------------------------------
// MACROs
//----------------------------------------------------
#define RR_TO_INT_SEC(x)                        (!(x) ? NOMINAL_REPOR_NA : (60.0f / (x)))
#define INT_SEC_TO_RR(x)                        (!(x) ? 1 : 60.0f / (x))
#define INT_SLOT_TO_SEC(x)                      (!(x) ? NOMINAL_REPOR_NA : (60.0f / ((float)NUM_SLOT_PER_FRAME / (x))))

#define AIS_TO_POS(x)                           ((double)(x)/COOR_UNIT)

#define RUN_MIN_GET_MACRO(X, Y)                 ((X) <= (Y) ? (X) : (Y))
#define RUN_MAX_GET_MACRO(X, Y)                 ((X) >= (Y) ? (X) : (Y))

#define IsNavStatusNotUnderway(uNavStatus)	\
    (uNavStatus == AIS_NAV_STATUS_AT_ANCHOR || uNavStatus == AIS_NAV_STATUS_MOORED || uNavStatus == AIS_NAV_STATUS_AGROUND)
#endif /*__DEFINE_AIS_H__*/
