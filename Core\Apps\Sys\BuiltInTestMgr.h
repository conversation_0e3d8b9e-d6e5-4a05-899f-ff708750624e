/**
 * @file    BuiltInTestMgr.h
 * @brief   BuiltInTestMgr header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef __BUILTINTESTMGR_H__
#define __BUILTINTESTMGR_H__

#include "DataType.h"
#include "AllConst.h"

// IEC61993-2 Built-in test equipment
// The AIS shall be equipped with BIIT to implement BITE. These tests shall run continuously or
// at appropriate intervals simultaneously with the standard functions of the equipment. These
// tests shall be run whenever software has been updated (see 5.3).
// If any failure or malfunction is detected that will significantly reduce integrity or stop operation
// of the AIS, an alert is initiated.
class CBuiltInTestMgr
{
public:
    CBuiltInTestMgr();
    ~CBuiltInTestMgr();

    static std::shared_ptr<CBuiltInTestMgr> getInst() {
        static std::shared_ptr<CBuiltInTestMgr> pInst = std::make_shared<CBuiltInTestMgr>();
        return pInst;
    }

public:
    void    SetROMError(bool bError);
    void    SetEEPROMError(bool bError);

    bool    IsErrorROM();
    bool    IsErrorEEPROM();
    bool    IsVswrFail();
    bool    IsTxMalFunction();
    bool    IsRxMalFunctionCH1();
    bool    IsRxMalFunctionCH2();
    bool    IsRxMalFunctionChDSC();
    WORD    GetDcVoltageAdcData();
    bool    IsDcVoltageFail();

protected:
    bool    m_bErrorROM;
    bool    m_bErrorEEPROM;
};

#endif//__BUILTINTESTMGR_H__
