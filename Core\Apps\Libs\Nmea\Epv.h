/**
 * @file    Epv.h
 * @brief   Epv header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __EPV_H__
#define __EPV_H__

#define EPV_STAT_FLAG_CMD               1
#define EPV_STAT_FLAG_REQ               2

// IEC 61993-2 ed3(2018-07)
// 7.6.3.6 Configuration using EPV sentence
#define EPV_PROP_SENSOR1_BAUDRATE       101
#define EPV_PROP_SENSOR2_BAUDRATE       102
#define EPV_PROP_SENSOR3_BAUDRATE       103
#define EPV_PROP_LR_BAUDRATE            104
#define EPV_PROP_DGNSS_BAUDRATE         105
#define EPV_PROP_MMSI                   106
#define EPV_PROP_IMO                    107
#define EPV_PROP_LR_CONFIG              108
#define EPV_PROP_LR_CH1                 109
#define EPV_PROP_LR_CH2                 110
#define EPV_PROP_CHANGE_ADMIN_PWD       111
#define EPV_PROP_CHANGE_USER_PWD        112
#define EPV_PROP_SHOW_TESTING_SART      113
#define EPV_PROP_SILENT_MODE            114
#define EPV_PROP_LOCATING_DEVICE_ALERT  115
#define EPV_PROP_SENSOR_ALERT           116
#define EPV_PROP_PILOT_PORT_RESTRICTED  117
// Identifiers for IEC 61162-450 interface configuration
#define EPV_PROP_IP_AND_NETMASK         120
#define EPV_PROP_SFI                    121
#define EPV_PROP_MULTICAST_GROUP        122
#define EPV_PROP_MULTICAST_GROUP2       123
#define EPV_PROP_TRANSMISSION_GROUP     124
// Identifiers for SFI configuration
#define EPV_PROP_PRI_POSITION_SENSOR    130
#define EPV_PROP_SEC_POSITION_SENSOR    131
#define EPV_PROP_PRI_SOG_COG_SENSOR     132
#define EPV_PROP_SEC_SOG_COG_SENSOR     133
#define EPV_PROP_PRI_HDG_SENSOR         134
#define EPV_PROP_SEC_HDG_SENSOR         135
#define EPV_PROP_PRI_ROT_SENSOR         136
#define EPV_PROP_SEC_ROT_SENSOR         137
#define EPV_PROP_PRI_AIS_CONTROL        138
#define EPV_PROP_SEC_AIS_CONTROL        139
#define EPV_PROP_PRI_ALERT_SOURCE       140
#define EPV_PROP_SEC_ALERT_SOURCE       141
// Extended dimensions configuration
#define EPV_PROP_ANTENNA_EA             150
#define EPV_PROP_ANTENNA_EB             151
#define EPV_PROP_ANTENNA_EC             152
#define EPV_PROP_ANTENNA_ED             153

/*****************************************************************************
 *
 * EPV - Command or report equipment property value
 *
 * $--EPV,a,c--c.,c--c,x.x,c--c*hh<CR><LF>
 *        |  |     |    |   |
 *        1  2     3    4   5
 *
 * 1. Sentence status flag
 * 2. Equipment type
 * 3. Unique identifier
 * 4. Property identifier for the property to be set
 * 5. Value of property to be set
 *
 ******************************************************************************/
class CEpv : public CSentence
{
public:
    CEpv();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Make the EPV sentence
     * @param pszSentence The sentence to be made
     * @param nPropertyID The property ID
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, int nPropertyID);

    /**
     * @brief Get the status flag
     * @return The status flag
     */
    static uint8_t GetStatusFlag(void) { return m_nStatusFlag; }

    /**
     * @brief Get the equipment type
     * @return The equipment type
     */
    static char *GetEquipType(void) { return m_chEquipType; }

    /**
     * @brief Get the unique ID
     * @return The unique ID
     */
    static uint32_t GetUniqueID(void) { return m_dwUniqueID; }

    /**
     * @brief Get the property ID
     * @return The property ID
     */
    static uint8_t GetPropertyID(void) { return m_nPropertyID; }

    /**
     * @brief Get the property value
     * @return The property value
     */
    static int32_t GetPropertyInt(void) { return atoi(m_pPropertyValue); }

    /**
     * @brief Get the property value
     * @return The property value
     */
    static char *GetPropertyStr(void) { return m_pPropertyValue; }

public:
    static uint8_t  m_nStatusFlag;
    static char     m_chEquipType[8];
    static uint32_t m_dwUniqueID;
    static uint8_t  m_nPropertyID;
    static char     m_pPropertyValue[64];
};

#endif /* __EPV_H__ */

