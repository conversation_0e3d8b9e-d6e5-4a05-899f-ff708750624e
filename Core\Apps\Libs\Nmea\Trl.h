/**
 * @file    Trl.h
 * @brief   Trl header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __TRL_H__
#define __TRL_H__

/*****************************************************************************
 *
 * TRL - AIS transmitter-non-functioning log
 *
 * $--TRL,x.x,x.x,x,xxxxxxxx,hhmmss.ss,xxxxxxxx,hhmmss.ss,x*hh<CR><LF>
 *        |   |   |   |      |         |        |         |
 *        1   2   3   4      5         6        7         8
 *
 * 1. Total number of log entries 
 * 2. Log entry number 
 * 3. Sequential message identifier 
 * 4. Switch off date 
 * 5. Switch off UTC time 
 * 6. Switch on date 
 * 7. Switch on UTC time 
 * 8. Reason code 
 *
 ******************************************************************************/
class CTrl : public CSentence
{
public:
    CTrl();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the TRL sentence
     * @param pszSentence The sentence to be made
     * @param pstrTalker The talker ID
     * @param pLogData The event log data
     * @param nLogTotalCnt The total number of log entries
     * @param nLogIdx The log entry number
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence, 
                                char *pstrTalker, 
                                EVENTLOG_DATA *pLogData, 
                                int nLogTotalCnt, 
                                int nLogIdx);

public:
    static int8_t m_nSequentialId;
};

#endif /* __TRL_H__ */

