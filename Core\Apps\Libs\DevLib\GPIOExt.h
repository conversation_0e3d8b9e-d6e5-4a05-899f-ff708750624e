#ifndef  __GPIOETC_H__
#define  __GPIOETC_H__

#include "SysConst.h"
#include "gpio.h"

//-------------------------------------------------------------------------------
// MACROS
//-------------------------------------------------------------------------------
#define GetGpioAlertRelayAck()      FALSE
#define SetGpioAlertRelayEn(bOn)
//-------------------------------------------------------------------------------
#define GetGpioLockStatPLL1()       SysGetGPIOxBitData(GPIOK, GPIO_PLL_LD1)
#define GetGpioLockStatPLL2()       SysGetGPIOxBitData(GPIOK, GPIO_PLL_LD2)
#define GetGpioLockStatPLL3()       SysGetGPIOxBitData(GPIOK, GPIO_PLL_LD3)
//-------------------------------------------------------------------------------
#define SetGpioPLL1_LE(bOn)         SysSetGPIOxBitData(GPIOJ, GPIO_DDS1_LE, bOn)
#define SetGpioPLL2_LE(bOn)         SysSetGPIOxBitData(GPIOJ, GPIO_DDS2_LE, bOn)
#define SetGpioPLL3_LE(bOn)         SysSetGPIOxBitData(GPIOJ, GPIO_DDS3_LE, bOn)
//-------------------------------------------------------------------------------
#define SetGpioTurnOnRx(bOn)        SysSetGPIOxBitData(GPIOK, GPIO_RXRF_ON, bOn)
#define GetGpioTurnOnRx()           SysGetGPIOxBitData(GPIOK, GPIO_RXRF_ON)
#define SetGpioTurnOnTx(bOn)        SysSetGPIOxBitData(GPIOK, GPIO_TXRF_ON, bOn)
#define GetGpioTurnOnTx()           SysGetGPIOxBitData(GPIOK, GPIO_TXRF_ON)
#define SetGpioTxPowerOn(bOn)       SysSetGPIOxBitData(GPIOJ, GPIO_VCXO_EN, bOn)
#define GetGpioTxPowerOn()          SysGetGPIOxBitData(GPIOJ, GPIO_VCXO_EN)
//-------------------------------------------------------------------------------
#define GetGpioGpsPPS()             FALSE //SysGetGPIOxBitData(GPIOD, GPIO_GPS_1_PPS)
//-------------------------------------------------------------------------------
#define SetGpioLed_Rx(bLedOn)       SysSetGPIOxBitData(GPIOG, GPIO_RX_LED, !bLedOn)
#define SetGpioLed_Tx(bLedOn)       SysSetGPIOxBitData(GPIOG, GPIO_TX_LED, !bLedOn)
#define SetGpioLed_Error(bLedOn)    SysSetGPIOxBitData(GPIOG, GPIO_ER_LED,  !bLedOn)
#define SetGpioLed_Time(bLedOn)     SysSetGPIOxBitData(GPIOG, GPIO_TIME_LED, !bLedOn)
#define SetGpioLed_Sys1(bLedOn)     SysSetGPIOxBitData(GPIOG, GPIO_SYS_LED1, !bLedOn)
#define SetGpioLed_Sys2(bLedOn)     SysSetGPIOxBitData(GPIOG, GPIO_SYS_LED2, !bLedOn)
#define SetGpioLed_Sys3(bLedOn)     SysSetGPIOxBitData(GPIOG, GPIO_SYS_LED3, !bLedOn)
//-------------------------------------------------------------------------------
#define SetGpioTP1(bOn)             SysSetGPIOxBitData(GPIOD, GPIO_TP1, bOn)
#define SetGpioTP2(bOn)             SysSetGPIOxBitData(GPIOG, GPIO_TP2, bOn)
//-------------------------------------------------------------------------------
#define SetGpioEEPROM_CS(bOn)       SysSetGPIOxBitData(GPIOE, GPIO_EEPROM_CS, bOn)
#define SetGpioEEPROM_WP(bOn)       SysSetGPIOxBitData(GPIOE, GPIO_EEPROM_WP, bOn)
//-------------------------------------------------------------------------------
#ifdef  __cplusplus
extern "C" {
#endif

//-------------------------------------------------------------------------------
// Fucntion prototype
//-------------------------------------------------------------------------------
void InitGpioEtc();
void SetLedAllOn(BOOL bOn);
void BlinkLed_Tx();
void BlinkLed_Rx();
void BlinkLed_Error();
void BlinkLed_Time();
void BlinkLed_Sys1();
void BlinkLed_Sys2();
void BlinkLed_Sys3();
void ToggleTP1();
void ToggleTP2();
//-------------------------------------------------------------------------------

#ifdef  __cplusplus
}
#endif

#endif
