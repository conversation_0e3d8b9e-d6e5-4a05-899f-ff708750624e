/**
 * @file    GpsLib.c
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-06-09
 * 
 * @copyright Copyright (c) 2025
 */

#include <math.h>
#include "GpsLib.h"

//=============================================================================
#define  WGS84_AXIS_A                 6378137.000000
#define  WGS84_AXIS_B                 6356752.314245
#define  WGS84_MEAN_R                 6371009.000000   // (2 * a + b) / 3

#define  EARTH_RADIUS                 (WGS84_MEAN_R / 1852.0)

//============================================================================
#define  GRID_DEG_MUL_FACTOR		  (3600000)

//============================================================================
#define  EPSILON_VALUE_FOR_ZERO       (0.0000000000001)

//============================================================================
#define  MOD_GET_MACRO(Y,X)           ((Y) - (X) * floor((Y) / (X)))

//============================================================================
int CGps::ParsePosModeIndicator(char cMode)
{
    switch(cMode)
    {
    case 'A':
        return POS_MODE_AUTO;
    case 'D':
        return POS_MODE_DIFFERENTIAL;
    case 'E':
        return POS_MODE_ESTIMATED;
    case 'F':
        return POS_MODE_FLOATRTK;
    case 'M':
        return POS_MODE_MANUAL;
    case 'N':
        return POS_MODE_NOFIX;
    case 'P':
        return POS_MODE_PRECISE;
    case 'R':
        return POS_MODE_RTK;
    case 'S':
        return POS_MODE_SIM;
    }
    return POS_MODE_NONE;
}

int CGps::ParsePosModeIndicatorGGA(char cMode)
{
    switch(cMode)
    {
    case '0':
        return POS_MODE_NONE;
    case '1':
    case '3':
        return POS_MODE_AUTO;
    case '2':
        return POS_MODE_PRECISE;
    case '4':
        return POS_MODE_RTK;
    case '5':
        return POS_MODE_FLOATRTK;
    case '6':
        return POS_MODE_ESTIMATED;
    case '7':
        return POS_MODE_MANUAL;
    case '8':
        return POS_MODE_SIM;
    default:
        return POS_MODE_NOFIX;
    }
    return POS_MODE_NONE;
}

bool CGps::IsModeIndicatorTrustable(int nPosMode)
{
    return (nPosMode != POS_MODE_NONE && 
            nPosMode != POS_MODE_ESTIMATED && 
            nPosMode != POS_MODE_MANUAL && 
            nPosMode != POS_MODE_SIM && 
            nPosMode != POS_MODE_NOFIX);
}

REAL CGps::ConvertDstToDst(REAL nDstVal, int nFromUnit, int nToUnit)
{
    REAL  rTemp = nDstVal;

    if (nFromUnit == nToUnit)
        return (rTemp);

    if (nToUnit == DIST_UNIT_NM)
    {
        if (nFromUnit == DIST_UNIT_MI)   rTemp = rTemp / 1.1507790;         // 1 NM = 1.1507790 MI
        if (nFromUnit == DIST_UNIT_KM)   rTemp = rTemp / 1.8520000;         // 1 NM = 1.8520000 KM
    }
    if (nToUnit == DIST_UNIT_MI)
    {
        if (nFromUnit == DIST_UNIT_NM)   rTemp = rTemp * 1.1507790;         // 1 NM = 1.1507790 MI
        if (nFromUnit == DIST_UNIT_KM)   rTemp = rTemp / 1.6093440;         // 1 MI = 1.609344 KM
    }
    if (nToUnit == DIST_UNIT_KM)
    {
        if (nFromUnit == DIST_UNIT_NM)   rTemp = rTemp * 1.8520000;         // 1 NM = 1.8520000 KM
        if (nFromUnit == DIST_UNIT_MI)   rTemp = rTemp * 1.6093440;         // 1 MI = 1.609344 KM
    }

    return (rTemp);
}

int   CGps::ConvertSpdToSpd(int nSpdVal, int nFromUnit, int nToUnit)
{
    int   nTemp = nSpdVal;

    if (nSpdVal == NMEA_SOG_NULL)
        return (nSpdVal);

    if (nFromUnit == nToUnit)
        return (nTemp);

    if (nToUnit == SPD_UNIT_KN)
    {
        if (nFromUnit == SPD_UNIT_MPH)  nTemp = (nTemp * 100 + 5) / 115;   // 1 kt (knot) = 1.15155 mile/h
        if (nFromUnit == SPD_UNIT_KPH)  nTemp = (nTemp * 100 + 5) / 185;   // 1 kt (knot) = 1.85324km/h
    }
    if (nToUnit == SPD_UNIT_MPH)
    {
        if (nFromUnit == SPD_UNIT_KN)   nTemp = (nTemp * 115 + 50) / 100;   // 1 kt (knot) = 1.15155 mile/h
        if (nFromUnit == SPD_UNIT_KPH)  nTemp = (nTemp * 100 + 50) / 161;   // 1 mph = 1.609344 kph
    }
    if (nToUnit == SPD_UNIT_KPH)
    {
        if (nFromUnit == SPD_UNIT_KN)   nTemp = (nTemp * 185 + 50) / 100;   // 1 kt (knot) = 1.85324km/h
        if (nFromUnit == SPD_UNIT_MPH)  nTemp = (nTemp * 161 + 50) / 100;   // 1 mph = 1.609344 kph
    }

    return (nTemp);
}

int   CGps::ConvertCrsToCrs(int nCrsVal, int nFromUnit, int nToUnit)
{
    int   nTemp = nCrsVal;

    if (nFromUnit == nToUnit)
        return (nTemp);

    if (nToUnit == CMPS_UNIT_T)
    {
        if (nFromUnit == CMPS_UNIT_M) nTemp = CheckCompassValue(nCrsVal);
    }
    if (nToUnit == CMPS_UNIT_M)
    {
        if (nFromUnit == CMPS_UNIT_T) nTemp = CheckCompassValue(nCrsVal);
    }

    return (nTemp);
}

//========================================================================
int   CGps::CheckCompassValue(int nValue)
{
    if (nValue >= 3600) nValue -= 3600;
    if (nValue <     0) nValue += 3600;

    return (nValue);
}

//========================================================================
LGRID CGps::RealLatToGrid(LREAL rData)
{
    if (rData == AIS_REAL_LAT_NULL_VAL)
        return (AIS_GRID_LAT_NULL_VAL);
    return ((LGRID)(rData * GRID_DEG_MUL_FACTOR));
}

LGRID CGps::RealLonToGrid(LREAL rData)
{
    if (rData == AIS_REAL_LON_NULL_VAL)
        return (AIS_GRID_LON_NULL_VAL);

    return ((LGRID)(rData * GRID_DEG_MUL_FACTOR));
}

LREAL CGps::GridLatToReal(LGRID nData)
{
    if (nData == AIS_GRID_LAT_NULL_VAL)
        return (AIS_REAL_LAT_NULL_VAL);

    return ((LREAL)nData / GRID_DEG_MUL_FACTOR);
}

LREAL CGps::GridLonToReal(LGRID nData)
{
    if (nData == AIS_GRID_LON_NULL_VAL)
        return (AIS_REAL_LON_NULL_VAL);

    if (nData > GRID_ABS_COOR_180)
        nData = nData - GRID_ABS_COOR_360;

    return ((LREAL)nData / GRID_DEG_MUL_FACTOR);
}

//========================================================================
LREAL CGps::DiffLatReal(LREAL rLat1,LREAL rLat2)
{
      return (fabs(rLat1 - rLat2));
}

LREAL CGps::DiffLonReal(LREAL rLon1, LREAL rLon2)
{
      LREAL rResult;

      rResult = rLon1 - rLon2;

      if (rResult <   0.0)  rResult = rResult + 360.0;
      if (rResult > 180.0)  rResult = 360.0 - rResult;

      return (rResult);
}

FLOAT CGps::DiffLatFloat(FLOAT fLat1, FLOAT fLat2)
{
      return (fabsf(fLat1 - fLat2));
}

FLOAT CGps::DiffLonFloat(FLOAT fLon1, FLOAT fLon2)
{
      FLOAT fResult;

      fResult = fLon1 - fLon2;

      if (fResult <   0.0f)  fResult = fResult + 360.0f;
      if (fResult > 180.0f)  fResult = 360.0f - fResult;

      return (fResult);
}

//========================================================================
bool CGps::CheckLatInGridLatRange(LGRID nLat, LGRID nLatDN, LGRID nLatUP, int nEqualMode)
{
    if (nEqualMode)
        return (nLatDN <= nLat && nLat <= nLatUP);
    return (nLatDN < nLat && nLat <  nLatUP);
}

bool CGps::CheckLonInGridLonRange(LGRID nLon, LGRID nLonLeft, LGRID nLonRight, int nEqualMode)
{
    if (nLonLeft > nLonRight)   // nLonLeft -----0----- nLonRight
    {
        if (nEqualMode)
            return (nLon >= nLonLeft || nLon <= nLonRight);
        return (nLon >  nLonLeft || nLon <  nLonRight);
    }

    if (nEqualMode)
        return (nLonLeft <= nLon && nLon <= nLonRight);
    return (nLonLeft < nLon && nLon <  nLonRight);
}

//========================================================================
FLOAT CGps::GetDistanceByFLOAT(FLOAT fLat1, FLOAT fLon1, FLOAT fLat2, FLOAT fLon2, int nDistMode)
{
    FLOAT fLat1R;
    FLOAT fLatDF, fLonDF;
    FLOAT fDist;

    if(fLat1 == AIS_FLOAT_LAT_NULL_VAL || fLat2 == AIS_FLOAT_LAT_NULL_VAL ||
        fLon1 == AIS_FLOAT_LON_NULL_VAL || fLon2 == AIS_FLOAT_LON_NULL_VAL)
        return AIS_DIST_NM_INVALID;

    fLat1R = fLat1 * M_DEG_TO_RAD_D;

    fLatDF = DiffLatFloat(fLat1, fLat2) * 60.0f;    // deg ==> min
    fLonDF = DiffLonFloat(fLon1, fLon2) * 60.0f;    // deg ==> min

    fLonDF = fLonDF * cosf(fLat1R);

    fDist  = sqrtf(fLatDF * fLatDF + fLonDF * fLonDF);

    if (nDistMode == DIST_UNIT_KM) fDist = fDist * 1.852000f; // 1 NM = 1.8520000 KM
    if (nDistMode == DIST_UNIT_MI) fDist = fDist * 1.150779f; // 1 NM = 1.1507790 MI

    return (fDist);
}

//------------------------------------------------------------------------
void  CGps::GetDistanceAndCourse(LREAL rLat1, LREAL rLon1, LREAL rLat2, LREAL rLon2, REAL *pDist, REAL *pCourse, int nDistMode)
{
      GetDistanceAndCourseInMCR(rLat1, rLon1, rLat2, rLon2, pDist, pCourse, nDistMode);
}

void  CGps::GetDistanceAndCourseInGRC(LREAL rLat1, LREAL rLon1, LREAL rLat2, LREAL rLon2, REAL *pDist, REAL *pCourse, int nDistMode)
{
    REAL rD,rDlo,rTemp;
    REAL rR1,rR2,rR3,rY1;
    REAL rSinR1,rSinR2;
    REAL rCosR1,rCosR2;
    REAL rX1,rX2,rDist;

    if (rLat1 == rLat2 && rLon1 == rLon2)
    {
        *pDist   = 0.0;
        *pCourse = 0.0;
        return;
    }

    rDlo   = DiffLonReal(rLon1,rLon2);
    rR3    = rDlo  * M_DEG_TO_RAD_D;
    rR1    = rLat1 * M_DEG_TO_RAD_D;
    rR2    = rLat2 * M_DEG_TO_RAD_D;

    rSinR1 = sin(rR1);
    rSinR2 = sin(rR2);
    rCosR1 = cos(rR1);
    rCosR2 = cos(rR2);

    rD     = acos(rSinR1 * rSinR2 + rCosR1 * rCosR2 * cos(rR3));
    rDist = rD * EARTH_RADIUS;
    if (nDistMode == DIST_UNIT_KM) rDist = ConvertDstToDst(rDist, DIST_UNIT_NM, nDistMode);
    if (nDistMode == DIST_UNIT_MI) rDist = ConvertDstToDst(rDist, DIST_UNIT_NM, nDistMode);

    if (rLon1 > 180.0) rLon1 = rLon1 - 360.0;
    if (rLon2 > 180.0) rLon2 = rLon2 - 360.0;

    rX1 = rLon1 * M_DEG_TO_RAD_D;
    rX2 = rLon2 * M_DEG_TO_RAD_D;

    rY1 = (rSinR2 - rSinR1 * cos(rD)) / (sin(rD) * rCosR1);
    if (rY1 >= +1.0) rY1 = +1.0;
    if (rY1 <= -1.0) rY1 = -1.0;

    rTemp= acos(rY1);
    if (sin(rX2 - rX1) < 0.0)
        rTemp = M_PI_MUL_2_D - rTemp;

    *pDist = rDist;
    *pCourse = rTemp * M_RAD_TO_DEG_D;
}

void  CGps::GetDistanceAndCourseInMCR(LREAL rLat1, LREAL rLon1, LREAL rLat2, LREAL rLon2, REAL *pDist, REAL *pCourse, int nDistMode)
{
    REAL rLat1R,rLat2R;
    REAL rLon1R,rLon2R;
    REAL rLonW,rLonE,rPhi,rQ,rTc,rD;
    REAL rDist;

    if (rLat1 == rLat2 && rLon1 == rLon2)
    {
        *pDist   = 0.0;
        *pCourse = 0.0;
        return;
    }

    if (rLon1 > 180.0) rLon1 = rLon1 - 360.0;
    if (rLon2 > 180.0) rLon2 = rLon2 - 360.0;

    rLat1R = rLat1 * M_DEG_TO_RAD_D;
    rLat2R = rLat2 * M_DEG_TO_RAD_D;
    rLon1R = rLon1 * M_DEG_TO_RAD_D;
    rLon2R = rLon2 * M_DEG_TO_RAD_D;

    rLonW  = MOD_GET_MACRO(rLon2R - rLon1R, 2.0 * M_PI_VALUE_D);
    rLonE  = MOD_GET_MACRO(rLon1R - rLon2R, 2.0 * M_PI_VALUE_D);
    rPhi   = log(tan(rLat2R / 2.0 + M_PI_VALUE_D / 4.0) / tan(rLat1R / 2.0 + M_PI_VALUE_D / 4.0));

    if (fabs(rLat2R - rLat1R) < EPSILON_VALUE_FOR_ZERO)
        rQ = cos(rLat1R);
    else
        rQ = (rLat2R - rLat1R) / rPhi;

    if (rLonW < rLonE) {      // Westerly rhumb line is the shortest
        rTc = MOD_GET_MACRO(atan2(-rLonW, rPhi), 2.0 * M_PI_VALUE_D);
        rD  = sqrt(rQ * rQ * rLonW * rLonW + (rLat2R - rLat1R) * (rLat2R - rLat1R));
    }
    else {
        rTc = MOD_GET_MACRO(atan2(rLonE, rPhi), 2.0 * M_PI_VALUE_D);
        rD  = sqrt(rQ * rQ * rLonE * rLonE + (rLat2R - rLat1R) * (rLat2R - rLat1R));
    }

    rDist = rD * EARTH_RADIUS;
    if (nDistMode == DIST_UNIT_KM) rDist = ConvertDstToDst(rDist, DIST_UNIT_NM, nDistMode);
    if (nDistMode == DIST_UNIT_MI) rDist = ConvertDstToDst(rDist, DIST_UNIT_NM, nDistMode);

    *pDist = rDist;
    *pCourse = rTc;

    *pCourse *= M_RAD_TO_DEG_D;
    *pCourse = 360.0 - *pCourse;
    if (*pCourse >= 360.0)   *pCourse -= 360.0;
    if (*pCourse <    0.0)   *pCourse += 360.0;
}